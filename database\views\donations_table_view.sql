-- Vue optimisée pour la table de gestion des dons
-- Cette vue combine toutes les informations nécessaires pour l'affichage de la table

CREATE OR REPLACE VIEW donations_table_view AS
SELECT 
    d.id,
    d.numero_don,
    d.type_don,
    d.donneur_nom,
    d.donneur_contact,
    d.donneur_adresse,
    d.demandeur_service,
    d.demandeur_email,
    d.lieu_affectation,
    d.raison_don,
    d.valeur_estimee,
    d.statut,
    d.created_at,
    d.updated_at,
    
    -- Générer un code de don si numero_don n'existe pas
    COALESCE(d.numero_don, 'DON-' || EXTRACT(YEAR FROM d.created_at) || '-' || LPAD(d.id::text, 4, '0')) as code_don,
    
    -- Calculer l'étape actuelle
    COALESCE(
        (SELECT numero_etape 
         FROM gestion_donation_etapes 
         WHERE donation_id = d.id AND statut_etape = 'en_cours' 
         ORDER BY numero_etape ASC 
         LIMIT 1),
        COALESCE(
            (SELECT MAX(numero_etape) + 1 
             FROM gestion_donation_etapes 
             WHERE donation_id = d.id AND statut_etape = 'termine'),
            1
        )
    ) as etape_actuelle,
    
    -- Générer la désignation à partir des items
    CASE 
        WHEN d.items IS NOT NULL AND jsonb_array_length(d.items) > 0 THEN
            CASE 
                WHEN jsonb_array_length(d.items) <= 3 THEN
                    (SELECT string_agg(item->>'nom_item', ', ')
                     FROM jsonb_array_elements(d.items) as item
                     LIMIT 3)
                ELSE
                    (SELECT string_agg(item->>'nom_item', ', ')
                     FROM jsonb_array_elements(d.items) as item
                     LIMIT 3) || ' (+' || (jsonb_array_length(d.items) - 3)::text || ' autres)'
            END
        ELSE 
            COALESCE(d.raison_don, 'Non spécifié')
    END as designation,
    
    -- Informations sur les étapes
    (SELECT jsonb_agg(
        jsonb_build_object(
            'numero', numero_etape,
            'statut', statut_etape,
            'date_debut', date_debut,
            'date_fin', date_fin,
            'en_retard', CASE 
                WHEN statut_etape = 'en_cours' AND date_limite < NOW() THEN true
                ELSE false
            END
        )
        ORDER BY numero_etape
    )
    FROM gestion_donation_etapes 
    WHERE donation_id = d.id) as etapes_info,
    
    -- Nombre total d'items
    CASE 
        WHEN d.items IS NOT NULL THEN jsonb_array_length(d.items)
        ELSE 0
    END as nombre_items,
    
    -- Statut de retard global
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM gestion_donation_etapes 
            WHERE donation_id = d.id 
            AND statut_etape = 'en_cours' 
            AND date_limite < NOW()
        ) THEN true
        ELSE false
    END as en_retard,
    
    -- Dernière activité
    GREATEST(
        d.updated_at,
        COALESCE(
            (SELECT MAX(updated_at) 
             FROM gestion_donation_etapes 
             WHERE donation_id = d.id),
            d.updated_at
        )
    ) as derniere_activite,
    
    -- Informations sur les documents
    (SELECT COUNT(*) 
     FROM gestion_donation_documents 
     WHERE donation_id = d.id) as nombre_documents,
    
    -- Progression en pourcentage
    CASE 
        WHEN d.statut = 'archive' THEN 100
        ELSE 
            COALESCE(
                (SELECT COUNT(*) * 10 
                 FROM gestion_donation_etapes 
                 WHERE donation_id = d.id AND statut_etape = 'termine'),
                0
            )
    END as progression_pourcentage

FROM gestion_donations d
WHERE d.deleted_at IS NULL
ORDER BY d.created_at DESC;

-- Commentaire sur la vue
COMMENT ON VIEW donations_table_view IS 'Vue optimisée pour l''affichage de la table de gestion des dons avec toutes les informations calculées';

-- Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_donations_table_view_statut ON gestion_donations(statut) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_donations_table_view_type ON gestion_donations(type_don) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_donations_table_view_created ON gestion_donations(created_at DESC) WHERE deleted_at IS NULL;
CREATE INDEX IF NOT EXISTS idx_donations_table_view_demandeur ON gestion_donations(demandeur_email) WHERE deleted_at IS NULL;

-- Politiques RLS pour la vue (hérite des politiques de la table principale)
ALTER VIEW donations_table_view OWNER TO postgres;

-- Fonction pour rafraîchir la vue si nécessaire (pour les vues matérialisées futures)
CREATE OR REPLACE FUNCTION refresh_donations_table_view()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Pour l'instant, rien à faire car c'est une vue normale
    -- Si on passe à une vue matérialisée plus tard :
    -- REFRESH MATERIALIZED VIEW donations_table_view;
    
    RAISE NOTICE 'Vue donations_table_view rafraîchie';
END;
$$;

-- Trigger pour mettre à jour automatiquement certaines informations
CREATE OR REPLACE FUNCTION update_donation_table_info()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Mettre à jour le timestamp de dernière modification
    NEW.updated_at = NOW();
    
    -- Générer le numéro de don si pas encore défini
    IF NEW.numero_don IS NULL AND OLD.numero_don IS NULL THEN
        NEW.numero_don = 'DON-' || EXTRACT(YEAR FROM NEW.created_at) || '-' || LPAD(NEW.id::text, 4, '0');
    END IF;
    
    RETURN NEW;
END;
$$;

-- Appliquer le trigger sur la table principale
DROP TRIGGER IF EXISTS trigger_update_donation_table_info ON gestion_donations;
CREATE TRIGGER trigger_update_donation_table_info
    BEFORE UPDATE ON gestion_donations
    FOR EACH ROW
    EXECUTE FUNCTION update_donation_table_info();

-- Vue pour les statistiques rapides de la table
CREATE OR REPLACE VIEW donations_table_stats AS
SELECT 
    COUNT(*) as total_dons,
    COUNT(*) FILTER (WHERE statut != 'archive') as dons_actifs,
    COUNT(*) FILTER (WHERE statut = 'brouillon') as dons_brouillon,
    COUNT(*) FILTER (WHERE statut IN ('bp_cree', 'chez_magasinier', 'chez_transitaire', 'chez_receptionniste', 'bs_cree', 'chez_rve', 'recap_cree', 'envoye_comptabilite')) as dons_en_cours,
    COUNT(*) FILTER (WHERE statut = 'archive') as dons_archives,
    COUNT(*) FILTER (WHERE type_don = 'article') as total_articles,
    COUNT(*) FILTER (WHERE type_don = 'equipement') as total_equipements,
    COUNT(*) FILTER (WHERE EXISTS (
        SELECT 1 FROM gestion_donation_etapes 
        WHERE donation_id = gestion_donations.id 
        AND statut_etape = 'en_cours' 
        AND date_limite < NOW()
    )) as dons_en_retard,
    AVG(CASE 
        WHEN statut = 'archive' THEN 100
        ELSE 
            COALESCE(
                (SELECT COUNT(*) * 10 
                 FROM gestion_donation_etapes 
                 WHERE donation_id = gestion_donations.id AND statut_etape = 'termine'),
                0
            )
    END) as progression_moyenne
FROM gestion_donations
WHERE deleted_at IS NULL;

COMMENT ON VIEW donations_table_stats IS 'Statistiques rapides pour la table de gestion des dons';

-- Fonction pour obtenir les actions disponibles pour un don selon le rôle
CREATE OR REPLACE FUNCTION get_donation_actions(
    donation_id UUID,
    user_role TEXT,
    user_email TEXT
)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    don_record RECORD;
    actions jsonb := '[]'::jsonb;
BEGIN
    -- Récupérer les informations du don
    SELECT * INTO don_record 
    FROM gestion_donations 
    WHERE id = donation_id AND deleted_at IS NULL;
    
    IF NOT FOUND THEN
        RETURN actions;
    END IF;
    
    -- Action "Voir" (toujours disponible)
    actions := actions || jsonb_build_object(
        'type', 'view',
        'label', 'Voir détails',
        'icon', '👁️',
        'class', 'btn-view'
    );
    
    -- Actions selon le rôle et le statut
    CASE user_role
        WHEN 'admin' THEN
            -- Admin peut tout faire
            actions := actions || jsonb_build_array(
                jsonb_build_object('type', 'edit', 'label', 'Modifier', 'icon', '✏️', 'class', 'btn-edit'),
                jsonb_build_object('type', 'validate', 'label', 'Valider', 'icon', '✅', 'class', 'btn-validate'),
                jsonb_build_object('type', 'reject', 'label', 'Refuser', 'icon', '❌', 'class', 'btn-reject')
            );
            
        WHEN 'user' THEN
            -- Utilisateur peut modifier ses propres dons en brouillon
            IF don_record.demandeur_email = user_email AND don_record.statut = 'brouillon' THEN
                actions := actions || jsonb_build_object(
                    'type', 'edit',
                    'label', 'Modifier',
                    'icon', '✏️',
                    'class', 'btn-edit'
                );
            END IF;
            
        WHEN 'magasinier' THEN
            IF don_record.statut IN ('bp_cree', 'chez_magasinier') THEN
                actions := actions || jsonb_build_object(
                    'type', 'validate',
                    'label', 'Traiter',
                    'icon', '✅',
                    'class', 'btn-validate'
                );
            END IF;
            
        WHEN 'transitaire' THEN
            IF don_record.statut = 'chez_transitaire' THEN
                actions := actions || jsonb_build_object(
                    'type', 'validate',
                    'label', 'Valider',
                    'icon', '✅',
                    'class', 'btn-validate'
                );
            END IF;
            
        WHEN 'receptionniste' THEN
            IF don_record.statut = 'chez_receptionniste' THEN
                actions := actions || jsonb_build_object(
                    'type', 'validate',
                    'label', 'Réceptionner',
                    'icon', '✅',
                    'class', 'btn-validate'
                );
            END IF;
            
        WHEN 'rve' THEN
            IF don_record.statut IN ('bs_cree', 'chez_rve') THEN
                actions := actions || jsonb_build_object(
                    'type', 'validate',
                    'label', 'Traiter',
                    'icon', '✅',
                    'class', 'btn-validate'
                );
            END IF;
            
        WHEN 'dg', 'dt', 'ms' THEN
            -- Rôles de direction peuvent refuser
            actions := actions || jsonb_build_object(
                'type', 'reject',
                'label', 'Refuser',
                'icon', '❌',
                'class', 'btn-reject'
            );
    END CASE;
    
    RETURN actions;
END;
$$;

COMMENT ON FUNCTION get_donation_actions IS 'Retourne les actions disponibles pour un don selon le rôle utilisateur';

-- Permissions sur les nouvelles vues et fonctions
GRANT SELECT ON donations_table_view TO authenticated;
GRANT SELECT ON donations_table_stats TO authenticated;
GRANT EXECUTE ON FUNCTION get_donation_actions TO authenticated;
GRANT EXECUTE ON FUNCTION refresh_donations_table_view TO authenticated;
