<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Synchronisation Temps Réel - Supabase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            margin-right: 8px;
        }

        .status-indicator.connected {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .test-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }

        .test-panel h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            margin: 5px;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .messages-list,
        .commands-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
        }

        .message-item,
        .command-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .realtime-message,
        .realtime-command {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .message-header,
        .command-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .message-type,
        .command-status {
            font-size: 12px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 12px;
            background: #667eea;
            color: white;
        }

        .message-time,
        .command-time {
            font-size: 12px;
            color: #666;
        }

        .message-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .message-content {
            color: #666;
            margin-bottom: 10px;
        }

        .message-meta,
        .command-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #888;
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }

        .sync-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
            font-size: 12px;
        }

        .browser-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .browser-id {
            font-weight: bold;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Test Synchronisation Temps Réel</h1>
            <p>Testez la synchronisation bidirectionnelle entre navigateurs</p>
        </div>

        <!-- Info navigateur -->
        <div class="browser-info">
            <div class="browser-id" id="browserInfo">Navigateur: Chargement...</div>
            <p>Ouvrez cette page dans plusieurs navigateurs pour tester la synchronisation</p>
        </div>

        <!-- Barre de statut -->
        <div class="status-bar">
            <div style="display: flex; align-items: center;">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Déconnecté</span>
            </div>
            <div id="userInfo">Non connecté</div>
            <div id="syncStats">0 événements synchronisés</div>
            <button class="btn" onclick="refreshConnection()">🔄 Actualiser</button>
        </div>

        <!-- Statistiques de synchronisation -->
        <div class="sync-stats" id="syncStatsGrid">
            <!-- Les statistiques seront générées dynamiquement -->
        </div>

        <!-- Grille de test -->
        <div class="test-grid">
            <!-- Panel Messages -->
            <div class="test-panel">
                <h3>📨 Messages Temps Réel</h3>

                <div class="form-group">
                    <label>Titre du message:</label>
                    <input type="text" id="messageTitle" placeholder="Titre du message">
                </div>

                <div class="form-group">
                    <label>Contenu:</label>
                    <textarea id="messageContent" placeholder="Contenu du message"></textarea>
                </div>

                <div class="form-group">
                    <label>Type:</label>
                    <select id="messageType">
                        <option value="general">Général</option>
                        <option value="urgent">Urgent</option>
                        <option value="info">Information</option>
                        <option value="warning">Avertissement</option>
                    </select>
                </div>

                <button class="btn success" onclick="createMessage()">📝 Créer Message</button>
                <button class="btn" onclick="loadMessages()">🔄 Charger Messages</button>

                <div class="messages-list" id="messagesList">
                    <p style="text-align: center; color: #666;">Aucun message</p>
                </div>
            </div>

            <!-- Panel Commandes -->
            <div class="test-panel">
                <h3>📦 Commandes Temps Réel</h3>

                <div class="form-group">
                    <label>Fournisseur:</label>
                    <input type="text" id="commandSupplier" placeholder="Nom du fournisseur">
                </div>

                <div class="form-group">
                    <label>Description:</label>
                    <input type="text" id="commandDescription" placeholder="Description de la commande">
                </div>

                <div class="form-group">
                    <label>Montant:</label>
                    <input type="number" id="commandAmount" placeholder="0.00" step="0.01">
                </div>

                <button class="btn success" onclick="createCommand()">📦 Créer Commande</button>
                <button class="btn" onclick="loadCommands()">🔄 Charger Commandes</button>

                <div class="commands-list" id="commandsList">
                    <p style="text-align: center; color: #666;">Aucune commande</p>
                </div>
            </div>
        </div>

        <!-- Actions de test -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" onclick="testRealtimeConnection()">🔌 Tester Connexion</button>
            <button class="btn" onclick="simulateMultipleChanges()">🎭 Simuler Changements Multiples</button>
            <button class="btn danger" onclick="clearAllData()">🗑️ Vider Toutes les Données</button>
        </div>

        <!-- Logs -->
        <div class="log-container" id="testLog"></div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth-complete.js"></script>
    <script src="assets/js/supabase-realtime-complete.js"></script>
    <script src="assets/js/supabase-system-complete.js"></script>

    <script>
        // Variables globales
        let supabaseSystem = null;
        let realtimeModule = null;
        let currentUser = null;
        let browserId = null;
        let syncStats = {
            messagesReceived: 0,
            commandsReceived: 0,
            totalEvents: 0,
            lastEventTime: null
        };

        // Générer un ID unique pour ce navigateur
        browserId = 'Browser_' + Math.random().toString(36).substring(2, 8);

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', async function() {
            log('🚀 Initialisation du test de synchronisation temps réel...', 'info');

            // Afficher l'info du navigateur
            document.getElementById('browserInfo').textContent = `Navigateur: ${browserId}`;

            // Initialiser le système Supabase
            await initializeSystem();

            // Configurer les événements
            setupEventListeners();

            // Démarrer la surveillance
            startStatsMonitoring();
        });

        // Initialiser le système Supabase
        async function initializeSystem() {
            try {
                log('🔧 Initialisation du système Supabase...', 'info');

                supabaseSystem = await initializeSupabaseComplete();

                if (supabaseSystem) {
                    log('✅ Système Supabase initialisé', 'success');

                    // Obtenir le module temps réel
                    realtimeModule = supabaseSystem.getModule('realtime');

                    if (realtimeModule) {
                        log('⚡ Module temps réel disponible', 'success');
                        setupRealtimeCallbacks();
                        updateConnectionStatus('connected', 'Temps réel actif');
                    } else {
                        log('❌ Module temps réel non disponible', 'error');
                        updateConnectionStatus('error', 'Module temps réel indisponible');
                    }

                    // Obtenir l'utilisateur actuel
                    const authModule = supabaseSystem.getModule('auth');
                    if (authModule) {
                        const userInfo = authModule.getUser();
                        currentUser = userInfo.user;
                        updateUserInfo(userInfo.profile);
                    }

                } else {
                    throw new Error('Échec initialisation système');
                }

            } catch (error) {
                log(`❌ Erreur initialisation: ${error.message}`, 'error');
                updateConnectionStatus('error', 'Erreur d\'initialisation');
            }
        }

        // Configurer les callbacks temps réel
        function setupRealtimeCallbacks() {
            if (!realtimeModule) return;

            // Écouter les changements de données
            realtimeModule.on('onDataChange', (data) => {
                handleRealtimeDataChange(data);
            });

            // Écouter les changements de connexion
            realtimeModule.on('onConnect', () => {
                log('⚡ Connexion temps réel établie', 'success');
                updateConnectionStatus('connected', 'Temps réel connecté');
            });

            realtimeModule.on('onDisconnect', () => {
                log('⚡ Connexion temps réel perdue', 'warning');
                updateConnectionStatus('warning', 'Temps réel déconnecté');
            });

            realtimeModule.on('onError', (error) => {
                log(`❌ Erreur temps réel: ${error.message}`, 'error');
            });
        }

        // Gérer les changements de données temps réel
        function handleRealtimeDataChange(data) {
            log(`🔄 Changement temps réel: ${data.table} (${data.eventType})`, 'info');

            // Mettre à jour les statistiques
            syncStats.totalEvents++;
            syncStats.lastEventTime = new Date();

            if (data.table === 'gestion_messages') {
                syncStats.messagesReceived++;
                handleMessageChange(data);
            } else if (data.table === 'gestion_commands') {
                syncStats.commandsReceived++;
                handleCommandChange(data);
            }

            updateSyncStats();
        }

        // Gérer les changements de messages
        function handleMessageChange(data) {
            log(`📨 Message ${data.eventType}: ${data.new?.titre || data.old?.titre}`, 'success');

            if (data.eventType === 'INSERT') {
                addMessageToUI(data.new, true);
            } else if (data.eventType === 'UPDATE') {
                updateMessageInUI(data.new, true);
            } else if (data.eventType === 'DELETE') {
                removeMessageFromUI(data.old.id, true);
            }
        }

        // Gérer les changements de commandes
        function handleCommandChange(data) {
            log(`📦 Commande ${data.eventType}: ${data.new?.numero_commande || data.old?.numero_commande}`, 'success');

            if (data.eventType === 'INSERT') {
                addCommandToUI(data.new, true);
            } else if (data.eventType === 'UPDATE') {
                updateCommandInUI(data.new, true);
            } else if (data.eventType === 'DELETE') {
                removeCommandFromUI(data.old.id, true);
            }
        }

        // Configurer les événements DOM
        function setupEventListeners() {
            // Écouter les événements personnalisés
            document.addEventListener('gestion_messagesUpdate', (e) => {
                log(`📡 Événement DOM reçu: gestion_messagesUpdate`, 'info');
            });

            document.addEventListener('gestion_commandsUpdate', (e) => {
                log(`📡 Événement DOM reçu: gestion_commandsUpdate`, 'info');
            });

            // Écouter les touches pour des raccourcis
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey && e.key === 'Enter') {
                    createMessage();
                } else if (e.ctrlKey && e.shiftKey && e.key === 'Enter') {
                    createCommand();
                }
            });
        }

        // Créer un nouveau message
        async function createMessage() {
            const title = document.getElementById('messageTitle').value;
            const content = document.getElementById('messageContent').value;
            const type = document.getElementById('messageType').value;

            if (!title || !content) {
                log('❌ Veuillez remplir tous les champs du message', 'error');
                return;
            }

            if (!currentUser) {
                log('❌ Vous devez être connecté pour créer un message', 'error');
                return;
            }

            try {
                log(`📝 Création du message: ${title}`, 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_messages')
                    .insert({
                        user_id: currentUser.id,
                        titre: title,
                        contenu: content,
                        type: type,
                        priorite: type === 'urgent' ? 5 : 1,
                        tags: [browserId] // Marquer avec l'ID du navigateur
                    })
                    .select();

                if (error) throw error;

                log(`✅ Message créé: ${title}`, 'success');

                // Vider les champs
                document.getElementById('messageTitle').value = '';
                document.getElementById('messageContent').value = '';

                // Ajouter à l'interface (sera aussi ajouté via temps réel)
                addMessageToUI(data[0], false);

            } catch (error) {
                log(`❌ Erreur création message: ${error.message}`, 'error');
            }
        }

        // Créer une nouvelle commande
        async function createCommand() {
            const supplier = document.getElementById('commandSupplier').value;
            const description = document.getElementById('commandDescription').value;
            const amount = parseFloat(document.getElementById('commandAmount').value);

            if (!supplier || !description || !amount) {
                log('❌ Veuillez remplir tous les champs de la commande', 'error');
                return;
            }

            if (!currentUser) {
                log('❌ Vous devez être connecté pour créer une commande', 'error');
                return;
            }

            try {
                const numeroCommande = `CMD-${browserId}-${Date.now()}`;
                log(`📦 Création de la commande: ${numeroCommande}`, 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_commands')
                    .insert({
                        user_id: currentUser.id,
                        numero_commande: numeroCommande,
                        fournisseur: supplier,
                        description: description,
                        montant_total: amount,
                        date_commande: new Date().toISOString().split('T')[0],
                        statut: 'en_attente'
                    })
                    .select();

                if (error) throw error;

                log(`✅ Commande créée: ${numeroCommande}`, 'success');

                // Vider les champs
                document.getElementById('commandSupplier').value = '';
                document.getElementById('commandDescription').value = '';
                document.getElementById('commandAmount').value = '';

                // Ajouter à l'interface
                addCommandToUI(data[0], false);

            } catch (error) {
                log(`❌ Erreur création commande: ${error.message}`, 'error');
            }
        }

        // Ajouter un message à l'interface
        function addMessageToUI(message, isRealtime = false) {
            const messagesList = document.getElementById('messagesList');

            // Supprimer le message "Aucun message" s'il existe
            const noMessages = messagesList.querySelector('p');
            if (noMessages && noMessages.textContent.includes('Aucun message')) {
                noMessages.remove();
            }

            // Vérifier si le message existe déjà
            const existingMessage = document.querySelector(`[data-message-id="${message.id}"]`);
            if (existingMessage) return;

            const messageElement = document.createElement('div');
            messageElement.className = `message-item ${isRealtime ? 'realtime-message' : ''}`;
            messageElement.setAttribute('data-message-id', message.id);

            const typeIcon = getMessageTypeIcon(message.type);
            const timeAgo = getTimeAgo(new Date(message.created_at));
            const isFromThisBrowser = message.tags && message.tags.includes(browserId);

            messageElement.innerHTML = `
                <div class="message-header">
                    <span class="message-type">${typeIcon} ${message.type}</span>
                    <span class="message-time">${timeAgo} ${isFromThisBrowser ? '(Vous)' : ''} ${isRealtime ? '⚡' : ''}</span>
                </div>
                <h4 class="message-title">${message.titre}</h4>
                <p class="message-content">${message.contenu}</p>
                <div class="message-meta">
                    <span>Priorité: ${message.priorite}</span>
                    <span>${message.statut}</span>
                </div>
            `;

            // Insérer au début de la liste
            messagesList.insertBefore(messageElement, messagesList.firstChild);

            // Animation d'apparition pour les messages temps réel
            if (isRealtime) {
                messageElement.style.opacity = '0';
                messageElement.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    messageElement.style.transition = 'all 0.3s ease';
                    messageElement.style.opacity = '1';
                    messageElement.style.transform = 'translateY(0)';
                }, 10);
            }
        }

        // Mettre à jour un message dans l'interface
        function updateMessageInUI(message, isRealtime = false) {
            const existingElement = document.querySelector(`[data-message-id="${message.id}"]`);
            if (existingElement) {
                // Créer un nouvel élément et remplacer l'ancien
                const messagesList = document.getElementById('messagesList');
                const newElement = document.createElement('div');
                newElement.className = `message-item ${isRealtime ? 'realtime-message' : ''}`;
                newElement.setAttribute('data-message-id', message.id);

                const typeIcon = getMessageTypeIcon(message.type);
                const timeAgo = getTimeAgo(new Date(message.updated_at || message.created_at));
                const isFromThisBrowser = message.tags && message.tags.includes(browserId);

                newElement.innerHTML = `
                    <div class="message-header">
                        <span class="message-type">${typeIcon} ${message.type}</span>
                        <span class="message-time">${timeAgo} ${isFromThisBrowser ? '(Vous)' : ''} ${isRealtime ? '⚡ Modifié' : ''}</span>
                    </div>
                    <h4 class="message-title">${message.titre}</h4>
                    <p class="message-content">${message.contenu}</p>
                    <div class="message-meta">
                        <span>Priorité: ${message.priorite}</span>
                        <span>${message.statut}</span>
                    </div>
                `;

                existingElement.replaceWith(newElement);

                // Animation de mise à jour
                if (isRealtime) {
                    newElement.style.backgroundColor = '#fff3cd';
                    setTimeout(() => {
                        newElement.style.transition = 'background-color 1s ease';
                        newElement.style.backgroundColor = '';
                    }, 100);
                }
            }
        }

        // Supprimer un message de l'interface
        function removeMessageFromUI(messageId, isRealtime = false) {
            const existingElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (existingElement) {
                if (isRealtime) {
                    existingElement.style.transition = 'all 0.3s ease';
                    existingElement.style.opacity = '0';
                    existingElement.style.transform = 'translateX(-100%)';
                    setTimeout(() => existingElement.remove(), 300);
                } else {
                    existingElement.remove();
                }
            }
        }

        // Ajouter une commande à l'interface
        function addCommandToUI(command, isRealtime = false) {
            const commandsList = document.getElementById('commandsList');

            // Supprimer le message "Aucune commande" s'il existe
            const noCommands = commandsList.querySelector('p');
            if (noCommands && noCommands.textContent.includes('Aucune commande')) {
                noCommands.remove();
            }

            // Vérifier si la commande existe déjà
            const existingCommand = document.querySelector(`[data-command-id="${command.id}"]`);
            if (existingCommand) return;

            const commandElement = document.createElement('div');
            commandElement.className = `command-item ${isRealtime ? 'realtime-command' : ''}`;
            commandElement.setAttribute('data-command-id', command.id);

            const statusColor = getCommandStatusColor(command.statut);
            const timeAgo = getTimeAgo(new Date(command.created_at));
            const isFromThisBrowser = command.numero_commande.includes(browserId);

            commandElement.innerHTML = `
                <div class="command-header">
                    <span class="command-number">${command.numero_commande}</span>
                    <span class="command-status" style="background-color: ${statusColor}">${command.statut}</span>
                </div>
                <div class="command-details">
                    <p><strong>Fournisseur:</strong> ${command.fournisseur}</p>
                    <p><strong>Description:</strong> ${command.description}</p>
                    <p><strong>Montant:</strong> ${command.montant_total}€</p>
                </div>
                <div class="command-meta">
                    <span class="command-time">${timeAgo} ${isFromThisBrowser ? '(Vous)' : ''} ${isRealtime ? '⚡' : ''}</span>
                </div>
            `;

            // Insérer au début de la liste
            commandsList.insertBefore(commandElement, commandsList.firstChild);

            // Animation d'apparition pour les commandes temps réel
            if (isRealtime) {
                commandElement.style.opacity = '0';
                commandElement.style.transform = 'translateY(-20px)';
                setTimeout(() => {
                    commandElement.style.transition = 'all 0.3s ease';
                    commandElement.style.opacity = '1';
                    commandElement.style.transform = 'translateY(0)';
                }, 10);
            }
        }

        // Mettre à jour une commande dans l'interface
        function updateCommandInUI(command, isRealtime = false) {
            const existingElement = document.querySelector(`[data-command-id="${command.id}"]`);
            if (existingElement) {
                const statusColor = getCommandStatusColor(command.statut);
                const timeAgo = getTimeAgo(new Date(command.updated_at || command.created_at));
                const isFromThisBrowser = command.numero_commande.includes(browserId);

                const newElement = document.createElement('div');
                newElement.className = `command-item ${isRealtime ? 'realtime-command' : ''}`;
                newElement.setAttribute('data-command-id', command.id);

                newElement.innerHTML = `
                    <div class="command-header">
                        <span class="command-number">${command.numero_commande}</span>
                        <span class="command-status" style="background-color: ${statusColor}">${command.statut}</span>
                    </div>
                    <div class="command-details">
                        <p><strong>Fournisseur:</strong> ${command.fournisseur}</p>
                        <p><strong>Description:</strong> ${command.description}</p>
                        <p><strong>Montant:</strong> ${command.montant_total}€</p>
                    </div>
                    <div class="command-meta">
                        <span class="command-time">${timeAgo} ${isFromThisBrowser ? '(Vous)' : ''} ${isRealtime ? '⚡ Modifiée' : ''}</span>
                    </div>
                `;

                existingElement.replaceWith(newElement);

                // Animation de mise à jour
                if (isRealtime) {
                    newElement.style.backgroundColor = '#fff3cd';
                    setTimeout(() => {
                        newElement.style.transition = 'background-color 1s ease';
                        newElement.style.backgroundColor = '';
                    }, 100);
                }
            }
        }

        // Supprimer une commande de l'interface
        function removeCommandFromUI(commandId, isRealtime = false) {
            const existingElement = document.querySelector(`[data-command-id="${commandId}"]`);
            if (existingElement) {
                if (isRealtime) {
                    existingElement.style.transition = 'all 0.3s ease';
                    existingElement.style.opacity = '0';
                    existingElement.style.transform = 'translateX(-100%)';
                    setTimeout(() => existingElement.remove(), 300);
                } else {
                    existingElement.remove();
                }
            }
        }

        // Charger les messages existants
        async function loadMessages() {
            try {
                log('📊 Chargement des messages...', 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_messages')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) throw error;

                // Vider la liste actuelle
                const messagesList = document.getElementById('messagesList');
                messagesList.innerHTML = '';

                if (data.length === 0) {
                    messagesList.innerHTML = '<p style="text-align: center; color: #666;">Aucun message</p>';
                } else {
                    data.forEach(message => addMessageToUI(message, false));
                }

                log(`✅ ${data.length} messages chargés`, 'success');

            } catch (error) {
                log(`❌ Erreur chargement messages: ${error.message}`, 'error');
            }
        }

        // Charger les commandes existantes
        async function loadCommands() {
            try {
                log('📊 Chargement des commandes...', 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_commands')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) throw error;

                // Vider la liste actuelle
                const commandsList = document.getElementById('commandsList');
                commandsList.innerHTML = '';

                if (data.length === 0) {
                    commandsList.innerHTML = '<p style="text-align: center; color: #666;">Aucune commande</p>';
                } else {
                    data.forEach(command => addCommandToUI(command, false));
                }

                log(`✅ ${data.length} commandes chargées`, 'success');

            } catch (error) {
                log(`❌ Erreur chargement commandes: ${error.message}`, 'error');
            }
        }

        // Tester la connexion temps réel
        async function testRealtimeConnection() {
            if (!realtimeModule) {
                log('❌ Module temps réel non disponible', 'error');
                return;
            }

            try {
                log('🔌 Test de la connexion temps réel...', 'info');

                const status = realtimeModule.getConnectionStatus();

                log(`📊 Statut connexion: ${status.isConnected ? 'Connecté' : 'Déconnecté'}`, status.isConnected ? 'success' : 'warning');
                log(`🔄 Tentatives de reconnexion: ${status.reconnectAttempts}`, 'info');
                log(`📡 Subscriptions actives: ${status.subscriptionsCount}`, 'info');
                log(`👥 Utilisateurs en ligne: ${status.onlineUsersCount}`, 'info');

                // Test de création d'un message de test
                if (currentUser) {
                    const testMessage = {
                        user_id: currentUser.id,
                        titre: `Test connexion ${browserId}`,
                        contenu: `Message de test créé à ${new Date().toLocaleTimeString()}`,
                        type: 'info',
                        priorite: 1,
                        tags: [browserId, 'test']
                    };

                    const { data, error } = await supabaseSystem.client
                        .from('gestion_messages')
                        .insert(testMessage)
                        .select();

                    if (error) throw error;

                    log('✅ Message de test créé pour vérifier la synchronisation', 'success');
                }

            } catch (error) {
                log(`❌ Erreur test connexion: ${error.message}`, 'error');
            }
        }

        // Simuler plusieurs changements
        async function simulateMultipleChanges() {
            if (!currentUser) {
                log('❌ Vous devez être connecté pour simuler des changements', 'error');
                return;
            }

            try {
                log('🎭 Simulation de changements multiples...', 'info');

                // Créer plusieurs messages rapidement
                for (let i = 1; i <= 3; i++) {
                    const message = {
                        user_id: currentUser.id,
                        titre: `Message simulé ${i} - ${browserId}`,
                        contenu: `Contenu du message simulé numéro ${i}`,
                        type: i === 2 ? 'urgent' : 'general',
                        priorite: i === 2 ? 5 : 1,
                        tags: [browserId, 'simulation']
                    };

                    await supabaseSystem.client
                        .from('gestion_messages')
                        .insert(message);

                    // Petite pause entre les créations
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                // Créer une commande
                const command = {
                    user_id: currentUser.id,
                    numero_commande: `SIM-${browserId}-${Date.now()}`,
                    fournisseur: 'Fournisseur Simulation',
                    description: 'Commande de simulation pour test temps réel',
                    montant_total: 123.45,
                    date_commande: new Date().toISOString().split('T')[0],
                    statut: 'en_attente'
                };

                await supabaseSystem.client
                    .from('gestion_commands')
                    .insert(command);

                log('✅ Simulation terminée - vérifiez la synchronisation dans les autres navigateurs', 'success');

            } catch (error) {
                log(`❌ Erreur simulation: ${error.message}`, 'error');
            }
        }

        // Vider toutes les données
        async function clearAllData() {
            if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les données de test ?')) {
                return;
            }

            try {
                log('🗑️ Suppression de toutes les données...', 'warning');

                // Supprimer les messages avec le tag du navigateur
                await supabaseSystem.client
                    .from('gestion_messages')
                    .delete()
                    .contains('tags', [browserId]);

                // Supprimer les commandes créées par ce navigateur
                await supabaseSystem.client
                    .from('gestion_commands')
                    .delete()
                    .like('numero_commande', `%${browserId}%`);

                // Vider les listes de l'interface
                document.getElementById('messagesList').innerHTML = '<p style="text-align: center; color: #666;">Aucun message</p>';
                document.getElementById('commandsList').innerHTML = '<p style="text-align: center; color: #666;">Aucune commande</p>';

                log('✅ Données supprimées', 'success');

            } catch (error) {
                log(`❌ Erreur suppression: ${error.message}`, 'error');
            }
        }

        // Actualiser la connexion
        function refreshConnection() {
            log('🔄 Actualisation de la connexion...', 'info');
            location.reload();
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(status, text) {
            const indicator = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');

            indicator.className = `status-indicator ${status}`;
            textElement.textContent = text;
        }

        // Mettre à jour les informations utilisateur
        function updateUserInfo(profile) {
            const userInfo = document.getElementById('userInfo');

            if (profile) {
                userInfo.textContent = `${profile.nom} ${profile.prenom} (${profile.role})`;
            } else {
                userInfo.textContent = 'Non connecté';
            }
        }

        // Mettre à jour les statistiques de synchronisation
        function updateSyncStats() {
            const syncStatsElement = document.getElementById('syncStats');
            syncStatsElement.textContent = `${syncStats.totalEvents} événements synchronisés`;

            const statsGrid = document.getElementById('syncStatsGrid');
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${syncStats.totalEvents}</div>
                    <div class="stat-label">Total Événements</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${syncStats.messagesReceived}</div>
                    <div class="stat-label">Messages Reçus</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${syncStats.commandsReceived}</div>
                    <div class="stat-label">Commandes Reçues</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${syncStats.lastEventTime ? syncStats.lastEventTime.toLocaleTimeString() : 'Jamais'}</div>
                    <div class="stat-label">Dernier Événement</div>
                </div>
            `;
        }

        // Démarrer la surveillance des statistiques
        function startStatsMonitoring() {
            setInterval(() => {
                updateSyncStats();
            }, 5000); // Toutes les 5 secondes
        }

        // Fonctions utilitaires
        function getMessageTypeIcon(type) {
            const icons = {
                'general': '📝',
                'urgent': '🚨',
                'info': 'ℹ️',
                'warning': '⚠️'
            };
            return icons[type] || '📝';
        }

        function getCommandStatusColor(status) {
            const colors = {
                'en_attente': '#ffc107',
                'validee': '#17a2b8',
                'livree': '#28a745',
                'annulee': '#dc3545'
            };
            return colors[status] || '#6c757d';
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) {
                return 'À l\'instant';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes}min`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours}h`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days}j`;
            }
        }

        function log(message, level = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Charger les données initiales au démarrage
        setTimeout(() => {
            if (supabaseSystem) {
                loadMessages();
                loadCommands();
            }
        }, 2000);
    </script>
</body>
</html>