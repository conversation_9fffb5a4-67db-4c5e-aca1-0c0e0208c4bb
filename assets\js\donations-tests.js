/**
 * Tests pour le système de gestion des dons IPT
 */

// Test 2: Vérification des tables
async function testTablesExistence() {
    const containerId = 'test2Results';
    document.getElementById(containerId).innerHTML = '';
    
    try {
        showTestResult(containerId, '🔄 Vérification des tables...', 'info');
        
        const tables = [
            'gestion_donations',
            'gestion_donation_items', 
            'gestion_donation_documents',
            'gestion_donation_workflow',
            'gestion_donation_approvals'
        ];
        
        let tablesFound = 0;
        
        for (const table of tables) {
            try {
                const { error } = await supabaseClient
                    .from(table)
                    .select('count')
                    .limit(1);
                
                if (!error || error.message.includes('permission')) {
                    showTestResult(containerId, `✅ Table ${table} existe`, 'success');
                    tablesFound++;
                } else {
                    showTestResult(containerId, `❌ Table ${table} manquante`, 'error', error.message);
                }
            } catch (err) {
                showTestResult(containerId, `❌ Erreur vérification ${table}`, 'error', err.message);
            }
        }
        
        if (tablesFound === tables.length) {
            showTestResult(containerId, `✅ Toutes les tables (${tablesFound}/${tables.length}) sont présentes`, 'success');
            testResults.passed++;
        } else {
            showTestResult(containerId, `⚠️ ${tablesFound}/${tables.length} tables trouvées`, 'error');
            testResults.failed++;
        }
        
    } catch (error) {
        showTestResult(containerId, '❌ Erreur lors de la vérification des tables', 'error', error.message);
        testResults.failed++;
    }
    
    testResults.completed++;
    updateProgress();
}

// Test 3: Test des fonctions SQL
async function testSQLFunctions() {
    const containerId = 'test3Results';
    document.getElementById(containerId).innerHTML = '';
    
    try {
        showTestResult(containerId, '🔄 Test des fonctions SQL...', 'info');
        
        // Test génération numéro de don
        try {
            const { data, error } = await supabaseClient
                .rpc('generate_donation_number');
            
            if (error) {
                showTestResult(containerId, '❌ Fonction generate_donation_number manquante', 'error', error.message);
            } else {
                showTestResult(containerId, `✅ generate_donation_number: ${data}`, 'success');
            }
        } catch (err) {
            showTestResult(containerId, '❌ Erreur test generate_donation_number', 'error', err.message);
        }
        
        // Test génération numéro inventaire
        try {
            const { data, error } = await supabaseClient
                .rpc('generate_inventory_number');
            
            if (error) {
                showTestResult(containerId, '❌ Fonction generate_inventory_number manquante', 'error', error.message);
            } else {
                showTestResult(containerId, `✅ generate_inventory_number: ${data}`, 'success');
            }
        } catch (err) {
            showTestResult(containerId, '❌ Erreur test generate_inventory_number', 'error', err.message);
        }
        
        // Test fonction workflow
        try {
            // Cette fonction nécessite des paramètres, on teste juste son existence
            const { error } = await supabaseClient
                .rpc('log_donation_workflow', {
                    p_donation_id: '00000000-0000-0000-0000-000000000000',
                    p_etape: 'test',
                    p_statut_precedent: 'test',
                    p_statut_nouveau: 'test',
                    p_acteur_nom: 'test',
                    p_acteur_role: 'test',
                    p_action: 'test'
                });
            
            if (error && !error.message.includes('foreign key')) {
                showTestResult(containerId, '❌ Fonction log_donation_workflow manquante', 'error', error.message);
            } else {
                showTestResult(containerId, '✅ log_donation_workflow existe', 'success');
            }
        } catch (err) {
            showTestResult(containerId, '❌ Erreur test log_donation_workflow', 'error', err.message);
        }
        
        showTestResult(containerId, '✅ Tests des fonctions terminés', 'success');
        testResults.passed++;
        
    } catch (error) {
        showTestResult(containerId, '❌ Erreur générale test fonctions', 'error', error.message);
        testResults.failed++;
    }
    
    testResults.completed++;
    updateProgress();
}

// Test 4: Test des vues
async function testViews() {
    const containerId = 'test4Results';
    document.getElementById(containerId).innerHTML = '';
    
    try {
        showTestResult(containerId, '🔄 Test des vues...', 'info');
        
        // Test vue donations_complete
        try {
            const { data, error } = await supabaseClient
                .from('donations_complete')
                .select('*')
                .limit(1);
            
            if (error && !error.message.includes('permission')) {
                showTestResult(containerId, '❌ Vue donations_complete manquante', 'error', error.message);
            } else {
                showTestResult(containerId, '✅ Vue donations_complete accessible', 'success');
            }
        } catch (err) {
            showTestResult(containerId, '❌ Erreur test donations_complete', 'error', err.message);
        }
        
        // Test vue donations_stats
        try {
            const { data, error } = await supabaseClient
                .from('donations_stats')
                .select('*')
                .limit(1);
            
            if (error && !error.message.includes('permission')) {
                showTestResult(containerId, '❌ Vue donations_stats manquante', 'error', error.message);
            } else {
                showTestResult(containerId, '✅ Vue donations_stats accessible', 'success');
            }
        } catch (err) {
            showTestResult(containerId, '❌ Erreur test donations_stats', 'error', err.message);
        }
        
        showTestResult(containerId, '✅ Tests des vues terminés', 'success');
        testResults.passed++;
        
    } catch (error) {
        showTestResult(containerId, '❌ Erreur générale test vues', 'error', error.message);
        testResults.failed++;
    }
    
    testResults.completed++;
    updateProgress();
}

// Test 5: Test CRUD
async function testCRUDOperations() {
    const containerId = 'test5Results';
    document.getElementById(containerId).innerHTML = '';
    
    try {
        showTestResult(containerId, '🔄 Test des opérations CRUD...', 'info');
        
        // Vérifier si l'utilisateur est connecté
        const { data: { user } } = await supabaseClient.auth.getUser();
        
        if (!user) {
            showTestResult(containerId, '⚠️ Utilisateur non connecté - Tests CRUD ignorés', 'info');
            testResults.passed++;
            testResults.completed++;
            updateProgress();
            return;
        }
        
        let testDonationId = null;
        
        // Test CREATE
        try {
            const testDonation = {
                numero_don: `TEST-${Date.now()}`,
                type_don: 'article',
                donneur_nom: 'Test Donneur',
                demandeur_nom: 'Test Demandeur',
                demandeur_service: 'Test Service',
                demandeur_email: user.email,
                lieu_affectation: 'Test Lieu',
                raison_don: 'Test de création',
                created_by: user.email
            };
            
            const { data, error } = await supabaseClient
                .from('gestion_donations')
                .insert([testDonation])
                .select()
                .single();
            
            if (error) {
                showTestResult(containerId, '❌ Erreur CREATE', 'error', error.message);
            } else {
                testDonationId = data.id;
                showTestResult(containerId, '✅ CREATE réussi', 'success', `ID: ${testDonationId}`);
            }
        } catch (err) {
            showTestResult(containerId, '❌ Erreur test CREATE', 'error', err.message);
        }
        
        // Test READ
        if (testDonationId) {
            try {
                const { data, error } = await supabaseClient
                    .from('gestion_donations')
                    .select('*')
                    .eq('id', testDonationId)
                    .single();
                
                if (error) {
                    showTestResult(containerId, '❌ Erreur READ', 'error', error.message);
                } else {
                    showTestResult(containerId, '✅ READ réussi', 'success', `Don: ${data.numero_don}`);
                }
            } catch (err) {
                showTestResult(containerId, '❌ Erreur test READ', 'error', err.message);
            }
        }
        
        // Test UPDATE
        if (testDonationId) {
            try {
                const { error } = await supabaseClient
                    .from('gestion_donations')
                    .update({ observations: 'Test UPDATE' })
                    .eq('id', testDonationId);
                
                if (error) {
                    showTestResult(containerId, '❌ Erreur UPDATE', 'error', error.message);
                } else {
                    showTestResult(containerId, '✅ UPDATE réussi', 'success');
                }
            } catch (err) {
                showTestResult(containerId, '❌ Erreur test UPDATE', 'error', err.message);
            }
        }
        
        // Test DELETE
        if (testDonationId) {
            try {
                const { error } = await supabaseClient
                    .from('gestion_donations')
                    .delete()
                    .eq('id', testDonationId);
                
                if (error) {
                    showTestResult(containerId, '❌ Erreur DELETE', 'error', error.message);
                } else {
                    showTestResult(containerId, '✅ DELETE réussi', 'success');
                }
            } catch (err) {
                showTestResult(containerId, '❌ Erreur test DELETE', 'error', err.message);
            }
        }
        
        showTestResult(containerId, '✅ Tests CRUD terminés', 'success');
        testResults.passed++;
        
    } catch (error) {
        showTestResult(containerId, '❌ Erreur générale test CRUD', 'error', error.message);
        testResults.failed++;
    }
    
    testResults.completed++;
    updateProgress();
}

// Test 6: Test du workflow
async function testWorkflow() {
    const containerId = 'test6Results';
    document.getElementById(containerId).innerHTML = '';
    
    try {
        showTestResult(containerId, '🔄 Test du workflow...', 'info');
        
        // Vérifier si l'utilisateur est connecté
        const { data: { user } } = await supabaseClient.auth.getUser();
        
        if (!user) {
            showTestResult(containerId, '⚠️ Utilisateur non connecté - Tests workflow ignorés', 'info');
            testResults.passed++;
            testResults.completed++;
            updateProgress();
            return;
        }
        
        // Test de soumission (simulation)
        try {
            // On teste juste l'existence de la fonction
            const { error } = await supabaseClient
                .rpc('submit_donation_to_dg', {
                    p_donation_id: '00000000-0000-0000-0000-000000000000',
                    p_user_email: user.email
                });
            
            if (error && !error.message.includes('Don non trouvé')) {
                showTestResult(containerId, '❌ Fonction submit_donation_to_dg manquante', 'error', error.message);
            } else {
                showTestResult(containerId, '✅ Fonction submit_donation_to_dg existe', 'success');
            }
        } catch (err) {
            showTestResult(containerId, '❌ Erreur test submit_donation_to_dg', 'error', err.message);
        }
        
        // Test autres fonctions de workflow
        const workflowFunctions = [
            'validate_donation_by_dg',
            'provide_technical_advice',
            'authorize_donation_by_ms',
            'mark_donation_received'
        ];
        
        for (const func of workflowFunctions) {
            try {
                const { error } = await supabaseClient.rpc(func, {
                    p_donation_id: '00000000-0000-0000-0000-000000000000',
                    p_user_email: user.email,
                    p_avis: 'test',
                    p_approve: true
                });
                
                if (error && !error.message.includes('Don non trouvé')) {
                    showTestResult(containerId, `❌ Fonction ${func} manquante`, 'error', error.message);
                } else {
                    showTestResult(containerId, `✅ Fonction ${func} existe`, 'success');
                }
            } catch (err) {
                showTestResult(containerId, `❌ Erreur test ${func}`, 'error', err.message);
            }
        }
        
        showTestResult(containerId, '✅ Tests workflow terminés', 'success');
        testResults.passed++;
        
    } catch (error) {
        showTestResult(containerId, '❌ Erreur générale test workflow', 'error', error.message);
        testResults.failed++;
    }
    
    testResults.completed++;
    updateProgress();
}

// Test 7: Test des politiques RLS
async function testRLSPolicies() {
    const containerId = 'test7Results';
    document.getElementById(containerId).innerHTML = '';
    
    try {
        showTestResult(containerId, '🔄 Test des politiques RLS...', 'info');
        
        // Test d'accès aux tables avec RLS
        const tables = ['gestion_donations', 'gestion_donation_items', 'gestion_donation_documents'];
        
        for (const table of tables) {
            try {
                const { data, error } = await supabaseClient
                    .from(table)
                    .select('count')
                    .limit(1);
                
                // Si on a une erreur de permission, c'est que RLS fonctionne
                if (error && error.message.includes('permission')) {
                    showTestResult(containerId, `✅ RLS actif sur ${table}`, 'success', 'Accès restreint correctement');
                } else if (!error) {
                    showTestResult(containerId, `✅ Accès autorisé à ${table}`, 'success', 'Utilisateur authentifié');
                } else {
                    showTestResult(containerId, `❌ Erreur RLS ${table}`, 'error', error.message);
                }
            } catch (err) {
                showTestResult(containerId, `❌ Erreur test RLS ${table}`, 'error', err.message);
            }
        }
        
        showTestResult(containerId, '✅ Tests RLS terminés', 'success');
        testResults.passed++;
        
    } catch (error) {
        showTestResult(containerId, '❌ Erreur générale test RLS', 'error', error.message);
        testResults.failed++;
    }
    
    testResults.completed++;
    updateProgress();
}
