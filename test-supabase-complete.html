<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Système Supabase Complet - Gestion Interne</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
        }

        .status-indicator.connected {
            background: #28a745;
        }

        .status-indicator.warning {
            background: #ffc107;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .module-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #6c757d;
            transition: all 0.3s ease;
        }

        .module-card.active {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .module-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .module-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .module-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .module-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }

        .status-ready {
            background: #28a745;
            color: white;
        }

        .status-error {
            background: #dc3545;
            color: white;
        }

        .status-loading {
            background: #ffc107;
            color: black;
        }

        .status-inactive {
            background: #6c757d;
            color: white;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            margin: 5px;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .test-section h3 {
            margin-bottom: 15px;
            color: #333;
        }

        .test-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .notifications-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            max-width: 400px;
        }

        .notification {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-left: 4px solid #17a2b8;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .notification-info { border-left-color: #17a2b8; }
        .notification-success { border-left-color: #28a745; }
        .notification-warning { border-left-color: #ffc107; }
        .notification-error { border-left-color: #dc3545; }

        .notification-close {
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #999;
        }

        .file-drop-zone {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .file-drop-zone.drag-over {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .online-users {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .user-avatar {
            display: inline-block;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #667eea;
            color: white;
            text-align: center;
            line-height: 32px;
            margin-right: 8px;
            font-size: 12px;
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Test Système Supabase Complet</h1>
            <p>Authentification, Base de données temps réel, et Stockage automatique</p>
        </div>

        <!-- Barre de statut -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Déconnecté</span>
            </div>
            <div class="status-item">
                <span id="userInfo">Non connecté</span>
            </div>
            <div class="status-item">
                <span id="onlineCount">0 utilisateurs en ligne</span>
            </div>
            <div class="status-item">
                <button class="btn secondary" onclick="refreshSystemStatus()">🔄 Actualiser</button>
            </div>
        </div>

        <!-- Grille des modules -->
        <div class="modules-grid" id="modulesGrid">
            <!-- Les cartes de modules seront générées dynamiquement -->
        </div>

        <!-- Section Authentification -->
        <div class="test-section">
            <h3>🔐 Tests d'Authentification</h3>
            <div class="test-controls">
                <button class="btn" onclick="showSignUpForm()">📝 Inscription</button>
                <button class="btn" onclick="showSignInForm()">🔑 Connexion</button>
                <button class="btn secondary" onclick="testSignOut()">👋 Déconnexion</button>
                <button class="btn secondary" onclick="testPasswordReset()">🔄 Reset Password</button>
            </div>

            <!-- Formulaire d'inscription -->
            <div id="signUpForm" style="display: none;">
                <h4>Créer un compte</h4>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="signUpEmail" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>Mot de passe:</label>
                    <input type="password" id="signUpPassword" placeholder="Minimum 8 caractères">
                </div>
                <div class="form-group">
                    <label>Nom:</label>
                    <input type="text" id="signUpNom" placeholder="Votre nom">
                </div>
                <div class="form-group">
                    <label>Prénom:</label>
                    <input type="text" id="signUpPrenom" placeholder="Votre prénom">
                </div>
                <div class="form-group">
                    <label>Service:</label>
                    <input type="text" id="signUpService" placeholder="Votre service">
                </div>
                <button class="btn success" onclick="testSignUp()">✅ Créer le compte</button>
                <button class="btn secondary" onclick="hideAuthForms()">❌ Annuler</button>
            </div>

            <!-- Formulaire de connexion -->
            <div id="signInForm" style="display: none;">
                <h4>Se connecter</h4>
                <div class="form-group">
                    <label>Email:</label>
                    <input type="email" id="signInEmail" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>Mot de passe:</label>
                    <input type="password" id="signInPassword" placeholder="Votre mot de passe">
                </div>
                <button class="btn success" onclick="testSignIn()">🔑 Se connecter</button>
                <button class="btn secondary" onclick="hideAuthForms()">❌ Annuler</button>
            </div>
        </div>

        <!-- Section Base de données -->
        <div class="test-section">
            <h3>💾 Tests Base de Données</h3>
            <div class="test-controls">
                <button class="btn" onclick="testCreateMessage()">📝 Créer Message</button>
                <button class="btn" onclick="testCreateCommand()">📦 Créer Commande</button>
                <button class="btn" onclick="testLoadData()">📊 Charger Données</button>
                <button class="btn secondary" onclick="testDatabaseOperations()">🧪 Test CRUD</button>
            </div>

            <!-- Formulaire de message -->
            <div id="messageForm" style="display: none;">
                <h4>Nouveau Message</h4>
                <div class="form-group">
                    <label>Titre:</label>
                    <input type="text" id="messageTitle" placeholder="Titre du message">
                </div>
                <div class="form-group">
                    <label>Contenu:</label>
                    <textarea id="messageContent" placeholder="Contenu du message"></textarea>
                </div>
                <div class="form-group">
                    <label>Type:</label>
                    <select id="messageType">
                        <option value="general">Général</option>
                        <option value="urgent">Urgent</option>
                        <option value="info">Information</option>
                        <option value="warning">Avertissement</option>
                    </select>
                </div>
                <button class="btn success" onclick="createMessage()">✅ Créer</button>
                <button class="btn secondary" onclick="hideDataForms()">❌ Annuler</button>
            </div>

            <!-- Statistiques -->
            <div class="stats-grid" id="statsGrid">
                <!-- Les statistiques seront générées dynamiquement -->
            </div>
        </div>

        <!-- Section Stockage -->
        <div class="test-section">
            <h3>📁 Tests Stockage</h3>
            <div class="test-controls">
                <button class="btn" onclick="testFileUpload()">📤 Upload Fichier</button>
                <button class="btn" onclick="testListFiles()">📋 Lister Fichiers</button>
                <button class="btn secondary" onclick="testStorageStats()">📊 Statistiques</button>
            </div>

            <!-- Zone de drop -->
            <div class="file-drop-zone" id="fileDropZone">
                <p>📁 Glissez-déposez vos fichiers ici ou cliquez pour sélectionner</p>
                <input type="file" id="fileInput" multiple style="display: none;">
            </div>

            <!-- Liste des fichiers -->
            <div id="filesList"></div>
        </div>

        <!-- Section Temps Réel -->
        <div class="test-section">
            <h3>⚡ Tests Temps Réel</h3>
            <div class="test-controls">
                <button class="btn" onclick="testRealtimeConnection()">🔌 Test Connexion</button>
                <button class="btn" onclick="testPresence()">👥 Test Présence</button>
                <button class="btn secondary" onclick="testNotifications()">🔔 Test Notifications</button>
            </div>

            <!-- Utilisateurs en ligne -->
            <div class="online-users" id="onlineUsers">
                <h4>👥 Utilisateurs en ligne</h4>
                <div id="usersList">Aucun utilisateur en ligne</div>
            </div>
        </div>

        <!-- Logs -->
        <div class="log-container" id="testLog"></div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth-complete.js"></script>
    <script src="assets/js/supabase-storage-manager.js"></script>
    <script src="assets/js/supabase-realtime-complete.js"></script>
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>
    <script src="assets/js/supabase-migration.js"></script>
    <script src="assets/js/supabase-system-complete.js"></script>

    <script>
        // Variables globales
        let supabaseSystem = null;
        let currentUser = null;
        let systemStatus = {};

        // Modules disponibles
        const modules = [
            { name: 'auth', title: 'Authentification', icon: '🔐', description: 'Gestion des utilisateurs et sessions' },
            { name: 'storage', title: 'Stockage', icon: '📁', description: 'Upload et gestion des fichiers' },
            { name: 'realtime', title: 'Temps Réel', icon: '⚡', description: 'Synchronisation en temps réel' },
            { name: 'autoInit', title: 'Auto-Init', icon: '🚀', description: 'Initialisation automatique' },
            { name: 'monitor', title: 'Monitoring', icon: '📊', description: 'Surveillance du système' },
            { name: 'sync', title: 'Synchronisation', icon: '🔄', description: 'Sync bidirectionnelle' },
            { name: 'migration', title: 'Migration', icon: '🔧', description: 'Gestion des migrations' }
        ];

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', async function() {
            log('🚀 Initialisation de la page de test...', 'info');

            // Générer les cartes de modules
            generateModuleCards();

            // Initialiser le système Supabase
            await initializeSystem();

            // Configurer les événements
            setupEventListeners();

            // Démarrer la surveillance
            startStatusMonitoring();
        });

        // Initialiser le système Supabase
        async function initializeSystem() {
            try {
                log('🔧 Initialisation du système Supabase...', 'info');

                supabaseSystem = await initializeSupabaseComplete();

                if (supabaseSystem) {
                    log('✅ Système Supabase initialisé avec succès', 'success');

                    // Configurer les callbacks
                    setupSystemCallbacks();

                    // Mettre à jour le statut
                    await updateSystemStatus();
                } else {
                    throw new Error('Échec initialisation système');
                }

            } catch (error) {
                log(`❌ Erreur initialisation: ${error.message}`, 'error');
                updateConnectionStatus('error', 'Erreur d\'initialisation');
            }
        }

        // Configurer les callbacks du système
        function setupSystemCallbacks() {
            const system = getSupabaseSystem();
            if (!system) return;

            // Callbacks d'authentification
            system.on('onAuthStateChange', (data) => {
                handleAuthStateChange(data);
            });

            // Callbacks de données
            system.on('onDataChange', (data) => {
                handleDataChange(data);
            });

            // Callbacks de connexion
            system.on('onConnectionChange', (data) => {
                handleConnectionChange(data);
            });
        }

        // Gérer les changements d'état d'authentification
        function handleAuthStateChange(data) {
            if (data.type === 'signIn') {
                currentUser = data.data.user;
                updateUserInfo(data.data.profile);
                log(`👤 Utilisateur connecté: ${data.data.profile?.nom || 'Utilisateur'}`, 'success');
            } else if (data.type === 'signOut') {
                currentUser = null;
                updateUserInfo(null);
                log('👋 Utilisateur déconnecté', 'info');
            }
        }

        // Gérer les changements de données
        function handleDataChange(data) {
            log(`🔄 Données mises à jour: ${data.table} (${data.eventType})`, 'info');

            // Mettre à jour les statistiques
            updateStats();
        }

        // Gérer les changements de connexion
        function handleConnectionChange(data) {
            if (data.status === 'connected') {
                updateConnectionStatus('connected', 'Connecté');
                log('🌐 Connexion temps réel établie', 'success');
            } else {
                updateConnectionStatus('warning', 'Déconnecté');
                log('🌐 Connexion temps réel perdue', 'warning');
            }
        }

        // Générer les cartes de modules
        function generateModuleCards() {
            const modulesGrid = document.getElementById('modulesGrid');

            modules.forEach(module => {
                const card = document.createElement('div');
                card.className = 'module-card';
                card.id = `module-${module.name}`;

                card.innerHTML = `
                    <div class="module-title">
                        ${module.icon} ${module.title}
                    </div>
                    <div class="module-status status-loading" id="status-${module.name}">Chargement...</div>
                    <div class="module-description">${module.description}</div>
                    <div class="module-actions">
                        <button class="btn" onclick="testModule('${module.name}')">🧪 Tester</button>
                        <button class="btn secondary" onclick="restartModule('${module.name}')">🔄 Redémarrer</button>
                    </div>
                `;

                modulesGrid.appendChild(card);
            });
        }

        // Mettre à jour le statut du système
        async function updateSystemStatus() {
            const system = getSupabaseSystem();
            if (!system) return;

            try {
                systemStatus = system.getSystemStatus();

                // Mettre à jour les cartes de modules
                modules.forEach(module => {
                    const moduleStatus = systemStatus.modules[module.name];
                    updateModuleStatus(module.name, moduleStatus);
                });

                // Mettre à jour la barre de statut
                if (systemStatus.isInitialized) {
                    updateConnectionStatus('connected', 'Système prêt');
                } else {
                    updateConnectionStatus('warning', 'Initialisation...');
                }

                // Mettre à jour les statistiques
                await updateStats();

            } catch (error) {
                log(`❌ Erreur mise à jour statut: ${error.message}`, 'error');
            }
        }

        // Mettre à jour le statut d'un module
        function updateModuleStatus(moduleName, moduleStatus) {
            const card = document.getElementById(`module-${moduleName}`);
            const statusElement = document.getElementById(`status-${moduleName}`);

            if (!card || !statusElement) return;

            // Réinitialiser les classes
            card.className = 'module-card';
            statusElement.className = 'module-status';

            if (moduleStatus && moduleStatus.available) {
                if (moduleStatus.status === 'ready') {
                    card.classList.add('active');
                    statusElement.classList.add('status-ready');
                    statusElement.textContent = '✅ Prêt';
                } else if (moduleStatus.status === 'error') {
                    card.classList.add('error');
                    statusElement.classList.add('status-error');
                    statusElement.textContent = '❌ Erreur';
                } else {
                    card.classList.add('warning');
                    statusElement.classList.add('status-loading');
                    statusElement.textContent = '⏳ Chargement';
                }
            } else {
                statusElement.classList.add('status-inactive');
                statusElement.textContent = '⚪ Inactif';
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(status, text) {
            const indicator = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');

            indicator.className = `status-indicator ${status}`;
            textElement.textContent = text;
        }

        // Mettre à jour les informations utilisateur
        function updateUserInfo(profile) {
            const userInfo = document.getElementById('userInfo');

            if (profile) {
                userInfo.textContent = `${profile.nom} ${profile.prenom} (${profile.role})`;
            } else {
                userInfo.textContent = 'Non connecté';
            }
        }

        // Mettre à jour les statistiques
        async function updateStats() {
            const system = getSupabaseSystem();
            if (!system) return;

            try {
                const authModule = system.getModule('auth');
                const realtimeModule = system.getModule('realtime');

                const stats = [
                    { label: 'Utilisateur', value: currentUser ? '1' : '0', icon: '👤' },
                    { label: 'Connexion', value: systemStatus.isInitialized ? 'OK' : 'KO', icon: '🔌' },
                    { label: 'Modules', value: Object.keys(systemStatus.modules || {}).length, icon: '🧩' },
                    { label: 'En ligne', value: realtimeModule ? realtimeModule.getOnlineUsers().length : '0', icon: '👥' }
                ];

                const statsGrid = document.getElementById('statsGrid');
                statsGrid.innerHTML = '';

                stats.forEach(stat => {
                    const statCard = document.createElement('div');
                    statCard.className = 'stat-card';
                    statCard.innerHTML = `
                        <div class="stat-value">${stat.value}</div>
                        <div class="stat-label">${stat.icon} ${stat.label}</div>
                    `;
                    statsGrid.appendChild(statCard);
                });

            } catch (error) {
                log(`❌ Erreur mise à jour stats: ${error.message}`, 'error');
            }
        }

        // Configurer les événements
        function setupEventListeners() {
            // Zone de drop pour les fichiers
            const dropZone = document.getElementById('fileDropZone');
            const fileInput = document.getElementById('fileInput');

            dropZone.addEventListener('click', () => fileInput.click());
            fileInput.addEventListener('change', handleFileSelect);

            // Événements personnalisés pour la synchronisation temps réel
            document.addEventListener('gestion_messagesUpdate', (e) => {
                const detail = e.detail;
                log(`📨 Message ${detail.eventType}: ${detail.new?.titre || detail.old?.titre}`, 'info');

                // Mettre à jour l'interface si nécessaire
                if (detail.eventType === 'INSERT' && detail.new) {
                    showRealtimeNotification('Nouveau message', detail.new.titre, '📨');
                } else if (detail.eventType === 'UPDATE' && detail.new) {
                    showRealtimeNotification('Message modifié', detail.new.titre, '✏️');
                } else if (detail.eventType === 'DELETE' && detail.old) {
                    showRealtimeNotification('Message supprimé', detail.old.titre, '🗑️');
                }
            });

            document.addEventListener('gestion_commandsUpdate', (e) => {
                const detail = e.detail;
                log(`📦 Commande ${detail.eventType}: ${detail.new?.numero_commande || detail.old?.numero_commande}`, 'info');

                if (detail.eventType === 'INSERT' && detail.new) {
                    showRealtimeNotification('Nouvelle commande', detail.new.numero_commande, '📦');
                } else if (detail.eventType === 'UPDATE' && detail.new) {
                    showRealtimeNotification('Commande modifiée', detail.new.numero_commande, '📝');
                } else if (detail.eventType === 'DELETE' && detail.old) {
                    showRealtimeNotification('Commande supprimée', detail.old.numero_commande, '🗑️');
                }
            });

            document.addEventListener('notificationsUpdate', (e) => {
                const detail = e.detail;
                log(`🔔 Notification ${detail.eventType}`, 'info');

                if (detail.eventType === 'INSERT' && detail.new) {
                    showRealtimeNotification('Nouvelle notification', detail.new.titre, '🔔');
                }
            });

            // Autres événements temps réel
            document.addEventListener('gestion_pvUpdate', (e) => {
                const detail = e.detail;
                log(`📄 PV ${detail.eventType}: ${detail.new?.titre || detail.old?.titre}`, 'info');
            });

            document.addEventListener('gestion_productsUpdate', (e) => {
                const detail = e.detail;
                log(`🛍️ Produit ${detail.eventType}: ${detail.new?.nom || detail.old?.nom}`, 'info');
            });

            document.addEventListener('user_profilesUpdate', (e) => {
                const detail = e.detail;
                log(`👤 Profil utilisateur ${detail.eventType}`, 'info');
            });
        }

        // Démarrer la surveillance du statut
        function startStatusMonitoring() {
            setInterval(async () => {
                await updateSystemStatus();
            }, 30000); // Toutes les 30 secondes
        }

        // Tests d'authentification
        function showSignUpForm() {
            hideAuthForms();
            document.getElementById('signUpForm').style.display = 'block';
        }

        function showSignInForm() {
            hideAuthForms();
            document.getElementById('signInForm').style.display = 'block';
        }

        function hideAuthForms() {
            document.getElementById('signUpForm').style.display = 'none';
            document.getElementById('signInForm').style.display = 'none';
        }

        async function testSignUp() {
            const system = getSupabaseSystem();
            const authModule = system?.getModule('auth');

            if (!authModule) {
                log('❌ Module d\'authentification non disponible', 'error');
                return;
            }

            const email = document.getElementById('signUpEmail').value;
            const password = document.getElementById('signUpPassword').value;
            const nom = document.getElementById('signUpNom').value;
            const prenom = document.getElementById('signUpPrenom').value;
            const service = document.getElementById('signUpService').value;

            if (!email || !password || !nom || !prenom) {
                log('❌ Veuillez remplir tous les champs obligatoires', 'error');
                return;
            }

            try {
                log('📝 Création du compte...', 'info');

                const result = await authModule.signUpWithEmail(email, password, {
                    nom, prenom, service
                });

                if (result.success) {
                    log('✅ Compte créé avec succès', 'success');
                    if (result.needsConfirmation) {
                        log('📧 Vérifiez votre email pour confirmer votre compte', 'info');
                    }
                    hideAuthForms();
                } else {
                    log(`❌ Erreur création compte: ${result.error}`, 'error');
                }

            } catch (error) {
                log(`❌ Erreur inattendue: ${error.message}`, 'error');
            }
        }

        async function testSignIn() {
            const system = getSupabaseSystem();
            const authModule = system?.getModule('auth');

            if (!authModule) {
                log('❌ Module d\'authentification non disponible', 'error');
                return;
            }

            const email = document.getElementById('signInEmail').value;
            const password = document.getElementById('signInPassword').value;

            if (!email || !password) {
                log('❌ Veuillez remplir tous les champs', 'error');
                return;
            }

            try {
                log('🔑 Connexion...', 'info');

                const result = await authModule.signInWithEmail(email, password);

                if (result.success) {
                    log('✅ Connexion réussie', 'success');
                    hideAuthForms();
                } else {
                    log(`❌ Erreur connexion: ${result.error}`, 'error');
                }

            } catch (error) {
                log(`❌ Erreur inattendue: ${error.message}`, 'error');
            }
        }

        async function testSignOut() {
            const system = getSupabaseSystem();
            const authModule = system?.getModule('auth');

            if (!authModule) {
                log('❌ Module d\'authentification non disponible', 'error');
                return;
            }

            try {
                log('👋 Déconnexion...', 'info');

                const result = await authModule.signOut();

                if (result.success) {
                    log('✅ Déconnexion réussie', 'success');
                } else {
                    log(`❌ Erreur déconnexion: ${result.error}`, 'error');
                }

            } catch (error) {
                log(`❌ Erreur inattendue: ${error.message}`, 'error');
            }
        }

        async function testPasswordReset() {
            const email = prompt('Entrez votre email pour réinitialiser le mot de passe:');
            if (!email) return;

            const system = getSupabaseSystem();
            const authModule = system?.getModule('auth');

            if (!authModule) {
                log('❌ Module d\'authentification non disponible', 'error');
                return;
            }

            try {
                log('🔄 Envoi email de réinitialisation...', 'info');

                const result = await authModule.resetPassword(email);

                if (result.success) {
                    log('✅ Email de réinitialisation envoyé', 'success');
                } else {
                    log(`❌ Erreur: ${result.error}`, 'error');
                }

            } catch (error) {
                log(`❌ Erreur inattendue: ${error.message}`, 'error');
            }
        }

        // Tests de base de données
        function testCreateMessage() {
            if (!currentUser) {
                log('❌ Vous devez être connecté pour créer un message', 'error');
                return;
            }

            hideDataForms();
            document.getElementById('messageForm').style.display = 'block';
        }

        function hideDataForms() {
            document.getElementById('messageForm').style.display = 'none';
        }

        async function createMessage() {
            if (!currentUser) {
                log('❌ Vous devez être connecté', 'error');
                return;
            }

            const system = getSupabaseSystem();
            if (!system || !system.client) {
                log('❌ Système non initialisé', 'error');
                return;
            }

            const title = document.getElementById('messageTitle').value;
            const content = document.getElementById('messageContent').value;
            const type = document.getElementById('messageType').value;

            if (!title || !content) {
                log('❌ Veuillez remplir tous les champs', 'error');
                return;
            }

            try {
                log('📝 Création du message...', 'info');

                const { data, error } = await system.client
                    .from('gestion_messages')
                    .insert({
                        user_id: currentUser.id,
                        titre: title,
                        contenu: content,
                        type: type,
                        priorite: type === 'urgent' ? 5 : 1
                    })
                    .select();

                if (error) throw error;

                log('✅ Message créé avec succès', 'success');
                hideDataForms();

                // Vider les champs
                document.getElementById('messageTitle').value = '';
                document.getElementById('messageContent').value = '';

            } catch (error) {
                log(`❌ Erreur création message: ${error.message}`, 'error');
            }
        }

        async function testCreateCommand() {
            if (!currentUser) {
                log('❌ Vous devez être connecté pour créer une commande', 'error');
                return;
            }

            const system = getSupabaseSystem();
            if (!system || !system.client) {
                log('❌ Système non initialisé', 'error');
                return;
            }

            try {
                log('📦 Création d\'une commande de test...', 'info');

                const numeroCommande = `CMD-${Date.now()}`;

                const { data, error } = await system.client
                    .from('gestion_commands')
                    .insert({
                        user_id: currentUser.id,
                        numero_commande: numeroCommande,
                        fournisseur: 'Fournisseur Test',
                        description: 'Commande de test créée automatiquement',
                        montant_total: 99.99,
                        date_commande: new Date().toISOString().split('T')[0],
                        statut: 'en_attente'
                    })
                    .select();

                if (error) throw error;

                log(`✅ Commande créée: ${numeroCommande}`, 'success');

            } catch (error) {
                log(`❌ Erreur création commande: ${error.message}`, 'error');
            }
        }

        async function testLoadData() {
            const system = getSupabaseSystem();
            if (!system || !system.client) {
                log('❌ Système non initialisé', 'error');
                return;
            }

            try {
                log('📊 Chargement des données...', 'info');

                // Charger les messages
                const { data: messages, error: messagesError } = await system.client
                    .from('gestion_messages')
                    .select('*')
                    .limit(5);

                if (messagesError) throw messagesError;

                // Charger les commandes
                const { data: commands, error: commandsError } = await system.client
                    .from('gestion_commands')
                    .select('*')
                    .limit(5);

                if (commandsError) throw commandsError;

                // Charger les produits
                const { data: products, error: productsError } = await system.client
                    .from('gestion_products')
                    .select('*')
                    .limit(5);

                if (productsError) throw productsError;

                log(`✅ Données chargées: ${messages.length} messages, ${commands.length} commandes, ${products.length} produits`, 'success');

            } catch (error) {
                log(`❌ Erreur chargement données: ${error.message}`, 'error');
            }
        }

        async function testDatabaseOperations() {
            const system = getSupabaseSystem();
            if (!system || !system.client) {
                log('❌ Système non initialisé', 'error');
                return;
            }

            try {
                log('🧪 Test des opérations CRUD...', 'info');

                // Test de lecture
                const { data: readData, error: readError } = await system.client
                    .from('gestion_products')
                    .select('count');

                if (readError) throw readError;

                log(`✅ Lecture: ${readData.length} produits trouvés`, 'success');

                // Test des vues
                const { data: statsData, error: statsError } = await system.client
                    .from('dashboard_stats')
                    .select('*');

                if (statsError) {
                    log(`⚠️ Vues non disponibles: ${statsError.message}`, 'warning');
                } else {
                    log(`✅ Statistiques: ${JSON.stringify(statsData[0])}`, 'success');
                }

            } catch (error) {
                log(`❌ Erreur test CRUD: ${error.message}`, 'error');
            }
        }

        // Tests de stockage
        async function testFileUpload() {
            const fileInput = document.getElementById('fileInput');
            fileInput.click();
        }

        async function handleFileSelect(event) {
            const files = event.target.files;
            if (files.length === 0) return;

            const system = getSupabaseSystem();
            const storageModule = system?.getModule('storage');

            if (!storageModule) {
                log('❌ Module de stockage non disponible', 'error');
                return;
            }

            try {
                log(`📤 Upload de ${files.length} fichier(s)...`, 'info');

                for (const file of files) {
                    const result = await storageModule.uploadFile(file, 'attachments');

                    if (result.success) {
                        log(`✅ Fichier uploadé: ${file.name}`, 'success');
                        displayUploadedFile(file, result);
                    } else {
                        log(`❌ Erreur upload ${file.name}: ${result.error}`, 'error');
                    }
                }

            } catch (error) {
                log(`❌ Erreur upload: ${error.message}`, 'error');
            }
        }

        function displayUploadedFile(file, result) {
            const filesList = document.getElementById('filesList');

            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.style.cssText = 'background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px; display: flex; justify-content: space-between; align-items: center;';

            fileItem.innerHTML = `
                <div>
                    <strong>${file.name}</strong> (${formatFileSize(file.size)})
                    <br><small>${result.publicUrl || result.data.path}</small>
                </div>
                <button class="btn secondary" onclick="copyToClipboard('${result.publicUrl || result.data.path}')">📋 Copier</button>
            `;

            filesList.appendChild(fileItem);
        }

        async function testListFiles() {
            const system = getSupabaseSystem();
            const storageModule = system?.getModule('storage');

            if (!storageModule) {
                log('❌ Module de stockage non disponible', 'error');
                return;
            }

            try {
                log('📋 Listage des fichiers...', 'info');

                const result = await storageModule.listFiles('attachments');

                if (result.success) {
                    log(`✅ ${result.files.length} fichiers trouvés`, 'success');

                    result.files.forEach(file => {
                        log(`📄 ${file.name} (${formatFileSize(file.metadata?.size || 0)})`, 'info');
                    });
                } else {
                    log(`❌ Erreur listage: ${result.error}`, 'error');
                }

            } catch (error) {
                log(`❌ Erreur listage: ${error.message}`, 'error');
            }
        }

        async function testStorageStats() {
            const system = getSupabaseSystem();
            const storageModule = system?.getModule('storage');

            if (!storageModule) {
                log('❌ Module de stockage non disponible', 'error');
                return;
            }

            try {
                log('📊 Calcul des statistiques de stockage...', 'info');

                const stats = await storageModule.getStorageStats();

                Object.entries(stats).forEach(([bucket, stat]) => {
                    if (stat.error) {
                        log(`❌ ${bucket}: ${stat.error}`, 'error');
                    } else {
                        log(`📦 ${bucket}: ${stat.count} fichiers, ${formatFileSize(stat.totalSize)}`, 'info');
                    }
                });

            } catch (error) {
                log(`❌ Erreur statistiques: ${error.message}`, 'error');
            }
        }

        // Tests temps réel
        async function testRealtimeConnection() {
            const system = getSupabaseSystem();
            const realtimeModule = system?.getModule('realtime');

            if (!realtimeModule) {
                log('❌ Module temps réel non disponible', 'error');
                return;
            }

            try {
                log('⚡ Test de connexion temps réel...', 'info');

                const status = realtimeModule.getConnectionStatus();

                log(`📊 Statut: ${status.isConnected ? 'Connecté' : 'Déconnecté'}`, status.isConnected ? 'success' : 'warning');
                log(`🔄 Tentatives de reconnexion: ${status.reconnectAttempts}`, 'info');
                log(`📡 Subscriptions actives: ${status.subscriptionsCount}`, 'info');
                log(`👥 Utilisateurs en ligne: ${status.onlineUsersCount}`, 'info');

            } catch (error) {
                log(`❌ Erreur test temps réel: ${error.message}`, 'error');
            }
        }

        async function testPresence() {
            const system = getSupabaseSystem();
            const realtimeModule = system?.getModule('realtime');

            if (!realtimeModule) {
                log('❌ Module temps réel non disponible', 'error');
                return;
            }

            try {
                log('👥 Test de présence utilisateur...', 'info');

                const onlineUsers = realtimeModule.getOnlineUsers();

                log(`👥 ${onlineUsers.length} utilisateurs en ligne`, 'info');

                // Mettre à jour l'affichage
                updateOnlineUsers(onlineUsers);

                // Mettre à jour notre présence
                await realtimeModule.updateUserPresence();
                log('✅ Présence mise à jour', 'success');

            } catch (error) {
                log(`❌ Erreur test présence: ${error.message}`, 'error');
            }
        }

        function updateOnlineUsers(users) {
            const usersList = document.getElementById('usersList');
            const onlineCount = document.getElementById('onlineCount');

            onlineCount.textContent = `${users.length} utilisateurs en ligne`;

            if (users.length === 0) {
                usersList.innerHTML = 'Aucun utilisateur en ligne';
                return;
            }

            usersList.innerHTML = users.map(user => {
                const initials = (user.user_id || 'U').substring(0, 2).toUpperCase();
                return `<span class="user-avatar" title="Utilisateur ${user.user_id}">${initials}</span>`;
            }).join('');
        }

        async function testNotifications() {
            const system = getSupabaseSystem();
            const realtimeModule = system?.getModule('realtime');

            if (!realtimeModule) {
                log('❌ Module temps réel non disponible', 'error');
                return;
            }

            try {
                log('🔔 Test des notifications...', 'info');

                // Créer une notification de test
                realtimeModule.showNotification('Test de notification', {
                    body: 'Ceci est une notification de test du système temps réel',
                    icon: '🧪',
                    tag: 'test-notification'
                });

                log('✅ Notification de test envoyée', 'success');

            } catch (error) {
                log(`❌ Erreur test notifications: ${error.message}`, 'error');
            }
        }

        // Fonctions de test des modules
        async function testModule(moduleName) {
            const system = getSupabaseSystem();
            const module = system?.getModule(moduleName);

            if (!module) {
                log(`❌ Module ${moduleName} non disponible`, 'error');
                return;
            }

            try {
                log(`🧪 Test du module ${moduleName}...`, 'info');

                switch (moduleName) {
                    case 'auth':
                        const authUser = module.getUser();
                        log(`👤 Utilisateur: ${authUser.isAuthenticated ? 'Connecté' : 'Non connecté'}`, 'info');
                        break;

                    case 'storage':
                        const buckets = await module.checkBuckets();
                        log(`📦 Buckets disponibles: ${buckets.join(', ')}`, 'info');
                        break;

                    case 'realtime':
                        const rtStatus = module.getConnectionStatus();
                        log(`⚡ Temps réel: ${rtStatus.isConnected ? 'Connecté' : 'Déconnecté'}`, 'info');
                        break;

                    case 'monitor':
                        const metrics = module.getMetrics();
                        log(`📊 Métriques: ${JSON.stringify(metrics)}`, 'info');
                        break;

                    default:
                        log(`✅ Module ${moduleName} testé`, 'success');
                }

            } catch (error) {
                log(`❌ Erreur test ${moduleName}: ${error.message}`, 'error');
            }
        }

        async function restartModule(moduleName) {
            const system = getSupabaseSystem();

            if (!system) {
                log('❌ Système non disponible', 'error');
                return;
            }

            try {
                log(`🔄 Redémarrage du module ${moduleName}...`, 'info');

                const success = await system.restartModule(moduleName);

                if (success) {
                    log(`✅ Module ${moduleName} redémarré`, 'success');
                    await updateSystemStatus();
                } else {
                    log(`❌ Échec redémarrage ${moduleName}`, 'error');
                }

            } catch (error) {
                log(`❌ Erreur redémarrage ${moduleName}: ${error.message}`, 'error');
            }
        }

        // Fonctions utilitaires
        function refreshSystemStatus() {
            log('🔄 Actualisation du statut...', 'info');
            updateSystemStatus();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                log('📋 Copié dans le presse-papiers', 'success');
            }).catch(() => {
                log('❌ Erreur copie presse-papiers', 'error');
            });
        }

        function log(message, level = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Afficher une notification temps réel
        function showRealtimeNotification(title, message, icon = '⚡') {
            // Créer la notification
            const notification = document.createElement('div');
            notification.className = 'notification notification-info realtime-notification';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                border-left: 4px solid #28a745;
                z-index: 1000;
                max-width: 300px;
                animation: slideInRight 0.3s ease;
            `;

            notification.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                    <div>
                        <div style="font-weight: bold; margin-bottom: 5px;">${icon} ${title}</div>
                        <div style="color: #666; font-size: 14px;">${message}</div>
                        <div style="color: #999; font-size: 12px; margin-top: 5px;">Temps réel</div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; font-size: 18px; cursor: pointer; color: #999;">×</button>
                </div>
            `;

            // Ajouter au DOM
            document.body.appendChild(notification);

            // Supprimer automatiquement après 4 secondes
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideOutRight 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 4000);
        }

        // Ajouter les animations CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .realtime-notification {
                border-left-color: #28a745 !important;
            }
        `;
        document.head.appendChild(style);

        // Gestion des erreurs globales
        window.addEventListener('error', (event) => {
            log(`❌ Erreur globale: ${event.error.message}`, 'error');
        });

        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ Promesse rejetée: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>