// Gestionnaire d'interface utilisateur pour les mises à jour temps réel
// Synchronisation instantanée de l'interface entre tous les navigateurs

class RealtimeUIManager {
    constructor() {
        this.isActive = false;
        this.animationQueue = [];
        this.updateQueue = [];
        this.isProcessingQueue = false;
        
        // Configuration des animations
        this.animations = {
            newItem: 'fadeInUp',
            updateItem: 'pulse',
            deleteItem: 'fadeOutDown',
            highlight: 'highlight'
        };
        
        // Sélecteurs d'éléments UI
        this.selectors = {
            messagesList: '#messages-list',
            commandsList: '#commandes-list',
            pvList: '#pv-list',
            usersList: '#users-list',
            messageForm: '#message-form',
            commandForm: '#command-form',
            pvForm: '#pv-form'
        };
        
        // Cache des éléments DOM
        this.elements = new Map();
        
        // Callbacks pour les événements UI
        this.callbacks = {
            onItemAdded: [],
            onItemUpdated: [],
            onItemDeleted: [],
            onUIUpdate: []
        };
    }

    // Initialiser le gestionnaire UI
    initialize() {
        console.log('🎨 Initialisation du gestionnaire UI temps réel...');
        
        try {
            // Mettre en cache les éléments DOM
            this.cacheElements();
            
            // Ajouter les styles d'animation
            this.addAnimationStyles();
            
            // Configurer les observateurs de mutations
            this.setupMutationObservers();
            
            // Démarrer le processeur de queue
            this.startQueueProcessor();
            
            this.isActive = true;
            console.log('✅ Gestionnaire UI temps réel actif');
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation gestionnaire UI:', error);
            return false;
        }
    }

    // Mettre en cache les éléments DOM
    cacheElements() {
        Object.entries(this.selectors).forEach(([key, selector]) => {
            const element = document.querySelector(selector);
            if (element) {
                this.elements.set(key, element);
            } else {
                console.warn(`⚠️ Élément non trouvé: ${selector}`);
            }
        });
    }

    // Ajouter les styles d'animation
    addAnimationStyles() {
        const styles = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            @keyframes fadeOutDown {
                from {
                    opacity: 1;
                    transform: translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateY(20px);
                }
            }
            
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            @keyframes highlight {
                0% { background-color: transparent; }
                50% { background-color: #fff3cd; }
                100% { background-color: transparent; }
            }
            
            .realtime-new {
                animation: fadeInUp 0.5s ease;
            }
            
            .realtime-update {
                animation: pulse 0.3s ease;
            }
            
            .realtime-delete {
                animation: fadeOutDown 0.5s ease;
            }
            
            .realtime-highlight {
                animation: highlight 1s ease;
            }
            
            .realtime-badge {
                position: relative;
            }
            
            .realtime-badge::after {
                content: '';
                position: absolute;
                top: -2px;
                right: -2px;
                width: 8px;
                height: 8px;
                background: #dc3545;
                border-radius: 50%;
                animation: pulse 2s infinite;
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    // Configurer les observateurs de mutations
    setupMutationObservers() {
        // Observer les changements dans les listes
        ['messagesList', 'commandsList', 'pvList'].forEach(listKey => {
            const element = this.elements.get(listKey);
            if (element) {
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'childList') {
                            this.handleListMutation(listKey, mutation);
                        }
                    });
                });
                
                observer.observe(element, {
                    childList: true,
                    subtree: true
                });
            }
        });
    }

    // Gérer les mutations de liste
    handleListMutation(listKey, mutation) {
        mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.ELEMENT_NODE && !node.classList.contains('realtime-processed')) {
                this.animateNewItem(node);
                node.classList.add('realtime-processed');
            }
        });
    }

    // Démarrer le processeur de queue
    startQueueProcessor() {
        setInterval(() => {
            if (!this.isProcessingQueue && this.updateQueue.length > 0) {
                this.processUpdateQueue();
            }
        }, 100); // Traiter toutes les 100ms
    }

    // Traiter la queue de mises à jour
    async processUpdateQueue() {
        if (this.updateQueue.length === 0) return;
        
        this.isProcessingQueue = true;
        
        try {
            const update = this.updateQueue.shift();
            await this.processUpdate(update);
        } catch (error) {
            console.error('❌ Erreur traitement queue UI:', error);
        } finally {
            this.isProcessingQueue = false;
        }
    }

    // Traiter une mise à jour
    async processUpdate(update) {
        const { type, action, data, element } = update;
        
        switch (action) {
            case 'add':
                await this.addItemToUI(type, data);
                break;
            case 'update':
                await this.updateItemInUI(type, data);
                break;
            case 'delete':
                await this.deleteItemFromUI(type, data);
                break;
            case 'highlight':
                await this.highlightItem(element);
                break;
        }
    }

    // Ajouter un message à l'interface
    async addMessageToUI(message) {
        console.log('💬 Ajout message UI:', message);
        
        const messagesList = this.elements.get('messagesList');
        if (!messagesList) return;
        
        // Vérifier si le message existe déjà
        const existingMessage = messagesList.querySelector(`[data-message-id="${message.id}"]`);
        if (existingMessage) {
            console.log('⚠️ Message déjà présent dans l\'UI');
            return;
        }
        
        // Créer l'élément message
        const messageElement = this.createMessageElement(message);
        
        // Ajouter avec animation
        messageElement.classList.add('realtime-new');
        messagesList.insertBefore(messageElement, messagesList.firstChild);
        
        // Faire défiler vers le nouveau message
        this.scrollToElement(messageElement);
        
        // Mettre à jour les badges
        if (typeof window.updateNotificationBadge === 'function') {
            window.updateNotificationBadge('messages', 1);
        }
        
        // Déclencher callback
        this.triggerCallback('onItemAdded', { type: 'message', data: message, element: messageElement });
    }

    // Créer un élément message
    createMessageElement(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message-item';
        messageDiv.setAttribute('data-message-id', message.id);
        
        const isCurrentUser = message.sender_username === (window.currentUser?.username || '');
        const messageClass = isCurrentUser ? 'message-sent' : 'message-received';
        
        messageDiv.innerHTML = `
            <div class="message ${messageClass}">
                <div class="message-header">
                    <strong>${message.sender_name}</strong>
                    <span class="message-time">${new Date(message.created_at).toLocaleString()}</span>
                    ${!message.is_read ? '<span class="message-unread">●</span>' : ''}
                </div>
                <div class="message-content">${this.escapeHtml(message.content)}</div>
                ${message.file_name ? `
                    <div class="message-attachment">
                        📎 ${message.file_name}
                    </div>
                ` : ''}
                <div class="message-actions">
                    <button onclick="marquerCommeLu('${message.id}')" class="btn-sm">Marquer lu</button>
                    <button onclick="supprimerMessage('${message.id}')" class="btn-sm btn-danger">Supprimer</button>
                </div>
            </div>
        `;
        
        return messageDiv;
    }

    // Mettre à jour un message dans l'interface
    async updateMessageInUI(message) {
        console.log('✏️ Mise à jour message UI:', message);
        
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        if (!messageElement) {
            console.warn('⚠️ Message non trouvé pour mise à jour');
            return;
        }
        
        // Animer la mise à jour
        messageElement.classList.add('realtime-update');
        
        // Mettre à jour le contenu
        const contentElement = messageElement.querySelector('.message-content');
        if (contentElement) {
            contentElement.textContent = message.content;
        }
        
        // Mettre à jour le statut de lecture
        const unreadIndicator = messageElement.querySelector('.message-unread');
        if (message.is_read && unreadIndicator) {
            unreadIndicator.remove();
        } else if (!message.is_read && !unreadIndicator) {
            const header = messageElement.querySelector('.message-header');
            header.insertAdjacentHTML('beforeend', '<span class="message-unread">●</span>');
        }
        
        // Supprimer la classe d'animation après un délai
        setTimeout(() => {
            messageElement.classList.remove('realtime-update');
        }, 300);
        
        // Déclencher callback
        this.triggerCallback('onItemUpdated', { type: 'message', data: message, element: messageElement });
    }

    // Supprimer un message de l'interface
    async deleteMessageFromUI(messageId) {
        console.log('🗑️ Suppression message UI:', messageId);
        
        const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
        if (!messageElement) {
            console.warn('⚠️ Message non trouvé pour suppression');
            return;
        }
        
        // Animer la suppression
        messageElement.classList.add('realtime-delete');
        
        // Supprimer après l'animation
        setTimeout(() => {
            messageElement.remove();
        }, 500);
        
        // Déclencher callback
        this.triggerCallback('onItemDeleted', { type: 'message', id: messageId, element: messageElement });
    }

    // Mettre à jour une commande dans l'interface
    async updateCommandInUI(eventType, newRecord, oldRecord) {
        console.log(`📋 Commande ${eventType} UI:`, newRecord || oldRecord);
        
        const record = newRecord || oldRecord;
        const commandsList = this.elements.get('commandsList');
        if (!commandsList) return;
        
        switch (eventType) {
            case 'INSERT':
                await this.addCommandToUI(record);
                break;
            case 'UPDATE':
                await this.updateExistingCommand(record);
                break;
            case 'DELETE':
                await this.deleteCommandFromUI(record.id);
                break;
        }
    }

    // Ajouter une commande à l'interface
    async addCommandToUI(command) {
        const commandsList = this.elements.get('commandsList');
        if (!commandsList) return;
        
        // Vérifier si la commande existe déjà
        const existingCommand = commandsList.querySelector(`[data-command-id="${command.id}"]`);
        if (existingCommand) return;
        
        // Créer l'élément commande
        const commandElement = this.createCommandElement(command);
        
        // Ajouter avec animation
        commandElement.classList.add('realtime-new');
        commandsList.insertBefore(commandElement, commandsList.firstChild);
        
        // Mettre à jour les badges
        if (typeof window.updateNotificationBadge === 'function') {
            window.updateNotificationBadge('commands', 1);
        }
    }

    // Créer un élément commande
    createCommandElement(command) {
        const commandDiv = document.createElement('div');
        commandDiv.className = 'command-item';
        commandDiv.setAttribute('data-command-id', command.id);
        
        const statusClass = this.getStatusClass(command.statut);
        
        commandDiv.innerHTML = `
            <div class="command-card">
                <div class="command-header">
                    <h5>${command.numero_commande}</h5>
                    <span class="status-badge ${statusClass}">${command.statut}</span>
                </div>
                <div class="command-details">
                    <p><strong>Fournisseur:</strong> ${command.fournisseur}</p>
                    <p><strong>Date:</strong> ${new Date(command.date_commande).toLocaleDateString()}</p>
                    <p><strong>Total TTC:</strong> ${command.total_ttc || 'N/A'}€</p>
                </div>
                <div class="command-actions">
                    <button onclick="voirCommande('${command.id}')" class="btn-sm">Voir</button>
                    <button onclick="modifierCommande('${command.id}')" class="btn-sm">Modifier</button>
                </div>
            </div>
        `;
        
        return commandDiv;
    }

    // Mettre à jour un PV dans l'interface
    async updatePVInUI(eventType, newRecord, oldRecord) {
        console.log(`📄 PV ${eventType} UI:`, newRecord || oldRecord);
        
        const record = newRecord || oldRecord;
        const pvList = this.elements.get('pvList');
        if (!pvList) return;
        
        switch (eventType) {
            case 'INSERT':
                await this.addPVToUI(record);
                break;
            case 'UPDATE':
                await this.updateExistingPV(record);
                break;
            case 'DELETE':
                await this.deletePVFromUI(record.id);
                break;
        }
    }

    // Mettre à jour un utilisateur dans l'interface
    async updateUserInUI(eventType, newRecord, oldRecord) {
        console.log(`👤 Utilisateur ${eventType} UI:`, newRecord || oldRecord);
        
        // Mettre à jour la liste des utilisateurs connectés
        if (typeof window.updateUserPresenceUI === 'function') {
            window.updateUserPresenceUI();
        }
    }

    // Animer un nouvel élément
    animateNewItem(element) {
        element.classList.add('realtime-new');
        setTimeout(() => {
            element.classList.remove('realtime-new');
        }, 500);
    }

    // Surligner un élément
    async highlightItem(element) {
        if (!element) return;
        
        element.classList.add('realtime-highlight');
        setTimeout(() => {
            element.classList.remove('realtime-highlight');
        }, 1000);
    }

    // Faire défiler vers un élément
    scrollToElement(element) {
        if (!element) return;
        
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest'
        });
    }

    // Obtenir la classe de statut
    getStatusClass(status) {
        const statusClasses = {
            'en_attente': 'status-pending',
            'soumise': 'status-submitted',
            'validee': 'status-approved',
            'livree': 'status-delivered',
            'annulee': 'status-cancelled',
            'brouillon': 'status-draft',
            'en_cours': 'status-progress',
            'finalise': 'status-final',
            'archive': 'status-archived'
        };
        
        return statusClasses[status] || 'status-default';
    }

    // Échapper le HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Ajouter une mise à jour à la queue
    queueUpdate(update) {
        this.updateQueue.push(update);
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Déclencher les callbacks
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback UI ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les statistiques
    getStats() {
        return {
            isActive: this.isActive,
            queueLength: this.updateQueue.length,
            isProcessing: this.isProcessingQueue,
            elementsCache: this.elements.size
        };
    }
}

// Fonctions globales pour l'interface
window.addMessageToUI = function(message) {
    if (window.realtimeUIManager) {
        window.realtimeUIManager.addMessageToUI(message);
    }
};

window.updateCommandInUI = function(eventType, newRecord, oldRecord) {
    if (window.realtimeUIManager) {
        window.realtimeUIManager.updateCommandInUI(eventType, newRecord, oldRecord);
    }
};

window.updatePVInUI = function(eventType, newRecord, oldRecord) {
    if (window.realtimeUIManager) {
        window.realtimeUIManager.updatePVInUI(eventType, newRecord, oldRecord);
    }
};

window.updateUserInUI = function(eventType, newRecord, oldRecord) {
    if (window.realtimeUIManager) {
        window.realtimeUIManager.updateUserInUI(eventType, newRecord, oldRecord);
    }
};

// Export de la classe
if (typeof window !== 'undefined') {
    window.RealtimeUIManager = RealtimeUIManager;
}
