// Coordinateur principal pour tous les systèmes temps réel
// Orchestration de la synchronisation entre navigateurs

class RealtimeCoordinator {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.isActive = false;
        this.currentUser = null;
        
        // Instances des modules temps réel
        this.modules = {
            realtime: null,
            notifications: null,
            uiManager: null,
            userPresence: null
        };
        
        // État de synchronisation
        this.syncState = {
            lastSync: null,
            syncInProgress: false,
            pendingUpdates: [],
            errorCount: 0,
            successCount: 0
        };
        
        // Configuration de performance
        this.performance = {
            batchSize: 10,
            batchDelay: 100,
            maxRetries: 3,
            retryDelay: 1000
        };
        
        // Callbacks globaux
        this.callbacks = {
            onRealtimeReady: [],
            onSyncComplete: [],
            onError: [],
            onUserActivity: []
        };
    }

    // Initialiser le coordinateur temps réel
    async initialize(currentUser = null) {
        if (this.isActive) {
            console.log('⚠️ Coordinateur temps réel déjà actif');
            return;
        }

        this.currentUser = currentUser;
        console.log('🚀 Initialisation du coordinateur temps réel...');
        
        try {
            // Initialiser les modules dans l'ordre
            await this.initializeModules();
            
            // Configurer les interconnexions
            this.setupModuleConnections();
            
            // Démarrer la synchronisation
            await this.startRealtimeSync();
            
            // Configurer les gestionnaires d'événements globaux
            this.setupGlobalEventHandlers();
            
            this.isActive = true;
            console.log('✅ Coordinateur temps réel actif');
            
            // Déclencher le callback de prêt
            this.triggerCallback('onRealtimeReady', {
                modules: this.getModuleStatus(),
                user: this.currentUser
            });
            
        } catch (error) {
            console.error('❌ Erreur initialisation coordinateur:', error);
            this.triggerCallback('onError', error);
            throw error;
        }
    }

    // Initialiser tous les modules
    async initializeModules() {
        console.log('🔧 Initialisation des modules temps réel...');
        
        try {
            // 1. Système de notifications (doit être en premier)
            if (typeof NotificationSystem !== 'undefined') {
                this.modules.notifications = new NotificationSystem();
                await this.modules.notifications.initialize();
                console.log('✅ Système de notifications initialisé');
            }
            
            // 2. Gestionnaire d'interface utilisateur
            if (typeof RealtimeUIManager !== 'undefined') {
                this.modules.uiManager = new RealtimeUIManager();
                this.modules.uiManager.initialize();
                console.log('✅ Gestionnaire UI temps réel initialisé');
            }
            
            // 3. Système temps réel principal
            if (typeof SupabaseRealtime !== 'undefined') {
                this.modules.realtime = new SupabaseRealtime(this.config, this.client);
                await this.modules.realtime.startRealtime(this.currentUser);
                console.log('✅ Système temps réel Supabase initialisé');
            }
            
            // 4. Présence utilisateur
            if (typeof UserPresence !== 'undefined' && this.currentUser) {
                this.modules.userPresence = new UserPresence(this.config, this.client);
                await this.modules.userPresence.initialize(this.currentUser);
                console.log('✅ Présence utilisateur initialisée');
            }
            
        } catch (error) {
            console.error('❌ Erreur initialisation modules:', error);
            throw error;
        }
    }

    // Configurer les connexions entre modules
    setupModuleConnections() {
        console.log('🔗 Configuration des interconnexions...');
        
        // Connecter le système temps réel aux notifications
        if (this.modules.realtime && this.modules.notifications) {
            this.modules.realtime.on('onMessage', (data) => {
                this.handleRealtimeMessage(data);
            });
            
            this.modules.realtime.on('onCommand', (data) => {
                this.handleRealtimeCommand(data);
            });
            
            this.modules.realtime.on('onPV', (data) => {
                this.handleRealtimePV(data);
            });
            
            this.modules.realtime.on('onNotification', (notification) => {
                this.modules.notifications.showNotification(notification);
            });
        }
        
        // Connecter la présence aux notifications
        if (this.modules.userPresence && this.modules.notifications) {
            this.modules.userPresence.on('onUserJoin', (data) => {
                this.handleUserJoin(data);
            });
            
            this.modules.userPresence.on('onUserLeave', (data) => {
                this.handleUserLeave(data);
            });
        }
        
        // Connecter l'UI manager aux autres modules
        if (this.modules.uiManager) {
            this.modules.uiManager.on('onItemAdded', (data) => {
                this.handleUIUpdate(data);
            });
        }
    }

    // Démarrer la synchronisation temps réel
    async startRealtimeSync() {
        console.log('🔄 Démarrage de la synchronisation temps réel...');
        
        try {
            // Synchronisation initiale
            await this.performInitialSync();
            
            // Démarrer la surveillance continue
            this.startContinuousSync();
            
            console.log('✅ Synchronisation temps réel active');
            
        } catch (error) {
            console.error('❌ Erreur démarrage synchronisation:', error);
            throw error;
        }
    }

    // Effectuer la synchronisation initiale
    async performInitialSync() {
        this.syncState.syncInProgress = true;
        this.syncState.lastSync = new Date().toISOString();
        
        try {
            // Synchroniser avec le gestionnaire de sync existant si disponible
            if (typeof window.syncManager !== 'undefined' && window.syncManager) {
                const result = await window.syncManager.forceSync();
                console.log('✅ Synchronisation initiale terminée:', result);
                this.syncState.successCount++;
            }
            
            // Déclencher le callback de synchronisation
            this.triggerCallback('onSyncComplete', {
                type: 'initial',
                timestamp: this.syncState.lastSync,
                success: true
            });
            
        } catch (error) {
            console.error('❌ Erreur synchronisation initiale:', error);
            this.syncState.errorCount++;
            this.triggerCallback('onError', error);
        } finally {
            this.syncState.syncInProgress = false;
        }
    }

    // Démarrer la surveillance continue
    startContinuousSync() {
        // Surveillance des changements de visibilité
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isActive) {
                this.handlePageVisible();
            }
        });
        
        // Surveillance des événements de focus
        window.addEventListener('focus', () => {
            if (this.isActive) {
                this.handleWindowFocus();
            }
        });
        
        // Synchronisation périodique
        setInterval(() => {
            if (this.isActive && !this.syncState.syncInProgress) {
                this.performPeriodicSync();
            }
        }, 30000); // Toutes les 30 secondes
    }

    // Gérer la visibilité de la page
    async handlePageVisible() {
        console.log('👁️ Page visible, synchronisation...');
        
        try {
            await this.performQuickSync();
        } catch (error) {
            console.warn('⚠️ Erreur sync visibilité:', error);
        }
    }

    // Gérer le focus de la fenêtre
    async handleWindowFocus() {
        console.log('🎯 Fenêtre en focus, synchronisation...');
        
        try {
            await this.performQuickSync();
            
            // Réinitialiser les badges de notification
            if (this.modules.notifications) {
                this.modules.notifications.resetBadge('notifications');
            }
        } catch (error) {
            console.warn('⚠️ Erreur sync focus:', error);
        }
    }

    // Effectuer une synchronisation rapide
    async performQuickSync() {
        if (this.syncState.syncInProgress) return;
        
        this.syncState.syncInProgress = true;
        
        try {
            // Synchronisation légère
            if (typeof window.syncManager !== 'undefined' && window.syncManager) {
                const stats = window.syncManager.getStats();
                console.log('📊 Stats sync:', stats);
            }
            
        } catch (error) {
            console.error('❌ Erreur sync rapide:', error);
        } finally {
            this.syncState.syncInProgress = false;
        }
    }

    // Effectuer une synchronisation périodique
    async performPeriodicSync() {
        console.log('⏰ Synchronisation périodique...');
        
        try {
            // Vérifier s'il y a des mises à jour en attente
            if (this.syncState.pendingUpdates.length > 0) {
                await this.processPendingUpdates();
            }
            
            // Mettre à jour les statistiques
            this.updateSyncStats();
            
        } catch (error) {
            console.warn('⚠️ Erreur sync périodique:', error);
        }
    }

    // Traiter les mises à jour en attente
    async processPendingUpdates() {
        const updates = this.syncState.pendingUpdates.splice(0, this.performance.batchSize);
        
        for (const update of updates) {
            try {
                await this.processUpdate(update);
                this.syncState.successCount++;
            } catch (error) {
                console.error('❌ Erreur traitement mise à jour:', error);
                this.syncState.errorCount++;
                
                // Remettre en queue si pas trop d'échecs
                if (update.retries < this.performance.maxRetries) {
                    update.retries = (update.retries || 0) + 1;
                    this.syncState.pendingUpdates.push(update);
                }
            }
        }
    }

    // Traiter une mise à jour
    async processUpdate(update) {
        const { type, action, data } = update;
        
        switch (type) {
            case 'message':
                await this.processMessageUpdate(action, data);
                break;
            case 'command':
                await this.processCommandUpdate(action, data);
                break;
            case 'pv':
                await this.processPVUpdate(action, data);
                break;
        }
    }

    // Configurer les gestionnaires d'événements globaux
    setupGlobalEventHandlers() {
        // Gestionnaire d'erreurs global
        window.addEventListener('error', (event) => {
            this.handleGlobalError(event.error);
        });
        
        // Gestionnaire d'erreurs de promesses
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError(event.reason);
        });
        
        // Gestionnaire de déconnexion
        window.addEventListener('beforeunload', () => {
            this.handleBeforeUnload();
        });
    }

    // Gérer les messages temps réel
    handleRealtimeMessage(data) {
        console.log('💬 Message temps réel reçu:', data);
        
        const { eventType, newRecord, oldRecord } = data;
        
        // Ajouter à la queue de traitement UI
        if (this.modules.uiManager) {
            this.modules.uiManager.queueUpdate({
                type: 'message',
                action: eventType.toLowerCase(),
                data: newRecord || oldRecord
            });
        }
        
        // Créer une notification si approprié
        if (eventType === 'INSERT' && newRecord) {
            this.createMessageNotification(newRecord);
        }
    }

    // Gérer les commandes temps réel
    handleRealtimeCommand(data) {
        console.log('📋 Commande temps réel reçue:', data);
        
        const { eventType, newRecord, oldRecord } = data;
        
        // Traitement spécifique aux commandes
        if (eventType === 'UPDATE' && newRecord && oldRecord) {
            if (newRecord.statut !== oldRecord.statut) {
                this.createCommandStatusNotification(newRecord, oldRecord);
            }
        }
    }

    // Gérer les PV temps réel
    handleRealtimePV(data) {
        console.log('📄 PV temps réel reçu:', data);
        
        const { eventType, newRecord } = data;
        
        if (eventType === 'INSERT' && newRecord) {
            this.createPVNotification(newRecord);
        }
    }

    // Gérer l'arrivée d'un utilisateur
    handleUserJoin(data) {
        console.log('👋 Utilisateur connecté:', data);
        
        // Notification discrète
        if (this.modules.notifications) {
            this.modules.notifications.showNotification({
                title: 'Nouvel utilisateur',
                body: `${data.presences[0]?.name} s'est connecté`,
                type: 'system',
                autoHide: true
            });
        }
    }

    // Gérer le départ d'un utilisateur
    handleUserLeave(data) {
        console.log('👋 Utilisateur déconnecté:', data);
        
        // Pas de notification pour les déconnexions (moins intrusif)
    }

    // Gérer les mises à jour UI
    handleUIUpdate(data) {
        console.log('🎨 Mise à jour UI:', data);
        
        // Déclencher le callback d'activité utilisateur
        this.triggerCallback('onUserActivity', data);
    }

    // Créer une notification de message
    createMessageNotification(message) {
        if (!this.modules.notifications) return;
        
        // Ne pas notifier ses propres messages
        if (message.sender_username === this.currentUser?.username) return;
        
        // Vérifier si le message nous concerne
        const isForMe = message.recipient === this.currentUser?.username || 
                       message.recipient === 'all';
        
        if (isForMe) {
            this.modules.notifications.showNotification({
                title: `Nouveau message de ${message.sender_name}`,
                body: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : ''),
                type: 'message',
                data: message,
                autoHide: false
            });
        }
    }

    // Créer une notification de changement de statut de commande
    createCommandStatusNotification(newCommand, oldCommand) {
        if (!this.modules.notifications) return;
        
        this.modules.notifications.showNotification({
            title: `Commande ${newCommand.numero_commande}`,
            body: `Statut changé: ${oldCommand.statut} → ${newCommand.statut}`,
            type: 'command',
            data: newCommand,
            autoHide: true
        });
    }

    // Créer une notification de PV
    createPVNotification(pv) {
        if (!this.modules.notifications) return;
        
        this.modules.notifications.showNotification({
            title: `Nouveau PV: ${pv.numero_pv}`,
            body: `Type: ${pv.type_pv} - Dépôt: ${pv.depot}`,
            type: 'pv',
            data: pv,
            autoHide: true
        });
    }

    // Gérer les erreurs globales
    handleGlobalError(error) {
        console.error('🚨 Erreur globale temps réel:', error);
        
        this.syncState.errorCount++;
        this.triggerCallback('onError', error);
        
        // Notification d'erreur si critique
        if (this.modules.notifications && error.message.includes('Supabase')) {
            this.modules.notifications.showNotification({
                title: 'Erreur de synchronisation',
                body: 'Problème de connexion détecté. Tentative de reconnexion...',
                type: 'error',
                autoHide: true
            });
        }
    }

    // Gérer la fermeture de la page
    handleBeforeUnload() {
        console.log('👋 Fermeture de la page, nettoyage...');
        
        // Arrêter tous les modules
        this.stop();
    }

    // Mettre à jour les statistiques de synchronisation
    updateSyncStats() {
        this.syncState.lastSync = new Date().toISOString();
        
        // Envoyer les stats au monitoring si disponible
        if (typeof window.updateRealtimeStats === 'function') {
            window.updateRealtimeStats({
                ...this.syncState,
                modules: this.getModuleStatus()
            });
        }
    }

    // Obtenir le statut des modules
    getModuleStatus() {
        return {
            realtime: this.modules.realtime?.isActive || false,
            notifications: this.modules.notifications?.isEnabled || false,
            uiManager: this.modules.uiManager?.isActive || false,
            userPresence: this.modules.userPresence?.isActive || false
        };
    }

    // Arrêter le coordinateur
    async stop() {
        this.isActive = false;
        
        // Arrêter tous les modules
        if (this.modules.realtime) {
            this.modules.realtime.stopRealtime();
        }
        
        if (this.modules.userPresence) {
            await this.modules.userPresence.stop();
        }
        
        console.log('🛑 Coordinateur temps réel arrêté');
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Déclencher les callbacks
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback coordinateur ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les statistiques complètes
    getStats() {
        return {
            isActive: this.isActive,
            syncState: { ...this.syncState },
            modules: this.getModuleStatus(),
            performance: { ...this.performance }
        };
    }

    // Forcer une synchronisation complète
    async forceFullSync() {
        console.log('🔄 Synchronisation complète forcée...');
        
        try {
            await this.performInitialSync();
            return { success: true };
        } catch (error) {
            console.error('❌ Erreur sync forcée:', error);
            return { success: false, error: error.message };
        }
    }
}

// Export de la classe
if (typeof window !== 'undefined') {
    window.RealtimeCoordinator = RealtimeCoordinator;
}
