<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Workflow de Validation - Institut Past<PERSON></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            color: #2d3748;
            font-size: 28px;
            font-weight: 700;
        }

        .user-info {
            background: #f7fafc;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .user-role {
            font-weight: 600;
            color: #4299e1;
        }

        .pending-actions {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .badge {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .section-content {
            padding: 30px;
        }

        .donation-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            transition: all 0.2s;
            cursor: pointer;
        }

        .donation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            border-color: #4299e1;
        }

        .donation-card.urgent {
            border-left: 4px solid #e53e3e;
            background: #fed7d7;
        }

        .donation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .donation-number {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
        }

        .donation-type {
            background: #4299e1;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .donation-type.equipement {
            background: #9f7aea;
        }

        .donation-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 12px;
            color: #718096;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 4px;
        }

        .info-value {
            color: #2d3748;
            font-weight: 500;
        }

        .donation-description {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4299e1;
        }

        .workflow-progress {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding: 10px 0;
        }

        .workflow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 80px;
            position: relative;
        }

        .workflow-step::after {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: #e2e8f0;
            z-index: -1;
        }

        .workflow-step:last-child::after {
            display: none;
        }

        .workflow-step.completed::after {
            background: #48bb78;
        }

        .workflow-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            color: #718096;
            margin-bottom: 8px;
        }

        .workflow-step.completed .workflow-circle {
            background: #48bb78;
            color: white;
        }

        .workflow-step.current .workflow-circle {
            background: #4299e1;
            color: white;
            animation: pulse 2s infinite;
        }

        .workflow-step.pending .workflow-circle {
            background: #fed7d7;
            color: #c53030;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(66, 153, 225, 0); }
            100% { box-shadow: 0 0 0 0 rgba(66, 153, 225, 0); }
        }

        .workflow-label {
            font-size: 10px;
            color: #718096;
            text-align: center;
            line-height: 1.2;
        }

        .workflow-step.current .workflow-label {
            color: #4299e1;
            font-weight: 600;
        }

        .donation-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .btn-secondary {
            background: #edf2f7;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #4a5568;
        }

        .empty-description {
            font-size: 16px;
            line-height: 1.5;
        }

        .filters {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .filter-select,
        .filter-input {
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
        }

        .filter-select:focus,
        .filter-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            padding: 25px;
            text-align: center;
        }

        .stat-icon {
            font-size: 32px;
            margin-bottom: 15px;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .stat-label {
            color: #718096;
            font-size: 14px;
            font-weight: 500;
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px;
            color: #718096;
        }

        .spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #e2e8f0;
            border-top: 3px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .donation-header {
                flex-direction: column;
                gap: 10px;
            }

            .donation-info {
                grid-template-columns: 1fr;
            }

            .workflow-progress {
                justify-content: flex-start;
            }

            .donation-actions {
                justify-content: center;
            }

            .filters-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
            box-sizing: border-box;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .alert-info {
            background: #ebf8ff;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔄 Workflow de Validation des Dons</h1>
            <div class="user-info">
                <div>
                    <span>Connecté en tant que :</span>
                    <span class="user-role" id="userRole">Chargement...</span>
                </div>
                <div>
                    <span id="userEmail">Chargement...</span>
                </div>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-icon">📋</div>
                <div class="stat-value" id="statTotal">0</div>
                <div class="stat-label">Total Dons</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⏳</div>
                <div class="stat-value" id="statPending">0</div>
                <div class="stat-label">En Attente</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🚨</div>
                <div class="stat-value" id="statUrgent">0</div>
                <div class="stat-label">Urgents</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">✅</div>
                <div class="stat-value" id="statCompleted">0</div>
                <div class="stat-label">Validés</div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filters">
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Type de don</label>
                    <select class="filter-select" id="filterType">
                        <option value="">Tous les types</option>
                        <option value="article">Articles</option>
                        <option value="equipement">Équipements</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Priorité</label>
                    <select class="filter-select" id="filterPriority">
                        <option value="">Toutes les priorités</option>
                        <option value="urgente">Urgente</option>
                        <option value="elevee">Élevée</option>
                        <option value="normale">Normale</option>
                        <option value="faible">Faible</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Service</label>
                    <select class="filter-select" id="filterService">
                        <option value="">Tous les services</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Recherche</label>
                    <input type="text" class="filter-input" id="filterSearch" placeholder="Numéro, donateur...">
                </div>
            </div>
        </div>

        <!-- Actions en attente -->
        <div class="pending-actions">
            <div class="section-header">
                <div class="section-title">
                    <span>🎯</span>
                    Actions Requises
                </div>
                <div class="badge" id="pendingBadge">0</div>
            </div>
            <div class="section-content" id="pendingContent">
                <div class="loading">
                    <div class="spinner"></div>
                    Chargement des actions en attente...
                </div>
            </div>
        </div>

        <!-- Tous les dons -->
        <div class="pending-actions">
            <div class="section-header">
                <div class="section-title">
                    <span>📋</span>
                    Tous les Dons
                </div>
                <div class="badge" id="totalBadge">0</div>
            </div>
            <div class="section-content" id="allDonationsContent">
                <div class="loading">
                    <div class="spinner"></div>
                    Chargement des dons...
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de validation -->
    <div id="validationModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Validation du Don</h3>
                <button class="modal-close" onclick="closeValidationModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="modalAlerts"></div>
                
                <div class="form-group">
                    <label class="form-label">Don concerné</label>
                    <div id="modalDonationInfo" style="background: #f7fafc; padding: 15px; border-radius: 8px; font-weight: 500;"></div>
                </div>

                <div class="form-group">
                    <label class="form-label">Action</label>
                    <select id="modalAction" class="form-select">
                        <option value="approve">✅ Approuver</option>
                        <option value="reject">❌ Refuser</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Commentaires</label>
                    <textarea id="modalComments" class="form-textarea" 
                             placeholder="Ajoutez vos commentaires (optionnel pour approbation, requis pour refus)..."></textarea>
                </div>

                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeValidationModal()">
                        Annuler
                    </button>
                    <button type="button" class="btn btn-primary" onclick="submitValidation()" id="submitValidationBtn">
                        Valider
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/donations-workflow.js"></script>
    
    <script>
        // Initialisation de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔄 Initialisation du workflow de validation...');
            
            try {
                // Initialiser Supabase
                if (typeof initializeSupabase === 'function') {
                    const { client, available } = await initializeSupabase();
                    
                    if (available) {
                        console.log('✅ Supabase connecté pour le workflow');
                        
                        // Initialiser le gestionnaire de workflow
                        if (typeof DonationsWorkflow !== 'undefined') {
                            await donationsWorkflow.initialize();
                            console.log('✅ Gestionnaire de workflow initialisé');
                        }
                    } else {
                        console.warn('⚠️ Supabase non disponible - mode dégradé');
                        showAlert('Connexion à la base de données indisponible.', 'error');
                    }
                } else {
                    console.error('❌ Fonction initializeSupabase non trouvée');
                }
                
            } catch (error) {
                console.error('❌ Erreur initialisation workflow:', error);
                showAlert('Erreur lors de l\'initialisation. Veuillez rafraîchir la page.', 'error');
            }
        });

        // Fonctions globales pour l'interface
        function showAlert(message, type = 'info') {
            const container = document.getElementById('modalAlerts') || document.body;
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            alert.innerHTML = `<span>${icon}</span><div>${message}</div>`;
            
            container.appendChild(alert);
            
            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        function openValidationModal(donationId, action = 'validate') {
            if (window.donationsWorkflow) {
                donationsWorkflow.openValidationModal(donationId, action);
            }
        }

        function closeValidationModal() {
            document.getElementById('validationModal').style.display = 'none';
        }

        function submitValidation() {
            if (window.donationsWorkflow) {
                donationsWorkflow.submitValidation();
            }
        }

        function viewDonationDetails(donationId) {
            window.open(`donations-management.html?id=${donationId}`, '_blank');
        }

        // Gestion des filtres
        document.addEventListener('DOMContentLoaded', function() {
            const filters = ['filterType', 'filterPriority', 'filterService', 'filterSearch'];
            
            filters.forEach(filterId => {
                const element = document.getElementById(filterId);
                if (element) {
                    element.addEventListener('change', () => {
                        if (window.donationsWorkflow) {
                            donationsWorkflow.applyFilters();
                        }
                    });
                    
                    if (filterId === 'filterSearch') {
                        element.addEventListener('input', () => {
                            clearTimeout(window.searchTimeout);
                            window.searchTimeout = setTimeout(() => {
                                if (window.donationsWorkflow) {
                                    donationsWorkflow.applyFilters();
                                }
                            }, 300);
                        });
                    }
                }
            });
        });
    </script>
</body>
</html>
