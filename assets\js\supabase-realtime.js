// Système de temps réel avancé pour Supabase
// Synchronisation instantanée entre tous les navigateurs connectés

class SupabaseRealtime {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.isActive = false;
        this.channels = new Map();
        this.listeners = new Map();
        this.userSessions = new Set();
        this.currentUser = null;
        
        // Statistiques temps réel
        this.stats = {
            messagesReceived: 0,
            messagesSent: 0,
            connectionsActive: 0,
            lastActivity: null,
            uptime: 0,
            startTime: Date.now()
        };
        
        // Queue des notifications
        this.notificationQueue = [];
        this.maxNotifications = 50;
        
        // Callbacks pour les événements
        this.callbacks = {
            onMessage: [],
            onCommand: [],
            onPV: [],
            onUser: [],
            onNotification: [],
            onPresence: [],
            onError: []
        };
        
        // Configuration des tables à surveiller
        this.watchedTables = {
            messages: {
                events: ['INSERT', 'UPDATE', 'DELETE'],
                callback: this.handleMessageUpdate.bind(this),
                notification: true
            },
            commands: {
                events: ['INSERT', 'UPDATE', 'DELETE'],
                callback: this.handleCommandUpdate.bind(this),
                notification: true
            },
            pv: {
                events: ['INSERT', 'UPDATE', 'DELETE'],
                callback: this.handlePVUpdate.bind(this),
                notification: true
            },
            users: {
                events: ['INSERT', 'UPDATE'],
                callback: this.handleUserUpdate.bind(this),
                notification: false
            }
        };
    }

    // Démarrer le système temps réel
    async startRealtime(currentUser = null) {
        if (this.isActive) {
            console.log('⚠️ Système temps réel déjà actif');
            return;
        }

        this.currentUser = currentUser;
        this.isActive = true;
        this.stats.startTime = Date.now();
        
        console.log('🔄 Démarrage du système temps réel Supabase...');
        
        try {
            // Configurer les listeners pour chaque table
            await this.setupTableListeners();
            
            // Configurer la présence utilisateur
            if (this.currentUser) {
                await this.setupUserPresence();
            }
            
            // Démarrer le heartbeat
            this.startHeartbeat();
            
            // Démarrer les statistiques
            this.startStatsCollection();
            
            console.log('✅ Système temps réel actif');
            this.triggerCallback('onNotification', {
                type: 'system',
                message: 'Système temps réel activé',
                level: 'success'
            });
            
        } catch (error) {
            console.error('❌ Erreur démarrage temps réel:', error);
            this.isActive = false;
            throw error;
        }
    }

    // Arrêter le système temps réel
    stopRealtime() {
        this.isActive = false;
        
        // Fermer tous les canaux
        this.channels.forEach((channel, name) => {
            console.log(`🔌 Fermeture canal ${name}`);
            channel.unsubscribe();
        });
        this.channels.clear();
        
        // Arrêter le heartbeat
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        
        // Arrêter les statistiques
        if (this.statsInterval) {
            clearInterval(this.statsInterval);
        }
        
        console.log('🛑 Système temps réel arrêté');
    }

    // Configurer les listeners pour les tables
    async setupTableListeners() {
        console.log('🔧 Configuration des listeners temps réel...');
        
        for (const [table, config] of Object.entries(this.watchedTables)) {
            try {
                const tableName = this.config.tables[table];
                if (!tableName) {
                    console.warn(`⚠️ Table ${table} non configurée`);
                    continue;
                }
                
                const channelName = `realtime_${table}`;
                console.log(`📡 Configuration listener pour ${tableName}...`);
                
                const channel = this.client
                    .channel(channelName)
                    .on('postgres_changes', {
                        event: '*',
                        schema: 'public',
                        table: tableName
                    }, (payload) => this.handleTableChange(table, payload))
                    .subscribe((status) => {
                        if (status === 'SUBSCRIBED') {
                            console.log(`✅ Listener ${table} actif`);
                        } else if (status === 'CHANNEL_ERROR') {
                            console.error(`❌ Erreur listener ${table}`);
                        }
                    });
                
                this.channels.set(channelName, channel);
                
            } catch (error) {
                console.error(`❌ Erreur configuration listener ${table}:`, error);
            }
        }
    }

    // Configurer la présence utilisateur
    async setupUserPresence() {
        if (!this.currentUser) return;
        
        console.log(`👤 Configuration présence pour ${this.currentUser.username}...`);
        
        try {
            const presenceChannel = this.client
                .channel('user_presence')
                .on('presence', { event: 'sync' }, () => {
                    const state = presenceChannel.presenceState();
                    this.updateUserPresence(state);
                })
                .on('presence', { event: 'join' }, ({ key, newPresences }) => {
                    console.log(`👋 Utilisateur connecté: ${key}`);
                    this.triggerCallback('onPresence', {
                        type: 'join',
                        user: key,
                        presences: newPresences
                    });
                })
                .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
                    console.log(`👋 Utilisateur déconnecté: ${key}`);
                    this.triggerCallback('onPresence', {
                        type: 'leave',
                        user: key,
                        presences: leftPresences
                    });
                })
                .subscribe(async (status) => {
                    if (status === 'SUBSCRIBED') {
                        await presenceChannel.track({
                            user: this.currentUser.username,
                            name: `${this.currentUser.prenom} ${this.currentUser.nom}`,
                            role: this.currentUser.role,
                            online_at: new Date().toISOString()
                        });
                        console.log('✅ Présence utilisateur configurée');
                    }
                });
            
            this.channels.set('user_presence', presenceChannel);
            
        } catch (error) {
            console.error('❌ Erreur configuration présence:', error);
        }
    }

    // Gérer les changements de table
    handleTableChange(table, payload) {
        console.log(`🔄 Changement détecté ${table}:`, payload);
        
        this.stats.messagesReceived++;
        this.stats.lastActivity = new Date().toISOString();
        
        const { eventType, new: newRecord, old: oldRecord } = payload;
        
        // Appeler le callback spécifique à la table
        const tableConfig = this.watchedTables[table];
        if (tableConfig && tableConfig.callback) {
            tableConfig.callback(eventType, newRecord, oldRecord);
        }
        
        // Créer une notification si configuré
        if (tableConfig && tableConfig.notification) {
            this.createNotification(table, eventType, newRecord, oldRecord);
        }
        
        // Déclencher les callbacks génériques
        this.triggerCallback(`on${table.charAt(0).toUpperCase() + table.slice(1)}`, {
            eventType,
            newRecord,
            oldRecord,
            table
        });
    }

    // Gérer les mises à jour de messages
    handleMessageUpdate(eventType, newRecord, oldRecord) {
        console.log(`💬 Message ${eventType}:`, newRecord || oldRecord);
        
        switch (eventType) {
            case 'INSERT':
                this.handleNewMessage(newRecord);
                break;
            case 'UPDATE':
                this.handleMessageUpdate(newRecord, oldRecord);
                break;
            case 'DELETE':
                this.handleMessageDelete(oldRecord);
                break;
        }
    }

    // Gérer les nouveaux messages
    handleNewMessage(message) {
        // Mettre à jour l'interface utilisateur
        if (typeof window.addMessageToUI === 'function') {
            window.addMessageToUI(message);
        }
        
        // Mettre à jour le localStorage
        this.updateLocalStorage('messages', message, 'INSERT');
        
        // Créer une notification
        if (message.recipient === this.currentUser?.username || message.recipient === 'all') {
            this.showNotification({
                title: `Nouveau message de ${message.sender_name}`,
                body: message.content.substring(0, 100) + (message.content.length > 100 ? '...' : ''),
                icon: '💬',
                type: 'message',
                data: message
            });
        }
        
        // Mettre à jour les badges
        this.updateBadges('messages', 1);
    }

    // Gérer les mises à jour de commandes
    handleCommandUpdate(eventType, newRecord, oldRecord) {
        console.log(`📋 Commande ${eventType}:`, newRecord || oldRecord);
        
        // Mettre à jour l'interface utilisateur
        if (typeof window.updateCommandInUI === 'function') {
            window.updateCommandInUI(eventType, newRecord, oldRecord);
        }
        
        // Mettre à jour le localStorage
        this.updateLocalStorage('commands', newRecord || oldRecord, eventType);
        
        // Notification pour les changements de statut importants
        if (eventType === 'UPDATE' && newRecord.statut !== oldRecord?.statut) {
            this.showNotification({
                title: `Commande ${newRecord.numero_commande}`,
                body: `Statut changé: ${newRecord.statut}`,
                icon: '📋',
                type: 'command',
                data: newRecord
            });
        }
        
        // Mettre à jour les badges
        this.updateBadges('commands', eventType === 'INSERT' ? 1 : 0);
    }

    // Gérer les mises à jour de PV
    handlePVUpdate(eventType, newRecord, oldRecord) {
        console.log(`📄 PV ${eventType}:`, newRecord || oldRecord);
        
        // Mettre à jour l'interface utilisateur
        if (typeof window.updatePVInUI === 'function') {
            window.updatePVInUI(eventType, newRecord, oldRecord);
        }
        
        // Mettre à jour le localStorage
        this.updateLocalStorage('pv', newRecord || oldRecord, eventType);
        
        // Notification pour les nouveaux PV
        if (eventType === 'INSERT') {
            this.showNotification({
                title: `Nouveau PV: ${newRecord.numero_pv}`,
                body: `Type: ${newRecord.type_pv} - Dépôt: ${newRecord.depot}`,
                icon: '📄',
                type: 'pv',
                data: newRecord
            });
        }
        
        // Mettre à jour les badges
        this.updateBadges('pv', eventType === 'INSERT' ? 1 : 0);
    }

    // Gérer les mises à jour d'utilisateurs
    handleUserUpdate(eventType, newRecord, oldRecord) {
        console.log(`👤 Utilisateur ${eventType}:`, newRecord || oldRecord);
        
        // Mettre à jour l'interface utilisateur
        if (typeof window.updateUserInUI === 'function') {
            window.updateUserInUI(eventType, newRecord, oldRecord);
        }
    }

    // Mettre à jour le localStorage
    updateLocalStorage(table, record, eventType) {
        try {
            const key = `gestion_${table}`;
            const existing = JSON.parse(localStorage.getItem(key) || '[]');
            
            switch (eventType) {
                case 'INSERT':
                    // Vérifier si l'élément n'existe pas déjà
                    if (!existing.find(item => item.id === record.id)) {
                        existing.push(record);
                    }
                    break;
                    
                case 'UPDATE':
                    const updateIndex = existing.findIndex(item => item.id === record.id);
                    if (updateIndex !== -1) {
                        existing[updateIndex] = record;
                    }
                    break;
                    
                case 'DELETE':
                    const deleteIndex = existing.findIndex(item => item.id === record.id);
                    if (deleteIndex !== -1) {
                        existing.splice(deleteIndex, 1);
                    }
                    break;
            }
            
            localStorage.setItem(key, JSON.stringify(existing));
            console.log(`💾 LocalStorage mis à jour: ${table}/${eventType}`);
            
        } catch (error) {
            console.error(`❌ Erreur mise à jour localStorage ${table}:`, error);
        }
    }

    // Créer une notification
    createNotification(table, eventType, newRecord, oldRecord) {
        const notification = {
            id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            table,
            eventType,
            record: newRecord || oldRecord,
            timestamp: new Date().toISOString(),
            read: false
        };
        
        this.notificationQueue.unshift(notification);
        
        // Limiter la taille de la queue
        if (this.notificationQueue.length > this.maxNotifications) {
            this.notificationQueue = this.notificationQueue.slice(0, this.maxNotifications);
        }
        
        // Déclencher le callback de notification
        this.triggerCallback('onNotification', notification);
    }

    // Afficher une notification système
    showNotification(options) {
        console.log(`🔔 Notification: ${options.title}`);
        
        // Notification navigateur si autorisée
        if (typeof window.showBrowserNotification === 'function') {
            window.showBrowserNotification(options);
        }
        
        // Notification dans l'interface
        if (typeof window.showUINotification === 'function') {
            window.showUINotification(options);
        }
        
        // Déclencher le callback
        this.triggerCallback('onNotification', {
            type: 'user',
            ...options
        });
    }

    // Mettre à jour les badges
    updateBadges(type, increment = 1) {
        if (typeof window.updateNotificationBadge === 'function') {
            window.updateNotificationBadge(type, increment);
        }
    }

    // Mettre à jour la présence utilisateur
    updateUserPresence(state) {
        const users = Object.keys(state).map(key => state[key][0]);
        this.stats.connectionsActive = users.length;
        
        console.log(`👥 Utilisateurs connectés: ${users.length}`);
        
        // Mettre à jour l'interface
        if (typeof window.updateUserPresenceUI === 'function') {
            window.updateUserPresenceUI(users);
        }
    }

    // Démarrer le heartbeat
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isActive) {
                this.stats.uptime = Date.now() - this.stats.startTime;
                
                // Vérifier la santé des canaux
                this.checkChannelsHealth();
            }
        }, 30000); // Toutes les 30 secondes
    }

    // Vérifier la santé des canaux
    checkChannelsHealth() {
        this.channels.forEach((channel, name) => {
            if (channel.state !== 'joined') {
                console.warn(`⚠️ Canal ${name} déconnecté, tentative de reconnexion...`);
                channel.subscribe();
            }
        });
    }

    // Démarrer la collecte de statistiques
    startStatsCollection() {
        this.statsInterval = setInterval(() => {
            if (this.isActive) {
                this.updateStats();
            }
        }, 60000); // Toutes les minutes
    }

    // Mettre à jour les statistiques
    updateStats() {
        this.stats.uptime = Date.now() - this.stats.startTime;
        
        // Envoyer les stats au monitoring si disponible
        if (typeof window.updateRealtimeStats === 'function') {
            window.updateRealtimeStats(this.stats);
        }
    }

    // Envoyer un message temps réel
    async sendRealtimeMessage(channel, event, payload) {
        try {
            const channelInstance = this.channels.get(channel);
            if (channelInstance) {
                await channelInstance.send({
                    type: 'broadcast',
                    event,
                    payload
                });
                
                this.stats.messagesSent++;
                console.log(`📤 Message envoyé: ${channel}/${event}`);
            }
        } catch (error) {
            console.error(`❌ Erreur envoi message temps réel:`, error);
        }
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Supprimer un callback
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    // Déclencher les callbacks
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les statistiques
    getStats() {
        return { ...this.stats };
    }

    // Obtenir les notifications
    getNotifications(unreadOnly = false) {
        return unreadOnly 
            ? this.notificationQueue.filter(n => !n.read)
            : [...this.notificationQueue];
    }

    // Marquer une notification comme lue
    markNotificationRead(notificationId) {
        const notification = this.notificationQueue.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
        }
    }

    // Vider les notifications
    clearNotifications() {
        this.notificationQueue = [];
    }
}

// Export de la classe
if (typeof window !== 'undefined') {
    window.SupabaseRealtime = SupabaseRealtime;
}
