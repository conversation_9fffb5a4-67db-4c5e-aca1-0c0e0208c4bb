<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Intégration Supabase - Gestion Interne</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #6c757d;
        }
        
        .status-card.connected {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-card.disconnected {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #495057;
        }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin: 5px 0;
            box-sizing: border-box;
        }
        
        .form-group {
            margin: 15px 0;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Intégration Supabase</h1>
        
        <!-- Statut de connexion -->
        <div id="connectionStatus" class="status-card">
            <h3>📡 Statut de Connexion</h3>
            <p id="statusText">Initialisation...</p>
            <button onclick="testConnection()">Tester la Connexion</button>
            <button onclick="initializeSupabase()">Réinitialiser</button>
        </div>
        
        <!-- Test des messages -->
        <div class="test-section">
            <h3>💬 Test Messages</h3>
            <div class="form-group">
                <label>Expéditeur :</label>
                <input type="text" id="senderName" value="Test User" placeholder="Nom de l'expéditeur">
            </div>
            <div class="form-group">
                <label>Destinataire :</label>
                <input type="text" id="recipient" value="admin" placeholder="Destinataire">
            </div>
            <div class="form-group">
                <label>Message :</label>
                <textarea id="messageContent" placeholder="Contenu du message de test"></textarea>
            </div>
            <button onclick="testSaveMessage()">Envoyer Message Test</button>
            <button onclick="testLoadMessages()">Charger Messages</button>
            <div id="messageLog" class="log"></div>
        </div>
        
        <!-- Test des commandes -->
        <div class="test-section">
            <h3>📦 Test Commandes</h3>
            <div class="form-group">
                <label>Fournisseur :</label>
                <input type="text" id="supplier" value="Fournisseur Test" placeholder="Nom du fournisseur">
            </div>
            <div class="form-group">
                <label>Observations :</label>
                <textarea id="commandNotes" placeholder="Notes sur la commande"></textarea>
            </div>
            <button onclick="testSaveCommand()">Créer Commande Test</button>
            <button onclick="testLoadCommands()">Charger Commandes</button>
            <div id="commandLog" class="log"></div>
        </div>
        
        <!-- Test de synchronisation -->
        <div class="test-section">
            <h3>🔄 Test Synchronisation</h3>
            <button onclick="testSyncAll()">Synchroniser Tout</button>
            <button onclick="clearLocalData()">Vider Données Locales</button>
            <button onclick="showStats()">Afficher Statistiques</button>
            <div id="syncLog" class="log"></div>
        </div>
        
        <!-- Logs généraux -->
        <div class="test-section">
            <h3>📋 Logs Généraux</h3>
            <button onclick="clearLogs()">Vider Logs</button>
            <div id="generalLog" class="log"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    
    <script>
        // Variables globales pour les tests
        let testResults = {
            connection: false,
            messages: false,
            commands: false,
            sync: false
        };

        // Fonction de logging
        function log(message, type = 'info', targetId = 'generalLog') {
            const logElement = document.getElementById(targetId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = type;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
            
            // Log aussi dans la console
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Test de connexion
        async function testConnection() {
            log('🔍 Test de connexion Supabase...', 'info');
            
            try {
                if (typeof initializeSupabase === 'function') {
                    const result = await initializeSupabase();
                    
                    if (result.available) {
                        updateConnectionStatus('connected', '✅ Supabase connecté avec succès');
                        log('✅ Connexion Supabase réussie', 'success');
                        testResults.connection = true;
                    } else {
                        updateConnectionStatus('disconnected', '⚠️ Supabase non disponible - Mode local');
                        log('⚠️ Supabase non disponible, mode local activé', 'warning');
                        testResults.connection = false;
                    }
                } else {
                    throw new Error('Configuration Supabase non trouvée');
                }
            } catch (error) {
                updateConnectionStatus('error', '❌ Erreur de connexion');
                log(`❌ Erreur de connexion: ${error.message}`, 'error');
                testResults.connection = false;
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(status, message) {
            const statusCard = document.getElementById('connectionStatus');
            const statusText = document.getElementById('statusText');
            
            statusCard.className = `status-card ${status}`;
            statusText.textContent = message;
        }

        // Test sauvegarde message
        async function testSaveMessage() {
            const sender = document.getElementById('senderName').value;
            const recipient = document.getElementById('recipient').value;
            const content = document.getElementById('messageContent').value;
            
            if (!content.trim()) {
                log('❌ Veuillez saisir un message', 'error', 'messageLog');
                return;
            }
            
            log('💾 Test sauvegarde message...', 'info', 'messageLog');
            
            try {
                const messageData = {
                    sender_username: 'test_user',
                    sender_name: sender,
                    recipient: recipient,
                    content: content,
                    is_read: false
                };
                
                if (typeof DatabaseManager !== 'undefined') {
                    const result = await DatabaseManager.save('messages', messageData);
                    
                    if (result) {
                        log(`✅ Message sauvegardé: ${result.id}`, 'success', 'messageLog');
                        testResults.messages = true;
                        
                        // Vider le formulaire
                        document.getElementById('messageContent').value = '';
                    } else {
                        throw new Error('Échec de la sauvegarde');
                    }
                } else {
                    throw new Error('DatabaseManager non disponible');
                }
            } catch (error) {
                log(`❌ Erreur sauvegarde message: ${error.message}`, 'error', 'messageLog');
                testResults.messages = false;
            }
        }

        // Test chargement messages
        async function testLoadMessages() {
            log('📥 Test chargement messages...', 'info', 'messageLog');
            
            try {
                if (typeof DatabaseManager !== 'undefined') {
                    const messages = await DatabaseManager.load('messages');
                    
                    log(`✅ ${messages.length} messages chargés`, 'success', 'messageLog');
                    
                    // Afficher les derniers messages
                    const recentMessages = messages.slice(-3);
                    recentMessages.forEach(msg => {
                        log(`📨 ${msg.sender_name}: ${msg.content.substring(0, 50)}...`, 'info', 'messageLog');
                    });
                } else {
                    throw new Error('DatabaseManager non disponible');
                }
            } catch (error) {
                log(`❌ Erreur chargement messages: ${error.message}`, 'error', 'messageLog');
            }
        }

        // Test sauvegarde commande
        async function testSaveCommand() {
            const supplier = document.getElementById('supplier').value;
            const notes = document.getElementById('commandNotes').value;
            
            log('💾 Test sauvegarde commande...', 'info', 'commandLog');
            
            try {
                const commandData = {
                    numero_commande: `CMD-${Date.now()}`,
                    fournisseur: supplier,
                    date_commande: new Date().toISOString().split('T')[0],
                    statut: 'en_attente',
                    produits: [],
                    observations: notes,
                    created_by: 'test_user'
                };
                
                if (typeof DatabaseManager !== 'undefined') {
                    const result = await DatabaseManager.save('commands', commandData);
                    
                    if (result) {
                        log(`✅ Commande sauvegardée: ${result.numero_commande}`, 'success', 'commandLog');
                        testResults.commands = true;
                        
                        // Vider le formulaire
                        document.getElementById('commandNotes').value = '';
                    } else {
                        throw new Error('Échec de la sauvegarde');
                    }
                } else {
                    throw new Error('DatabaseManager non disponible');
                }
            } catch (error) {
                log(`❌ Erreur sauvegarde commande: ${error.message}`, 'error', 'commandLog');
                testResults.commands = false;
            }
        }

        // Test chargement commandes
        async function testLoadCommands() {
            log('📥 Test chargement commandes...', 'info', 'commandLog');
            
            try {
                if (typeof DatabaseManager !== 'undefined') {
                    const commands = await DatabaseManager.load('commands');
                    
                    log(`✅ ${commands.length} commandes chargées`, 'success', 'commandLog');
                    
                    // Afficher les dernières commandes
                    const recentCommands = commands.slice(-3);
                    recentCommands.forEach(cmd => {
                        log(`📦 ${cmd.numero_commande}: ${cmd.fournisseur}`, 'info', 'commandLog');
                    });
                } else {
                    throw new Error('DatabaseManager non disponible');
                }
            } catch (error) {
                log(`❌ Erreur chargement commandes: ${error.message}`, 'error', 'commandLog');
            }
        }

        // Test synchronisation complète
        async function testSyncAll() {
            log('🔄 Test synchronisation complète...', 'info', 'syncLog');
            
            try {
                if (typeof DatabaseManager !== 'undefined') {
                    const result = await DatabaseManager.syncAll();
                    
                    if (result.success) {
                        log('✅ Synchronisation réussie', 'success', 'syncLog');
                        log(`📊 Résultats: ${JSON.stringify(result.results)}`, 'info', 'syncLog');
                        testResults.sync = true;
                    } else {
                        log(`⚠️ Synchronisation échouée: ${result.error}`, 'warning', 'syncLog');
                        testResults.sync = false;
                    }
                } else {
                    throw new Error('DatabaseManager non disponible');
                }
            } catch (error) {
                log(`❌ Erreur synchronisation: ${error.message}`, 'error', 'syncLog');
                testResults.sync = false;
            }
        }

        // Vider les données locales
        function clearLocalData() {
            try {
                localStorage.removeItem('gestion_messages');
                localStorage.removeItem('gestion_commands');
                localStorage.removeItem('gestion_pv');
                
                log('🗑️ Données locales vidées', 'info', 'syncLog');
            } catch (error) {
                log(`❌ Erreur vidage données: ${error.message}`, 'error', 'syncLog');
            }
        }

        // Afficher les statistiques
        function showStats() {
            log('📊 Statistiques des tests:', 'info', 'syncLog');
            log(`🔗 Connexion: ${testResults.connection ? '✅' : '❌'}`, 'info', 'syncLog');
            log(`💬 Messages: ${testResults.messages ? '✅' : '❌'}`, 'info', 'syncLog');
            log(`📦 Commandes: ${testResults.commands ? '✅' : '❌'}`, 'info', 'syncLog');
            log(`🔄 Synchronisation: ${testResults.sync ? '✅' : '❌'}`, 'info', 'syncLog');
            
            const successCount = Object.values(testResults).filter(Boolean).length;
            const totalTests = Object.keys(testResults).length;
            
            log(`📈 Score: ${successCount}/${totalTests} tests réussis`, 'info', 'syncLog');
        }

        // Vider tous les logs
        function clearLogs() {
            document.getElementById('generalLog').innerHTML = '';
            document.getElementById('messageLog').innerHTML = '';
            document.getElementById('commandLog').innerHTML = '';
            document.getElementById('syncLog').innerHTML = '';
        }

        // Initialisation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Page de test chargée', 'info');
            log('🔧 Initialisation de Supabase...', 'info');
            
            // Test automatique de connexion
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
