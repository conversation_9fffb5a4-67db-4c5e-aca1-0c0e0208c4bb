// Système d'authentification Supabase complet
// Version complète avec gestion automatique des sessions, profils et permissions

class SupabaseAuthComplete {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.currentUser = null;
        this.currentProfile = null;
        this.isAuthenticated = false;
        this.sessionCheckInterval = null;
        
        // Callbacks pour les événements
        this.callbacks = {
            onSignIn: [],
            onSignOut: [],
            onSignUp: [],
            onPasswordReset: [],
            onUserUpdate: [],
            onError: [],
            onSessionExpired: [],
            onProfileUpdate: []
        };
        
        // État de l'authentification
        this.authState = {
            loading: false,
            error: null,
            lastActivity: Date.now()
        };
    }

    // Initialisation complète du système d'authentification
    async initialize() {
        console.log('🔐 Initialisation du système d\'authentification complet...');
        
        try {
            // Vérifier la session actuelle
            await this.checkCurrentSession();
            
            // Configurer les listeners d'événements
            this.setupAuthStateListener();
            
            // Démarrer la surveillance des sessions
            this.startSessionMonitoring();
            
            // Configurer la gestion automatique des tokens
            if (this.config.auth.autoRefreshToken) {
                this.setupTokenRefresh();
            }
            
            console.log('✅ Système d\'authentification initialisé');
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation authentification:', error);
            this.triggerCallback('onError', error);
            return false;
        }
    }

    // Vérifier la session actuelle
    async checkCurrentSession() {
        try {
            const { data: { session }, error } = await this.client.auth.getSession();
            
            if (error) {
                throw error;
            }
            
            if (session) {
                await this.handleSessionUpdate(session);
                console.log('✅ Session active trouvée');
            } else {
                console.log('ℹ️ Aucune session active');
                this.handleSignOut();
            }
            
        } catch (error) {
            console.error('❌ Erreur vérification session:', error);
            this.handleSignOut();
        }
    }

    // Configurer le listener d'état d'authentification
    setupAuthStateListener() {
        this.client.auth.onAuthStateChange(async (event, session) => {
            console.log(`🔄 Événement auth: ${event}`);
            
            switch (event) {
                case 'SIGNED_IN':
                    await this.handleSessionUpdate(session);
                    this.triggerCallback('onSignIn', { user: this.currentUser, profile: this.currentProfile });
                    break;
                    
                case 'SIGNED_OUT':
                    this.handleSignOut();
                    this.triggerCallback('onSignOut');
                    break;
                    
                case 'TOKEN_REFRESHED':
                    await this.handleSessionUpdate(session);
                    console.log('🔄 Token rafraîchi');
                    break;
                    
                case 'USER_UPDATED':
                    await this.handleUserUpdate(session);
                    this.triggerCallback('onUserUpdate', { user: this.currentUser, profile: this.currentProfile });
                    break;
            }
        });
    }

    // Gérer la mise à jour de session
    async handleSessionUpdate(session) {
        if (!session || !session.user) {
            this.handleSignOut();
            return;
        }
        
        this.currentUser = session.user;
        this.isAuthenticated = true;
        this.authState.lastActivity = Date.now();
        
        // Charger le profil utilisateur
        await this.loadUserProfile();
        
        // Mettre à jour le dernier login
        await this.updateLastLogin();
        
        // Créer/mettre à jour la session dans la base
        await this.createUserSession(session);
    }

    // Charger le profil utilisateur
    async loadUserProfile() {
        if (!this.currentUser) return;
        
        try {
            const { data: profile, error } = await this.client
                .from(this.config.tables.users)
                .select('*')
                .eq('id', this.currentUser.id)
                .single();
            
            if (error && error.code !== 'PGRST116') { // Pas d'erreur si pas trouvé
                throw error;
            }
            
            if (profile) {
                this.currentProfile = profile;
                console.log(`👤 Profil chargé: ${profile.nom} ${profile.prenom} (${profile.role})`);
            } else {
                // Créer un profil par défaut si inexistant
                await this.createDefaultProfile();
            }
            
        } catch (error) {
            console.error('❌ Erreur chargement profil:', error);
        }
    }

    // Créer un profil par défaut
    async createDefaultProfile() {
        if (!this.currentUser) return;
        
        try {
            const profileData = {
                id: this.currentUser.id,
                email: this.currentUser.email,
                nom: this.currentUser.user_metadata?.nom || 'Utilisateur',
                prenom: this.currentUser.user_metadata?.prenom || 'Nouveau',
                role: this.currentUser.user_metadata?.role || 'user',
                service: this.currentUser.user_metadata?.service || '',
                is_active: true
            };
            
            const { data: profile, error } = await this.client
                .from(this.config.tables.users)
                .insert(profileData)
                .select()
                .single();
            
            if (error) throw error;
            
            this.currentProfile = profile;
            console.log('✅ Profil par défaut créé');
            
        } catch (error) {
            console.error('❌ Erreur création profil:', error);
        }
    }

    // Mettre à jour le dernier login
    async updateLastLogin() {
        if (!this.currentUser) return;
        
        try {
            await this.client.rpc('update_last_login');
        } catch (error) {
            console.warn('⚠️ Erreur mise à jour dernier login:', error);
        }
    }

    // Créer une session utilisateur
    async createUserSession(session) {
        if (!session || !this.currentUser) return;
        
        try {
            const sessionData = {
                user_id: this.currentUser.id,
                session_token: session.access_token,
                ip_address: await this.getClientIP(),
                user_agent: navigator.userAgent,
                expires_at: new Date(session.expires_at * 1000).toISOString(),
                is_active: true
            };
            
            await this.client
                .from(this.config.tables.sessions)
                .insert(sessionData);
                
        } catch (error) {
            console.warn('⚠️ Erreur création session:', error);
        }
    }

    // Obtenir l'IP du client
    async getClientIP() {
        try {
            const response = await fetch('https://api.ipify.org?format=json');
            const data = await response.json();
            return data.ip;
        } catch {
            return null;
        }
    }

    // Gérer la déconnexion
    handleSignOut() {
        this.currentUser = null;
        this.currentProfile = null;
        this.isAuthenticated = false;
        this.authState.error = null;
        
        // Nettoyer les données locales
        this.clearLocalData();
        
        console.log('👋 Utilisateur déconnecté');
    }

    // Nettoyer les données locales
    clearLocalData() {
        // Supprimer les données sensibles du localStorage
        const keysToRemove = ['user_profile', 'user_preferences', 'temp_data'];
        keysToRemove.forEach(key => localStorage.removeItem(key));
    }

    // Connexion avec email/mot de passe
    async signInWithEmail(email, password) {
        this.authState.loading = true;
        this.authState.error = null;
        
        try {
            console.log(`🔐 Tentative de connexion: ${email}`);
            
            const { data, error } = await this.client.auth.signInWithPassword({
                email: email.trim().toLowerCase(),
                password: password
            });
            
            if (error) {
                this.authState.error = this.translateError(error);
                this.triggerCallback('onError', this.authState.error);
                return { success: false, error: this.authState.error };
            }
            
            console.log('✅ Connexion réussie');
            return { success: true, user: data.user, session: data.session };
            
        } catch (error) {
            this.authState.error = 'Erreur de connexion inattendue';
            this.triggerCallback('onError', this.authState.error);
            return { success: false, error: this.authState.error };
        } finally {
            this.authState.loading = false;
        }
    }

    // Inscription avec email/mot de passe
    async signUpWithEmail(email, password, userData = {}) {
        this.authState.loading = true;
        this.authState.error = null;
        
        try {
            console.log(`📝 Création de compte: ${email}`);
            
            // Validation des données
            const validation = this.validateSignUpData(email, password, userData);
            if (!validation.valid) {
                this.authState.error = validation.error;
                return { success: false, error: validation.error };
            }
            
            const { data, error } = await this.client.auth.signUp({
                email: email.trim().toLowerCase(),
                password: password,
                options: {
                    data: {
                        nom: userData.nom || '',
                        prenom: userData.prenom || '',
                        role: userData.role || 'user',
                        service: userData.service || ''
                    }
                }
            });
            
            if (error) {
                this.authState.error = this.translateError(error);
                this.triggerCallback('onError', this.authState.error);
                return { success: false, error: this.authState.error };
            }
            
            console.log('✅ Compte créé avec succès');
            this.triggerCallback('onSignUp', { user: data.user });
            return { success: true, user: data.user, needsConfirmation: !data.session };
            
        } catch (error) {
            this.authState.error = 'Erreur de création de compte';
            this.triggerCallback('onError', this.authState.error);
            return { success: false, error: this.authState.error };
        } finally {
            this.authState.loading = false;
        }
    }

    // Déconnexion
    async signOut() {
        try {
            console.log('👋 Déconnexion...');
            
            // Marquer la session comme inactive
            if (this.currentUser) {
                await this.deactivateUserSession();
            }
            
            const { error } = await this.client.auth.signOut();
            
            if (error) {
                console.warn('⚠️ Erreur lors de la déconnexion:', error);
            }
            
            return { success: true };
            
        } catch (error) {
            console.error('❌ Erreur déconnexion:', error);
            return { success: false, error: error.message };
        }
    }

    // Désactiver la session utilisateur
    async deactivateUserSession() {
        try {
            await this.client
                .from(this.config.tables.sessions)
                .update({ is_active: false })
                .eq('user_id', this.currentUser.id)
                .eq('is_active', true);
        } catch (error) {
            console.warn('⚠️ Erreur désactivation session:', error);
        }
    }

    // Réinitialisation de mot de passe
    async resetPassword(email) {
        try {
            console.log(`🔄 Réinitialisation mot de passe: ${email}`);
            
            const { error } = await this.client.auth.resetPasswordForEmail(
                email.trim().toLowerCase(),
                {
                    redirectTo: `${window.location.origin}${this.config.auth.redirectUrls.passwordReset}`
                }
            );
            
            if (error) {
                this.triggerCallback('onError', this.translateError(error));
                return { success: false, error: this.translateError(error) };
            }
            
            console.log('✅ Email de réinitialisation envoyé');
            this.triggerCallback('onPasswordReset', { email });
            return { success: true };
            
        } catch (error) {
            this.triggerCallback('onError', error.message);
            return { success: false, error: error.message };
        }
    }

    // Validation des données d'inscription
    validateSignUpData(email, password, userData) {
        // Validation email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return { valid: false, error: 'Format d\'email invalide' };
        }
        
        // Validation mot de passe
        if (password.length < this.config.auth.passwordMinLength) {
            return { valid: false, error: `Le mot de passe doit contenir au moins ${this.config.auth.passwordMinLength} caractères` };
        }
        
        // Validation nom/prénom
        if (!userData.nom || userData.nom.trim().length < 2) {
            return { valid: false, error: 'Le nom doit contenir au moins 2 caractères' };
        }
        
        if (!userData.prenom || userData.prenom.trim().length < 2) {
            return { valid: false, error: 'Le prénom doit contenir au moins 2 caractères' };
        }
        
        return { valid: true };
    }

    // Traduction des erreurs
    translateError(error) {
        const errorMessages = {
            'Invalid login credentials': 'Email ou mot de passe incorrect',
            'Email not confirmed': 'Email non confirmé. Vérifiez votre boîte mail.',
            'User already registered': 'Un compte existe déjà avec cet email',
            'Password should be at least 6 characters': 'Le mot de passe doit contenir au moins 6 caractères',
            'Signup is disabled': 'Les inscriptions sont désactivées',
            'Email rate limit exceeded': 'Trop de tentatives. Réessayez plus tard.',
            'Invalid email': 'Format d\'email invalide'
        };
        
        return errorMessages[error.message] || error.message || 'Une erreur est survenue';
    }

    // Démarrer la surveillance des sessions
    startSessionMonitoring() {
        if (this.sessionCheckInterval) {
            clearInterval(this.sessionCheckInterval);
        }
        
        this.sessionCheckInterval = setInterval(() => {
            this.checkSessionValidity();
        }, 60000); // Vérifier toutes les minutes
    }

    // Vérifier la validité de la session
    async checkSessionValidity() {
        if (!this.isAuthenticated) return;
        
        const now = Date.now();
        const timeSinceActivity = now - this.authState.lastActivity;
        
        // Vérifier le timeout de session
        if (timeSinceActivity > this.config.auth.sessionTimeout) {
            console.warn('⚠️ Session expirée par inactivité');
            this.triggerCallback('onSessionExpired');
            await this.signOut();
            return;
        }
        
        // Vérifier la validité du token
        try {
            const { data: { session }, error } = await this.client.auth.getSession();
            
            if (error || !session) {
                console.warn('⚠️ Session invalide');
                await this.signOut();
            }
        } catch (error) {
            console.error('❌ Erreur vérification session:', error);
        }
    }

    // Configurer le rafraîchissement automatique des tokens
    setupTokenRefresh() {
        // Le client Supabase gère automatiquement le rafraîchissement
        // Cette méthode peut être étendue pour des besoins spécifiques
        console.log('🔄 Rafraîchissement automatique des tokens activé');
    }

    // Mettre à jour l'activité utilisateur
    updateActivity() {
        this.authState.lastActivity = Date.now();
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Supprimer un callback
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    // Déclencher un callback
    triggerCallback(event, data = null) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les informations utilisateur
    getUser() {
        return {
            user: this.currentUser,
            profile: this.currentProfile,
            isAuthenticated: this.isAuthenticated,
            authState: this.authState
        };
    }

    // Vérifier les permissions
    hasPermission(requiredRole) {
        if (!this.currentProfile) return false;
        
        const roleHierarchy = {
            'guest': 0,
            'user': 1,
            'manager': 2,
            'admin': 3
        };
        
        const userLevel = roleHierarchy[this.currentProfile.role] || 0;
        const requiredLevel = roleHierarchy[requiredRole] || 0;
        
        return userLevel >= requiredLevel;
    }

    // Nettoyer les ressources
    destroy() {
        if (this.sessionCheckInterval) {
            clearInterval(this.sessionCheckInterval);
        }
        
        // Nettoyer les callbacks
        Object.keys(this.callbacks).forEach(key => {
            this.callbacks[key] = [];
        });
        
        console.log('🧹 Système d\'authentification nettoyé');
    }
}

// Export pour utilisation
if (typeof window !== 'undefined') {
    window.SupabaseAuthComplete = SupabaseAuthComplete;
}
