-- Données de test pour le système de gestion des dons IPT

-- =====================================================
-- INSERTION DES DONS DE TEST
-- =====================================================

-- Don d'article (réactifs de laboratoire)
INSERT INTO gestion_donations (
    numero_don, type_don, donneur_nom, donneur_contact, donneur_adresse,
    demandeur_nom, demandeur_service, demandeur_email, lieu_affectation, raison_don,
    statut, date_demande, valeur_estimee, created_by
) VALUES (
    'DON-2024-0001', 'article', 'Laboratoire Pfizer Tunisie', '<EMAIL>', 
    'Zone Industrielle, Tunis',
    'Dr. <PERSON>', 'Laboratoire de Microbiologie', '<EMAIL>',
    'Laboratoire de Microbiologie - IPT', 
    'Besoin urgent de réactifs pour les analyses de routine',
    'soumis_dg', '2024-01-15', 2500.00, '<EMAIL>'
);

-- Don d'équipement (microscope)
INSERT INTO gestion_donations (
    numero_don, type_don, donneur_nom, donneur_contact, donneur_adresse,
    demandeur_nom, demandeur_service, demandeur_email, lieu_affectation, raison_don,
    statut, date_demande, valeur_estimee, created_by
) VALUES (
    'DON-2024-0002', 'equipement', 'Université de Médecine de Tunis', '<EMAIL>',
    'Rue Djebel Lakhdhar, Tunis',
    'Dr. Fatma Khelifi', 'Service d''Anatomie Pathologique', '<EMAIL>',
    'Service d''Anatomie Pathologique - IPT',
    'Remplacement d''un microscope défaillant pour les diagnostics',
    'avis_dt', '2024-01-20', 15000.00, '<EMAIL>'
);

-- Don d'article approuvé et reçu
INSERT INTO gestion_donations (
    numero_don, type_don, donneur_nom, donneur_contact, donneur_adresse,
    demandeur_nom, demandeur_service, demandeur_email, lieu_affectation, raison_don,
    statut, date_demande, date_soumission_dg, date_decision_ms, date_reception,
    valeur_estimee, avis_dg, decision_ms, approuve_par_dg, decision_ms_par,
    created_by, updated_by
) VALUES (
    'DON-2024-0003', 'article', 'OMS Bureau Tunisie', '<EMAIL>',
    'Immeuble Ennour, Tunis',
    'Dr. Mohamed Trabelsi', 'Laboratoire de Virologie', '<EMAIL>',
    'Laboratoire de Virologie - IPT',
    'Kits de diagnostic COVID-19 pour surveillance épidémiologique',
    'recu', '2024-01-10', '2024-01-12', '2024-01-18', '2024-01-25',
    5000.00, 'Don approuvé - Besoin justifié', 'Autorisation accordée pour don OMS',
    '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'
);

-- =====================================================
-- INSERTION DES ITEMS DE DONS
-- =====================================================

-- Items pour le don d'article DON-2024-0001
INSERT INTO gestion_donation_items (donation_id, nom_item, description, quantite, unite)
SELECT d.id, 'Réactif PCR Master Mix', 'Kit complet pour amplification PCR', 10, 'kit'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

INSERT INTO gestion_donation_items (donation_id, nom_item, description, quantite, unite)
SELECT d.id, 'Tubes Eppendorf 1.5ml', 'Tubes de microcentrifugation stériles', 1000, 'pièce'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

INSERT INTO gestion_donation_items (donation_id, nom_item, description, quantite, unite)
SELECT d.id, 'Pipettes automatiques', 'Pipettes de précision 10-100µl', 5, 'pièce'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

-- Items pour le don d'équipement DON-2024-0002
INSERT INTO gestion_donation_items (
    donation_id, nom_item, description, quantite, unite, marque, modele, numero_serie,
    specifications
)
SELECT 
    d.id, 'Microscope optique binoculaire', 
    'Microscope de recherche avec objectifs 4x, 10x, 40x, 100x', 
    1, 'pièce', 'Olympus', 'CX23', 'OLY-CX23-2024-001',
    '{"grossissement": "40x-1000x", "eclairage": "LED", "condenseur": "Abbe NA 1.25", "oculaires": "10x"}'::jsonb
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0002';

-- Items pour le don reçu DON-2024-0003
INSERT INTO gestion_donation_items (donation_id, nom_item, description, quantite, unite)
SELECT d.id, 'Kit RT-PCR COVID-19', 'Kit de diagnostic moléculaire SARS-CoV-2', 50, 'kit'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

INSERT INTO gestion_donation_items (donation_id, nom_item, description, quantite, unite)
SELECT d.id, 'Écouvillons nasopharyngés', 'Écouvillons stériles pour prélèvement', 500, 'pièce'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

-- =====================================================
-- INSERTION DES DOCUMENTS DE TEST
-- =====================================================

-- Documents pour DON-2024-0001
INSERT INTO gestion_donation_documents (
    donation_id, type_document, nom_document, numero_document, date_document,
    file_name, uploaded_by
)
SELECT 
    d.id, 'demande_explicative', 'Demande de don réactifs PCR', 'DEM-2024-001', '2024-01-15',
    'demande_reactifs_pcr.pdf', '<EMAIL>'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

INSERT INTO gestion_donation_documents (
    donation_id, type_document, nom_document, numero_document, date_document,
    file_name, uploaded_by
)
SELECT 
    d.id, 'lettre_don', 'Lettre de don Pfizer', 'PFIZER-DON-2024-001', '2024-01-14',
    'lettre_don_pfizer.pdf', '<EMAIL>'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

-- Documents pour DON-2024-0002
INSERT INTO gestion_donation_documents (
    donation_id, type_document, nom_document, numero_document, date_document,
    file_name, uploaded_by
)
SELECT 
    d.id, 'demande_explicative', 'Demande microscope anatomie pathologique', 'DEM-2024-002', '2024-01-20',
    'demande_microscope.pdf', '<EMAIL>'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0002';

INSERT INTO gestion_donation_documents (
    donation_id, type_document, nom_document, numero_document, date_document,
    file_name, uploaded_by
)
SELECT 
    d.id, 'lettre_don', 'Lettre de don Université Médecine', 'UMT-DON-2024-001', '2024-01-19',
    'lettre_don_universite.pdf', '<EMAIL>'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0002';

-- Documents pour DON-2024-0003 (don complet)
INSERT INTO gestion_donation_documents (
    donation_id, type_document, nom_document, numero_document, date_document,
    file_name, uploaded_by
)
SELECT 
    d.id, 'bon_livraison', 'Bon de livraison OMS', 'BL-OMS-2024-001', '2024-01-25',
    'bl_oms_covid_kits.pdf', '<EMAIL>'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

INSERT INTO gestion_donation_documents (
    donation_id, type_document, nom_document, numero_document, date_document,
    file_name, uploaded_by
)
SELECT 
    d.id, 'autorisation_ms', 'Autorisation Ministère Santé', 'AUTH-MS-2024-001', '2024-01-18',
    'autorisation_ms_oms.pdf', '<EMAIL>'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

-- =====================================================
-- INSERTION DU WORKFLOW DE TEST
-- =====================================================

-- Workflow pour DON-2024-0001
INSERT INTO gestion_donation_workflow (
    donation_id, etape, statut_precedent, statut_nouveau,
    acteur_nom, acteur_role, action, commentaire
)
SELECT 
    d.id, 'creation', NULL, 'brouillon',
    '<EMAIL>', 'demandeur', 'Création du don',
    'Don créé pour réactifs PCR'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

INSERT INTO gestion_donation_workflow (
    donation_id, etape, statut_precedent, statut_nouveau,
    acteur_nom, acteur_role, action, commentaire
)
SELECT 
    d.id, 'soumission_dg', 'brouillon', 'soumis_dg',
    '<EMAIL>', 'demandeur', 'Soumission au DG',
    'Don soumis avec tous les documents requis'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

-- Workflow pour DON-2024-0002
INSERT INTO gestion_donation_workflow (
    donation_id, etape, statut_precedent, statut_nouveau,
    acteur_nom, acteur_role, action, commentaire
)
SELECT 
    d.id, 'validation_dg', 'soumis_dg', 'avis_dt',
    '<EMAIL>', 'dg', 'Validation DG',
    'Don validé - Avis technique DT requis pour équipement'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0002';

-- Workflow complet pour DON-2024-0003
INSERT INTO gestion_donation_workflow (
    donation_id, etape, statut_precedent, statut_nouveau,
    acteur_nom, acteur_role, action, commentaire
)
SELECT 
    d.id, 'autorisation_ms', 'soumis_ms', 'approuve_ms',
    '<EMAIL>', 'ms', 'Autorisation MS',
    'Don autorisé par le Ministère de la Santé'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

INSERT INTO gestion_donation_workflow (
    donation_id, etape, statut_precedent, statut_nouveau,
    acteur_nom, acteur_role, action, commentaire
)
SELECT 
    d.id, 'reception', 'approuve_ms', 'recu',
    '<EMAIL>', 'receptionniste', 'Réception du don',
    'Don reçu et vérifié conforme'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

-- =====================================================
-- INSERTION DES APPROBATIONS DE TEST
-- =====================================================

-- Approbation en attente pour DON-2024-0001
INSERT INTO gestion_donation_approvals (
    donation_id, type_approbation, statut
)
SELECT d.id, 'validation_dg', 'en_attente'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0001';

-- Approbation en attente pour DON-2024-0002
INSERT INTO gestion_donation_approvals (
    donation_id, type_approbation, statut
)
SELECT d.id, 'avis_technique_dt', 'en_attente'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0002';

-- Approbations complètes pour DON-2024-0003
INSERT INTO gestion_donation_approvals (
    donation_id, type_approbation, statut, approuve_par, date_approbation, commentaire
)
SELECT 
    d.id, 'validation_dg', 'approuve', '<EMAIL>', '2024-01-12',
    'Don approuvé - Besoin justifié pour surveillance COVID'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

INSERT INTO gestion_donation_approvals (
    donation_id, type_approbation, statut, approuve_par, date_approbation, commentaire
)
SELECT 
    d.id, 'autorisation_ms', 'approuve', '<EMAIL>', '2024-01-18',
    'Autorisation accordée pour don OMS - Priorité santé publique'
FROM gestion_donations d WHERE d.numero_don = 'DON-2024-0003';

-- =====================================================
-- COMMENTAIRES
-- =====================================================

COMMENT ON TABLE gestion_donations IS 'Données de test insérées pour validation du système';
COMMENT ON TABLE gestion_donation_items IS '6 items de test répartis sur 3 dons différents';
COMMENT ON TABLE gestion_donation_documents IS '7 documents de test couvrant différents types';
COMMENT ON TABLE gestion_donation_workflow IS 'Historique workflow pour 3 dons à différents stades';
COMMENT ON TABLE gestion_donation_approvals IS 'Approbations de test en différents états';
