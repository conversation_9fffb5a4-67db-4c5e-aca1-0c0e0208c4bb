<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord Workflow - Gestion des Dons IPT</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    <link rel="stylesheet" href="assets/css/donations-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    <script src="assets/js/supabase-database.js"></script>
    
    <style>
        .workflow-dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .workflow-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .workflow-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #3b82f6;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3b82f6;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #6b7280;
            font-weight: 500;
        }
        
        .workflow-steps {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
        }
        
        .steps-timeline {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            position: relative;
        }
        
        .steps-timeline::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: #e5e7eb;
            z-index: 1;
        }
        
        .step-item {
            background: white;
            border: 3px solid #e5e7eb;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            position: relative;
            z-index: 2;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .step-item.completed {
            background: #10b981;
            border-color: #10b981;
            color: white;
        }
        
        .step-item.current {
            background: #3b82f6;
            border-color: #3b82f6;
            color: white;
            animation: pulse 2s infinite;
        }
        
        .step-item.delayed {
            background: #ef4444;
            border-color: #ef4444;
            color: white;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }
        
        .step-label {
            position: absolute;
            top: 70px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            text-align: center;
            width: 100px;
            color: #6b7280;
        }
        
        .donations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }
        
        .donation-workflow-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            transition: transform 0.2s;
        }
        
        .donation-workflow-card:hover {
            transform: translateY(-2px);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .card-title {
            font-weight: bold;
            color: #1f2937;
        }
        
        .card-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #3b82f6);
            transition: width 0.3s;
        }
        
        .current-step {
            background: #f3f4f6;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .step-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .step-name {
            font-weight: 600;
            color: #1f2937;
        }
        
        .step-duration {
            font-size: 12px;
            color: #6b7280;
        }
        
        .step-actions {
            margin-top: 10px;
            display: flex;
            gap: 10px;
        }
        
        .btn-step {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-complete {
            background: #10b981;
            color: white;
        }
        
        .btn-block {
            background: #ef4444;
            color: white;
        }
        
        .btn-details {
            background: #6b7280;
            color: white;
        }
        
        .filters-section {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .filter-group {
            display: flex;
            flex-direction: column;
        }
        
        .filter-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 5px;
        }
        
        .filter-input {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .delayed-indicator {
            background: #fee2e2;
            color: #dc2626;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .on-time-indicator {
            background: #d1fae5;
            color: #059669;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .modal-workflow {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-workflow-content {
            background: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow: hidden;
        }
        
        .modal-workflow-header {
            background: #f8fafc;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .modal-workflow-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .detailed-timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .detailed-timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e5e7eb;
        }
        
        .timeline-step {
            position: relative;
            margin-bottom: 30px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
        }
        
        .timeline-step::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e5e7eb;
        }
        
        .timeline-step.completed::before {
            background: #10b981;
        }
        
        .timeline-step.current::before {
            background: #3b82f6;
        }
        
        .timeline-step.delayed::before {
            background: #ef4444;
        }
    </style>
</head>
<body>
    <div class="workflow-dashboard">
        <!-- Header -->
        <div class="workflow-header">
            <h1>📊 Tableau de Bord Workflow - Gestion des Dons IPT</h1>
            <p>Suivi en temps réel du processus de gestion des dons selon le logigramme officiel</p>
        </div>

        <!-- Statistiques globales -->
        <div class="workflow-stats">
            <div class="stat-card">
                <div class="stat-number" id="totalDons">0</div>
                <div class="stat-label">Total Dons en Cours</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="donsEnRetard">0</div>
                <div class="stat-label">Dons en Retard</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="tempsMovenTraitement">0</div>
                <div class="stat-label">Temps Moyen (jours)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="tauxCompletion">0%</div>
                <div class="stat-label">Taux de Completion</div>
            </div>
        </div>

        <!-- Timeline des étapes -->
        <div class="workflow-steps">
            <h2>🔄 Étapes du Workflow</h2>
            <div class="steps-timeline">
                <div class="step-item" data-step="1">
                    1
                    <div class="step-label">Création<br/>Dossier</div>
                </div>
                <div class="step-item" data-step="2">
                    2
                    <div class="step-label">Création<br/>BP</div>
                </div>
                <div class="step-item" data-step="3">
                    3
                    <div class="step-label">Traitement<br/>Magasinier</div>
                </div>
                <div class="step-item" data-step="4">
                    4
                    <div class="step-label">Validation<br/>Transitaire</div>
                </div>
                <div class="step-item" data-step="5">
                    5
                    <div class="step-label">Réception</div>
                </div>
                <div class="step-item" data-step="6">
                    6
                    <div class="step-label">Création<br/>BS</div>
                </div>
                <div class="step-item" data-step="7">
                    7
                    <div class="step-label">Traitement<br/>RVE</div>
                </div>
                <div class="step-item" data-step="8">
                    8
                    <div class="step-label">Récapitulatif</div>
                </div>
                <div class="step-item" data-step="9">
                    9
                    <div class="step-label">Envoi<br/>Comptabilité</div>
                </div>
                <div class="step-item" data-step="10">
                    10
                    <div class="step-label">Archivage</div>
                </div>
            </div>
        </div>

        <!-- Filtres -->
        <div class="filters-section">
            <h3>🔍 Filtres</h3>
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Statut</label>
                    <select id="filterStatut" class="filter-input">
                        <option value="">Tous les statuts</option>
                        <option value="bp_cree">BP Créé</option>
                        <option value="chez_magasinier">Chez Magasinier</option>
                        <option value="chez_transitaire">Chez Transitaire</option>
                        <option value="chez_receptionniste">Chez Réceptionniste</option>
                        <option value="bs_cree">BS Créé</option>
                        <option value="chez_rve">Chez RVE</option>
                        <option value="recap_cree">Récap Créé</option>
                        <option value="envoye_comptabilite">Envoyé Comptabilité</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Type</label>
                    <select id="filterType" class="filter-input">
                        <option value="">Tous les types</option>
                        <option value="article">Articles</option>
                        <option value="equipement">Équipements</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">Responsable</label>
                    <select id="filterResponsable" class="filter-input">
                        <option value="">Tous les responsables</option>
                        <option value="magasinier">Magasinier</option>
                        <option value="transitaire">Transitaire</option>
                        <option value="receptionniste">Réceptionniste</option>
                        <option value="rve">RVE</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">État</label>
                    <select id="filterEtat" class="filter-input">
                        <option value="">Tous</option>
                        <option value="en_retard">En Retard</option>
                        <option value="dans_les_temps">Dans les Temps</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Liste des dons -->
        <div class="donations-grid" id="donationsGrid">
            <!-- Les cartes de dons seront générées ici -->
        </div>
    </div>

    <!-- Modal détails workflow -->
    <div id="modalWorkflowDetails" class="modal-workflow">
        <div class="modal-workflow-content">
            <div class="modal-workflow-header">
                <h2 id="workflowModalTitle">Détails du Workflow</h2>
                <span class="close" onclick="closeWorkflowModal()">&times;</span>
            </div>
            <div class="modal-workflow-body" id="workflowModalBody">
                <!-- Contenu généré dynamiquement -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/donations-workflow-dashboard.js"></script>
    
    <script>
        // Initialisation
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📊 Initialisation du tableau de bord workflow...');
            
            try {
                // Vérifier l'authentification
                if (typeof SupabaseAuth !== 'undefined' && authSystem) {
                    const user = await authSystem.getCurrentUser();
                    if (!user) {
                        window.location.href = 'login.html';
                        return;
                    }
                }
                
                // Initialiser le dashboard
                if (typeof WorkflowDashboard !== 'undefined') {
                    await WorkflowDashboard.initialize();
                    console.log('✅ Dashboard workflow initialisé');
                }
                
            } catch (error) {
                console.error('❌ Erreur initialisation dashboard:', error);
            }
        });
        
        function closeWorkflowModal() {
            document.getElementById('modalWorkflowDetails').style.display = 'none';
        }
        
        // Fermer modal en cliquant à l'extérieur
        window.onclick = function(event) {
            const modal = document.getElementById('modalWorkflowDetails');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
