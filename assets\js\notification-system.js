// Système de notifications push avancé
// Notifications en temps réel avec badges et indicateurs visuels

class NotificationSystem {
    constructor() {
        this.isEnabled = false;
        this.permission = 'default';
        this.notifications = [];
        this.badges = new Map();
        this.sounds = new Map();
        this.maxNotifications = 100;
        
        // Configuration des types de notifications
        this.notificationTypes = {
            message: {
                icon: '💬',
                color: '#007bff',
                sound: 'message',
                priority: 'high',
                autoHide: false
            },
            command: {
                icon: '📋',
                color: '#28a745',
                sound: 'notification',
                priority: 'medium',
                autoHide: true
            },
            pv: {
                icon: '📄',
                color: '#ffc107',
                sound: 'notification',
                priority: 'medium',
                autoHide: true
            },
            system: {
                icon: '⚙️',
                color: '#6c757d',
                sound: 'system',
                priority: 'low',
                autoHide: true
            },
            error: {
                icon: '❌',
                color: '#dc3545',
                sound: 'error',
                priority: 'urgent',
                autoHide: false
            },
            success: {
                icon: '✅',
                color: '#28a745',
                sound: 'success',
                priority: 'low',
                autoHide: true
            }
        };
        
        // Callbacks
        this.callbacks = {
            onNotification: [],
            onBadgeUpdate: [],
            onPermissionChange: []
        };
    }

    // Initialiser le système de notifications
    async initialize() {
        console.log('🔔 Initialisation du système de notifications...');
        
        try {
            // Vérifier le support des notifications
            if (!('Notification' in window)) {
                console.warn('⚠️ Notifications non supportées par ce navigateur');
                return false;
            }
            
            // Demander la permission
            await this.requestPermission();
            
            // Initialiser les sons
            this.initializeSounds();
            
            // Créer l'interface de notifications
            this.createNotificationUI();
            
            // Initialiser les badges
            this.initializeBadges();
            
            this.isEnabled = true;
            console.log('✅ Système de notifications initialisé');
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation notifications:', error);
            return false;
        }
    }

    // Demander la permission pour les notifications
    async requestPermission() {
        if (this.permission === 'granted') {
            return true;
        }
        
        try {
            this.permission = await Notification.requestPermission();
            
            if (this.permission === 'granted') {
                console.log('✅ Permission notifications accordée');
                this.triggerCallback('onPermissionChange', this.permission);
                return true;
            } else {
                console.warn('⚠️ Permission notifications refusée');
                return false;
            }
            
        } catch (error) {
            console.error('❌ Erreur demande permission:', error);
            return false;
        }
    }

    // Initialiser les sons
    initializeSounds() {
        const soundsConfig = {
            message: { file: 'assets/sounds/message.mp3', volume: 0.7 },
            notification: { file: 'assets/sounds/notification.mp3', volume: 0.5 },
            system: { file: 'assets/sounds/system.mp3', volume: 0.3 },
            error: { file: 'assets/sounds/error.mp3', volume: 0.8 },
            success: { file: 'assets/sounds/success.mp3', volume: 0.5 }
        };
        
        Object.entries(soundsConfig).forEach(([name, config]) => {
            try {
                const audio = new Audio();
                audio.volume = config.volume;
                audio.preload = 'auto';
                
                // Utiliser un son par défaut si le fichier n'existe pas
                audio.onerror = () => {
                    console.warn(`⚠️ Son ${name} non trouvé, utilisation du son par défaut`);
                };
                
                this.sounds.set(name, audio);
            } catch (error) {
                console.warn(`⚠️ Erreur chargement son ${name}:`, error);
            }
        });
    }

    // Créer l'interface de notifications
    createNotificationUI() {
        // Conteneur principal des notifications
        if (!document.getElementById('notification-container')) {
            const container = document.createElement('div');
            container.id = 'notification-container';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
        
        // Centre de notifications
        if (!document.getElementById('notification-center')) {
            const center = document.createElement('div');
            center.id = 'notification-center';
            center.style.cssText = `
                position: fixed;
                top: 60px;
                right: 20px;
                width: 350px;
                max-height: 500px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                z-index: 9999;
                display: none;
                overflow: hidden;
            `;
            center.innerHTML = `
                <div style="padding: 15px; border-bottom: 1px solid #eee; background: #f8f9fa;">
                    <h6 style="margin: 0; font-weight: bold;">🔔 Notifications</h6>
                    <button onclick="notificationSystem.clearAllNotifications()" 
                            style="float: right; margin-top: -20px; background: none; border: none; color: #6c757d; cursor: pointer;">
                        Tout effacer
                    </button>
                </div>
                <div id="notification-list" style="max-height: 400px; overflow-y: auto;"></div>
            `;
            document.body.appendChild(center);
        }
    }

    // Initialiser les badges
    initializeBadges() {
        const badgeTypes = ['messages', 'commands', 'pv', 'notifications'];
        
        badgeTypes.forEach(type => {
            this.badges.set(type, 0);
            this.createBadgeElement(type);
        });
    }

    // Créer un élément badge
    createBadgeElement(type) {
        const badgeId = `badge-${type}`;
        
        if (!document.getElementById(badgeId)) {
            const badge = document.createElement('span');
            badge.id = badgeId;
            badge.className = 'notification-badge';
            badge.style.cssText = `
                position: absolute;
                top: -8px;
                right: -8px;
                background: #dc3545;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                font-size: 11px;
                font-weight: bold;
                display: none;
                align-items: center;
                justify-content: center;
                z-index: 1000;
            `;
            
            // Trouver l'élément parent approprié
            const parentSelector = this.getBadgeParentSelector(type);
            const parent = document.querySelector(parentSelector);
            
            if (parent) {
                parent.style.position = 'relative';
                parent.appendChild(badge);
            }
        }
    }

    // Obtenir le sélecteur parent pour un badge
    getBadgeParentSelector(type) {
        const selectors = {
            messages: '.nav-link[onclick*="showSection(\'messagerie\')"]',
            commands: '.nav-link[onclick*="showSection(\'commandes\')"]',
            pv: '.nav-link[onclick*="showSection(\'pv\')"]',
            notifications: '#notification-bell'
        };
        
        return selectors[type] || `#${type}-tab`;
    }

    // Afficher une notification
    async showNotification(options) {
        if (!this.isEnabled) {
            console.warn('⚠️ Système de notifications non initialisé');
            return;
        }
        
        const notification = this.createNotificationObject(options);
        
        // Ajouter à la liste
        this.notifications.unshift(notification);
        
        // Limiter le nombre de notifications
        if (this.notifications.length > this.maxNotifications) {
            this.notifications = this.notifications.slice(0, this.maxNotifications);
        }
        
        // Afficher la notification navigateur
        if (this.permission === 'granted' && !document.hasFocus()) {
            this.showBrowserNotification(notification);
        }
        
        // Afficher la notification dans l'interface
        this.showUINotification(notification);
        
        // Jouer le son
        this.playNotificationSound(notification.type);
        
        // Mettre à jour les badges
        this.updateBadge('notifications', 1);
        
        // Déclencher les callbacks
        this.triggerCallback('onNotification', notification);
        
        console.log(`🔔 Notification affichée: ${notification.title}`);
        
        return notification;
    }

    // Créer un objet notification
    createNotificationObject(options) {
        const type = options.type || 'system';
        const config = this.notificationTypes[type] || this.notificationTypes.system;
        
        return {
            id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            title: options.title || 'Notification',
            body: options.body || '',
            type: type,
            icon: options.icon || config.icon,
            color: options.color || config.color,
            priority: options.priority || config.priority,
            autoHide: options.autoHide !== undefined ? options.autoHide : config.autoHide,
            data: options.data || {},
            timestamp: new Date().toISOString(),
            read: false,
            clicked: false
        };
    }

    // Afficher notification navigateur
    showBrowserNotification(notification) {
        try {
            const browserNotif = new Notification(notification.title, {
                body: notification.body,
                icon: this.getNotificationIcon(notification.type),
                tag: notification.id,
                requireInteraction: !notification.autoHide
            });
            
            browserNotif.onclick = () => {
                window.focus();
                this.handleNotificationClick(notification);
                browserNotif.close();
            };
            
            // Auto-fermeture
            if (notification.autoHide) {
                setTimeout(() => {
                    browserNotif.close();
                }, 5000);
            }
            
        } catch (error) {
            console.error('❌ Erreur notification navigateur:', error);
        }
    }

    // Afficher notification dans l'interface
    showUINotification(notification) {
        const container = document.getElementById('notification-container');
        if (!container) return;
        
        const notifElement = document.createElement('div');
        notifElement.className = 'ui-notification';
        notifElement.style.cssText = `
            background: white;
            border-left: 4px solid ${notification.color};
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 10px;
            padding: 15px;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: slideInRight 0.3s ease;
        `;
        
        notifElement.innerHTML = `
            <div style="display: flex; align-items: flex-start;">
                <div style="font-size: 20px; margin-right: 10px;">${notification.icon}</div>
                <div style="flex: 1;">
                    <div style="font-weight: bold; margin-bottom: 5px;">${notification.title}</div>
                    <div style="color: #666; font-size: 14px;">${notification.body}</div>
                    <div style="color: #999; font-size: 12px; margin-top: 5px;">
                        ${new Date(notification.timestamp).toLocaleTimeString()}
                    </div>
                </div>
                <button onclick="notificationSystem.closeUINotification('${notification.id}')" 
                        style="background: none; border: none; color: #999; cursor: pointer; font-size: 18px;">×</button>
            </div>
        `;
        
        notifElement.onclick = () => {
            this.handleNotificationClick(notification);
        };
        
        container.appendChild(notifElement);
        
        // Ajouter au centre de notifications
        this.addToNotificationCenter(notification);
        
        // Auto-suppression
        if (notification.autoHide) {
            setTimeout(() => {
                this.closeUINotification(notification.id);
            }, 5000);
        }
    }

    // Ajouter au centre de notifications
    addToNotificationCenter(notification) {
        const list = document.getElementById('notification-list');
        if (!list) return;
        
        const item = document.createElement('div');
        item.id = `notif-center-${notification.id}`;
        item.style.cssText = `
            padding: 12px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.2s;
        `;
        
        item.innerHTML = `
            <div style="display: flex; align-items: center;">
                <span style="font-size: 16px; margin-right: 8px;">${notification.icon}</span>
                <div style="flex: 1;">
                    <div style="font-weight: ${notification.read ? 'normal' : 'bold'}; font-size: 14px;">
                        ${notification.title}
                    </div>
                    <div style="color: #666; font-size: 12px; margin-top: 2px;">
                        ${notification.body.substring(0, 50)}${notification.body.length > 50 ? '...' : ''}
                    </div>
                    <div style="color: #999; font-size: 11px; margin-top: 2px;">
                        ${this.formatTimestamp(notification.timestamp)}
                    </div>
                </div>
                ${!notification.read ? '<div style="width: 8px; height: 8px; background: #007bff; border-radius: 50%;"></div>' : ''}
            </div>
        `;
        
        item.onclick = () => {
            this.handleNotificationClick(notification);
        };
        
        item.onmouseenter = () => {
            item.style.background = '#f8f9fa';
        };
        
        item.onmouseleave = () => {
            item.style.background = 'white';
        };
        
        list.insertBefore(item, list.firstChild);
    }

    // Fermer notification UI
    closeUINotification(notificationId) {
        const element = document.querySelector(`[onclick*="${notificationId}"]`)?.closest('.ui-notification');
        if (element) {
            element.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                element.remove();
            }, 300);
        }
    }

    // Gérer le clic sur notification
    handleNotificationClick(notification) {
        // Marquer comme lue et cliquée
        notification.read = true;
        notification.clicked = true;
        
        // Actions spécifiques selon le type
        switch (notification.type) {
            case 'message':
                if (typeof window.showSection === 'function') {
                    window.showSection('messagerie');
                }
                break;
            case 'command':
                if (typeof window.showSection === 'function') {
                    window.showSection('commandes');
                }
                break;
            case 'pv':
                if (typeof window.showSection === 'function') {
                    window.showSection('pv');
                }
                break;
        }
        
        // Mettre à jour l'affichage
        this.updateNotificationDisplay(notification);
    }

    // Mettre à jour l'affichage d'une notification
    updateNotificationDisplay(notification) {
        const centerItem = document.getElementById(`notif-center-${notification.id}`);
        if (centerItem) {
            const titleElement = centerItem.querySelector('div[style*="font-weight"]');
            if (titleElement) {
                titleElement.style.fontWeight = 'normal';
            }
            
            const indicator = centerItem.querySelector('div[style*="border-radius: 50%"]');
            if (indicator) {
                indicator.remove();
            }
        }
    }

    // Jouer son de notification
    playNotificationSound(type) {
        try {
            const sound = this.sounds.get(type) || this.sounds.get('notification');
            if (sound) {
                sound.currentTime = 0;
                sound.play().catch(error => {
                    console.warn('⚠️ Impossible de jouer le son:', error);
                });
            }
        } catch (error) {
            console.warn('⚠️ Erreur lecture son:', error);
        }
    }

    // Mettre à jour un badge
    updateBadge(type, increment = 1) {
        const currentCount = this.badges.get(type) || 0;
        const newCount = Math.max(0, currentCount + increment);
        
        this.badges.set(type, newCount);
        
        const badgeElement = document.getElementById(`badge-${type}`);
        if (badgeElement) {
            if (newCount > 0) {
                badgeElement.textContent = newCount > 99 ? '99+' : newCount.toString();
                badgeElement.style.display = 'flex';
            } else {
                badgeElement.style.display = 'none';
            }
        }
        
        // Déclencher callback
        this.triggerCallback('onBadgeUpdate', { type, count: newCount });
        
        console.log(`🔢 Badge ${type}: ${newCount}`);
    }

    // Réinitialiser un badge
    resetBadge(type) {
        this.updateBadge(type, -(this.badges.get(type) || 0));
    }

    // Obtenir l'icône de notification
    getNotificationIcon(type) {
        // Retourner une icône par défaut ou un chemin vers une image
        return '/favicon.ico';
    }

    // Formater timestamp
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // Moins d'1 minute
            return 'À l\'instant';
        } else if (diff < 3600000) { // Moins d'1 heure
            return `${Math.floor(diff / 60000)}m`;
        } else if (diff < 86400000) { // Moins d'1 jour
            return `${Math.floor(diff / 3600000)}h`;
        } else {
            return date.toLocaleDateString();
        }
    }

    // Effacer toutes les notifications
    clearAllNotifications() {
        this.notifications = [];
        
        const list = document.getElementById('notification-list');
        if (list) {
            list.innerHTML = '';
        }
        
        this.resetBadge('notifications');
    }

    // Basculer le centre de notifications
    toggleNotificationCenter() {
        const center = document.getElementById('notification-center');
        if (center) {
            center.style.display = center.style.display === 'none' ? 'block' : 'none';
            
            if (center.style.display === 'block') {
                // Marquer toutes les notifications comme lues
                this.notifications.forEach(notif => {
                    if (!notif.read) {
                        notif.read = true;
                        this.updateNotificationDisplay(notif);
                    }
                });
                
                this.resetBadge('notifications');
            }
        }
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Déclencher les callbacks
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les statistiques
    getStats() {
        return {
            total: this.notifications.length,
            unread: this.notifications.filter(n => !n.read).length,
            byType: this.getNotificationsByType(),
            badges: Object.fromEntries(this.badges)
        };
    }

    // Obtenir les notifications par type
    getNotificationsByType() {
        const byType = {};
        this.notifications.forEach(notif => {
            byType[notif.type] = (byType[notif.type] || 0) + 1;
        });
        return byType;
    }
}

// Ajouter les styles CSS
const notificationStyles = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .notification-badge {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
`;

// Ajouter les styles au document
if (typeof document !== 'undefined') {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = notificationStyles;
    document.head.appendChild(styleSheet);
}

// Export de la classe
if (typeof window !== 'undefined') {
    window.NotificationSystem = NotificationSystem;
}
