-- =====================================================
-- FONCTIONS DE GESTION - SYSTÈME DONS IPT
-- =====================================================

-- =====================================================
-- GÉNÉRATION AUTOMATIQUE DE NUMÉROS
-- =====================================================

-- Fonction pour générer le numéro de don
CREATE OR REPLACE FUNCTION generate_donation_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    next_number INTEGER;
    formatted_number TEXT;
BEGIN
    current_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    -- Trouver le prochain numéro pour l'année courante
    SELECT COALESCE(MAX(
        CAST(SUBSTRING(numero_don FROM 'DON-' || current_year || '-(.*)') AS INTEGER)
    ), 0) + 1
    INTO next_number
    FROM gestion_donations_ipt
    WHERE numero_don LIKE 'DON-' || current_year || '-%';
    
    -- Formater avec des zéros à gauche
    formatted_number := 'DON-' || current_year || '-' || LPAD(next_number::TEXT, 4, '0');
    
    RETURN formatted_number;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour générer le numéro d'inventaire
CREATE OR REPLACE FUNCTION generate_inventory_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    next_number INTEGER;
    formatted_number TEXT;
BEGIN
    current_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    -- Trouver le prochain numéro pour l'année courante
    SELECT COALESCE(MAX(
        CAST(SUBSTRING(numero_inventaire FROM 'INV-' || current_year || '-(.*)') AS INTEGER)
    ), 0) + 1
    INTO next_number
    FROM gestion_donation_inventaire
    WHERE numero_inventaire LIKE 'INV-' || current_year || '-%';
    
    -- Formater avec des zéros à gauche
    formatted_number := 'INV-' || current_year || '-' || LPAD(next_number::TEXT, 4, '0');
    
    RETURN formatted_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INITIALISATION DU WORKFLOW
-- =====================================================

CREATE OR REPLACE FUNCTION initialize_donation_workflow(
    p_donation_id UUID,
    p_type_don TEXT,
    p_created_by TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    workflow_steps RECORD;
BEGIN
    -- Définir les étapes selon le type de don
    IF p_type_don = 'equipement' THEN
        -- Workflow complet avec avis technique
        INSERT INTO gestion_donation_workflow (donation_id, numero_etape, nom_etape, description_etape, role_responsable)
        VALUES 
            (p_donation_id, 1, 'Validation Approvisionnement', 'Validation par le responsable approvisionnement', 'responsable_approvisionnement'),
            (p_donation_id, 2, 'Avis Technique', 'Avis technique de la direction technique', 'direction_technique'),
            (p_donation_id, 3, 'Validation DG', 'Validation par le directeur général', 'directeur_general'),
            (p_donation_id, 4, 'Validation SD Achats', 'Validation par la sous-direction achats', 'sous_direction_achats'),
            (p_donation_id, 5, 'Soumission MS', 'Soumission au Ministère de la Santé', 'sous_direction_achats'),
            (p_donation_id, 6, 'Décision MS', 'Décision du Ministère de la Santé', 'sous_direction_achats'),
            (p_donation_id, 7, 'Réception', 'Réception physique du don', 'receptionniste'),
            (p_donation_id, 8, 'Inventaire', 'Attribution numéro inventaire', 'rve'),
            (p_donation_id, 9, 'Affectation', 'Affectation au service/laboratoire', 'rve'),
            (p_donation_id, 10, 'Archivage', 'Archivage du dossier', 'admin_systeme');
    ELSE
        -- Workflow simplifié pour les articles (sans avis technique)
        INSERT INTO gestion_donation_workflow (donation_id, numero_etape, nom_etape, description_etape, role_responsable)
        VALUES 
            (p_donation_id, 1, 'Validation Approvisionnement', 'Validation par le responsable approvisionnement', 'responsable_approvisionnement'),
            (p_donation_id, 2, 'Validation DG', 'Validation par le directeur général', 'directeur_general'),
            (p_donation_id, 3, 'Validation SD Achats', 'Validation par la sous-direction achats', 'sous_direction_achats'),
            (p_donation_id, 4, 'Soumission MS', 'Soumission au Ministère de la Santé', 'sous_direction_achats'),
            (p_donation_id, 5, 'Décision MS', 'Décision du Ministère de la Santé', 'sous_direction_achats'),
            (p_donation_id, 6, 'Réception', 'Réception physique du don', 'receptionniste'),
            (p_donation_id, 7, 'Inventaire', 'Attribution numéro inventaire', 'rve'),
            (p_donation_id, 8, 'Affectation', 'Affectation au service/laboratoire', 'rve'),
            (p_donation_id, 9, 'Archivage', 'Archivage du dossier', 'admin_systeme');
    END IF;
    
    -- Marquer la première étape comme en cours
    UPDATE gestion_donation_workflow 
    SET statut_etape = 'en_cours', date_debut = NOW()
    WHERE donation_id = p_donation_id AND numero_etape = 1;
    
    -- Enregistrer dans l'historique
    INSERT INTO gestion_donation_historique (
        donation_id, type_action, description_action, 
        utilisateur_email, date_action
    ) VALUES (
        p_donation_id, 'creation', 'Initialisation du workflow de validation',
        p_created_by, NOW()
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTIONS DE VALIDATION
-- =====================================================

-- Validation par le responsable approvisionnement
CREATE OR REPLACE FUNCTION validate_donation_approvisionnement(
    p_donation_id UUID,
    p_validator_email TEXT,
    p_commentaires TEXT DEFAULT NULL,
    p_approved BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
DECLARE
    current_statut TEXT;
BEGIN
    -- Vérifier le statut actuel
    SELECT statut INTO current_statut 
    FROM gestion_donations_ipt 
    WHERE id = p_donation_id;
    
    IF current_statut != 'soumis' THEN
        RAISE EXCEPTION 'Don non dans le bon statut pour validation approvisionnement';
    END IF;
    
    IF p_approved THEN
        -- Approuver
        UPDATE gestion_donations_ipt 
        SET 
            statut = 'valide_appro',
            etape_actuelle = etape_actuelle + 1,
            valide_appro_par = p_validator_email,
            valide_appro_date = NOW(),
            valide_appro_commentaires = p_commentaires,
            updated_by = p_validator_email
        WHERE id = p_donation_id;
        
        -- Mettre à jour le workflow
        UPDATE gestion_donation_workflow 
        SET 
            statut_etape = 'termine',
            date_fin = NOW(),
            duree_traitement = NOW() - date_debut,
            commentaires = p_commentaires
        WHERE donation_id = p_donation_id AND numero_etape = 1;
        
        -- Activer l'étape suivante
        UPDATE gestion_donation_workflow 
        SET statut_etape = 'en_cours', date_debut = NOW()
        WHERE donation_id = p_donation_id AND numero_etape = 2;
        
        -- Historique
        INSERT INTO gestion_donation_historique (
            donation_id, type_action, description_action, 
            utilisateur_email, commentaires, date_action
        ) VALUES (
            p_donation_id, 'validation', 'Validation par responsable approvisionnement',
            p_validator_email, p_commentaires, NOW()
        );
    ELSE
        -- Refuser
        UPDATE gestion_donations_ipt 
        SET 
            statut = 'refuse',
            valide_appro_par = p_validator_email,
            valide_appro_date = NOW(),
            valide_appro_commentaires = p_commentaires,
            updated_by = p_validator_email
        WHERE id = p_donation_id;
        
        -- Mettre à jour le workflow
        UPDATE gestion_donation_workflow 
        SET 
            statut_etape = 'refuse',
            date_fin = NOW(),
            commentaires = p_commentaires
        WHERE donation_id = p_donation_id AND numero_etape = 1;
        
        -- Historique
        INSERT INTO gestion_donation_historique (
            donation_id, type_action, description_action, 
            utilisateur_email, commentaires, date_action
        ) VALUES (
            p_donation_id, 'refus', 'Refus par responsable approvisionnement',
            p_validator_email, p_commentaires, NOW()
        );
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Avis technique (pour équipements uniquement)
CREATE OR REPLACE FUNCTION provide_technical_advice(
    p_donation_id UUID,
    p_advisor_email TEXT,
    p_commentaires TEXT DEFAULT NULL,
    p_favorable BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
DECLARE
    current_statut TEXT;
    don_type TEXT;
BEGIN
    -- Vérifier le type et le statut
    SELECT statut, type_don INTO current_statut, don_type
    FROM gestion_donations_ipt 
    WHERE id = p_donation_id;
    
    IF don_type != 'equipement' THEN
        RAISE EXCEPTION 'Avis technique requis uniquement pour les équipements';
    END IF;
    
    IF current_statut != 'valide_appro' THEN
        RAISE EXCEPTION 'Don non dans le bon statut pour avis technique';
    END IF;
    
    -- Mettre à jour le don
    UPDATE gestion_donations_ipt 
    SET 
        statut = CASE WHEN p_favorable THEN 'avis_technique' ELSE 'refuse' END,
        etape_actuelle = CASE WHEN p_favorable THEN etape_actuelle + 1 ELSE etape_actuelle END,
        avis_technique_par = p_advisor_email,
        avis_technique_date = NOW(),
        avis_technique_commentaires = p_commentaires,
        avis_technique_favorable = p_favorable,
        updated_by = p_advisor_email
    WHERE id = p_donation_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_donation_workflow 
    SET 
        statut_etape = CASE WHEN p_favorable THEN 'termine' ELSE 'refuse' END,
        date_fin = NOW(),
        duree_traitement = NOW() - date_debut,
        commentaires = p_commentaires
    WHERE donation_id = p_donation_id AND numero_etape = 2;
    
    IF p_favorable THEN
        -- Activer l'étape suivante
        UPDATE gestion_donation_workflow 
        SET statut_etape = 'en_cours', date_debut = NOW()
        WHERE donation_id = p_donation_id AND numero_etape = 3;
    END IF;
    
    -- Historique
    INSERT INTO gestion_donation_historique (
        donation_id, type_action, description_action, 
        utilisateur_email, commentaires, date_action
    ) VALUES (
        p_donation_id, 
        CASE WHEN p_favorable THEN 'validation' ELSE 'refus' END,
        'Avis technique de la direction technique',
        p_advisor_email, p_commentaires, NOW()
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- VALIDATION DG ET SD ACHATS
-- =====================================================

-- Validation par le Directeur Général
CREATE OR REPLACE FUNCTION validate_donation_dg(
    p_donation_id UUID,
    p_validator_email TEXT,
    p_commentaires TEXT DEFAULT NULL,
    p_approved BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
DECLARE
    current_statut TEXT;
    current_etape INTEGER;
BEGIN
    -- Vérifier le statut actuel
    SELECT statut, etape_actuelle INTO current_statut, current_etape
    FROM gestion_donations_ipt
    WHERE id = p_donation_id;

    IF current_statut NOT IN ('valide_appro', 'avis_technique') THEN
        RAISE EXCEPTION 'Don non dans le bon statut pour validation DG';
    END IF;

    IF p_approved THEN
        -- Approuver
        UPDATE gestion_donations_ipt
        SET
            statut = 'valide_dg',
            etape_actuelle = etape_actuelle + 1,
            valide_dg_par = p_validator_email,
            valide_dg_date = NOW(),
            valide_dg_commentaires = p_commentaires,
            updated_by = p_validator_email
        WHERE id = p_donation_id;

        -- Mettre à jour le workflow
        UPDATE gestion_donation_workflow
        SET
            statut_etape = 'termine',
            date_fin = NOW(),
            duree_traitement = NOW() - date_debut,
            commentaires = p_commentaires
        WHERE donation_id = p_donation_id AND numero_etape = current_etape;

        -- Activer l'étape suivante
        UPDATE gestion_donation_workflow
        SET statut_etape = 'en_cours', date_debut = NOW()
        WHERE donation_id = p_donation_id AND numero_etape = current_etape + 1;

        -- Historique
        INSERT INTO gestion_donation_historique (
            donation_id, type_action, description_action,
            utilisateur_email, commentaires, date_action
        ) VALUES (
            p_donation_id, 'validation', 'Validation par le Directeur Général',
            p_validator_email, p_commentaires, NOW()
        );
    ELSE
        -- Refuser
        UPDATE gestion_donations_ipt
        SET
            statut = 'refuse',
            valide_dg_par = p_validator_email,
            valide_dg_date = NOW(),
            valide_dg_commentaires = p_commentaires,
            updated_by = p_validator_email
        WHERE id = p_donation_id;

        -- Historique
        INSERT INTO gestion_donation_historique (
            donation_id, type_action, description_action,
            utilisateur_email, commentaires, date_action
        ) VALUES (
            p_donation_id, 'refus', 'Refus par le Directeur Général',
            p_validator_email, p_commentaires, NOW()
        );
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Validation par la Sous-Direction Achats
CREATE OR REPLACE FUNCTION validate_donation_sd_achats(
    p_donation_id UUID,
    p_validator_email TEXT,
    p_commentaires TEXT DEFAULT NULL,
    p_approved BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
DECLARE
    current_statut TEXT;
    current_etape INTEGER;
BEGIN
    -- Vérifier le statut actuel
    SELECT statut, etape_actuelle INTO current_statut, current_etape
    FROM gestion_donations_ipt
    WHERE id = p_donation_id;

    IF current_statut != 'valide_dg' THEN
        RAISE EXCEPTION 'Don non validé par le DG';
    END IF;

    IF p_approved THEN
        -- Approuver
        UPDATE gestion_donations_ipt
        SET
            statut = 'valide_sd_achats',
            etape_actuelle = etape_actuelle + 1,
            valide_sd_achats_par = p_validator_email,
            valide_sd_achats_date = NOW(),
            valide_sd_achats_commentaires = p_commentaires,
            updated_by = p_validator_email
        WHERE id = p_donation_id;

        -- Mettre à jour le workflow
        UPDATE gestion_donation_workflow
        SET
            statut_etape = 'termine',
            date_fin = NOW(),
            duree_traitement = NOW() - date_debut,
            commentaires = p_commentaires
        WHERE donation_id = p_donation_id AND numero_etape = current_etape;

        -- Activer l'étape suivante (Soumission MS)
        UPDATE gestion_donation_workflow
        SET statut_etape = 'en_attente', date_debut = NOW()
        WHERE donation_id = p_donation_id AND numero_etape = current_etape + 1;

        -- Historique
        INSERT INTO gestion_donation_historique (
            donation_id, type_action, description_action,
            utilisateur_email, commentaires, date_action
        ) VALUES (
            p_donation_id, 'validation', 'Validation par la Sous-Direction Achats',
            p_validator_email, p_commentaires, NOW()
        );
    ELSE
        -- Refuser
        UPDATE gestion_donations_ipt
        SET
            statut = 'refuse',
            valide_sd_achats_par = p_validator_email,
            valide_sd_achats_date = NOW(),
            valide_sd_achats_commentaires = p_commentaires,
            updated_by = p_validator_email
        WHERE id = p_donation_id;

        -- Historique
        INSERT INTO gestion_donation_historique (
            donation_id, type_action, description_action,
            utilisateur_email, commentaires, date_action
        ) VALUES (
            p_donation_id, 'refus', 'Refus par la Sous-Direction Achats',
            p_validator_email, p_commentaires, NOW()
        );
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
