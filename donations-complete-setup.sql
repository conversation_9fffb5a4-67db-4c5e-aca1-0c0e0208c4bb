-- Installation complète du système de gestion des dons IPT
-- À exécuter dans l'éditeur SQL de Supabase

-- =====================================================
-- ÉTAPE 1: CRÉATION DES TABLES
-- =====================================================

-- Table principale des dons
CREATE TABLE IF NOT EXISTS gestion_donations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    numero_don TEXT UNIQUE NOT NULL,
    type_don TEXT NOT NULL CHECK (type_don IN ('article', 'equipement')),
    
    -- Informations du donneur
    donneur_nom TEXT NOT NULL,
    donneur_contact TEXT,
    donneur_adresse TEXT,
    
    -- Informations du bénéficiaire/demandeur
    demandeur_nom TEXT NOT NULL,
    demandeur_service TEXT NOT NULL,
    demandeur_email TEXT NOT NULL,
    lieu_affectation TEXT NOT NULL,
    raison_don TEXT NOT NULL,
    
    -- Statut et workflow
    statut TEXT NOT NULL DEFAULT 'brouillon' CHECK (statut IN (
        'brouillon', 'soumis_dg', 'avis_dt', 'soumis_ms', 'approuve_ms', 
        'refuse_ms', 'refuse_dg', 'refuse_dt', 'en_expedition', 'recu', 'affecte', 'archive'
    )),
    
    -- Dates importantes
    date_demande DATE NOT NULL DEFAULT CURRENT_DATE,
    date_soumission_dg TIMESTAMP WITH TIME ZONE,
    date_avis_dt TIMESTAMP WITH TIME ZONE,
    date_soumission_ms TIMESTAMP WITH TIME ZONE,
    date_decision_ms TIMESTAMP WITH TIME ZONE,
    date_reception TIMESTAMP WITH TIME ZONE,
    date_affectation TIMESTAMP WITH TIME ZONE,
    
    -- Approbations et avis
    avis_dg TEXT,
    avis_dt TEXT,
    decision_ms TEXT,
    approuve_par_dg TEXT,
    avis_technique_par TEXT,
    decision_ms_par TEXT,
    
    -- Informations complémentaires
    observations TEXT,
    valeur_estimee DECIMAL(12,2),
    devise TEXT DEFAULT 'TND',
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    updated_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des articles/équipements du don
CREATE TABLE IF NOT EXISTS gestion_donation_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    donation_id UUID NOT NULL REFERENCES gestion_donations(id) ON DELETE CASCADE,
    
    -- Informations de l'article/équipement
    nom_item TEXT NOT NULL,
    description TEXT,
    quantite INTEGER NOT NULL DEFAULT 1,
    unite TEXT DEFAULT 'pièce',
    
    -- Spécifique aux équipements
    marque TEXT,
    modele TEXT,
    numero_serie TEXT,
    numero_inventaire TEXT,
    
    -- Informations techniques
    specifications JSONB DEFAULT '{}'::jsonb,
    etat_reception TEXT CHECK (etat_reception IN ('neuf', 'bon', 'moyen', 'defaillant')),
    
    -- Affectation
    service_affecte TEXT,
    responsable_affecte TEXT,
    date_affectation TIMESTAMP WITH TIME ZONE,
    
    -- Métadonnées
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des documents associés
CREATE TABLE IF NOT EXISTS gestion_donation_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    donation_id UUID NOT NULL REFERENCES gestion_donations(id) ON DELETE CASCADE,
    
    -- Type de document
    type_document TEXT NOT NULL CHECK (type_document IN (
        'demande_explicative', 'lettre_don', 'bon_prelevement', 'facture', 
        'bon_livraison', 'bon_sortie', 'autorisation_ms', 'avis_technique'
    )),
    
    -- Informations du document
    nom_document TEXT NOT NULL,
    numero_document TEXT,
    date_document DATE,
    
    -- Stockage du fichier
    file_path TEXT,
    file_name TEXT,
    file_size INTEGER,
    file_type TEXT,
    
    -- Métadonnées
    uploaded_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table du workflow et historique
CREATE TABLE IF NOT EXISTS gestion_donation_workflow (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    donation_id UUID NOT NULL REFERENCES gestion_donations(id) ON DELETE CASCADE,
    
    -- Étape du workflow
    etape TEXT NOT NULL,
    statut_precedent TEXT,
    statut_nouveau TEXT NOT NULL,
    
    -- Acteur et action
    acteur_nom TEXT NOT NULL,
    acteur_role TEXT NOT NULL,
    action TEXT NOT NULL,
    commentaire TEXT,
    
    -- Métadonnées
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des approbations
CREATE TABLE IF NOT EXISTS gestion_donation_approvals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    donation_id UUID NOT NULL REFERENCES gestion_donations(id) ON DELETE CASCADE,
    
    -- Type d'approbation
    type_approbation TEXT NOT NULL CHECK (type_approbation IN (
        'validation_dg', 'avis_technique_dt', 'autorisation_ms'
    )),
    
    -- Statut de l'approbation
    statut TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut IN (
        'en_attente', 'approuve', 'refuse', 'en_revision'
    )),
    
    -- Détails de l'approbation
    approuve_par TEXT,
    date_approbation TIMESTAMP WITH TIME ZONE,
    commentaire TEXT,
    conditions TEXT,
    
    -- Métadonnées
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- ÉTAPE 2: TRIGGERS POUR UPDATED_AT
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Appliquer les triggers
CREATE TRIGGER update_gestion_donations_updated_at 
    BEFORE UPDATE ON gestion_donations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gestion_donation_items_updated_at 
    BEFORE UPDATE ON gestion_donation_items 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gestion_donation_documents_updated_at 
    BEFORE UPDATE ON gestion_donation_documents 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gestion_donation_approvals_updated_at 
    BEFORE UPDATE ON gestion_donation_approvals 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ÉTAPE 3: INDEX POUR PERFORMANCE
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_donations_statut ON gestion_donations(statut);
CREATE INDEX IF NOT EXISTS idx_donations_type ON gestion_donations(type_don);
CREATE INDEX IF NOT EXISTS idx_donations_demandeur ON gestion_donations(demandeur_email);
CREATE INDEX IF NOT EXISTS idx_donations_date ON gestion_donations(date_demande);
CREATE INDEX IF NOT EXISTS idx_donation_items_donation ON gestion_donation_items(donation_id);
CREATE INDEX IF NOT EXISTS idx_donation_documents_donation ON gestion_donation_documents(donation_id);
CREATE INDEX IF NOT EXISTS idx_donation_workflow_donation ON gestion_donation_workflow(donation_id);
CREATE INDEX IF NOT EXISTS idx_donation_approvals_donation ON gestion_donation_approvals(donation_id);

-- =====================================================
-- ÉTAPE 4: VUES UTILES
-- =====================================================

-- Vue des dons avec détails complets
CREATE OR REPLACE VIEW donations_complete AS
SELECT 
    d.*,
    COUNT(di.id) as nombre_items,
    COUNT(dd.id) as nombre_documents,
    STRING_AGG(DISTINCT di.nom_item, ', ') as items_list,
    CASE 
        WHEN d.type_don = 'equipement' THEN 
            CASE 
                WHEN d.statut IN ('brouillon', 'soumis_dg') THEN 'En attente validation DG'
                WHEN d.statut = 'avis_dt' THEN 'En attente avis technique DT'
                WHEN d.statut = 'soumis_ms' THEN 'En attente autorisation MS'
                WHEN d.statut = 'approuve_ms' THEN 'Approuvé - En attente expédition'
                WHEN d.statut = 'refuse_ms' THEN 'Refusé par MS'
                WHEN d.statut = 'en_expedition' THEN 'En cours d''expédition'
                WHEN d.statut = 'recu' THEN 'Reçu - En attente affectation'
                WHEN d.statut = 'affecte' THEN 'Affecté'
                ELSE d.statut
            END
        ELSE 
            CASE 
                WHEN d.statut IN ('brouillon', 'soumis_dg') THEN 'En attente validation DG'
                WHEN d.statut = 'soumis_ms' THEN 'En attente autorisation MS'
                WHEN d.statut = 'approuve_ms' THEN 'Approuvé - En attente expédition'
                WHEN d.statut = 'refuse_ms' THEN 'Refusé par MS'
                WHEN d.statut = 'en_expedition' THEN 'En cours d''expédition'
                WHEN d.statut = 'recu' THEN 'Reçu'
                ELSE d.statut
            END
    END as statut_libelle
FROM gestion_donations d
LEFT JOIN gestion_donation_items di ON d.id = di.donation_id
LEFT JOIN gestion_donation_documents dd ON d.id = dd.donation_id
GROUP BY d.id;

-- Vue des statistiques des dons
CREATE OR REPLACE VIEW donations_stats AS
SELECT 
    COUNT(*) as total_dons,
    COUNT(CASE WHEN type_don = 'article' THEN 1 END) as total_articles,
    COUNT(CASE WHEN type_don = 'equipement' THEN 1 END) as total_equipements,
    COUNT(CASE WHEN statut IN ('brouillon', 'soumis_dg', 'avis_dt', 'soumis_ms') THEN 1 END) as en_attente,
    COUNT(CASE WHEN statut = 'approuve_ms' THEN 1 END) as approuves,
    COUNT(CASE WHEN statut LIKE 'refuse%' THEN 1 END) as refuses,
    COUNT(CASE WHEN statut = 'affecte' THEN 1 END) as affectes,
    COUNT(CASE WHEN date_demande >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as ce_mois,
    SUM(valeur_estimee) as valeur_totale
FROM gestion_donations;

-- =====================================================
-- ÉTAPE 5: COMMENTAIRES
-- =====================================================

COMMENT ON TABLE gestion_donations IS 'Table principale des dons (articles et équipements)';
COMMENT ON TABLE gestion_donation_items IS 'Détails des articles/équipements dans chaque don';
COMMENT ON TABLE gestion_donation_documents IS 'Documents associés aux dons (BP, factures, BL, etc.)';
COMMENT ON TABLE gestion_donation_workflow IS 'Historique du workflow et des actions sur les dons';
COMMENT ON TABLE gestion_donation_approvals IS 'Approbations requises pour chaque don';

-- =====================================================
-- MESSAGE DE CONFIRMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Installation du système de gestion des dons terminée avec succès!';
    RAISE NOTICE '📋 Tables créées: gestion_donations, gestion_donation_items, gestion_donation_documents, gestion_donation_workflow, gestion_donation_approvals';
    RAISE NOTICE '🔧 Triggers, index et vues configurés';
    RAISE NOTICE '📊 Prêt pour l''installation des fonctions et des politiques RLS';
END $$;
