<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Système Commandes - IPT</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    <script src="assets/js/commands-dashboard-widget.js"></script>
    <script src="assets/js/commands-table-manager.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 25px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .test-btn.primary {
            background: #059669;
            color: white;
        }
        
        .test-btn.secondary {
            background: #6b7280;
            color: white;
        }
        
        .test-btn.success {
            background: #10b981;
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-warning { background: #f59e0b; }
        .status-info { background: #3b82f6; }
        
        .test-result {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .result-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .result-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .result-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #059669;
        }
        
        .metric-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            margin-top: 5px;
        }
        
        .log-container {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
        }
        
        .log-timestamp {
            color: #9ca3af;
        }
        
        .log-level-info { color: #60a5fa; }
        .log-level-success { color: #34d399; }
        .log-level-error { color: #f87171; }
        .log-level-warning { color: #fbbf24; }

        .widget-preview {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: #f9fafb;
            position: relative;
            min-height: 200px;
        }

        .widget-preview h4 {
            margin: 0 0 15px 0;
            color: #374151;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1>🧪 Test Système de Gestion des Commandes</h1>
            <p>Interface de test complète pour valider toutes les fonctionnalités du système de commandes IPT</p>
        </div>

        <!-- Statut de connexion -->
        <div class="test-section">
            <h3>🔗 Statut de Connexion</h3>
            <div id="connectionStatus">
                <span class="status-indicator status-warning"></span>
                <span>Vérification en cours...</span>
            </div>
            <div id="userInfo" style="margin-top: 10px; font-size: 14px; color: #6b7280;">
                Utilisateur non connecté
            </div>
        </div>

        <!-- Tests de base de données -->
        <div class="test-section">
            <h3>🗄️ Tests Base de Données</h3>
            <div class="test-controls">
                <button class="test-btn primary" onclick="testDatabaseTables()">
                    🔍 Vérifier Tables
                </button>
                <button class="test-btn secondary" onclick="testDatabaseViews()">
                    👁️ Vérifier Vues
                </button>
                <button class="test-btn secondary" onclick="testDatabaseFunctions()">
                    ⚙️ Vérifier Fonctions
                </button>
                <button class="test-btn secondary" onclick="testSampleData()">
                    📊 Données d'Exemple
                </button>
            </div>
            <div id="databaseTestResults"></div>
        </div>

        <!-- Tests du widget -->
        <div class="test-section">
            <h3>📦 Tests Widget Dashboard</h3>
            <div class="test-controls">
                <button class="test-btn primary" onclick="testWidgetInitialization()">
                    🚀 Initialiser Widget
                </button>
                <button class="test-btn secondary" onclick="testWidgetData()">
                    📊 Charger Données
                </button>
                <button class="test-btn secondary" onclick="testWidgetRealtime()">
                    🔄 Test Temps Réel
                </button>
                <button class="test-btn success" onclick="showWidget()">
                    👁️ Afficher Widget
                </button>
            </div>
            <div id="widgetTestResults"></div>
            
            <!-- Aperçu du widget -->
            <div class="widget-preview" id="widgetPreview">
                <h4>Aperçu du Widget (sera affiché après initialisation)</h4>
            </div>
        </div>

        <!-- Tests de la table -->
        <div class="test-section">
            <h3>📋 Tests Table Interactive</h3>
            <div class="test-controls">
                <button class="test-btn primary" onclick="testTableManager()">
                    🚀 Initialiser Table
                </button>
                <button class="test-btn secondary" onclick="testTableFilters()">
                    🔍 Test Filtres
                </button>
                <button class="test-btn secondary" onclick="testTableSorting()">
                    📈 Test Tri
                </button>
                <button class="test-btn secondary" onclick="testTablePagination()">
                    📄 Test Pagination
                </button>
                <button class="test-btn success" onclick="testTableExport()">
                    📤 Test Export
                </button>
            </div>
            <div id="tableTestResults"></div>
        </div>

        <!-- Métriques de performance -->
        <div class="test-section">
            <h3>📊 Métriques de Performance</h3>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="metricTotalCommands">0</div>
                    <div class="metric-label">Total Commandes</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metricLoadTime">0ms</div>
                    <div class="metric-label">Temps Chargement</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metricRenderTime">0ms</div>
                    <div class="metric-label">Temps Rendu</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metricMemoryUsage">0MB</div>
                    <div class="metric-label">Utilisation Mémoire</div>
                </div>
            </div>
        </div>

        <!-- Tests d'intégration -->
        <div class="test-section">
            <h3>🔗 Tests d'Intégration</h3>
            <div class="test-controls">
                <button class="test-btn primary" onclick="testFullIntegration()">
                    🎯 Test Complet
                </button>
                <button class="test-btn secondary" onclick="testWorkflowFunctions()">
                    🔄 Test Workflow
                </button>
                <button class="test-btn secondary" onclick="testPermissions()">
                    🔒 Test Permissions
                </button>
                <button class="test-btn success" onclick="openCommandsManagement()">
                    🚀 Interface Complète
                </button>
            </div>
            <div id="integrationTestResults"></div>
        </div>

        <!-- Résultats des tests -->
        <div class="test-section">
            <h3>✅ Résultats des Tests</h3>
            <div id="testResults">
                <p>Aucun test exécuté pour le moment.</p>
            </div>
        </div>

        <!-- Console de logs -->
        <div class="test-section">
            <h3>📝 Console de Logs</h3>
            <button class="test-btn secondary" onclick="clearLogs()" style="margin-bottom: 10px;">🗑️ Effacer Logs</button>
            <div class="log-container" id="logContainer">
                <div class="log-entry">
                    <span class="log-timestamp">[00:00:00]</span>
                    <span class="log-level-info">[INFO]</span>
                    Interface de test du système de commandes initialisée
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales pour les tests
        let testCommandsWidget = null;
        let testTableManager = null;
        let testStartTime = 0;
        let testMetrics = {
            totalCommands: 0,
            loadTime: 0,
            renderTime: 0,
            memoryUsage: 0
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', async function() {
            log('Initialisation de l\'interface de test du système de commandes', 'info');
            await checkConnection();
        });

        // Fonction de logging
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Vérifier la connexion
        async function checkConnection() {
            try {
                const supabase = window.supabaseClient || window.supabase;
                if (!supabase) {
                    throw new Error('Client Supabase non disponible');
                }

                const { data: { user } } = await supabase.auth.getUser();
                
                const statusEl = document.getElementById('connectionStatus');
                const userInfoEl = document.getElementById('userInfo');
                
                if (user) {
                    statusEl.innerHTML = `
                        <span class="status-indicator status-success"></span>
                        <span>Connecté à Supabase</span>
                    `;
                    userInfoEl.textContent = `Utilisateur: ${user.email} (${user.user_metadata?.role || 'user'})`;
                    log(`Utilisateur connecté: ${user.email}`, 'success');
                } else {
                    statusEl.innerHTML = `
                        <span class="status-indicator status-warning"></span>
                        <span>Supabase disponible mais utilisateur non connecté</span>
                    `;
                    userInfoEl.textContent = 'Veuillez vous connecter pour tester toutes les fonctionnalités';
                    log('Utilisateur non connecté', 'warning');
                }
                
            } catch (error) {
                const statusEl = document.getElementById('connectionStatus');
                statusEl.innerHTML = `
                    <span class="status-indicator status-error"></span>
                    <span>Erreur de connexion: ${error.message}</span>
                `;
                log(`Erreur de connexion: ${error.message}`, 'error');
            }
        }

        // Tests de base de données
        async function testDatabaseTables() {
            try {
                log('Test des tables de base de données...', 'info');
                const supabase = window.supabaseClient || window.supabase;
                
                const tables = ['gestion_commandes', 'gestion_commande_items', 'gestion_commande_documents', 'gestion_commande_workflow'];
                let results = [];
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase.from(table).select('count').limit(1);
                        if (error) throw error;
                        results.push(`✅ Table ${table}: OK`);
                    } catch (error) {
                        results.push(`❌ Table ${table}: ${error.message}`);
                    }
                }
                
                addTestResult('Tables de base de données', 'success', results.join('<br>'));
                log('Tests des tables terminés', 'success');
                
            } catch (error) {
                addTestResult('Erreur test tables', 'error', error.message);
                log(`Erreur test tables: ${error.message}`, 'error');
            }
        }

        async function testDatabaseViews() {
            try {
                log('Test des vues de base de données...', 'info');
                const supabase = window.supabaseClient || window.supabase;
                
                const views = ['commands_complete', 'commands_table_view', 'commands_stats'];
                let results = [];
                
                for (const view of views) {
                    try {
                        const { data, error } = await supabase.from(view).select('*').limit(1);
                        if (error) throw error;
                        results.push(`✅ Vue ${view}: OK`);
                    } catch (error) {
                        results.push(`❌ Vue ${view}: ${error.message}`);
                    }
                }
                
                addTestResult('Vues de base de données', 'success', results.join('<br>'));
                log('Tests des vues terminés', 'success');
                
            } catch (error) {
                addTestResult('Erreur test vues', 'error', error.message);
                log(`Erreur test vues: ${error.message}`, 'error');
            }
        }

        async function testSampleData() {
            try {
                log('Test des données d\'exemple...', 'info');
                const supabase = window.supabaseClient || window.supabase;
                
                const { data, error } = await supabase
                    .from('gestion_commandes')
                    .select('*')
                    .limit(5);
                
                if (error) throw error;
                
                testMetrics.totalCommands = data.length;
                updateMetrics();
                
                addTestResult('Données d\'exemple', 'success', `${data.length} commandes trouvées`);
                log(`${data.length} commandes d'exemple trouvées`, 'success');
                
            } catch (error) {
                addTestResult('Erreur données exemple', 'error', error.message);
                log(`Erreur données exemple: ${error.message}`, 'error');
            }
        }

        // Tests du widget
        async function testWidgetInitialization() {
            try {
                log('Test d\'initialisation du widget...', 'info');
                testStartTime = performance.now();
                
                if (typeof CommandsDashboardWidget === 'undefined') {
                    throw new Error('Classe CommandsDashboardWidget non trouvée');
                }
                
                testCommandsWidget = new CommandsDashboardWidget();
                await testCommandsWidget.initialize();
                
                const initTime = performance.now() - testStartTime;
                testMetrics.loadTime = Math.round(initTime);
                updateMetrics();
                
                addTestResult('Initialisation widget', 'success', `Temps: ${testMetrics.loadTime}ms`);
                log(`Widget initialisé en ${testMetrics.loadTime}ms`, 'success');
                
            } catch (error) {
                addTestResult('Erreur initialisation widget', 'error', error.message);
                log(`Erreur initialisation widget: ${error.message}`, 'error');
            }
        }

        async function testWidgetData() {
            if (!testCommandsWidget) {
                addTestResult('Widget non initialisé', 'error', 'Initialisez d\'abord le widget');
                return;
            }
            
            try {
                log('Test de chargement des données du widget...', 'info');
                testStartTime = performance.now();
                
                await testCommandsWidget.loadData();
                
                const loadTime = performance.now() - testStartTime;
                testMetrics.loadTime = Math.round(loadTime);
                updateMetrics();
                
                addTestResult('Données widget', 'success', `Chargées en ${testMetrics.loadTime}ms`);
                log(`Données widget chargées en ${testMetrics.loadTime}ms`, 'success');
                
            } catch (error) {
                addTestResult('Erreur données widget', 'error', error.message);
                log(`Erreur données widget: ${error.message}`, 'error');
            }
        }

        function showWidget() {
            if (!testCommandsWidget) {
                addTestResult('Widget non initialisé', 'error', 'Initialisez d\'abord le widget');
                return;
            }
            
            // Créer une version du widget dans l'aperçu
            const preview = document.getElementById('widgetPreview');
            preview.innerHTML = `
                <h4>📦 Widget Commandes (Aperçu)</h4>
                <div style="background: white; border-radius: 8px; padding: 15px; border: 1px solid #e5e7eb;">
                    <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin-bottom: 15px;">
                        <div style="text-align: center; padding: 10px; background: #f0fdf4; border-radius: 6px;">
                            <div style="font-size: 18px; font-weight: bold; color: #059669;">${testCommandsWidget.data.totalCommandes}</div>
                            <div style="font-size: 11px; color: #6b7280;">Total</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: #fef3c7; border-radius: 6px;">
                            <div style="font-size: 18px; font-weight: bold; color: #d97706;">${testCommandsWidget.data.commandesEnCours}</div>
                            <div style="font-size: 11px; color: #6b7280;">En Cours</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: #fee2e2; border-radius: 6px;">
                            <div style="font-size: 18px; font-weight: bold; color: #dc2626;">${testCommandsWidget.data.commandesEnRetard}</div>
                            <div style="font-size: 11px; color: #6b7280;">En Retard</div>
                        </div>
                        <div style="text-align: center; padding: 10px; background: #dcfce7; border-radius: 6px;">
                            <div style="font-size: 18px; font-weight: bold; color: #16a34a;">${testCommandsWidget.data.commandesLivrees}</div>
                            <div style="font-size: 11px; color: #6b7280;">Livrées</div>
                        </div>
                    </div>
                    <div style="font-size: 12px; color: #6b7280; text-align: center;">
                        Widget fonctionnel avec données en temps réel
                    </div>
                </div>
            `;
            
            addTestResult('Affichage widget', 'success', 'Widget affiché dans l\'aperçu');
            log('Widget affiché dans l\'aperçu', 'success');
        }

        // Tests de la table
        async function testTableManager() {
            try {
                log('Test d\'initialisation du gestionnaire de table...', 'info');
                testStartTime = performance.now();
                
                if (typeof CommandsTableManager === 'undefined') {
                    throw new Error('Classe CommandsTableManager non trouvée');
                }
                
                testTableManager = new CommandsTableManager();
                await testTableManager.initialize();
                
                const initTime = performance.now() - testStartTime;
                testMetrics.renderTime = Math.round(initTime);
                updateMetrics();
                
                addTestResult('Gestionnaire de table', 'success', `Initialisé en ${testMetrics.renderTime}ms`);
                log(`Gestionnaire de table initialisé en ${testMetrics.renderTime}ms`, 'success');
                
            } catch (error) {
                addTestResult('Erreur gestionnaire table', 'error', error.message);
                log(`Erreur gestionnaire table: ${error.message}`, 'error');
            }
        }

        function testTableFilters() {
            if (!testTableManager) {
                addTestResult('Table non initialisée', 'error', 'Initialisez d\'abord la table');
                return;
            }
            
            log('Test des filtres de table...', 'info');
            
            // Simuler l'application de filtres
            const originalCount = testTableManager.filteredData.length;
            
            // Test filtre par statut
            testTableManager.filters = { statut: 'en_cours' };
            testTableManager.applyFilters();
            
            const filteredCount = testTableManager.filteredData.length;
            
            addTestResult('Filtres de table', 'success', `${originalCount} → ${filteredCount} commandes`);
            log(`Filtres testés: ${originalCount} → ${filteredCount} commandes`, 'success');
        }

        function testTableExport() {
            if (!testTableManager) {
                addTestResult('Table non initialisée', 'error');
                return;
            }
            
            try {
                log('Test de l\'export de table...', 'info');
                
                const csvData = testTableManager.generateCSV();
                const lines = csvData.split('\n').length - 1;
                
                addTestResult('Export de table', 'success', `${lines} lignes exportées`);
                log(`Export CSV généré: ${lines} lignes`, 'success');
                
            } catch (error) {
                addTestResult('Erreur export table', 'error', error.message);
                log(`Erreur export: ${error.message}`, 'error');
            }
        }

        // Test d'intégration complète
        async function testFullIntegration() {
            log('Démarrage du test d\'intégration complète...', 'info');
            
            try {
                // Test 1: Base de données
                await testDatabaseTables();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Test 2: Widget
                await testWidgetInitialization();
                await testWidgetData();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Test 3: Table
                await testTableManager();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Test 4: Données
                await testSampleData();
                
                addTestResult('Intégration complète', 'success', 'Tous les composants fonctionnent correctement');
                log('Test d\'intégration complète réussi', 'success');
                
            } catch (error) {
                addTestResult('Erreur intégration', 'error', error.message);
                log(`Erreur intégration: ${error.message}`, 'error');
            }
        }

        function openCommandsManagement() {
            window.open('commands-management.html', '_blank');
            log('Interface de gestion des commandes ouverte', 'info');
        }

        // Fonctions utilitaires
        function updateMetrics() {
            document.getElementById('metricTotalCommands').textContent = testMetrics.totalCommands;
            document.getElementById('metricLoadTime').textContent = testMetrics.loadTime + 'ms';
            document.getElementById('metricRenderTime').textContent = testMetrics.renderTime + 'ms';
            document.getElementById('metricMemoryUsage').textContent = Math.round(performance.memory?.usedJSHeapSize / 1024 / 1024 || 0) + 'MB';
        }

        function addTestResult(title, type, details = '') {
            const container = document.getElementById('testResults');
            
            if (container.innerHTML.includes('Aucun test')) {
                container.innerHTML = '';
            }
            
            const result = document.createElement('div');
            result.className = `test-result result-${type}`;
            result.innerHTML = `
                <strong>${title}</strong>
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            
            container.appendChild(result);
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            log('Logs effacés', 'info');
        }

        // Mettre à jour les métriques périodiquement
        setInterval(updateMetrics, 5000);
    </script>
</body>
</html>
