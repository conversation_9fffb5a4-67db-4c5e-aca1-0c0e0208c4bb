-- =====================================================
-- SCHÉMA DE BASE DE DONNÉES POUR LA GESTION DES COMMANDES
-- Système complet de gestion des commandes IPT
-- =====================================================

-- Supprimer les tables existantes si elles existent (pour réinstallation)
DROP TABLE IF EXISTS gestion_commande_workflow CASCADE;
DROP TABLE IF EXISTS gestion_commande_documents CASCADE;
DROP TABLE IF EXISTS gestion_commande_items CASCADE;
DROP TABLE IF EXISTS gestion_commandes CASCADE;

-- =====================================================
-- TABLE PRINCIPALE DES COMMANDES
-- =====================================================

CREATE TABLE gestion_commandes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    numero_commande TEXT UNIQUE NOT NULL, -- Format CMD-YYYY-NNNN
    
    -- Type de commande
    type_commande TEXT NOT NULL CHECK (type_commande IN ('fournitures', 'equipements', 'services', 'maintenance')),
    
    -- Informations du demandeur
    demandeur_nom TEXT NOT NULL,
    demandeur_service TEXT NOT NULL,
    demandeur_email TEXT NOT NULL,
    demandeur_telephone TEXT,
    
    -- Informations du fournisseur
    fournisseur_nom TEXT NOT NULL,
    fournisseur_contact TEXT,
    fournisseur_adresse TEXT,
    fournisseur_telephone TEXT,
    fournisseur_email TEXT,
    
    -- Détails de la commande
    objet_commande TEXT NOT NULL, -- Description générale
    justification TEXT NOT NULL, -- Justification de la commande
    urgence TEXT NOT NULL DEFAULT 'normale' CHECK (urgence IN ('faible', 'normale', 'elevee', 'urgente')),
    
    -- Montants
    montant_ht DECIMAL(12,3) DEFAULT 0,
    montant_tva DECIMAL(12,3) DEFAULT 0,
    montant_ttc DECIMAL(12,3) DEFAULT 0,
    devise TEXT DEFAULT 'TND',
    
    -- Dates importantes
    date_commande DATE NOT NULL DEFAULT CURRENT_DATE,
    date_livraison_prevue DATE,
    date_livraison_reelle DATE,
    date_limite_livraison DATE,
    
    -- Statut et workflow
    statut TEXT NOT NULL DEFAULT 'brouillon' CHECK (statut IN (
        'brouillon', 'soumise', 'validee_chef', 'validee_dg', 'approuvee_ms',
        'en_cours', 'expedie', 'livre_partiel', 'livre_complet', 'facture',
        'paye', 'cloture', 'annule', 'refuse'
    )),
    
    -- Étape actuelle du workflow (1-10)
    etape_actuelle INTEGER DEFAULT 1 CHECK (etape_actuelle BETWEEN 1 AND 10),
    
    -- Informations de validation
    validee_chef_par TEXT,
    validee_chef_date TIMESTAMP WITH TIME ZONE,
    validee_chef_commentaire TEXT,
    
    validee_dg_par TEXT,
    validee_dg_date TIMESTAMP WITH TIME ZONE,
    validee_dg_commentaire TEXT,
    
    approuvee_ms_par TEXT,
    approuvee_ms_date TIMESTAMP WITH TIME ZONE,
    approuvee_ms_commentaire TEXT,
    
    -- Informations de livraison
    lieu_livraison TEXT,
    responsable_reception TEXT,
    observations_reception TEXT,
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Index pour les recherches
    CONSTRAINT valid_dates CHECK (
        date_livraison_prevue IS NULL OR date_livraison_prevue >= date_commande
    )
);

-- =====================================================
-- TABLE DES ITEMS DE COMMANDE
-- =====================================================

CREATE TABLE gestion_commande_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    commande_id UUID NOT NULL REFERENCES gestion_commandes(id) ON DELETE CASCADE,
    
    -- Informations de l'item
    designation TEXT NOT NULL,
    description TEXT,
    reference_fournisseur TEXT,
    code_interne TEXT,
    
    -- Quantités
    quantite_commandee DECIMAL(10,3) NOT NULL DEFAULT 1,
    quantite_livree DECIMAL(10,3) DEFAULT 0,
    unite TEXT DEFAULT 'pièce',
    
    -- Prix
    prix_unitaire_ht DECIMAL(10,3) DEFAULT 0,
    taux_tva DECIMAL(5,2) DEFAULT 19.00,
    prix_total_ht DECIMAL(12,3) GENERATED ALWAYS AS (quantite_commandee * prix_unitaire_ht) STORED,
    prix_total_ttc DECIMAL(12,3) GENERATED ALWAYS AS (prix_total_ht * (1 + taux_tva/100)) STORED,
    
    -- Spécifications techniques
    specifications JSONB DEFAULT '{}'::jsonb,
    
    -- Informations de livraison
    date_livraison_item DATE,
    etat_reception TEXT CHECK (etat_reception IN ('conforme', 'non_conforme', 'endommage', 'manquant')),
    observations_item TEXT,
    
    -- Numéro d'inventaire (pour les équipements)
    numero_inventaire TEXT,
    
    -- Métadonnées
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TABLE DES DOCUMENTS DE COMMANDE
-- =====================================================

CREATE TABLE gestion_commande_documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    commande_id UUID NOT NULL REFERENCES gestion_commandes(id) ON DELETE CASCADE,
    
    -- Type de document
    type_document TEXT NOT NULL CHECK (type_document IN (
        'demande_achat', 'devis', 'bon_commande', 'facture_proforma',
        'facture_definitive', 'bon_livraison', 'bon_reception',
        'autorisation_ms', 'validation_chef', 'validation_dg'
    )),
    
    -- Informations du document
    nom_document TEXT NOT NULL,
    numero_document TEXT,
    date_document DATE,
    
    -- Stockage du fichier
    file_path TEXT, -- Chemin dans Supabase Storage
    file_name TEXT,
    file_size INTEGER,
    file_type TEXT,
    
    -- Métadonnées
    uploaded_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TABLE DU WORKFLOW DES COMMANDES
-- =====================================================

CREATE TABLE gestion_commande_workflow (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    commande_id UUID NOT NULL REFERENCES gestion_commandes(id) ON DELETE CASCADE,
    
    -- Informations de l'étape
    numero_etape INTEGER NOT NULL CHECK (numero_etape BETWEEN 1 AND 10),
    nom_etape TEXT NOT NULL,
    description_etape TEXT,
    
    -- Statut de l'étape
    statut_etape TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut_etape IN (
        'en_attente', 'en_cours', 'termine', 'bloque', 'annule'
    )),
    
    -- Responsable de l'étape
    responsable_email TEXT,
    responsable_role TEXT,
    
    -- Dates
    date_debut TIMESTAMP WITH TIME ZONE,
    date_fin TIMESTAMP WITH TIME ZONE,
    date_limite TIMESTAMP WITH TIME ZONE,
    
    -- Résultat de l'étape
    resultat TEXT CHECK (resultat IN ('approuve', 'refuse', 'en_attente', 'reporte')),
    commentaire TEXT,
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEX POUR LES PERFORMANCES
-- =====================================================

-- Index sur les colonnes de recherche fréquente
CREATE INDEX idx_commandes_numero ON gestion_commandes(numero_commande);
CREATE INDEX idx_commandes_statut ON gestion_commandes(statut) WHERE deleted_at IS NULL;
CREATE INDEX idx_commandes_type ON gestion_commandes(type_commande) WHERE deleted_at IS NULL;
CREATE INDEX idx_commandes_demandeur ON gestion_commandes(demandeur_email) WHERE deleted_at IS NULL;
CREATE INDEX idx_commandes_fournisseur ON gestion_commandes(fournisseur_nom) WHERE deleted_at IS NULL;
CREATE INDEX idx_commandes_dates ON gestion_commandes(date_commande, date_livraison_prevue) WHERE deleted_at IS NULL;
CREATE INDEX idx_commandes_created_at ON gestion_commandes(created_at DESC) WHERE deleted_at IS NULL;

-- Index sur les items
CREATE INDEX idx_commande_items_commande ON gestion_commande_items(commande_id);
CREATE INDEX idx_commande_items_designation ON gestion_commande_items(designation);

-- Index sur les documents
CREATE INDEX idx_commande_documents_commande ON gestion_commande_documents(commande_id);
CREATE INDEX idx_commande_documents_type ON gestion_commande_documents(type_document);

-- Index sur le workflow
CREATE INDEX idx_commande_workflow_commande ON gestion_commande_workflow(commande_id);
CREATE INDEX idx_commande_workflow_etape ON gestion_commande_workflow(numero_etape, statut_etape);
CREATE INDEX idx_commande_workflow_responsable ON gestion_commande_workflow(responsable_email);

-- =====================================================
-- TRIGGERS POUR UPDATED_AT
-- =====================================================

-- Fonction générique pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers sur toutes les tables
CREATE TRIGGER update_commandes_updated_at
    BEFORE UPDATE ON gestion_commandes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_commande_items_updated_at
    BEFORE UPDATE ON gestion_commande_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_commande_workflow_updated_at
    BEFORE UPDATE ON gestion_commande_workflow
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- TRIGGER POUR CALCULER LES MONTANTS AUTOMATIQUEMENT
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_commande_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Recalculer les totaux de la commande
    UPDATE gestion_commandes
    SET 
        montant_ht = (
            SELECT COALESCE(SUM(prix_total_ht), 0)
            FROM gestion_commande_items
            WHERE commande_id = COALESCE(NEW.commande_id, OLD.commande_id)
        ),
        montant_ttc = (
            SELECT COALESCE(SUM(prix_total_ttc), 0)
            FROM gestion_commande_items
            WHERE commande_id = COALESCE(NEW.commande_id, OLD.commande_id)
        )
    WHERE id = COALESCE(NEW.commande_id, OLD.commande_id);
    
    -- Calculer la TVA
    UPDATE gestion_commandes
    SET montant_tva = montant_ttc - montant_ht
    WHERE id = COALESCE(NEW.commande_id, OLD.commande_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Triggers pour recalculer automatiquement
CREATE TRIGGER calculate_totals_on_item_change
    AFTER INSERT OR UPDATE OR DELETE ON gestion_commande_items
    FOR EACH ROW EXECUTE FUNCTION calculate_commande_totals();

-- =====================================================
-- COMMENTAIRES DE DOCUMENTATION
-- =====================================================

COMMENT ON TABLE gestion_commandes IS 'Table principale des commandes (fournitures, équipements, services)';
COMMENT ON TABLE gestion_commande_items IS 'Détails des items dans chaque commande';
COMMENT ON TABLE gestion_commande_documents IS 'Documents associés aux commandes (devis, factures, BL, etc.)';
COMMENT ON TABLE gestion_commande_workflow IS 'Historique du workflow et des étapes de validation';

COMMENT ON COLUMN gestion_commandes.numero_commande IS 'Numéro unique de la commande (format CMD-YYYY-NNNN)';
COMMENT ON COLUMN gestion_commandes.type_commande IS 'Type de commande : fournitures, équipements, services, maintenance';
COMMENT ON COLUMN gestion_commandes.statut IS 'Statut actuel de la commande dans le workflow';
COMMENT ON COLUMN gestion_commandes.etape_actuelle IS 'Étape actuelle du workflow (1-10)';
COMMENT ON COLUMN gestion_commandes.urgence IS 'Niveau d''urgence de la commande';

COMMENT ON COLUMN gestion_commande_items.prix_total_ht IS 'Prix total HT calculé automatiquement (quantité × prix unitaire)';
COMMENT ON COLUMN gestion_commande_items.prix_total_ttc IS 'Prix total TTC calculé automatiquement avec TVA';

-- =====================================================
-- DONNÉES DE RÉFÉRENCE
-- =====================================================

-- Insérer les étapes du workflow par défaut
INSERT INTO gestion_commande_workflow (commande_id, numero_etape, nom_etape, description_etape, created_by)
SELECT 
    gen_random_uuid(), -- Sera remplacé par l'ID réel lors de la création d'une commande
    etape.numero,
    etape.nom,
    etape.description,
    'system'
FROM (VALUES
    (1, 'Création', 'Création de la demande de commande'),
    (2, 'Validation Chef', 'Validation par le chef de service'),
    (3, 'Validation DG', 'Validation par le Directeur Général'),
    (4, 'Approbation MS', 'Approbation par le Ministère de la Santé'),
    (5, 'Passation Commande', 'Passation de la commande au fournisseur'),
    (6, 'Suivi Commande', 'Suivi de l''exécution de la commande'),
    (7, 'Expédition', 'Expédition par le fournisseur'),
    (8, 'Livraison', 'Livraison et réception des items'),
    (9, 'Contrôle Qualité', 'Contrôle qualité et conformité'),
    (10, 'Clôture', 'Clôture de la commande et archivage')
) AS etape(numero, nom, description)
WHERE FALSE; -- Cette insertion ne s'exécute pas, c'est juste un modèle

-- =====================================================
-- PERMISSIONS ET SÉCURITÉ
-- =====================================================

-- Activer RLS sur toutes les tables
ALTER TABLE gestion_commandes ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_commande_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_commande_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_commande_workflow ENABLE ROW LEVEL SECURITY;

-- Les politiques RLS seront définies dans un fichier séparé
-- commands-rls-policies.sql

-- =====================================================
-- VALIDATION DE L'INSTALLATION
-- =====================================================

-- Vérifier que toutes les tables ont été créées
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('gestion_commandes', 'gestion_commande_items', 'gestion_commande_documents', 'gestion_commande_workflow');
    
    IF table_count = 4 THEN
        RAISE NOTICE '✅ Toutes les tables de commandes ont été créées avec succès';
    ELSE
        RAISE EXCEPTION '❌ Erreur : Seulement % tables créées sur 4 attendues', table_count;
    END IF;
END $$;
