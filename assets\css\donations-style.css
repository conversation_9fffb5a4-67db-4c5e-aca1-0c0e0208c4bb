/**
 * Styles spécifiques pour le système de gestion des dons IPT
 */

/* =====================================================
   VARIABLES CSS
   ===================================================== */
:root {
    --donation-primary: #3b82f6;
    --donation-success: #10b981;
    --donation-warning: #f59e0b;
    --donation-danger: #ef4444;
    --donation-info: #06b6d4;
    --donation-secondary: #6b7280;
    
    --donation-bg-light: #f8fafc;
    --donation-bg-white: #ffffff;
    --donation-border: #e5e7eb;
    --donation-text: #1f2937;
    --donation-text-light: #6b7280;
    
    --donation-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --donation-shadow-lg: 0 4px 6px rgba(0,0,0,0.1);
    --donation-radius: 8px;
    --donation-radius-sm: 4px;
}

/* =====================================================
   LAYOUT GÉNÉRAL
   ===================================================== */
.donations-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: var(--donation-bg-light);
    min-height: 100vh;
}

.donations-header {
    background: var(--donation-bg-white);
    border-radius: var(--donation-radius);
    box-shadow: var(--donation-shadow);
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.donations-title {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--donation-text);
    margin: 0;
}

.donations-actions {
    display: flex;
    gap: 10px;
}

/* =====================================================
   FILTRES ET RECHERCHE
   ===================================================== */
.filters-section {
    background: var(--donation-bg-white);
    border-radius: var(--donation-radius);
    box-shadow: var(--donation-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: center;
}

.filter-input,
.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--donation-border);
    border-radius: var(--donation-radius-sm);
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--donation-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* =====================================================
   CARTES DE DONS
   ===================================================== */
.donation-card {
    background: var(--donation-bg-white);
    border-radius: var(--donation-radius);
    box-shadow: var(--donation-shadow);
    margin-bottom: 20px;
    padding: 20px;
    transition: transform 0.2s, box-shadow 0.2s;
    border-left: 4px solid transparent;
}

.donation-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--donation-shadow-lg);
}

.donation-card.status-brouillon {
    border-left-color: var(--donation-secondary);
}

.donation-card.status-soumis_dg {
    border-left-color: var(--donation-warning);
}

.donation-card.status-avis_dt {
    border-left-color: var(--donation-info);
}

.donation-card.status-soumis_ms {
    border-left-color: var(--donation-warning);
}

.donation-card.status-approuve_ms {
    border-left-color: var(--donation-success);
}

.donation-card.status-refuse_ms {
    border-left-color: var(--donation-danger);
}

.donation-card.status-recu {
    border-left-color: var(--donation-info);
}

.donation-card.status-affecte {
    border-left-color: var(--donation-success);
}

.donation-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--donation-border);
}

.donation-title {
    font-size: 1.2rem;
    font-weight: bold;
    color: var(--donation-text);
    margin: 0 0 5px 0;
}

.donation-meta {
    display: flex;
    gap: 10px;
    align-items: center;
}

.donation-value {
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--donation-primary);
}

/* =====================================================
   BADGES ET STATUTS
   ===================================================== */
.donation-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-brouillon {
    background: #f3f4f6;
    color: #6b7280;
}

.status-soumis_dg {
    background: #fef3c7;
    color: #d97706;
}

.status-avis_dt {
    background: #dbeafe;
    color: #2563eb;
}

.status-soumis_ms {
    background: #fde68a;
    color: #d97706;
}

.status-approuve_ms {
    background: #d1fae5;
    color: #059669;
}

.status-refuse_ms {
    background: #fee2e2;
    color: #dc2626;
}

.status-recu {
    background: #e0e7ff;
    color: #4338ca;
}

.status-affecte {
    background: #dcfce7;
    color: #16a34a;
}

.donation-type {
    display: inline-block;
    padding: 2px 8px;
    border-radius: var(--donation-radius-sm);
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.type-article {
    background: #e0f2fe;
    color: #0277bd;
}

.type-equipement {
    background: #f3e5f5;
    color: #7b1fa2;
}

/* =====================================================
   INFORMATIONS DU DON
   ===================================================== */
.donation-info {
    margin-bottom: 15px;
}

.info-row {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-row strong {
    min-width: 100px;
    color: var(--donation-text);
}

.info-row span {
    color: var(--donation-text-light);
}

/* =====================================================
   ACTIONS ET BOUTONS
   ===================================================== */
.donation-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--donation-border);
}

.btn-action {
    padding: 6px 12px;
    border: none;
    border-radius: var(--donation-radius-sm);
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.btn-action:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

.btn-primary {
    background: var(--donation-primary);
    color: white;
}

.btn-success {
    background: var(--donation-success);
    color: white;
}

.btn-warning {
    background: var(--donation-warning);
    color: white;
}

.btn-danger {
    background: var(--donation-danger);
    color: white;
}

.btn-secondary {
    background: var(--donation-secondary);
    color: white;
}

.btn-info {
    background: var(--donation-info);
    color: white;
}

/* =====================================================
   MODALS
   ===================================================== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: var(--donation-bg-white);
    margin: 5% auto;
    padding: 0;
    border-radius: var(--donation-radius);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--donation-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--donation-bg-light);
}

.modal-header h2 {
    margin: 0;
    color: var(--donation-text);
    font-size: 1.3rem;
}

.close {
    color: var(--donation-text-light);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: var(--donation-text);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

/* =====================================================
   FORMULAIRES
   ===================================================== */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: var(--donation-text);
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--donation-border);
    border-radius: var(--donation-radius-sm);
    font-size: 14px;
    transition: border-color 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--donation-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group small {
    color: var(--donation-text-light);
    font-size: 12px;
    margin-top: 4px;
    display: block;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid var(--donation-border);
    margin-top: 20px;
}

/* =====================================================
   ITEMS ET LISTES
   ===================================================== */
.items-container {
    border: 1px solid var(--donation-border);
    border-radius: var(--donation-radius-sm);
    padding: 15px;
    margin-top: 10px;
    background: var(--donation-bg-light);
}

.item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    background: var(--donation-bg-white);
    border-radius: var(--donation-radius-sm);
    border: 1px solid var(--donation-border);
}

.item-detail {
    background: var(--donation-bg-light);
    border-radius: var(--donation-radius-sm);
    padding: 15px;
    margin-bottom: 10px;
    border: 1px solid var(--donation-border);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.item-quantity {
    background: var(--donation-primary);
    color: white;
    padding: 2px 8px;
    border-radius: var(--donation-radius-sm);
    font-size: 12px;
    font-weight: bold;
}

.item-description {
    color: var(--donation-text-light);
    font-size: 13px;
    margin-bottom: 5px;
}

.item-specs {
    font-size: 12px;
    color: var(--donation-text-light);
    margin-bottom: 5px;
}

.item-inventory {
    background: var(--donation-success);
    color: white;
    padding: 4px 8px;
    border-radius: var(--donation-radius-sm);
    font-size: 11px;
    font-weight: bold;
    display: inline-block;
}

/* =====================================================
   TIMELINE ET WORKFLOW
   ===================================================== */
.workflow-timeline {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--donation-border);
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background: var(--donation-bg-light);
    border-radius: var(--donation-radius-sm);
    border-left: 4px solid var(--donation-primary);
    position: relative;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 20px;
    width: 8px;
    height: 8px;
    background: var(--donation-primary);
    border-radius: 50%;
}

.timeline-date {
    font-size: 12px;
    color: var(--donation-text-light);
    margin-right: 15px;
    min-width: 120px;
    font-weight: 500;
}

.timeline-content {
    flex: 1;
}

.timeline-action {
    font-weight: bold;
    color: var(--donation-text);
    margin-bottom: 2px;
}

.timeline-actor {
    font-size: 13px;
    color: var(--donation-text-light);
    margin-bottom: 4px;
}

.timeline-comment {
    font-size: 13px;
    color: var(--donation-text-light);
    font-style: italic;
}

/* =====================================================
   STATISTIQUES
   ===================================================== */
.stats-section {
    background: var(--donation-bg-white);
    border-radius: var(--donation-radius);
    box-shadow: var(--donation-shadow);
    padding: 20px;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: var(--donation-bg-light);
    padding: 20px;
    border-radius: var(--donation-radius);
    text-align: center;
    border: 1px solid var(--donation-border);
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    color: var(--donation-primary);
    margin-bottom: 5px;
}

.stat-label {
    color: var(--donation-text-light);
    font-size: 14px;
    font-weight: 500;
}

/* =====================================================
   ÉTAT VIDE
   ===================================================== */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--donation-text-light);
}

.empty-state h3 {
    color: var(--donation-text);
    margin-bottom: 10px;
}

.empty-state p {
    margin-bottom: 20px;
}

/* =====================================================
   RESPONSIVE
   ===================================================== */
@media (max-width: 768px) {
    .donations-container {
        padding: 10px;
    }
    
    .donations-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .filters-row {
        grid-template-columns: 1fr;
    }
    
    .donation-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .donation-actions {
        justify-content: center;
    }
    
    .item-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .timeline-item {
        flex-direction: column;
        gap: 10px;
    }
    
    .timeline-date {
        min-width: auto;
        margin-right: 0;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .modal-content {
        width: 95%;
        margin: 2% auto;
    }
}
