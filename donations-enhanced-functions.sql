-- Fonctions améliorées pour le workflow détaillé des dons IPT

-- =====================================================
-- FONCTION POUR INITIALISER LES ÉTAPES D'UN DON
-- =====================================================

CREATE OR REPLACE FUNCTION initialize_donation_workflow(
    p_donation_id UUID,
    p_type_don TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    etapes_config JSONB;
    etape JSONB;
BEGIN
    -- Configuration des étapes selon le type de don
    etapes_config := CASE p_type_don
        WHEN 'article' THEN '[
            {"numero": 1, "nom": "Création dossier donneur", "responsable": "donneur", "duree": 24},
            {"numero": 2, "nom": "Création BP bénéficiaire", "responsable": "beneficiaire", "duree": 48},
            {"numero": 3, "nom": "Traitement magasinier", "responsable": "magasinier", "duree": 24},
            {"numero": 4, "nom": "Validation transitaire", "responsable": "transitaire", "duree": 24},
            {"numero": 5, "nom": "Réception", "responsable": "receptionniste", "duree": 24},
            {"numero": 6, "nom": "Création BS", "responsable": "magasinier", "duree": 24},
            {"numero": 7, "nom": "Traitement RVE", "responsable": "rve", "duree": 48},
            {"numero": 8, "nom": "Création récapitulatif", "responsable": "rve", "duree": 24},
            {"numero": 9, "nom": "Envoi comptabilité", "responsable": "rve", "duree": 24},
            {"numero": 10, "nom": "Archivage", "responsable": "rve", "duree": 24}
        ]'::jsonb
        WHEN 'equipement' THEN '[
            {"numero": 1, "nom": "Création dossier donneur", "responsable": "donneur", "duree": 24},
            {"numero": 2, "nom": "Création BP bénéficiaire", "responsable": "beneficiaire", "duree": 48},
            {"numero": 3, "nom": "Traitement magasinier", "responsable": "magasinier", "duree": 24},
            {"numero": 4, "nom": "Validation transitaire", "responsable": "transitaire", "duree": 24},
            {"numero": 5, "nom": "Réception", "responsable": "receptionniste", "duree": 24},
            {"numero": 6, "nom": "Création BS + Inventaire", "responsable": "magasinier", "duree": 48},
            {"numero": 7, "nom": "Attribution NI par RVE", "responsable": "rve", "duree": 72},
            {"numero": 8, "nom": "Création récapitulatif", "responsable": "rve", "duree": 24},
            {"numero": 9, "nom": "Envoi comptabilité", "responsable": "rve", "duree": 24},
            {"numero": 10, "nom": "Archivage", "responsable": "rve", "duree": 24}
        ]'::jsonb
    END;

    -- Insérer chaque étape
    FOR etape IN SELECT * FROM jsonb_array_elements(etapes_config)
    LOOP
        INSERT INTO gestion_donation_etapes (
            donation_id,
            numero_etape,
            nom_etape,
            responsable_role,
            duree_prevue_heures,
            statut_etape,
            documents_requis
        ) VALUES (
            p_donation_id,
            (etape->>'numero')::integer,
            etape->>'nom',
            etape->>'responsable',
            (etape->>'duree')::integer,
            CASE WHEN (etape->>'numero')::integer = 1 THEN 'en_cours' ELSE 'en_attente' END,
            CASE (etape->>'numero')::integer
                WHEN 1 THEN '["facture", "bon_livraison", "lettre_don"]'::jsonb
                WHEN 2 THEN '["bon_prelevement"]'::jsonb
                WHEN 6 THEN '["bon_sortie"]'::jsonb
                WHEN 8 THEN '["tableau_recapitulatif"]'::jsonb
                ELSE '[]'::jsonb
            END
        );
    END LOOP;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR AVANCER À L'ÉTAPE SUIVANTE
-- =====================================================

CREATE OR REPLACE FUNCTION advance_to_next_step(
    p_donation_id UUID,
    p_current_step INTEGER,
    p_user_email TEXT,
    p_commentaire TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    next_step INTEGER;
    new_status TEXT;
BEGIN
    -- Marquer l'étape actuelle comme terminée
    UPDATE gestion_donation_etapes
    SET statut_etape = 'termine',
        date_fin = NOW(),
        responsable_nom = p_user_email,
        commentaire = p_commentaire
    WHERE donation_id = p_donation_id 
    AND numero_etape = p_current_step;

    -- Déterminer l'étape suivante
    next_step := p_current_step + 1;
    
    -- Vérifier s'il y a une étape suivante
    IF EXISTS (
        SELECT 1 FROM gestion_donation_etapes 
        WHERE donation_id = p_donation_id 
        AND numero_etape = next_step
    ) THEN
        -- Activer l'étape suivante
        UPDATE gestion_donation_etapes
        SET statut_etape = 'en_cours',
            date_debut = NOW()
        WHERE donation_id = p_donation_id 
        AND numero_etape = next_step;
        
        -- Mettre à jour le statut principal du don
        new_status := CASE next_step
            WHEN 2 THEN 'bp_cree'
            WHEN 3 THEN 'chez_magasinier'
            WHEN 4 THEN 'chez_transitaire'
            WHEN 5 THEN 'chez_receptionniste'
            WHEN 6 THEN 'bs_cree'
            WHEN 7 THEN 'chez_rve'
            WHEN 8 THEN 'recap_cree'
            WHEN 9 THEN 'envoye_comptabilite'
            WHEN 10 THEN 'archive'
            ELSE 'en_cours'
        END;
        
        UPDATE gestion_donations
        SET statut = new_status,
            updated_by = p_user_email
        WHERE id = p_donation_id;
    ELSE
        -- Toutes les étapes sont terminées
        UPDATE gestion_donations
        SET statut = 'archive',
            date_archivage = NOW(),
            updated_by = p_user_email
        WHERE id = p_donation_id;
    END IF;

    -- Enregistrer dans le workflow
    PERFORM log_donation_workflow(
        p_donation_id,
        'etape_' || p_current_step,
        'en_cours',
        CASE WHEN next_step <= 10 THEN 'etape_' || next_step ELSE 'archive' END,
        p_user_email,
        'system',
        'Passage à l''étape suivante',
        p_commentaire
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR CRÉER UN BON DE PRÉLÈVEMENT
-- =====================================================

CREATE OR REPLACE FUNCTION create_bon_prelevement(
    p_donation_id UUID,
    p_beneficiaire_email TEXT,
    p_motif TEXT,
    p_lieu TEXT,
    p_items JSONB
)
RETURNS TEXT AS $$
DECLARE
    bp_number TEXT;
    bp_id UUID;
BEGIN
    -- Générer le numéro de BP
    bp_number := 'BP-' || EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                 LPAD((
                     SELECT COALESCE(MAX(CAST(SUBSTRING(numero_bp FROM 'BP-[0-9]{4}-(.*)') AS INTEGER)), 0) + 1
                     FROM gestion_donation_bp
                     WHERE numero_bp LIKE 'BP-' || EXTRACT(YEAR FROM CURRENT_DATE) || '-%'
                 )::TEXT, 4, '0');

    -- Créer le BP
    INSERT INTO gestion_donation_bp (
        donation_id,
        numero_bp,
        beneficiaire_nom,
        beneficiaire_service,
        beneficiaire_email,
        motif_prelevement,
        lieu_prelevement,
        items_demandes,
        created_by
    )
    SELECT 
        p_donation_id,
        bp_number,
        d.demandeur_nom,
        d.demandeur_service,
        p_beneficiaire_email,
        p_motif,
        p_lieu,
        p_items,
        p_beneficiaire_email
    FROM gestion_donations d
    WHERE d.id = p_donation_id
    RETURNING id INTO bp_id;

    -- Mettre à jour le don
    UPDATE gestion_donations
    SET numero_bp = bp_number,
        date_bp = CURRENT_DATE
    WHERE id = p_donation_id;

    -- Avancer à l'étape suivante
    PERFORM advance_to_next_step(p_donation_id, 2, p_beneficiaire_email, 'BP créé: ' || bp_number);

    RETURN bp_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR CRÉER UN BON DE SORTIE
-- =====================================================

CREATE OR REPLACE FUNCTION create_bon_sortie(
    p_donation_id UUID,
    p_magasinier_email TEXT,
    p_items_sortis JSONB
)
RETURNS TEXT AS $$
DECLARE
    bs_number TEXT;
    bs_id UUID;
    bp_id UUID;
BEGIN
    -- Récupérer l'ID du BP
    SELECT id INTO bp_id
    FROM gestion_donation_bp
    WHERE donation_id = p_donation_id
    LIMIT 1;

    -- Générer le numéro de BS
    bs_number := 'BS-' || EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                 LPAD((
                     SELECT COALESCE(MAX(CAST(SUBSTRING(numero_bs FROM 'BS-[0-9]{4}-(.*)') AS INTEGER)), 0) + 1
                     FROM gestion_donation_bs
                     WHERE numero_bs LIKE 'BS-' || EXTRACT(YEAR FROM CURRENT_DATE) || '-%'
                 )::TEXT, 4, '0');

    -- Créer le BS
    INSERT INTO gestion_donation_bs (
        donation_id,
        bp_id,
        numero_bs,
        magasinier_nom,
        items_sortis,
        quantite_totale,
        destination_service,
        destination_responsable,
        created_by
    )
    SELECT 
        p_donation_id,
        bp_id,
        bs_number,
        p_magasinier_email,
        p_items_sortis,
        (SELECT SUM((item->>'quantite')::integer) FROM jsonb_array_elements(p_items_sortis) item),
        d.demandeur_service,
        d.demandeur_nom,
        p_magasinier_email
    FROM gestion_donations d
    WHERE d.id = p_donation_id
    RETURNING id INTO bs_id;

    -- Mettre à jour le don
    UPDATE gestion_donations
    SET numero_bs = bs_number,
        date_bs = CURRENT_DATE,
        magasinier_responsable = p_magasinier_email
    WHERE id = p_donation_id;

    -- Avancer à l'étape suivante
    PERFORM advance_to_next_step(p_donation_id, 6, p_magasinier_email, 'BS créé: ' || bs_number);

    RETURN bs_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR CRÉER UN TABLEAU RÉCAPITULATIF
-- =====================================================

CREATE OR REPLACE FUNCTION create_tableau_recapitulatif(
    p_periode_debut DATE,
    p_periode_fin DATE,
    p_rve_email TEXT
)
RETURNS TEXT AS $$
DECLARE
    recap_number TEXT;
    recap_id UUID;
    donations_data JSONB;
    stats_data JSONB;
BEGIN
    -- Générer le numéro de récapitulatif
    recap_number := 'RECAP-' || EXTRACT(YEAR FROM p_periode_fin) || '-' || 
                    LPAD((
                        SELECT COALESCE(MAX(CAST(SUBSTRING(numero_recap FROM 'RECAP-[0-9]{4}-(.*)') AS INTEGER)), 0) + 1
                        FROM gestion_donation_recap
                        WHERE numero_recap LIKE 'RECAP-' || EXTRACT(YEAR FROM p_periode_fin) || '-%'
                    )::TEXT, 3, '0');

    -- Récupérer les dons de la période
    SELECT json_agg(
        json_build_object(
            'id', id,
            'numero_don', numero_don,
            'type_don', type_don,
            'donneur_nom', donneur_nom,
            'valeur_estimee', valeur_estimee,
            'date_archivage', date_archivage
        )
    ) INTO donations_data
    FROM gestion_donations
    WHERE date_archivage BETWEEN p_periode_debut AND p_periode_fin
    AND statut = 'archive';

    -- Calculer les statistiques
    SELECT json_build_object(
        'total_dons', COUNT(*),
        'valeur_totale', SUM(valeur_estimee),
        'par_type', json_build_object(
            'articles', COUNT(*) FILTER (WHERE type_don = 'article'),
            'equipements', COUNT(*) FILTER (WHERE type_don = 'equipement')
        )
    ) INTO stats_data
    FROM gestion_donations
    WHERE date_archivage BETWEEN p_periode_debut AND p_periode_fin
    AND statut = 'archive';

    -- Créer le récapitulatif
    INSERT INTO gestion_donation_recap (
        periode_debut,
        periode_fin,
        numero_recap,
        titre,
        donations_inclus,
        nombre_dons,
        valeur_totale,
        stats_par_type,
        rve_nom,
        created_by
    ) VALUES (
        p_periode_debut,
        p_periode_fin,
        recap_number,
        'Récapitulatif des dons du ' || p_periode_debut || ' au ' || p_periode_fin,
        donations_data,
        (stats_data->>'total_dons')::integer,
        (stats_data->>'valeur_totale')::decimal,
        stats_data->'par_type',
        p_rve_email,
        p_rve_email
    ) RETURNING id INTO recap_id;

    RETURN recap_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR ENVOYER À LA COMPTABILITÉ
-- =====================================================

CREATE OR REPLACE FUNCTION send_to_comptabilite(
    p_recap_id UUID,
    p_rve_email TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Marquer comme envoyé
    UPDATE gestion_donation_recap
    SET envoye_comptabilite = TRUE,
        date_envoi_comptabilite = NOW(),
        statut = 'envoye'
    WHERE id = p_recap_id;

    -- Mettre à jour tous les dons concernés
    UPDATE gestion_donations
    SET date_envoi_comptabilite = NOW(),
        rve_responsable = p_rve_email
    WHERE id IN (
        SELECT (don->>'id')::uuid
        FROM gestion_donation_recap r,
        jsonb_array_elements(r.donations_inclus) don
        WHERE r.id = p_recap_id
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR OBTENIR LE STATUT DÉTAILLÉ D'UN DON
-- =====================================================

CREATE OR REPLACE FUNCTION get_donation_detailed_status(p_donation_id UUID)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    SELECT json_build_object(
        'donation', json_build_object(
            'id', d.id,
            'numero_don', d.numero_don,
            'statut', d.statut,
            'type_don', d.type_don
        ),
        'etapes', (
            SELECT json_agg(
                json_build_object(
                    'numero', e.numero_etape,
                    'nom', e.nom_etape,
                    'responsable_role', e.responsable_role,
                    'responsable_nom', e.responsable_nom,
                    'statut', e.statut_etape,
                    'date_debut', e.date_debut,
                    'date_fin', e.date_fin,
                    'duree_prevue_heures', e.duree_prevue_heures,
                    'en_retard', CASE 
                        WHEN e.statut_etape = 'en_cours' 
                        AND e.date_debut + INTERVAL '1 hour' * e.duree_prevue_heures < NOW()
                        THEN true ELSE false END
                ) ORDER BY e.numero_etape
            )
            FROM gestion_donation_etapes e 
            WHERE e.donation_id = d.id
        ),
        'documents', json_build_object(
            'bp', (SELECT json_build_object('numero', numero_bp, 'statut', statut) 
                   FROM gestion_donation_bp WHERE donation_id = d.id LIMIT 1),
            'bs', (SELECT json_build_object('numero', numero_bs, 'statut', statut) 
                   FROM gestion_donation_bs WHERE donation_id = d.id LIMIT 1)
        )
    ) INTO result
    FROM gestion_donations d
    WHERE d.id = p_donation_id;

    RETURN result;
END;
$$ LANGUAGE plpgsql;
