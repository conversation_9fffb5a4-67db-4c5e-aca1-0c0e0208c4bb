-- Fonctions utilitaires pour le système de gestion des dons IPT

-- =====================================================
-- FONCTION POUR GÉNÉRER UN NUMÉRO DE DON UNIQUE
-- =====================================================

CREATE OR REPLACE FUNCTION generate_donation_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    sequence_num INTEGER;
    donation_number TEXT;
BEGIN
    current_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    -- Obtenir le prochain numéro de séquence pour l'année
    SELECT COALESCE(MAX(CAST(SUBSTRING(numero_don FROM 'DON-' || current_year || '-(.*)') AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM gestion_donations
    WHERE numero_don LIKE 'DON-' || current_year || '-%';
    
    -- Formater le numéro avec padding
    donation_number := 'DON-' || current_year || '-' || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN donation_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR GÉNÉRER UN NUMÉRO D'INVENTAIRE (NI)
-- =====================================================

CREATE OR REPLACE FUNCTION generate_inventory_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    sequence_num INTEGER;
    inventory_number TEXT;
BEGIN
    current_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    -- Obtenir le prochain numéro de séquence pour l'année
    SELECT COALESCE(MAX(CAST(SUBSTRING(numero_inventaire FROM 'NI-' || current_year || '-(.*)') AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM gestion_donation_items
    WHERE numero_inventaire LIKE 'NI-' || current_year || '-%'
    AND numero_inventaire IS NOT NULL;
    
    -- Formater le numéro avec padding
    inventory_number := 'NI-' || current_year || '-' || LPAD(sequence_num::TEXT, 6, '0');
    
    RETURN inventory_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR ENREGISTRER UNE ACTION WORKFLOW
-- =====================================================

CREATE OR REPLACE FUNCTION log_donation_workflow(
    p_donation_id UUID,
    p_etape TEXT,
    p_statut_precedent TEXT,
    p_statut_nouveau TEXT,
    p_acteur_nom TEXT,
    p_acteur_role TEXT,
    p_action TEXT,
    p_commentaire TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    workflow_id UUID;
BEGIN
    INSERT INTO gestion_donation_workflow (
        donation_id, etape, statut_precedent, statut_nouveau,
        acteur_nom, acteur_role, action, commentaire
    ) VALUES (
        p_donation_id, p_etape, p_statut_precedent, p_statut_nouveau,
        p_acteur_nom, p_acteur_role, p_action, p_commentaire
    ) RETURNING id INTO workflow_id;
    
    RETURN workflow_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR CRÉER UNE NOTIFICATION
-- =====================================================

CREATE OR REPLACE FUNCTION create_donation_notification(
    p_user_email TEXT,
    p_title TEXT,
    p_message TEXT,
    p_donation_id UUID DEFAULT NULL,
    p_type TEXT DEFAULT 'info'
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    -- Insérer dans la table notifications si elle existe
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
        INSERT INTO notifications (
            user_email, title, message, type, 
            related_table, related_id, created_at
        ) VALUES (
            p_user_email, p_title, p_message, p_type,
            'gestion_donations', p_donation_id, NOW()
        ) RETURNING id INTO notification_id;
    END IF;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR SOUMETTRE UN DON AU DG
-- =====================================================

CREATE OR REPLACE FUNCTION submit_donation_to_dg(
    p_donation_id UUID,
    p_user_email TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    donation_record RECORD;
    success BOOLEAN := FALSE;
BEGIN
    -- Vérifier que le don existe et peut être soumis
    SELECT * INTO donation_record
    FROM gestion_donations
    WHERE id = p_donation_id
    AND statut = 'brouillon'
    AND (created_by = p_user_email OR demandeur_email = p_user_email);
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Don non trouvé ou non modifiable';
    END IF;
    
    -- Vérifier que les documents requis sont présents
    IF NOT EXISTS (
        SELECT 1 FROM gestion_donation_documents
        WHERE donation_id = p_donation_id
        AND type_document IN ('demande_explicative', 'lettre_don')
    ) THEN
        RAISE EXCEPTION 'Documents requis manquants (demande explicative, lettre de don)';
    END IF;
    
    -- Mettre à jour le statut
    UPDATE gestion_donations
    SET statut = 'soumis_dg',
        date_soumission_dg = NOW(),
        updated_by = p_user_email
    WHERE id = p_donation_id;
    
    -- Enregistrer l'action dans le workflow
    PERFORM log_donation_workflow(
        p_donation_id, 'soumission_dg', 'brouillon', 'soumis_dg',
        p_user_email, 'demandeur', 'Soumission au DG',
        'Don soumis pour validation du Directeur Général'
    );
    
    -- Créer une notification pour le DG
    PERFORM create_donation_notification(
        '<EMAIL>', -- Email du DG
        'Nouveau don à valider',
        'Un nouveau don (' || donation_record.numero_don || ') nécessite votre validation.',
        p_donation_id,
        'pending'
    );
    
    success := TRUE;
    RETURN success;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors de la soumission: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR VALIDER UN DON PAR LE DG
-- =====================================================

CREATE OR REPLACE FUNCTION validate_donation_by_dg(
    p_donation_id UUID,
    p_user_email TEXT,
    p_avis TEXT,
    p_approve BOOLEAN
)
RETURNS BOOLEAN AS $$
DECLARE
    donation_record RECORD;
    new_status TEXT;
    success BOOLEAN := FALSE;
BEGIN
    -- Vérifier que le don existe et peut être validé
    SELECT * INTO donation_record
    FROM gestion_donations
    WHERE id = p_donation_id
    AND statut = 'soumis_dg';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Don non trouvé ou non validable';
    END IF;
    
    -- Déterminer le nouveau statut selon le type et l'approbation
    IF p_approve THEN
        IF donation_record.type_don = 'equipement' THEN
            new_status := 'avis_dt'; -- Les équipements nécessitent un avis technique DT
        ELSE
            new_status := 'soumis_ms'; -- Les articles vont directement au MS
        END IF;
    ELSE
        new_status := 'refuse_dg';
    END IF;
    
    -- Mettre à jour le don
    UPDATE gestion_donations
    SET statut = new_status,
        avis_dg = p_avis,
        approuve_par_dg = p_user_email,
        updated_by = p_user_email
    WHERE id = p_donation_id;
    
    -- Enregistrer l'action dans le workflow
    PERFORM log_donation_workflow(
        p_donation_id, 'validation_dg', 'soumis_dg', new_status,
        p_user_email, 'dg', 
        CASE WHEN p_approve THEN 'Validation DG' ELSE 'Refus DG' END,
        p_avis
    );
    
    -- Créer les notifications appropriées
    IF p_approve THEN
        IF donation_record.type_don = 'equipement' THEN
            -- Notifier la DT pour avis technique
            PERFORM create_donation_notification(
                '<EMAIL>',
                'Avis technique requis',
                'Un don d''équipement (' || donation_record.numero_don || ') nécessite votre avis technique.',
                p_donation_id,
                'pending'
            );
        ELSE
            -- Notifier le MS pour autorisation
            PERFORM create_donation_notification(
                '<EMAIL>',
                'Autorisation de don requise',
                'Un don d''article (' || donation_record.numero_don || ') nécessite votre autorisation.',
                p_donation_id,
                'pending'
            );
        END IF;
        
        -- Notifier le demandeur
        PERFORM create_donation_notification(
            donation_record.demandeur_email,
            'Don validé par le DG',
            'Votre don (' || donation_record.numero_don || ') a été validé par le Directeur Général.',
            p_donation_id,
            'success'
        );
    ELSE
        -- Notifier le demandeur du refus
        PERFORM create_donation_notification(
            donation_record.demandeur_email,
            'Don refusé',
            'Votre don (' || donation_record.numero_don || ') a été refusé par le Directeur Général.',
            p_donation_id,
            'error'
        );
    END IF;
    
    success := TRUE;
    RETURN success;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors de la validation: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR DONNER UN AVIS TECHNIQUE (DT)
-- =====================================================

CREATE OR REPLACE FUNCTION provide_technical_advice(
    p_donation_id UUID,
    p_user_email TEXT,
    p_avis TEXT,
    p_approve BOOLEAN
)
RETURNS BOOLEAN AS $$
DECLARE
    donation_record RECORD;
    new_status TEXT;
    success BOOLEAN := FALSE;
BEGIN
    -- Vérifier que le don existe et nécessite un avis technique
    SELECT * INTO donation_record
    FROM gestion_donations
    WHERE id = p_donation_id
    AND statut = 'avis_dt'
    AND type_don = 'equipement';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Don non trouvé ou avis technique non requis';
    END IF;
    
    -- Déterminer le nouveau statut
    new_status := CASE WHEN p_approve THEN 'soumis_ms' ELSE 'refuse_dt' END;
    
    -- Mettre à jour le don
    UPDATE gestion_donations
    SET statut = new_status,
        avis_dt = p_avis,
        avis_technique_par = p_user_email,
        date_avis_dt = NOW(),
        updated_by = p_user_email
    WHERE id = p_donation_id;
    
    -- Enregistrer l'action dans le workflow
    PERFORM log_donation_workflow(
        p_donation_id, 'avis_technique', 'avis_dt', new_status,
        p_user_email, 'dt',
        CASE WHEN p_approve THEN 'Avis technique favorable' ELSE 'Avis technique défavorable' END,
        p_avis
    );
    
    -- Créer les notifications
    IF p_approve THEN
        -- Notifier le MS
        PERFORM create_donation_notification(
            '<EMAIL>',
            'Autorisation de don requise',
            'Un don d''équipement (' || donation_record.numero_don || ') avec avis technique favorable nécessite votre autorisation.',
            p_donation_id,
            'pending'
        );
    END IF;
    
    -- Notifier le demandeur
    PERFORM create_donation_notification(
        donation_record.demandeur_email,
        CASE WHEN p_approve THEN 'Avis technique favorable' ELSE 'Avis technique défavorable' END,
        'L''avis technique pour votre don (' || donation_record.numero_don || ') a été donné.',
        p_donation_id,
        CASE WHEN p_approve THEN 'info' ELSE 'warning' END
    );
    
    success := TRUE;
    RETURN success;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors de l''avis technique: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR AUTORISER/REFUSER UN DON PAR LE MS
-- =====================================================

CREATE OR REPLACE FUNCTION authorize_donation_by_ms(
    p_donation_id UUID,
    p_user_email TEXT,
    p_decision TEXT,
    p_approve BOOLEAN
)
RETURNS BOOLEAN AS $$
DECLARE
    donation_record RECORD;
    new_status TEXT;
    success BOOLEAN := FALSE;
BEGIN
    -- Vérifier que le don existe et peut être autorisé
    SELECT * INTO donation_record
    FROM gestion_donations
    WHERE id = p_donation_id
    AND statut = 'soumis_ms';

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Don non trouvé ou non autorisable';
    END IF;

    -- Déterminer le nouveau statut
    new_status := CASE WHEN p_approve THEN 'approuve_ms' ELSE 'refuse_ms' END;

    -- Mettre à jour le don
    UPDATE gestion_donations
    SET statut = new_status,
        decision_ms = p_decision,
        decision_ms_par = p_user_email,
        date_decision_ms = NOW(),
        updated_by = p_user_email
    WHERE id = p_donation_id;

    -- Enregistrer l'action dans le workflow
    PERFORM log_donation_workflow(
        p_donation_id, 'autorisation_ms', 'soumis_ms', new_status,
        p_user_email, 'ms',
        CASE WHEN p_approve THEN 'Autorisation MS' ELSE 'Refus MS' END,
        p_decision
    );

    -- Créer les notifications
    IF p_approve THEN
        -- Notifier le demandeur et le donateur
        PERFORM create_donation_notification(
            donation_record.demandeur_email,
            'Don autorisé par le Ministère',
            'Votre don (' || donation_record.numero_don || ') a été autorisé. L''expédition peut commencer.',
            p_donation_id,
            'success'
        );

        -- Notifier les services logistiques
        PERFORM create_donation_notification(
            '<EMAIL>',
            'Don autorisé - Expédition',
            'Le don (' || donation_record.numero_don || ') est autorisé et peut être expédié.',
            p_donation_id,
            'info'
        );
    ELSE
        -- Notifier le demandeur du refus
        PERFORM create_donation_notification(
            donation_record.demandeur_email,
            'Don refusé par le Ministère',
            'Votre don (' || donation_record.numero_don || ') a été refusé par le Ministère de la Santé.',
            p_donation_id,
            'error'
        );
    END IF;

    success := TRUE;
    RETURN success;

EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors de l''autorisation: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;
