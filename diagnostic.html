<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic - Gestion Interne</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .diagnostic-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .diagnostic-section h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #6c757d;
        }
        
        .status-item.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .status-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .status-item.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .status-icon {
            font-size: 20px;
            font-weight: bold;
        }
        
        .status-text {
            flex: 1;
            margin: 0 15px;
        }
        
        .status-details {
            font-size: 12px;
            color: #6c757d;
        }
        
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }
        
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .test-results {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .summary {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .summary h2 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        
        .score {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        
        .actions {
            text-align: center;
            margin: 20px 0;
        }
        
        .back-link {
            display: inline-block;
            margin: 20px 0;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .back-link:hover {
            background: #218838;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Diagnostic de l'Application</h1>
        
        <div class="actions">
            <button onclick="runFullDiagnostic()">🚀 Lancer Diagnostic Complet</button>
            <button onclick="testLibraries()">📚 Tester Bibliothèques</button>
            <button onclick="testExports()">📄 Tester Exports</button>
            <button onclick="clearResults()">🗑️ Vider Résultats</button>
        </div>
        
        <!-- Résumé -->
        <div id="summary" class="summary" style="display: none;">
            <h2>📊 Résumé du Diagnostic</h2>
            <div id="scoreDisplay" class="score"></div>
            <div id="summaryText"></div>
        </div>
        
        <!-- Bibliothèques -->
        <div class="diagnostic-section">
            <h3>📚 Bibliothèques Externes</h3>
            <div id="librariesStatus">
                <p>Cliquez sur "Tester Bibliothèques" pour vérifier le chargement...</p>
            </div>
        </div>
        
        <!-- Configuration -->
        <div class="diagnostic-section">
            <h3>⚙️ Configuration</h3>
            <div id="configStatus">
                <p>Vérification de la configuration...</p>
            </div>
        </div>
        
        <!-- Fonctionnalités -->
        <div class="diagnostic-section">
            <h3>🔧 Fonctionnalités</h3>
            <div id="featuresStatus">
                <p>Test des fonctionnalités en cours...</p>
            </div>
        </div>
        
        <!-- Résultats des tests -->
        <div class="diagnostic-section">
            <h3>📋 Résultats des Tests</h3>
            <div id="testResults" class="test-results">
                Aucun test exécuté pour le moment.
            </div>
        </div>
        
        <div class="actions">
            <a href="index.html" class="back-link">← Retour à l'Application</a>
            <a href="test-supabase.html" class="back-link">🧪 Tests Supabase</a>
        </div>
    </div>

    <!-- Chargement des mêmes bibliothèques que l'application principale -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"
            integrity="sha512-r22gChDnGvBylk90+2e/ycr3RVrDi8DIOkIGNhJlKfuyQM4tIRAI062MaV8sfjQKYVGjOBaZBOA87z+IhZE9DA=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            integrity="sha512-qZvrmS2ekKPF2mSznTQsxqPgnpkI4DNTlrdUmTzrDgektczlKNRRhy5X5AAOnx5S09ydFYWWNSfcEqDTTHgtNA=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"
            integrity="sha512-Hkz9icFKZhBsHqLcz0t0XE8Cco3MmZGlTqGFxzn/zWX/MmZAa8RnEElJVaIahxpMmJrf/4zYzJuqUjqwVnCuQ=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"
            crossorigin="anonymous"></script>

    <!-- Configuration (dans le même ordre que l'application principale) -->
    <script src="assets/config/app-config.js"></script>
    <script src="assets/config/supabase-config.js"></script>
    
    <script>
        let diagnosticResults = {
            libraries: {},
            config: {},
            features: {},
            exports: {}
        };

        // Fonction de logging
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'success' ? '#28a745' : 
                                  type === 'error' ? '#dc3545' : 
                                  type === 'warning' ? '#ffc107' : '#6c757d';
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            resultsDiv.appendChild(logEntry);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Créer un élément de statut
        function createStatusItem(name, status, details = '') {
            const item = document.createElement('div');
            item.className = `status-item ${status}`;
            
            const icon = status === 'success' ? '✅' : 
                        status === 'error' ? '❌' : 
                        status === 'warning' ? '⚠️' : '❓';
            
            item.innerHTML = `
                <div class="status-icon">${icon}</div>
                <div class="status-text">
                    <strong>${name}</strong>
                    ${details ? `<div class="status-details">${details}</div>` : ''}
                </div>
            `;
            
            return item;
        }

        // Test des bibliothèques
        function testLibraries() {
            log('🔍 Test des bibliothèques externes...', 'info');
            
            const libraries = {
                'XLSX.js': {
                    available: typeof XLSX !== 'undefined',
                    details: typeof XLSX !== 'undefined' ? `Version: ${XLSX.version || 'Inconnue'}` : 'Non chargé'
                },
                'jsPDF': {
                    available: typeof jsPDF !== 'undefined' || typeof window.jsPDF !== 'undefined',
                    details: 'Bibliothèque de génération PDF'
                },
                'jsPDF AutoTable': {
                    available: typeof window.jsPDF !== 'undefined' && window.jsPDF.autoTable !== 'undefined',
                    details: 'Plugin pour tableaux PDF'
                },
                'Supabase SDK': {
                    available: typeof supabase !== 'undefined' || typeof window.supabase !== 'undefined',
                    details: 'Client JavaScript pour Supabase'
                },
                'Font Awesome': {
                    available: document.querySelector('link[href*="font-awesome"]') !== null,
                    details: 'Icônes vectorielles'
                }
            };

            const statusDiv = document.getElementById('librariesStatus');
            statusDiv.innerHTML = '';

            Object.entries(libraries).forEach(([name, info]) => {
                const status = info.available ? 'success' : 'error';
                const statusText = info.available ? 'Chargé' : 'Non disponible';
                const item = createStatusItem(`${name}: ${statusText}`, status, info.details);
                statusDiv.appendChild(item);
                
                diagnosticResults.libraries[name] = info.available;
                log(`${info.available ? '✅' : '❌'} ${name}: ${statusText}`, info.available ? 'success' : 'error');
            });

            updateSummary();
        }

        // Test de la configuration
        function testConfig() {
            log('⚙️ Test de la configuration...', 'info');

            const configs = {
                'APP_CONFIG': {
                    available: typeof APP_CONFIG !== 'undefined' && APP_CONFIG !== null,
                    details: typeof APP_CONFIG !== 'undefined' ?
                        `Version: ${APP_CONFIG.app?.version || 'Inconnue'}, Rôles: ${Object.keys(APP_CONFIG.roles || {}).length}` :
                        'Configuration principale de l\'application non chargée'
                },
                'SUPABASE_CONFIG': {
                    available: typeof SUPABASE_CONFIG !== 'undefined' && SUPABASE_CONFIG !== null,
                    details: typeof SUPABASE_CONFIG !== 'undefined' ?
                        `URL: ${SUPABASE_CONFIG.url ? 'Configurée' : 'Non configurée'}` :
                        'Configuration Supabase non chargée'
                },
                'LocalStorage': {
                    available: typeof localStorage !== 'undefined' && localStorage !== null,
                    details: 'Stockage local du navigateur'
                },
                'SessionStorage': {
                    available: typeof sessionStorage !== 'undefined' && sessionStorage !== null,
                    details: 'Stockage de session du navigateur'
                }
            };

            const statusDiv = document.getElementById('configStatus');
            statusDiv.innerHTML = '';

            Object.entries(configs).forEach(([name, info]) => {
                const status = info.available ? 'success' : 'error';
                const statusText = info.available ? 'Disponible' : 'Non disponible';
                const item = createStatusItem(`${name}: ${statusText}`, status, info.details);
                statusDiv.appendChild(item);
                
                diagnosticResults.config[name] = info.available;
                log(`${info.available ? '✅' : '❌'} ${name}: ${statusText}`, info.available ? 'success' : 'error');
            });
        }

        // Test des fonctionnalités
        function testFeatures() {
            log('🔧 Test des fonctionnalités...', 'info');
            
            const features = {
                'Blob API': {
                    available: typeof Blob !== 'undefined',
                    details: 'Nécessaire pour les exports de fichiers'
                },
                'URL.createObjectURL': {
                    available: typeof URL !== 'undefined' && typeof URL.createObjectURL === 'function',
                    details: 'Nécessaire pour le téléchargement de fichiers'
                },
                'Fetch API': {
                    available: typeof fetch !== 'undefined',
                    details: 'API moderne pour les requêtes HTTP'
                },
                'Promises': {
                    available: typeof Promise !== 'undefined',
                    details: 'Support des promesses JavaScript'
                },
                'ES6 Classes': {
                    available: (function() {
                        try {
                            eval('class TestClass {}');
                            return true;
                        } catch (e) {
                            return false;
                        }
                    })(),
                    details: 'Support des classes ES6'
                }
            };

            const statusDiv = document.getElementById('featuresStatus');
            statusDiv.innerHTML = '';

            Object.entries(features).forEach(([name, info]) => {
                const status = info.available ? 'success' : 'error';
                const statusText = info.available ? 'Supporté' : 'Non supporté';
                const item = createStatusItem(`${name}: ${statusText}`, status, info.details);
                statusDiv.appendChild(item);
                
                diagnosticResults.features[name] = info.available;
                log(`${info.available ? '✅' : '❌'} ${name}: ${statusText}`, info.available ? 'success' : 'error');
            });
        }

        // Test des exports
        function testExports() {
            log('📄 Test des fonctionnalités d\'export...', 'info');
            
            const testData = [
                { nom: 'Test 1', valeur: 123, date: '2024-01-01' },
                { nom: 'Test 2', valeur: 456, date: '2024-01-02' }
            ];

            // Test export JSON
            try {
                const jsonData = JSON.stringify(testData, null, 2);
                diagnosticResults.exports['JSON'] = true;
                log('✅ Export JSON: Fonctionnel', 'success');
            } catch (error) {
                diagnosticResults.exports['JSON'] = false;
                log('❌ Export JSON: Erreur', 'error');
            }

            // Test export CSV
            try {
                const headers = Object.keys(testData[0]);
                let csvContent = headers.join(',') + '\n';
                testData.forEach(row => {
                    csvContent += headers.map(h => row[h]).join(',') + '\n';
                });
                diagnosticResults.exports['CSV'] = true;
                log('✅ Export CSV: Fonctionnel', 'success');
            } catch (error) {
                diagnosticResults.exports['CSV'] = false;
                log('❌ Export CSV: Erreur', 'error');
            }

            // Test export Excel
            if (typeof XLSX !== 'undefined') {
                try {
                    const ws = XLSX.utils.json_to_sheet(testData);
                    const wb = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(wb, ws, 'Test');
                    diagnosticResults.exports['Excel'] = true;
                    log('✅ Export Excel: Fonctionnel', 'success');
                } catch (error) {
                    diagnosticResults.exports['Excel'] = false;
                    log('❌ Export Excel: Erreur', 'error');
                }
            } else {
                diagnosticResults.exports['Excel'] = false;
                log('⚠️ Export Excel: XLSX.js non disponible', 'warning');
            }

            // Test export PDF
            if (typeof jsPDF !== 'undefined' || typeof window.jsPDF !== 'undefined') {
                try {
                    const jsPDFLib = typeof jsPDF !== 'undefined' ? jsPDF : window.jsPDF;
                    const doc = new jsPDFLib.jsPDF();
                    doc.text('Test PDF', 20, 20);
                    diagnosticResults.exports['PDF'] = true;
                    log('✅ Export PDF: Fonctionnel', 'success');
                } catch (error) {
                    diagnosticResults.exports['PDF'] = false;
                    log('❌ Export PDF: Erreur', 'error');
                }
            } else {
                diagnosticResults.exports['PDF'] = false;
                log('⚠️ Export PDF: jsPDF non disponible', 'warning');
            }

            updateSummary();
        }

        // Mettre à jour le résumé
        function updateSummary() {
            const allResults = {
                ...diagnosticResults.libraries,
                ...diagnosticResults.config,
                ...diagnosticResults.features,
                ...diagnosticResults.exports
            };

            const totalTests = Object.keys(allResults).length;
            const passedTests = Object.values(allResults).filter(Boolean).length;
            const score = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;

            const summaryDiv = document.getElementById('summary');
            const scoreDiv = document.getElementById('scoreDisplay');
            const textDiv = document.getElementById('summaryText');

            scoreDiv.textContent = `${score}% (${passedTests}/${totalTests})`;
            
            let status = '';
            if (score >= 90) {
                status = '🟢 Excellent - Toutes les fonctionnalités sont disponibles';
                scoreDiv.style.color = '#28a745';
            } else if (score >= 70) {
                status = '🟡 Bon - La plupart des fonctionnalités sont disponibles';
                scoreDiv.style.color = '#ffc107';
            } else {
                status = '🔴 Problèmes détectés - Certaines fonctionnalités peuvent ne pas fonctionner';
                scoreDiv.style.color = '#dc3545';
            }

            textDiv.textContent = status;
            summaryDiv.style.display = 'block';

            log(`📊 Score final: ${score}% (${passedTests}/${totalTests} tests réussis)`, 'info');
        }

        // Diagnostic complet
        function runFullDiagnostic() {
            log('🚀 Démarrage du diagnostic complet...', 'info');
            clearResults();
            
            setTimeout(() => {
                testLibraries();
                setTimeout(() => {
                    testConfig();
                    setTimeout(() => {
                        testFeatures();
                        setTimeout(() => {
                            testExports();
                            log('✅ Diagnostic complet terminé!', 'success');
                        }, 500);
                    }, 500);
                }, 500);
            }, 100);
        }

        // Vider les résultats
        function clearResults() {
            document.getElementById('testResults').innerHTML = 'Résultats vidés.';
            document.getElementById('summary').style.display = 'none';
            diagnosticResults = { libraries: {}, config: {}, features: {}, exports: {} };
        }

        // Fonction pour attendre le chargement des configurations
        function waitForConfigs(maxWait = 5000) {
            return new Promise((resolve) => {
                const startTime = Date.now();

                function check() {
                    const configsLoaded = typeof APP_CONFIG !== 'undefined' &&
                                         typeof SUPABASE_CONFIG !== 'undefined';

                    if (configsLoaded || (Date.now() - startTime) > maxWait) {
                        resolve(configsLoaded);
                    } else {
                        setTimeout(check, 100);
                    }
                }

                check();
            });
        }

        // Initialisation au chargement
        document.addEventListener('DOMContentLoaded', async function() {
            log('🔧 Page de diagnostic chargée', 'info');

            // Attendre le chargement des configurations
            log('⏳ Attente du chargement des configurations...', 'info');
            const configsLoaded = await waitForConfigs(5000);

            if (configsLoaded) {
                log('✅ Configurations chargées avec succès', 'success');
            } else {
                log('⚠️ Timeout - Certaines configurations peuvent ne pas être chargées', 'warning');
            }

            // Lancer les tests de base
            setTimeout(() => {
                testConfig();
                testFeatures();
            }, 500);
        });
    </script>
</body>
</html>
