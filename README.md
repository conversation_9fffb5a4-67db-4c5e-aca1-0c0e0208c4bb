# 🏢 Application de Gestion Interne IPT

## 📋 Description

Application web monopage (SPA) complète en français pour la gestion interne de l'Institut Pasteur de Tunis (IPT), incluant :
- 💬 **Messagerie interne** avec partage de fichiers
- 📦 **Gestion des commandes fournisseurs** avec workflow en 3 étapes
- 📋 **Gestion des PV (Procès-Verbaux) de dépôts** avec statuts produits
- 💱 **Convertisseur de devises** en temps réel
- 📊 **Analyse des messages** avec recommandations

## 🚀 Fonctionnalités Principales

### 🔐 Système d'Authentification
- **6 rôles différents** : Ad<PERSON>, Manager, Finance, User, Dépôt, Hygiène
- **Permissions granulaires** selon le rôle
- **Interface adaptative** qui masque/affiche les fonctionnalités selon les droits

### 💬 Messagerie Avancée
- **Messages privés** et diffusion générale
- **Partage de fichiers** avec prévisualisation
- **Messages système** automatiques
- **Historique persistant** des conversations
- **Suppression de messages** (admin uniquement)

### 📦 Gestion des Commandes
- **Workflow en 3 étapes** : Informations → Produits → Finalisation
- **Validation progressive** avec déblocage des étapes
- **Calcul automatique** des montants totaux
- **Support multi-devises** (EUR, USD, GBP, JPY)
- **Simulation d'envoi d'emails** aux fournisseurs
- **Export PDF/Excel** des commandes

### 📋 Gestion des PV Dépôts
- **Interface dépôt** : Création de PV avec sélection de produits
- **Gestion des statuts** : Conforme, Non conforme, À vérifier
- **Interface admin/hygiène** : Consultation et recherche des PV
- **Export des rapports** en PDF et Excel
- **Notifications automatiques** lors de création

## 🛠️ Technologies Utilisées

### Frontend
- **HTML5** - Structure sémantique moderne
- **CSS3** - Variables CSS, Flexbox, Grid, animations
- **JavaScript ES6+** - Modules, async/await, classes
- **Design responsive** - Mobile-first approach

### Backend & Base de Données
- **Supabase** - Base de données PostgreSQL temps réel
- **Row Level Security (RLS)** - Sécurité au niveau des données
- **Fallback localStorage** - Mode hors ligne automatique

### Bibliothèques Externes
- **XLSX.js** - Export Excel avancé
- **jsPDF + AutoTable** - Génération PDF avec tableaux
- **Supabase JS SDK** - Client JavaScript pour Supabase

## 📁 Structure du Projet

```
gestion-interne-app/
├── index.html                          # Page principale
├── assets/
│   ├── css/
│   │   └── style.css                   # Styles complets
│   ├── js/
│   │   └── script.js                   # Logique principale
│   └── config/
│       ├── supabase-config.js          # Configuration Supabase
│       └── app-config.js               # Configuration application
├── docs/
└── README.md                           # Documentation
```

## 🔧 Installation et Configuration

### 1. Prérequis
- Navigateur web moderne (Chrome, Firefox, Safari, Edge)
- Serveur web local (optionnel mais recommandé)

### 2. Installation Simple
```bash
# Cloner ou télécharger le projet
cd gestion-interne-app

# Lancer un serveur local (optionnel)
python -m http.server 8000
# ou
npx serve .
# ou
php -S localhost:8000
```

### 3. Configuration Supabase (Intégration Complète)

L'application est maintenant **entièrement intégrée avec Supabase** avec fallback automatique en mode local :

#### 🔧 Configuration Automatique
1. **Créer un projet Supabase** sur [supabase.com](https://supabase.com)
2. **Récupérer les clés** : URL du projet et clé anonyme
3. **Mettre à jour** `assets/config/supabase-config.js` avec vos clés :
   ```javascript
   const SUPABASE_CONFIG = {
       url: 'https://votre-projet.supabase.co',
       anonKey: 'votre-cle-anonyme'
   };
   ```
4. **Exécuter le schema SQL** dans l'éditeur SQL de Supabase (voir `supabase-schema.sql`)

#### 🔄 Fonctionnalités Supabase
- **Synchronisation temps réel** des messages, commandes et PV
- **Sauvegarde automatique** vers Supabase avec fallback localStorage
- **Indicateur de statut** de connexion en temps réel
- **Mode hors ligne** automatique si Supabase indisponible
- **Synchronisation manuelle** via bouton dans l'interface

#### 📊 Avantages de l'Intégration
- **Données partagées** entre tous les utilisateurs
- **Sauvegarde cloud** automatique
- **Synchronisation multi-appareils**
- **Performance optimisée** avec cache local
- **Robustesse** avec mode de fallback

## 🗄️ Structure de Base de Données

### Tables Supabase (Nouveau Schema)

L'application utilise maintenant un schema optimisé avec les tables suivantes :

#### 📋 Tables Principales
- **`gestion_users`** : Utilisateurs avec rôles et permissions
- **`gestion_messages`** : Messages avec support fichiers (Base64)
- **`gestion_commands`** : Commandes fournisseurs avec workflow
- **`gestion_pv`** : Procès-verbaux avec produits JSON
- **`gestion_products`** : Référentiel produits

#### 🔧 Création Automatique
Le fichier `supabase-schema.sql` contient le script complet pour créer :
- ✅ **Tables** avec contraintes et index
- ✅ **Triggers** pour `updated_at` automatique
- ✅ **Vues** pour les statistiques
- ✅ **Commentaires** de documentation

#### 📊 Exemple de Structure
```sql
-- Table des messages (exemple)
CREATE TABLE gestion_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_username TEXT NOT NULL,
    sender_name TEXT NOT NULL,
    recipient TEXT NOT NULL,
    content TEXT NOT NULL,
    file_name TEXT,
    file_data TEXT, -- Base64 encoded
    file_type TEXT,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 🚀 Installation Rapide
1. Copier le contenu de `supabase-schema.sql`
2. Coller dans l'éditeur SQL de Supabase
3. Exécuter le script
4. Les tables sont créées automatiquement !

## 👥 Comptes Utilisateurs Réels

### 📊 Statistiques
- **Total :** Plus de 80 comptes utilisateurs
- **Répartition :** 1 Admin, 2 Managers, 3 Finance, 17 Utilisateurs, 2 Hygiène, 55+ Dépôts

### 🔑 Comptes Principaux (Exemples)

| Utilisateur | Mot de passe | Rôle | Fonctionnalités |
|-------------|--------------|------|-----------------|
| `admin123` | `admin123*+` | Admin | 🔴 Accès complet, suppression données |
| `rym` | `rym` | Manager | 🟢 Commandes + analyse messages |
| `riadh` | `riadh` | Manager | 🟢 Commandes + analyse messages |
| `besma` | `besma` | Finance | 🟡 Consultation commandes fournisseurs |
| `moufida` | `moufida` | Finance | 🟡 Consultation commandes fournisseurs |
| `ichark` | `ichark` | Finance | 🟡 Consultation commandes fournisseurs |
| `rsoltani` | `rsoltani` | User | 🔵 Messagerie + convertisseur |
| `namara` | `namara` | User | 🔵 Messagerie + convertisseur |
| `bjlassi` | `bjlassi` | User | 🔵 Messagerie + convertisseur |
| `yosra` | `yosra2025*+` | Hygiène | 🟠 Consultation PV + rapports |
| `moncef` | `moncef2025*+` | Hygiène | 🟠 Consultation PV + rapports |
| `10` | `10012025*+` | Dépôt | 🟣 Création et gestion PV |
| `43` | `43022025*+` | Dépôt | 🟣 Création et gestion PV |
| `...` | `...` | Dépôt | 🟣 Dépôts 44 à 94 disponibles |

### 📋 Liste Complète

**👑 Admin :** admin123
**👔 Managers :** rym, riadh
**💰 Finance :** besma, moufida, ichark
**👤 Users :** rsoltani, namara, bjlassi, syousfi, mbaissa, sghouili, ghaith, issam, bbeji, ichikaoui, sdouagi, mhelal, skoudhai, asediri, ssalama, zboubaker, invite
**🧼 Hygiène :** yosra, moncef
**📦 Dépôts :** 10, 43-94 (55 dépôts au total)

## 🎯 Guide d'Utilisation

### 🔐 Connexion
1. Ouvrir `index.html` dans un navigateur
2. **Vérifier le statut Supabase** (indicateur en haut à droite)
3. Cliquer sur un compte de test ou saisir manuellement
4. L'interface s'adapte automatiquement selon le rôle

### 🔄 Utilisation Supabase
1. **Indicateur de statut** :
   - 🟢 **Connecté** : Données synchronisées avec Supabase
   - 🟡 **Mode local** : Données stockées localement
   - 🔴 **Erreur** : Problème de connexion

2. **Synchronisation automatique** :
   - Messages envoyés → Sauvegarde Supabase + local
   - Commandes créées → Synchronisation automatique
   - PV générés → Sauvegarde cloud instantanée

3. **Synchronisation manuelle** :
   - Cliquer sur l'icône de synchronisation
   - Force la synchronisation de toutes les données locales
   - Utile après une reconnexion

### 💬 Messagerie
1. Sélectionner un destinataire (ou "Tous")
2. Taper le message et/ou joindre des fichiers
3. Appuyer sur "Envoyer" ou touche Entrée

### 📦 Commandes (Manager/Finance)
1. Cliquer sur "Gestion Commandes"
2. "Nouvelle Commande" → Remplir les 3 étapes
3. Valider chaque étape pour débloquer la suivante
4. Soumettre la commande finale

### 📋 PV Dépôts
**Pour les dépôts :**
1. Cliquer sur "Gestion PV Dépôts"
2. Sélectionner les produits à contrôler
3. Définir statuts et quantités
4. "Créer PV"

**Pour admin/hygiène :**
1. Consultation automatique de tous les PV
2. Recherche et filtrage disponibles
3. Export PDF/Excel des rapports

## 🔒 Sécurité

### Fonctionnalités de Sécurité
- **Authentification par rôles** avec permissions granulaires
- **Validation côté client** et serveur
- **Row Level Security (RLS)** avec Supabase
- **Gestion des sessions** avec timeout automatique
- **Validation des fichiers** (taille, type)

### Mode Hors Ligne
- **Fallback automatique** vers localStorage si Supabase indisponible
- **Synchronisation** lors de la reconnexion
- **Indicateur de statut** de connexion

## 📱 Compatibilité

### Navigateurs Supportés
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### Appareils
- 💻 **Desktop** - Expérience complète
- 📱 **Mobile** - Interface adaptée, toutes fonctionnalités
- 📱 **Tablette** - Mise en page optimisée

## 🚀 Déploiement

### Déploiement Simple
1. **Hébergement statique** : Netlify, Vercel, GitHub Pages
2. **Upload des fichiers** dans le dossier `gestion-interne-app/`
3. **Configuration Supabase** si nécessaire

### Déploiement Avancé
1. **Serveur web** : Apache, Nginx
2. **HTTPS obligatoire** pour Supabase en production
3. **Variables d'environnement** pour les clés API

## 🔧 Personnalisation

### Thèmes et Couleurs
- Modifier les variables CSS dans `assets/css/style.css`
- Personnaliser les couleurs des rôles dans `app-config.js`

### Fonctionnalités
- Activer/désactiver des modules dans `app-config.js`
- Ajouter de nouveaux rôles et permissions
- Personnaliser les limites (taille fichiers, etc.)

## 📞 Support

### Problèmes Courants
- **Supabase indisponible** → Mode local automatique
- **Fichiers trop volumineux** → Vérifier les limites
- **Permissions insuffisantes** → Vérifier le rôle utilisateur

### Logs et Débogage
- Ouvrir la console développeur (F12)
- Vérifier les messages de log
- Tester en mode incognito

---

**Développé avec ❤️ pour la gestion interne d'entreprise**

*Version 1.0.0 - Application complète avec intégration Supabase*
