<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tables Étendues - Supabase</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            margin-right: 8px;
        }
        
        .status-indicator.connected {
            background: #28a745;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #667eea;
        }
        
        .test-panel h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            margin: 5px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .data-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            margin-top: 15px;
        }
        
        .data-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }
        
        .realtime-item {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .data-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-weight: bold;
        }
        
        .data-content {
            color: #666;
            margin-bottom: 10px;
        }
        
        .data-meta {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: #888;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Tables Étendues</h1>
            <p>Test des nouvelles tables avec synchronisation temps réel</p>
        </div>

        <!-- Barre de statut -->
        <div class="status-bar">
            <div style="display: flex; align-items: center;">
                <div class="status-indicator" id="connectionStatus"></div>
                <span id="connectionText">Déconnecté</span>
            </div>
            <div id="userInfo">Non connecté</div>
            <div id="syncStats">0 événements synchronisés</div>
            <button class="btn" onclick="refreshConnection()">🔄 Actualiser</button>
        </div>

        <!-- Statistiques -->
        <div class="stats-grid" id="statsGrid">
            <!-- Les statistiques seront générées dynamiquement -->
        </div>

        <!-- Grille de test -->
        <div class="test-grid">
            <!-- Panel Messages V2 -->
            <div class="test-panel">
                <h3>📧 Messages V2</h3>
                
                <div class="form-group">
                    <label>Destinataire:</label>
                    <input type="email" id="messageRecipient" placeholder="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label>Contenu:</label>
                    <textarea id="messageContent" placeholder="Contenu du message"></textarea>
                </div>
                
                <button class="btn success" onclick="createMessageV2()">📧 Envoyer Message V2</button>
                <button class="btn" onclick="loadMessagesV2()">🔄 Charger Messages V2</button>
                
                <div class="data-list" id="messagesV2List">
                    <p style="text-align: center; color: #666;">Aucun message V2</p>
                </div>
            </div>

            <!-- Panel Commandes Étendues -->
            <div class="test-panel">
                <h3>📦 Commandes Étendues</h3>
                
                <div class="form-group">
                    <label>Fournisseur:</label>
                    <input type="text" id="commandSupplier" placeholder="Nom du fournisseur">
                </div>
                
                <div class="form-group">
                    <label>Total TTC:</label>
                    <input type="number" id="commandTotal" placeholder="0.00" step="0.01">
                </div>
                
                <div class="form-group">
                    <label>Observations:</label>
                    <textarea id="commandObservations" placeholder="Observations"></textarea>
                </div>
                
                <button class="btn success" onclick="createCommandExtended()">📦 Créer Commande Étendue</button>
                <button class="btn" onclick="loadCommandsExtended()">🔄 Charger Commandes</button>
                
                <div class="data-list" id="commandsExtendedList">
                    <p style="text-align: center; color: #666;">Aucune commande étendue</p>
                </div>
            </div>

            <!-- Panel PV Détaillés -->
            <div class="test-panel">
                <h3>📄 PV Détaillés</h3>
                
                <div class="form-group">
                    <label>Type PV:</label>
                    <select id="pvType">
                        <option value="depot">Dépôt</option>
                        <option value="controle_qualite">Contrôle Qualité</option>
                        <option value="inventaire">Inventaire</option>
                        <option value="reception">Réception</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label>Dépôt:</label>
                    <input type="text" id="pvDepot" placeholder="Nom du dépôt">
                </div>
                
                <div class="form-group">
                    <label>Responsable:</label>
                    <input type="text" id="pvResponsable" placeholder="Nom du responsable">
                </div>
                
                <button class="btn success" onclick="createPVDetailed()">📄 Créer PV Détaillé</button>
                <button class="btn" onclick="loadPVDetailed()">🔄 Charger PV</button>
                
                <div class="data-list" id="pvDetailedList">
                    <p style="text-align: center; color: #666;">Aucun PV détaillé</p>
                </div>
            </div>

            <!-- Panel Produits Détaillés -->
            <div class="test-panel">
                <h3>🛍️ Produits Détaillés</h3>
                
                <div class="form-group">
                    <label>Nom:</label>
                    <input type="text" id="productName" placeholder="Nom du produit">
                </div>
                
                <div class="form-group">
                    <label>Catégorie:</label>
                    <input type="text" id="productCategory" placeholder="Catégorie">
                </div>
                
                <div class="form-group">
                    <label>Prix unitaire:</label>
                    <input type="number" id="productPrice" placeholder="0.00" step="0.01">
                </div>
                
                <button class="btn success" onclick="createProductDetailed()">🛍️ Créer Produit Détaillé</button>
                <button class="btn" onclick="loadProductsDetailed()">🔄 Charger Produits</button>
                
                <div class="data-list" id="productsDetailedList">
                    <p style="text-align: center; color: #666;">Aucun produit détaillé</p>
                </div>
            </div>
        </div>

        <!-- Actions de test -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn" onclick="testAllExtendedTables()">🧪 Tester Toutes les Tables</button>
            <button class="btn" onclick="loadExtendedStats()">📊 Charger Statistiques Étendues</button>
            <button class="btn danger" onclick="clearExtendedData()">🗑️ Vider Données de Test</button>
        </div>

        <!-- Logs -->
        <div class="log-container" id="testLog"></div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth-complete.js"></script>
    <script src="assets/js/supabase-realtime-complete.js"></script>
    <script src="assets/js/supabase-system-complete.js"></script>

    <script>
        // Variables globales
        let supabaseSystem = null;
        let currentUser = null;
        let syncStats = {
            messagesV2: 0,
            commandsExtended: 0,
            pvDetailed: 0,
            productsDetailed: 0,
            totalEvents: 0
        };

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', async function() {
            log('🚀 Initialisation du test des tables étendues...', 'info');

            // Initialiser le système Supabase
            await initializeSystem();

            // Configurer les événements
            setupEventListeners();

            // Charger les données initiales
            setTimeout(() => {
                if (supabaseSystem) {
                    loadAllExtendedData();
                    loadExtendedStats();
                }
            }, 2000);
        });

        // Initialiser le système Supabase
        async function initializeSystem() {
            try {
                log('🔧 Initialisation du système Supabase...', 'info');

                supabaseSystem = await initializeSupabaseComplete();

                if (supabaseSystem) {
                    log('✅ Système Supabase initialisé', 'success');

                    // Configurer les callbacks
                    setupSystemCallbacks();
                    updateConnectionStatus('connected', 'Système prêt');

                    // Obtenir l'utilisateur actuel
                    const authModule = supabaseSystem.getModule('auth');
                    if (authModule) {
                        const userInfo = authModule.getUser();
                        currentUser = userInfo.user;
                        updateUserInfo(userInfo.profile);
                    }

                } else {
                    throw new Error('Échec initialisation système');
                }

            } catch (error) {
                log(`❌ Erreur initialisation: ${error.message}`, 'error');
                updateConnectionStatus('error', 'Erreur d\'initialisation');
            }
        }

        // Configurer les callbacks du système
        function setupSystemCallbacks() {
            const system = getSupabaseSystem();
            if (!system) return;

            // Callbacks d'authentification
            system.on('onAuthStateChange', (data) => {
                if (data.type === 'signIn') {
                    currentUser = data.data.user;
                    updateUserInfo(data.data.profile);
                    log(`👤 Utilisateur connecté: ${data.data.profile?.nom || 'Utilisateur'}`, 'success');
                }
            });

            // Callbacks de données
            system.on('onDataChange', (data) => {
                handleRealtimeDataChange(data);
            });
        }

        // Gérer les changements de données temps réel
        function handleRealtimeDataChange(data) {
            log(`🔄 Changement temps réel: ${data.table} (${data.eventType})`, 'info');

            syncStats.totalEvents++;

            switch (data.table) {
                case 'gestion_messages_v2':
                    syncStats.messagesV2++;
                    handleMessagesV2Change(data);
                    break;
                case 'gestion_commands_extended':
                    syncStats.commandsExtended++;
                    handleCommandsExtendedChange(data);
                    break;
                case 'gestion_pv_detailed':
                    syncStats.pvDetailed++;
                    handlePVDetailedChange(data);
                    break;
                case 'gestion_products_detailed':
                    syncStats.productsDetailed++;
                    handleProductsDetailedChange(data);
                    break;
            }

            updateSyncStats();
        }

        // Configurer les événements DOM
        function setupEventListeners() {
            // Écouter les événements personnalisés pour les nouvelles tables
            document.addEventListener('gestion_messages_v2Update', (e) => {
                log(`📧 Événement DOM: Message V2 ${e.detail.eventType}`, 'info');
            });

            document.addEventListener('gestion_commands_extendedUpdate', (e) => {
                log(`📦 Événement DOM: Commande étendue ${e.detail.eventType}`, 'info');
            });

            document.addEventListener('gestion_pv_detailedUpdate', (e) => {
                log(`📄 Événement DOM: PV détaillé ${e.detail.eventType}`, 'info');
            });

            document.addEventListener('gestion_products_detailedUpdate', (e) => {
                log(`🛍️ Événement DOM: Produit détaillé ${e.detail.eventType}`, 'info');
            });
        }

        // Créer un message V2
        async function createMessageV2() {
            const recipient = document.getElementById('messageRecipient').value;
            const content = document.getElementById('messageContent').value;

            if (!recipient || !content) {
                log('❌ Veuillez remplir tous les champs du message V2', 'error');
                return;
            }

            if (!currentUser) {
                log('❌ Vous devez être connecté pour créer un message V2', 'error');
                return;
            }

            try {
                log(`📧 Création du message V2 vers: ${recipient}`, 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_messages_v2')
                    .insert({
                        sender_username: currentUser.email,
                        sender_name: `${currentUser.user_metadata?.prenom || 'Utilisateur'} ${currentUser.user_metadata?.nom || 'Test'}`,
                        recipient: recipient,
                        content: content,
                        file_type: 'text'
                    })
                    .select();

                if (error) throw error;

                log(`✅ Message V2 créé avec succès`, 'success');

                // Vider les champs
                document.getElementById('messageRecipient').value = '';
                document.getElementById('messageContent').value = '';

                // Ajouter à l'interface
                addMessageV2ToUI(data[0], false);

            } catch (error) {
                log(`❌ Erreur création message V2: ${error.message}`, 'error');
            }
        }

        // Créer une commande étendue
        async function createCommandExtended() {
            const supplier = document.getElementById('commandSupplier').value;
            const total = parseFloat(document.getElementById('commandTotal').value);
            const observations = document.getElementById('commandObservations').value;

            if (!supplier || !total) {
                log('❌ Veuillez remplir les champs obligatoires de la commande étendue', 'error');
                return;
            }

            if (!currentUser) {
                log('❌ Vous devez être connecté pour créer une commande étendue', 'error');
                return;
            }

            try {
                const numeroCommande = `CMD-EXT-${Date.now()}`;
                log(`📦 Création de la commande étendue: ${numeroCommande}`, 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_commands_extended')
                    .insert({
                        numero_commande: numeroCommande,
                        fournisseur: supplier,
                        date_commande: new Date().toISOString().split('T')[0],
                        total_ht: total / 1.2,
                        total_ttc: total,
                        observations: observations,
                        created_by: currentUser.email,
                        produits: [
                            {
                                nom: 'Produit test',
                                quantite: 1,
                                prix: total
                            }
                        ]
                    })
                    .select();

                if (error) throw error;

                log(`✅ Commande étendue créée: ${numeroCommande}`, 'success');

                // Vider les champs
                document.getElementById('commandSupplier').value = '';
                document.getElementById('commandTotal').value = '';
                document.getElementById('commandObservations').value = '';

                // Ajouter à l'interface
                addCommandExtendedToUI(data[0], false);

            } catch (error) {
                log(`❌ Erreur création commande étendue: ${error.message}`, 'error');
            }
        }

        // Créer un PV détaillé
        async function createPVDetailed() {
            const type = document.getElementById('pvType').value;
            const depot = document.getElementById('pvDepot').value;
            const responsable = document.getElementById('pvResponsable').value;

            if (!depot || !responsable) {
                log('❌ Veuillez remplir tous les champs du PV détaillé', 'error');
                return;
            }

            if (!currentUser) {
                log('❌ Vous devez être connecté pour créer un PV détaillé', 'error');
                return;
            }

            try {
                const numeroPV = `PV-DET-${Date.now()}`;
                log(`📄 Création du PV détaillé: ${numeroPV}`, 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_pv_detailed')
                    .insert({
                        numero_pv: numeroPV,
                        type_pv: type,
                        depot: depot,
                        date_pv: new Date().toISOString().split('T')[0],
                        responsable: responsable,
                        created_by: currentUser.email,
                        produits: [
                            {
                                nom: 'Article test',
                                quantite_theorique: 100,
                                quantite_reelle: 100,
                                ecart: 0
                            }
                        ],
                        observations: 'PV de test créé automatiquement'
                    })
                    .select();

                if (error) throw error;

                log(`✅ PV détaillé créé: ${numeroPV}`, 'success');

                // Vider les champs
                document.getElementById('pvDepot').value = '';
                document.getElementById('pvResponsable').value = '';

                // Ajouter à l'interface
                addPVDetailedToUI(data[0], false);

            } catch (error) {
                log(`❌ Erreur création PV détaillé: ${error.message}`, 'error');
            }
        }

        // Créer un produit détaillé
        async function createProductDetailed() {
            const name = document.getElementById('productName').value;
            const category = document.getElementById('productCategory').value;
            const price = parseFloat(document.getElementById('productPrice').value);

            if (!name || !category || !price) {
                log('❌ Veuillez remplir tous les champs du produit détaillé', 'error');
                return;
            }

            try {
                const reference = `PROD-${Date.now()}`;
                log(`🛍️ Création du produit détaillé: ${name}`, 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_products_detailed')
                    .insert({
                        nom: name,
                        categorie: category,
                        reference: reference,
                        prix_unitaire: price,
                        fournisseur: 'Fournisseur Test',
                        unite: 'unité',
                        description: 'Produit de test créé automatiquement'
                    })
                    .select();

                if (error) throw error;

                log(`✅ Produit détaillé créé: ${name}`, 'success');

                // Vider les champs
                document.getElementById('productName').value = '';
                document.getElementById('productCategory').value = '';
                document.getElementById('productPrice').value = '';

                // Ajouter à l'interface
                addProductDetailedToUI(data[0], false);

            } catch (error) {
                log(`❌ Erreur création produit détaillé: ${error.message}`, 'error');
            }
        }

        // Charger tous les données étendues
        async function loadAllExtendedData() {
            await loadMessagesV2();
            await loadCommandsExtended();
            await loadPVDetailed();
            await loadProductsDetailed();
        }

        // Charger les messages V2
        async function loadMessagesV2() {
            try {
                log('📧 Chargement des messages V2...', 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_messages_v2')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) throw error;

                const messagesList = document.getElementById('messagesV2List');
                messagesList.innerHTML = '';

                if (data.length === 0) {
                    messagesList.innerHTML = '<p style="text-align: center; color: #666;">Aucun message V2</p>';
                } else {
                    data.forEach(message => addMessageV2ToUI(message, false));
                }

                log(`✅ ${data.length} messages V2 chargés`, 'success');

            } catch (error) {
                log(`❌ Erreur chargement messages V2: ${error.message}`, 'error');
            }
        }

        // Charger les commandes étendues
        async function loadCommandsExtended() {
            try {
                log('📦 Chargement des commandes étendues...', 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_commands_extended')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) throw error;

                const commandsList = document.getElementById('commandsExtendedList');
                commandsList.innerHTML = '';

                if (data.length === 0) {
                    commandsList.innerHTML = '<p style="text-align: center; color: #666;">Aucune commande étendue</p>';
                } else {
                    data.forEach(command => addCommandExtendedToUI(command, false));
                }

                log(`✅ ${data.length} commandes étendues chargées`, 'success');

            } catch (error) {
                log(`❌ Erreur chargement commandes étendues: ${error.message}`, 'error');
            }
        }

        // Charger les PV détaillés
        async function loadPVDetailed() {
            try {
                log('📄 Chargement des PV détaillés...', 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_pv_detailed')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) throw error;

                const pvList = document.getElementById('pvDetailedList');
                pvList.innerHTML = '';

                if (data.length === 0) {
                    pvList.innerHTML = '<p style="text-align: center; color: #666;">Aucun PV détaillé</p>';
                } else {
                    data.forEach(pv => addPVDetailedToUI(pv, false));
                }

                log(`✅ ${data.length} PV détaillés chargés`, 'success');

            } catch (error) {
                log(`❌ Erreur chargement PV détaillés: ${error.message}`, 'error');
            }
        }

        // Charger les produits détaillés
        async function loadProductsDetailed() {
            try {
                log('🛍️ Chargement des produits détaillés...', 'info');

                const { data, error } = await supabaseSystem.client
                    .from('gestion_products_detailed')
                    .select('*')
                    .order('created_at', { ascending: false })
                    .limit(10);

                if (error) throw error;

                const productsList = document.getElementById('productsDetailedList');
                productsList.innerHTML = '';

                if (data.length === 0) {
                    productsList.innerHTML = '<p style="text-align: center; color: #666;">Aucun produit détaillé</p>';
                } else {
                    data.forEach(product => addProductDetailedToUI(product, false));
                }

                log(`✅ ${data.length} produits détaillés chargés`, 'success');

            } catch (error) {
                log(`❌ Erreur chargement produits détaillés: ${error.message}`, 'error');
            }
        }

        // Ajouter un message V2 à l'interface
        function addMessageV2ToUI(message, isRealtime = false) {
            const messagesList = document.getElementById('messagesV2List');

            // Supprimer le message "Aucun message V2" s'il existe
            const noMessages = messagesList.querySelector('p');
            if (noMessages && noMessages.textContent.includes('Aucun message V2')) {
                noMessages.remove();
            }

            const messageElement = document.createElement('div');
            messageElement.className = `data-item ${isRealtime ? 'realtime-item' : ''}`;
            messageElement.setAttribute('data-message-v2-id', message.id);

            const timeAgo = getTimeAgo(new Date(message.created_at));
            const readStatus = message.is_read ? '✅ Lu' : '📧 Non lu';

            messageElement.innerHTML = `
                <div class="data-header">
                    <span>📧 ${message.sender_name}</span>
                    <span>${readStatus} ${isRealtime ? '⚡' : ''}</span>
                </div>
                <div class="data-content">
                    <strong>À:</strong> ${message.recipient}<br>
                    <strong>Contenu:</strong> ${message.content}
                </div>
                <div class="data-meta">
                    <span>${timeAgo}</span>
                    <span>Type: ${message.file_type || 'text'}</span>
                </div>
            `;

            messagesList.insertBefore(messageElement, messagesList.firstChild);

            if (isRealtime) {
                messageElement.style.opacity = '0';
                setTimeout(() => {
                    messageElement.style.transition = 'opacity 0.3s ease';
                    messageElement.style.opacity = '1';
                }, 10);
            }
        }

        // Ajouter une commande étendue à l'interface
        function addCommandExtendedToUI(command, isRealtime = false) {
            const commandsList = document.getElementById('commandsExtendedList');

            const noCommands = commandsList.querySelector('p');
            if (noCommands && noCommands.textContent.includes('Aucune commande étendue')) {
                noCommands.remove();
            }

            const commandElement = document.createElement('div');
            commandElement.className = `data-item ${isRealtime ? 'realtime-item' : ''}`;
            commandElement.setAttribute('data-command-ext-id', command.id);

            const timeAgo = getTimeAgo(new Date(command.created_at));
            const statusColor = getCommandStatusColor(command.statut);

            commandElement.innerHTML = `
                <div class="data-header">
                    <span>📦 ${command.numero_commande}</span>
                    <span style="color: ${statusColor}">${command.statut} ${isRealtime ? '⚡' : ''}</span>
                </div>
                <div class="data-content">
                    <strong>Fournisseur:</strong> ${command.fournisseur}<br>
                    <strong>Total TTC:</strong> ${command.total_ttc}€<br>
                    <strong>Observations:</strong> ${command.observations || 'Aucune'}
                </div>
                <div class="data-meta">
                    <span>${timeAgo}</span>
                    <span>Par: ${command.created_by}</span>
                </div>
            `;

            commandsList.insertBefore(commandElement, commandsList.firstChild);

            if (isRealtime) {
                commandElement.style.opacity = '0';
                setTimeout(() => {
                    commandElement.style.transition = 'opacity 0.3s ease';
                    commandElement.style.opacity = '1';
                }, 10);
            }
        }

        // Ajouter un PV détaillé à l'interface
        function addPVDetailedToUI(pv, isRealtime = false) {
            const pvList = document.getElementById('pvDetailedList');

            const noPV = pvList.querySelector('p');
            if (noPV && noPV.textContent.includes('Aucun PV détaillé')) {
                noPV.remove();
            }

            const pvElement = document.createElement('div');
            pvElement.className = `data-item ${isRealtime ? 'realtime-item' : ''}`;
            pvElement.setAttribute('data-pv-detailed-id', pv.id);

            const timeAgo = getTimeAgo(new Date(pv.created_at));
            const typeIcon = getPVTypeIcon(pv.type_pv);

            pvElement.innerHTML = `
                <div class="data-header">
                    <span>${typeIcon} ${pv.numero_pv}</span>
                    <span>${pv.statut} ${isRealtime ? '⚡' : ''}</span>
                </div>
                <div class="data-content">
                    <strong>Type:</strong> ${pv.type_pv}<br>
                    <strong>Dépôt:</strong> ${pv.depot}<br>
                    <strong>Responsable:</strong> ${pv.responsable}
                </div>
                <div class="data-meta">
                    <span>${timeAgo}</span>
                    <span>Date PV: ${pv.date_pv}</span>
                </div>
            `;

            pvList.insertBefore(pvElement, pvList.firstChild);

            if (isRealtime) {
                pvElement.style.opacity = '0';
                setTimeout(() => {
                    pvElement.style.transition = 'opacity 0.3s ease';
                    pvElement.style.opacity = '1';
                }, 10);
            }
        }

        // Ajouter un produit détaillé à l'interface
        function addProductDetailedToUI(product, isRealtime = false) {
            const productsList = document.getElementById('productsDetailedList');

            const noProducts = productsList.querySelector('p');
            if (noProducts && noProducts.textContent.includes('Aucun produit détaillé')) {
                noProducts.remove();
            }

            const productElement = document.createElement('div');
            productElement.className = `data-item ${isRealtime ? 'realtime-item' : ''}`;
            productElement.setAttribute('data-product-detailed-id', product.id);

            const timeAgo = getTimeAgo(new Date(product.created_at));
            const priceCategory = getPriceCategory(product.prix_unitaire);

            productElement.innerHTML = `
                <div class="data-header">
                    <span>🛍️ ${product.nom}</span>
                    <span>${priceCategory} ${isRealtime ? '⚡' : ''}</span>
                </div>
                <div class="data-content">
                    <strong>Catégorie:</strong> ${product.categorie}<br>
                    <strong>Prix:</strong> ${product.prix_unitaire}€ / ${product.unite}<br>
                    <strong>Référence:</strong> ${product.reference}
                </div>
                <div class="data-meta">
                    <span>${timeAgo}</span>
                    <span>Fournisseur: ${product.fournisseur}</span>
                </div>
            `;

            productsList.insertBefore(productElement, productsList.firstChild);

            if (isRealtime) {
                productElement.style.opacity = '0';
                setTimeout(() => {
                    productElement.style.transition = 'opacity 0.3s ease';
                    productElement.style.opacity = '1';
                }, 10);
            }
        }

        // Handlers pour les changements temps réel
        function handleMessagesV2Change(data) {
            if (data.eventType === 'INSERT') {
                addMessageV2ToUI(data.new, true);
            }
        }

        function handleCommandsExtendedChange(data) {
            if (data.eventType === 'INSERT') {
                addCommandExtendedToUI(data.new, true);
            }
        }

        function handlePVDetailedChange(data) {
            if (data.eventType === 'INSERT') {
                addPVDetailedToUI(data.new, true);
            }
        }

        function handleProductsDetailedChange(data) {
            if (data.eventType === 'INSERT') {
                addProductDetailedToUI(data.new, true);
            }
        }

        // Tester toutes les tables étendues
        async function testAllExtendedTables() {
            if (!currentUser) {
                log('❌ Vous devez être connecté pour tester les tables étendues', 'error');
                return;
            }

            try {
                log('🧪 Test automatique de toutes les tables étendues...', 'info');

                // Test message V2
                await supabaseSystem.client
                    .from('gestion_messages_v2')
                    .insert({
                        sender_username: currentUser.email,
                        sender_name: 'Test Auto',
                        recipient: '<EMAIL>',
                        content: 'Message de test automatique',
                        file_type: 'text'
                    });

                // Test commande étendue
                await supabaseSystem.client
                    .from('gestion_commands_extended')
                    .insert({
                        numero_commande: `CMD-AUTO-${Date.now()}`,
                        fournisseur: 'Fournisseur Auto Test',
                        date_commande: new Date().toISOString().split('T')[0],
                        total_ht: 100,
                        total_ttc: 120,
                        created_by: currentUser.email,
                        observations: 'Test automatique'
                    });

                // Test PV détaillé
                await supabaseSystem.client
                    .from('gestion_pv_detailed')
                    .insert({
                        numero_pv: `PV-AUTO-${Date.now()}`,
                        type_pv: 'depot',
                        depot: 'Dépôt Test Auto',
                        date_pv: new Date().toISOString().split('T')[0],
                        responsable: 'Responsable Auto',
                        created_by: currentUser.email,
                        observations: 'PV de test automatique'
                    });

                // Test produit détaillé
                await supabaseSystem.client
                    .from('gestion_products_detailed')
                    .insert({
                        nom: `Produit Auto ${Date.now()}`,
                        categorie: 'Test',
                        reference: `AUTO-${Date.now()}`,
                        prix_unitaire: 25.99,
                        fournisseur: 'Fournisseur Auto',
                        description: 'Produit de test automatique'
                    });

                log('✅ Test automatique terminé - vérifiez la synchronisation', 'success');

            } catch (error) {
                log(`❌ Erreur test automatique: ${error.message}`, 'error');
            }
        }

        // Charger les statistiques étendues
        async function loadExtendedStats() {
            try {
                log('📊 Chargement des statistiques étendues...', 'info');

                const { data, error } = await supabaseSystem.client
                    .from('dashboard_stats_extended')
                    .select('*')
                    .single();

                if (error) throw error;

                const statsGrid = document.getElementById('statsGrid');
                statsGrid.innerHTML = `
                    <div class="stat-card">
                        <div class="stat-value">${data.total_users_legacy || 0}</div>
                        <div class="stat-label">👥 Utilisateurs Legacy</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.total_messages_v2_unread || 0}</div>
                        <div class="stat-label">📧 Messages V2 Non Lus</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.total_commands_extended || 0}</div>
                        <div class="stat-label">📦 Commandes Étendues</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.total_pv_detailed || 0}</div>
                        <div class="stat-label">📄 PV Détaillés</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.total_products_detailed || 0}</div>
                        <div class="stat-label">🛍️ Produits Détaillés</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.commands_pending || 0}</div>
                        <div class="stat-label">⏳ Commandes En Attente</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${data.total_validated_amount || 0}€</div>
                        <div class="stat-label">💰 Montant Validé</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${syncStats.totalEvents}</div>
                        <div class="stat-label">⚡ Événements Sync</div>
                    </div>
                `;

                log('✅ Statistiques étendues chargées', 'success');

            } catch (error) {
                log(`❌ Erreur chargement statistiques: ${error.message}`, 'error');
            }
        }

        // Vider les données de test
        async function clearExtendedData() {
            if (!confirm('Êtes-vous sûr de vouloir supprimer toutes les données de test étendues ?')) {
                return;
            }

            try {
                log('🗑️ Suppression des données de test étendues...', 'warning');

                // Supprimer les données de test (avec des conditions pour éviter de supprimer les vraies données)
                await supabaseSystem.client
                    .from('gestion_messages_v2')
                    .delete()
                    .like('content', '%test%');

                await supabaseSystem.client
                    .from('gestion_commands_extended')
                    .delete()
                    .like('numero_commande', '%AUTO%');

                await supabaseSystem.client
                    .from('gestion_pv_detailed')
                    .delete()
                    .like('numero_pv', '%AUTO%');

                await supabaseSystem.client
                    .from('gestion_products_detailed')
                    .delete()
                    .like('nom', '%Auto%');

                // Vider les listes de l'interface
                document.getElementById('messagesV2List').innerHTML = '<p style="text-align: center; color: #666;">Aucun message V2</p>';
                document.getElementById('commandsExtendedList').innerHTML = '<p style="text-align: center; color: #666;">Aucune commande étendue</p>';
                document.getElementById('pvDetailedList').innerHTML = '<p style="text-align: center; color: #666;">Aucun PV détaillé</p>';
                document.getElementById('productsDetailedList').innerHTML = '<p style="text-align: center; color: #666;">Aucun produit détaillé</p>';

                log('✅ Données de test supprimées', 'success');

            } catch (error) {
                log(`❌ Erreur suppression: ${error.message}`, 'error');
            }
        }

        // Fonctions utilitaires
        function refreshConnection() {
            log('🔄 Actualisation de la connexion...', 'info');
            location.reload();
        }

        function updateConnectionStatus(status, text) {
            const indicator = document.getElementById('connectionStatus');
            const textElement = document.getElementById('connectionText');

            indicator.className = `status-indicator ${status}`;
            textElement.textContent = text;
        }

        function updateUserInfo(profile) {
            const userInfo = document.getElementById('userInfo');

            if (profile) {
                userInfo.textContent = `${profile.nom} ${profile.prenom} (${profile.role})`;
            } else {
                userInfo.textContent = 'Non connecté';
            }
        }

        function updateSyncStats() {
            const syncStatsElement = document.getElementById('syncStats');
            syncStatsElement.textContent = `${syncStats.totalEvents} événements synchronisés`;
        }

        function getTimeAgo(date) {
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) {
                return 'À l\'instant';
            } else if (diffInSeconds < 3600) {
                const minutes = Math.floor(diffInSeconds / 60);
                return `${minutes}min`;
            } else if (diffInSeconds < 86400) {
                const hours = Math.floor(diffInSeconds / 3600);
                return `${hours}h`;
            } else {
                const days = Math.floor(diffInSeconds / 86400);
                return `${days}j`;
            }
        }

        function getCommandStatusColor(status) {
            const colors = {
                'en_attente': '#ffc107',
                'soumise': '#17a2b8',
                'validee': '#28a745',
                'livree': '#20c997',
                'annulee': '#dc3545'
            };
            return colors[status] || '#6c757d';
        }

        function getPVTypeIcon(type) {
            const icons = {
                'depot': '🏪',
                'controle_qualite': '🔍',
                'inventaire': '📋',
                'reception': '📦'
            };
            return icons[type] || '📄';
        }

        function getPriceCategory(price) {
            if (price > 100) return 'Coûteux';
            if (price > 50) return 'Moyen';
            return 'Économique';
        }

        function log(message, level = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            console.log(`[${level.toUpperCase()}] ${message}`);
        }
    </script>
</body>
</html>
