// Système de migration automatique des données
// Gère les migrations de schéma et de données entre versions

class SupabaseMigration {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.currentVersion = config.migrations?.version || '1.0.0';
        this.migrationHistory = [];
        
        // Définition des migrations
        this.migrations = {
            '1.0.0': {
                description: 'Migration initiale - Création des tables de base',
                up: this.migration_1_0_0_up.bind(this),
                down: this.migration_1_0_0_down.bind(this)
            },
            '1.1.0': {
                description: 'Ajout des champs de métadonnées',
                up: this.migration_1_1_0_up.bind(this),
                down: this.migration_1_1_0_down.bind(this)
            },
            '1.2.0': {
                description: 'Optimisation des index et performances',
                up: this.migration_1_2_0_up.bind(this),
                down: this.migration_1_2_0_down.bind(this)
            }
        };
    }

    // Exécuter les migrations automatiquement
    async runMigrations() {
        console.log('🔄 Démarrage des migrations automatiques...');
        
        try {
            // Vérifier la table de migrations
            await this.ensureMigrationTable();
            
            // Récupérer l'historique des migrations
            await this.loadMigrationHistory();
            
            // Déterminer les migrations à exécuter
            const pendingMigrations = this.getPendingMigrations();
            
            if (pendingMigrations.length === 0) {
                console.log('✅ Aucune migration en attente');
                return { success: true, migrationsRun: 0 };
            }
            
            console.log(`📋 ${pendingMigrations.length} migration(s) en attente`);
            
            // Exécuter les migrations
            let migrationsRun = 0;
            for (const version of pendingMigrations) {
                try {
                    await this.runMigration(version);
                    migrationsRun++;
                    console.log(`✅ Migration ${version} terminée`);
                } catch (error) {
                    console.error(`❌ Erreur migration ${version}:`, error);
                    throw error;
                }
            }
            
            console.log(`✅ ${migrationsRun} migration(s) exécutée(s) avec succès`);
            return { success: true, migrationsRun };
            
        } catch (error) {
            console.error('❌ Erreur lors des migrations:', error);
            return { success: false, error: error.message };
        }
    }

    // Assurer l'existence de la table de migrations
    async ensureMigrationTable() {
        try {
            // Tenter de lire la table de migrations
            const { data, error } = await this.client
                .from('gestion_migrations')
                .select('version')
                .limit(1);
            
            if (error && error.code === 'PGRST116') {
                // Table n'existe pas, la créer
                console.log('📋 Création de la table de migrations...');
                
                // Note: En réalité, cette table doit être créée manuellement
                // via l'éditeur SQL de Supabase
                const createTableSQL = `
                    CREATE TABLE IF NOT EXISTS gestion_migrations (
                        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                        version TEXT UNIQUE NOT NULL,
                        description TEXT,
                        executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        execution_time INTEGER, -- en millisecondes
                        success BOOLEAN DEFAULT true,
                        error_message TEXT
                    );
                `;
                
                console.log('⚠️ Veuillez exécuter ce SQL dans Supabase:', createTableSQL);
                
                // Simuler la création pour les tests
                return true;
            }
            
            return true;
            
        } catch (error) {
            console.warn('⚠️ Erreur vérification table migrations:', error);
            return false;
        }
    }

    // Charger l'historique des migrations
    async loadMigrationHistory() {
        try {
            const { data, error } = await this.client
                .from('gestion_migrations')
                .select('*')
                .order('executed_at', { ascending: true });
            
            if (error && error.code !== 'PGRST116') {
                throw error;
            }
            
            this.migrationHistory = data || [];
            console.log(`📋 ${this.migrationHistory.length} migration(s) dans l'historique`);
            
        } catch (error) {
            console.warn('⚠️ Erreur chargement historique migrations:', error);
            this.migrationHistory = [];
        }
    }

    // Obtenir les migrations en attente
    getPendingMigrations() {
        const executedVersions = this.migrationHistory.map(m => m.version);
        const allVersions = Object.keys(this.migrations).sort(this.compareVersions);
        
        return allVersions.filter(version => !executedVersions.includes(version));
    }

    // Exécuter une migration spécifique
    async runMigration(version) {
        const migration = this.migrations[version];
        if (!migration) {
            throw new Error(`Migration ${version} non trouvée`);
        }
        
        console.log(`🔄 Exécution migration ${version}: ${migration.description}`);
        
        const startTime = Date.now();
        let success = true;
        let errorMessage = null;
        
        try {
            // Backup avant migration si configuré
            if (this.config.migrations?.backupBeforeMigration) {
                await this.createBackup(version);
            }
            
            // Exécuter la migration
            await migration.up();
            
            const executionTime = Date.now() - startTime;
            
            // Enregistrer dans l'historique
            await this.recordMigration(version, migration.description, executionTime, true);
            
            console.log(`✅ Migration ${version} réussie en ${executionTime}ms`);
            
        } catch (error) {
            success = false;
            errorMessage = error.message;
            const executionTime = Date.now() - startTime;
            
            // Enregistrer l'échec
            await this.recordMigration(version, migration.description, executionTime, false, errorMessage);
            
            console.error(`❌ Migration ${version} échouée:`, error);
            throw error;
        }
    }

    // Enregistrer une migration dans l'historique
    async recordMigration(version, description, executionTime, success, errorMessage = null) {
        try {
            const { error } = await this.client
                .from('gestion_migrations')
                .insert({
                    version,
                    description,
                    execution_time: executionTime,
                    success,
                    error_message: errorMessage
                });
            
            if (error) {
                console.warn('⚠️ Erreur enregistrement migration:', error);
            }
            
        } catch (error) {
            console.warn('⚠️ Erreur enregistrement migration:', error);
        }
    }

    // Créer un backup avant migration
    async createBackup(version) {
        console.log(`💾 Création backup avant migration ${version}...`);
        
        try {
            const tables = Object.keys(this.config.tables);
            const backupData = {};
            
            for (const table of tables) {
                const tableName = this.config.tables[table];
                const { data, error } = await this.client
                    .from(tableName)
                    .select('*');
                
                if (!error) {
                    backupData[table] = data;
                }
            }
            
            // Sauvegarder en localStorage comme backup
            const backupKey = `backup_${version}_${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(backupData));
            
            console.log(`✅ Backup créé: ${backupKey}`);
            
        } catch (error) {
            console.warn('⚠️ Erreur création backup:', error);
        }
    }

    // Comparer les versions
    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            
            if (aPart < bPart) return -1;
            if (aPart > bPart) return 1;
        }
        
        return 0;
    }

    // Migration 1.0.0 - Création initiale
    async migration_1_0_0_up() {
        console.log('📋 Migration 1.0.0: Création des tables de base');
        
        // Vérifier que les tables existent
        const tables = Object.keys(this.config.tables);
        
        for (const table of tables) {
            const tableName = this.config.tables[table];
            
            try {
                const { data, error } = await this.client
                    .from(tableName)
                    .select('count')
                    .limit(1);
                
                if (error && error.code === 'PGRST116') {
                    console.log(`⚠️ Table ${tableName} n'existe pas - veuillez l'exécuter manuellement`);
                } else {
                    console.log(`✅ Table ${tableName} existe`);
                }
                
            } catch (error) {
                console.warn(`⚠️ Erreur vérification table ${tableName}:`, error);
            }
        }
        
        // Migrer les données existantes du localStorage
        await this.migrateLocalStorageData();
    }

    async migration_1_0_0_down() {
        console.log('🔄 Rollback migration 1.0.0');
        // Le rollback de la migration initiale n'est généralement pas nécessaire
    }

    // Migration 1.1.0 - Ajout de métadonnées
    async migration_1_1_0_up() {
        console.log('📋 Migration 1.1.0: Ajout des champs de métadonnées');
        
        // Note: Les modifications de schéma doivent être faites manuellement
        const alterStatements = [
            "ALTER TABLE gestion_messages ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;",
            "ALTER TABLE gestion_commands ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;",
            "ALTER TABLE gestion_pv ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb;"
        ];
        
        console.log('⚠️ Veuillez exécuter ces commandes SQL dans Supabase:');
        alterStatements.forEach(sql => console.log(sql));
        
        // Mettre à jour les données existantes avec des métadonnées par défaut
        await this.addDefaultMetadata();
    }

    async migration_1_1_0_down() {
        console.log('🔄 Rollback migration 1.1.0');
        // Supprimer les colonnes metadata (à faire manuellement)
    }

    // Migration 1.2.0 - Optimisation des performances
    async migration_1_2_0_up() {
        console.log('📋 Migration 1.2.0: Optimisation des index et performances');
        
        const indexStatements = [
            "CREATE INDEX IF NOT EXISTS idx_gestion_messages_created_at_desc ON gestion_messages(created_at DESC);",
            "CREATE INDEX IF NOT EXISTS idx_gestion_commands_date_statut ON gestion_commands(date_commande, statut);",
            "CREATE INDEX IF NOT EXISTS idx_gestion_pv_date_type ON gestion_pv(date_pv, type_pv);",
            "CREATE INDEX IF NOT EXISTS idx_gestion_messages_recipient_read ON gestion_messages(recipient, is_read);"
        ];
        
        console.log('⚠️ Veuillez exécuter ces index dans Supabase:');
        indexStatements.forEach(sql => console.log(sql));
    }

    async migration_1_2_0_down() {
        console.log('🔄 Rollback migration 1.2.0');
        // Supprimer les index (à faire manuellement)
    }

    // Migrer les données du localStorage vers Supabase
    async migrateLocalStorageData() {
        console.log('🔄 Migration des données localStorage vers Supabase...');
        
        const tables = Object.keys(this.config.tables);
        let totalMigrated = 0;
        
        for (const table of tables) {
            try {
                const localData = JSON.parse(localStorage.getItem(`gestion_${table}`) || '[]');
                
                if (localData.length > 0) {
                    console.log(`📤 Migration de ${localData.length} éléments pour ${table}...`);
                    
                    const tableName = this.config.tables[table];
                    const { data, error } = await this.client
                        .from(tableName)
                        .upsert(localData, { onConflict: 'id' });
                    
                    if (error) {
                        console.warn(`⚠️ Erreur migration ${table}:`, error);
                    } else {
                        totalMigrated += localData.length;
                        console.log(`✅ ${localData.length} éléments migrés pour ${table}`);
                    }
                }
                
            } catch (error) {
                console.error(`❌ Erreur migration ${table}:`, error);
            }
        }
        
        console.log(`✅ Migration terminée: ${totalMigrated} éléments au total`);
    }

    // Ajouter des métadonnées par défaut
    async addDefaultMetadata() {
        console.log('🔄 Ajout des métadonnées par défaut...');
        
        const tables = ['messages', 'commands', 'pv'];
        
        for (const table of tables) {
            try {
                const tableName = this.config.tables[table];
                
                // Récupérer les enregistrements sans métadonnées
                const { data, error } = await this.client
                    .from(tableName)
                    .select('id')
                    .is('metadata', null);
                
                if (!error && data && data.length > 0) {
                    // Mettre à jour avec des métadonnées par défaut
                    const updates = data.map(item => ({
                        id: item.id,
                        metadata: {
                            version: '1.1.0',
                            migrated_at: new Date().toISOString(),
                            source: 'migration'
                        }
                    }));
                    
                    const { error: updateError } = await this.client
                        .from(tableName)
                        .upsert(updates, { onConflict: 'id' });
                    
                    if (!updateError) {
                        console.log(`✅ Métadonnées ajoutées pour ${data.length} éléments de ${table}`);
                    }
                }
                
            } catch (error) {
                console.warn(`⚠️ Erreur ajout métadonnées ${table}:`, error);
            }
        }
    }

    // Obtenir l'historique des migrations
    getMigrationHistory() {
        return [...this.migrationHistory];
    }

    // Obtenir les migrations disponibles
    getAvailableMigrations() {
        return Object.keys(this.migrations).map(version => ({
            version,
            description: this.migrations[version].description,
            executed: this.migrationHistory.some(m => m.version === version)
        }));
    }

    // Rollback d'une migration (dangereux)
    async rollbackMigration(version) {
        console.warn(`⚠️ Rollback de la migration ${version}...`);
        
        const migration = this.migrations[version];
        if (!migration || !migration.down) {
            throw new Error(`Rollback non disponible pour la version ${version}`);
        }
        
        try {
            await migration.down();
            
            // Supprimer de l'historique
            const { error } = await this.client
                .from('gestion_migrations')
                .delete()
                .eq('version', version);
            
            if (error) {
                console.warn('⚠️ Erreur suppression historique:', error);
            }
            
            console.log(`✅ Rollback ${version} terminé`);
            
        } catch (error) {
            console.error(`❌ Erreur rollback ${version}:`, error);
            throw error;
        }
    }
}

// Export de la classe
if (typeof window !== 'undefined') {
    window.SupabaseMigration = SupabaseMigration;
}
