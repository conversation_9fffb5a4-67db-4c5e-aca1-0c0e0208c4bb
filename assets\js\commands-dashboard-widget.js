/**
 * Widget de tableau de bord des commandes avec temps réel
 * Système complet de gestion des commandes IPT
 */

class CommandsDashboardWidget {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.isVisible = false;
        this.refreshInterval = null;
        this.realtimeSubscription = null;
        this.data = {
            totalCommandes: 0,
            commandesEnCours: 0,
            commandesEnRetard: 0,
            commandesLivrees: 0,
            montantTotal: 0,
            montantEnCours: 0,
            userActions: [],
            workflowSteps: []
        };
    }

    async initialize() {
        console.log('📦 Initialisation du widget de gestion des commandes...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                console.warn('⚠️ Client Supabase non disponible pour le widget commandes');
                return;
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            if (!this.currentUser) {
                console.warn('⚠️ Utilisateur non authentifié pour le widget commandes');
                return;
            }

            // Charger les données initiales
            await this.loadData();

            // Configurer les mises à jour en temps réel
            this.setupRealtimeSubscription();

            // Configurer le rafraîchissement automatique (toutes les 30 secondes)
            this.refreshInterval = setInterval(() => {
                this.loadData();
            }, 30000);

            console.log('✅ Widget de gestion des commandes initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation widget commandes:', error);
        }
    }

    async loadData() {
        try {
            // Charger les statistiques globales
            await this.loadStatistics();
            
            // Charger les actions utilisateur
            await this.loadUserActions();
            
            // Charger l'état du workflow
            await this.loadWorkflowStatus();
            
            // Mettre à jour l'affichage
            this.updateDisplay();

        } catch (error) {
            console.error('❌ Erreur chargement données widget commandes:', error);
        }
    }

    async loadStatistics() {
        try {
            // Statistiques globales depuis la vue
            const { data: stats, error } = await this.supabase
                .from('commands_stats')
                .select('*')
                .single();

            if (error && error.code !== 'PGRST116') { // Ignore "no rows" error
                throw error;
            }

            if (stats) {
                this.data.totalCommandes = stats.total_commandes || 0;
                this.data.commandesEnCours = stats.commandes_en_cours || 0;
                this.data.commandesLivrees = stats.commandes_livrees || 0;
                this.data.montantTotal = stats.montant_total_ttc || 0;
            }

            // Commandes en retard
            const { data: retards, error: retardError } = await this.supabase
                .from('commands_en_retard')
                .select('count');

            if (!retardError) {
                this.data.commandesEnRetard = retards?.length || 0;
            }

            // Montant des commandes en cours
            const { data: enCours, error: enCoursError } = await this.supabase
                .from('gestion_commandes')
                .select('montant_ttc')
                .in('statut', ['soumise', 'validee_chef', 'validee_dg', 'approuvee_ms', 'en_cours', 'expedie']);

            if (!enCoursError && enCours) {
                this.data.montantEnCours = enCours.reduce((sum, cmd) => sum + (cmd.montant_ttc || 0), 0);
            }

        } catch (error) {
            console.error('❌ Erreur chargement statistiques commandes:', error);
            // Valeurs par défaut en cas d'erreur
            this.data.totalCommandes = 0;
            this.data.commandesEnCours = 0;
            this.data.commandesEnRetard = 0;
            this.data.commandesLivrees = 0;
            this.data.montantTotal = 0;
            this.data.montantEnCours = 0;
        }
    }

    async loadUserActions() {
        try {
            const userRole = this.currentUser?.user_metadata?.role || 'user';
            const userEmail = this.currentUser?.email;

            // Requête pour les commandes nécessitant une action de l'utilisateur
            let query = this.supabase
                .from('commands_complete')
                .select('*');

            // Filtrer selon le rôle de l'utilisateur
            switch (userRole) {
                case 'chef_service':
                    query = query.eq('statut', 'soumise');
                    break;
                case 'dg':
                    query = query.eq('statut', 'validee_chef');
                    break;
                case 'ms':
                    query = query.eq('statut', 'validee_dg');
                    break;
                case 'acheteur':
                    query = query.in('statut', ['approuvee_ms', 'en_cours']);
                    break;
                case 'magasinier':
                    query = query.in('statut', ['expedie', 'livre_partiel']);
                    break;
                default:
                    // Pour les utilisateurs normaux, montrer leurs propres commandes
                    query = query.eq('demandeur_email', userEmail);
            }

            const { data: actions, error } = await query.limit(5);

            if (error) throw error;

            this.data.userActions = (actions || []).map(cmd => ({
                id: cmd.id,
                title: `${cmd.numero_commande} - ${cmd.type_commande}`,
                description: this.getActionDescription(cmd, userRole),
                urgent: this.isActionUrgent(cmd),
                commande: cmd
            }));

        } catch (error) {
            console.error('❌ Erreur chargement actions utilisateur commandes:', error);
            this.data.userActions = [];
        }
    }

    getActionDescription(cmd, userRole) {
        const roleActions = {
            'chef_service': 'Validation requise',
            'dg': 'Validation DG requise',
            'ms': 'Approbation MS requise',
            'acheteur': 'Suivi commande requis',
            'magasinier': 'Réception à traiter'
        };

        return roleActions[userRole] || `Statut: ${cmd.statut}`;
    }

    isActionUrgent(cmd) {
        // Considérer comme urgent si :
        // 1. Urgence élevée ou urgente
        // 2. En retard de livraison
        // 3. Créé il y a plus de 3 jours sans validation
        
        if (cmd.urgence === 'urgente' || cmd.urgence === 'elevee') {
            return true;
        }
        
        if (cmd.en_retard) {
            return true;
        }
        
        const createdDate = new Date(cmd.created_at);
        const now = new Date();
        const daysDiff = (now - createdDate) / (1000 * 60 * 60 * 24);
        
        return daysDiff > 3 && cmd.statut === 'soumise';
    }

    async loadWorkflowStatus() {
        try {
            // Charger l'état global du workflow (étapes en cours)
            const { data: etapes, error } = await this.supabase
                .from('gestion_commande_workflow')
                .select('numero_etape, statut_etape')
                .in('statut_etape', ['en_cours', 'termine'])
                .order('numero_etape');

            if (error) throw error;

            // Créer un résumé des étapes 1-10
            this.data.workflowSteps = [];
            for (let i = 1; i <= 10; i++) {
                const etapesNum = etapes?.filter(e => e.numero_etape === i) || [];
                const completed = etapesNum.filter(e => e.statut_etape === 'termine').length;
                const current = etapesNum.filter(e => e.statut_etape === 'en_cours').length;
                
                let status = 'pending';
                if (completed > 0 && current === 0) status = 'completed';
                else if (current > 0) status = 'current';
                
                this.data.workflowSteps.push({
                    number: i,
                    status: status,
                    count: etapesNum.length
                });
            }

        } catch (error) {
            console.error('❌ Erreur chargement workflow commandes:', error);
            this.data.workflowSteps = [];
        }
    }

    setupRealtimeSubscription() {
        try {
            // S'abonner aux changements en temps réel
            this.realtimeSubscription = this.supabase
                .channel('commands_widget')
                .on('postgres_changes', 
                    { event: '*', schema: 'public', table: 'gestion_commandes' },
                    (payload) => {
                        console.log('🔄 Mise à jour temps réel commandes détectée:', payload);
                        // Recharger les données après un court délai
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .on('postgres_changes',
                    { event: '*', schema: 'public', table: 'gestion_commande_workflow' },
                    (payload) => {
                        console.log('🔄 Mise à jour workflow commandes détectée:', payload);
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .subscribe();

            console.log('✅ Abonnement temps réel configuré pour le widget commandes');

        } catch (error) {
            console.error('❌ Erreur configuration temps réel commandes:', error);
        }
    }

    updateDisplay() {
        // Mettre à jour les statistiques
        document.getElementById('widgetTotalCommandes').textContent = this.data.totalCommandes;
        document.getElementById('widgetCommandesEnCours').textContent = this.data.commandesEnCours;
        document.getElementById('widgetCommandesEnRetard').textContent = this.data.commandesEnRetard;
        document.getElementById('widgetCommandesLivrees').textContent = this.data.commandesLivrees;

        // Mettre à jour les montants
        document.getElementById('widgetMontantTotal').textContent = this.formatAmount(this.data.montantTotal);
        document.getElementById('widgetMontantEnCours').textContent = this.formatAmount(this.data.montantEnCours);

        // Mettre à jour les actions utilisateur
        this.updateUserActions();

        // Mettre à jour le workflow
        this.updateWorkflowSteps();

        // Mettre à jour le badge de notification
        this.updateNotificationBadge();
    }

    formatAmount(amount) {
        if (amount === 0) return '0 TND';
        if (amount >= 1000000) {
            return (amount / 1000000).toFixed(1) + 'M TND';
        } else if (amount >= 1000) {
            return (amount / 1000).toFixed(1) + 'K TND';
        } else {
            return amount.toFixed(0) + ' TND';
        }
    }

    updateUserActions() {
        const container = document.getElementById('commandsActionsList');
        
        if (this.data.userActions.length === 0) {
            container.innerHTML = '<div class="no-actions">Aucune action requise pour le moment</div>';
            return;
        }

        container.innerHTML = this.data.userActions.map(action => `
            <div class="action-item ${action.urgent ? 'urgent' : ''}" onclick="commandsWidget.openCommandDetails('${action.id}')">
                <div class="action-title">${action.title}</div>
                <div class="action-description">${action.description}</div>
            </div>
        `).join('');
    }

    updateWorkflowSteps() {
        const container = document.getElementById('commandsWorkflowStepsMini');
        
        container.innerHTML = this.data.workflowSteps.slice(0, 5).map(step => `
            <div class="workflow-step-mini ${step.status}" title="Étape ${step.number}">
                ${step.number}
            </div>
        `).join('');
    }

    updateNotificationBadge() {
        const badge = document.getElementById('commandsNotificationBadge');
        const urgentActions = this.data.userActions.filter(action => action.urgent).length;
        const totalActions = this.data.userActions.length;
        const totalUrgent = urgentActions + this.data.commandesEnRetard;

        if (totalUrgent > 0) {
            badge.textContent = totalUrgent;
            badge.style.display = 'flex';
            badge.style.background = '#ef4444'; // Rouge pour urgent
        } else if (totalActions > 0) {
            badge.textContent = totalActions;
            badge.style.display = 'flex';
            badge.style.background = '#f59e0b'; // Orange pour actions normales
        } else {
            badge.style.display = 'none';
        }
    }

    show() {
        const widget = document.getElementById('commandsDashboardWidget');
        widget.style.display = 'block';
        widget.classList.add('show');
        this.isVisible = true;
        
        // Charger les données si pas encore fait
        if (!this.supabase) {
            this.initialize();
        } else {
            this.loadData();
        }
    }

    hide() {
        const widget = document.getElementById('commandsDashboardWidget');
        widget.style.display = 'none';
        widget.classList.remove('show');
        this.isVisible = false;
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    openCommandDetails(commandId) {
        // Ouvrir la page de gestion des commandes avec la commande sélectionnée
        window.open(`commands-management.html?id=${commandId}`, '_blank');
    }

    destroy() {
        // Nettoyer les ressources
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        if (this.realtimeSubscription) {
            this.supabase.removeChannel(this.realtimeSubscription);
        }
    }
}

// Instance globale du widget
window.commandsWidget = new CommandsDashboardWidget();

// Fonctions globales pour l'interface
function toggleCommandsWidget() {
    commandsWidget.toggle();
}

function refreshCommandsDashboard() {
    commandsWidget.loadData();
}

function openCommandsManagement() {
    window.open('commands-management.html', '_blank');
}

function openCommandsWorkflowDashboard() {
    window.open('commands-workflow-dashboard.html', '_blank');
}

function openCommandsReports() {
    window.open('commands-reports.html', '_blank');
}

// Fonction pour basculer la gestion des commandes
function toggleGestionCommandes() {
    // Pour l'instant, juste afficher le widget
    // Plus tard, on pourra ajouter une zone complète comme pour les dons
    commandsWidget.toggle();
}
