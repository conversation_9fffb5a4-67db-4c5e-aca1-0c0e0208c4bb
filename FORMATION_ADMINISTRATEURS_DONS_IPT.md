# 🎓 Formation Administrateurs - Système de Gestion des Dons IPT

## 🎯 Objectifs de la Formation

Cette formation s'adresse aux **administrateurs système** et **super-utilisateurs** responsables de la gestion, maintenance et support du système de gestion des dons IPT.

### 📋 Compétences Acquises
- ✅ **Administration complète** du système
- ✅ **Gestion des utilisateurs** et rôles
- ✅ **Maintenance** et monitoring
- ✅ **Support utilisateur** avancé
- ✅ **Résolution** des problèmes techniques
- ✅ **Sauvegarde** et archivage

---

## 🏗️ Architecture Technique du Système

### 📊 Composants Principaux

```
┌─────────────────────────────────────────────────────────┐
│                    FRONTEND                             │
├─────────────────────────────────────────────────────────┤
│ • donations-registration.html (Enregistrement)         │
│ • donations-workflow.html (Validation)                 │
│ • donations-documents.html (Documents)                 │
│ • donations-management.html (Gestion)                  │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                   JAVASCRIPT                           │
├─────────────────────────────────────────────────────────┤
│ • donations-registration.js (400+ lignes)              │
│ • donations-workflow.js (300+ lignes)                  │
│ • donations-documents.js (300+ lignes)                 │
│ • supabase-auth.js (Authentification)                  │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                   SUPABASE                             │
├─────────────────────────────────────────────────────────┤
│ • 7 Tables principales                                  │
│ • 20+ Fonctions PostgreSQL                             │
│ • 6 Vues optimisées                                    │
│ • 25+ Politiques RLS                                   │
│ • Storage pour documents                                │
│ • Auth pour utilisateurs                               │
└─────────────────────────────────────────────────────────┘
```

### 🗄️ Base de Données - Tables Principales

| Table | Rôle | Enregistrements Typiques |
|-------|------|--------------------------|
| `gestion_donations_ipt` | Dons principaux | 100-500/an |
| `gestion_donation_workflow` | Étapes workflow | 1000-5000/an |
| `gestion_donation_documents` | Documents uploadés | 500-2000/an |
| `gestion_donation_inventaire` | Inventaire équipements | 50-200/an |
| `gestion_donation_mouvements` | Traçabilité | 200-1000/an |
| `gestion_users_ipt` | Utilisateurs IPT | 50-100 |
| `gestion_donation_historique` | Audit trail | 2000-10000/an |

---

## 👥 Gestion des Utilisateurs

### 🔐 Création d'un Nouvel Utilisateur

#### Via Interface Supabase
```sql
-- 1. Créer l'utilisateur dans Auth
INSERT INTO auth.users (email, encrypted_password, email_confirmed_at)
VALUES ('<EMAIL>', crypt('motdepasse', gen_salt('bf')), now());

-- 2. Ajouter les métadonnées
UPDATE auth.users 
SET raw_user_meta_data = jsonb_build_object(
    'nom', 'Nom',
    'prenom', 'Prénom', 
    'role_ipt', 'demandeur',
    'service', 'Laboratoire Microbiologie',
    'telephone', '+216 XX XXX XXX'
)
WHERE email = '<EMAIL>';

-- 3. Créer l'entrée dans la table IPT
INSERT INTO gestion_users_ipt (email, nom, prenom, role_ipt, service, actif)
VALUES ('<EMAIL>', 'Nom', 'Prénom', 'demandeur', 'Laboratoire Microbiologie', true);
```

#### Via Interface d'Administration
```javascript
// Fonction JavaScript pour création utilisateur
async function createUser(userData) {
    const { data, error } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        user_metadata: {
            nom: userData.nom,
            prenom: userData.prenom,
            role_ipt: userData.role,
            service: userData.service,
            telephone: userData.telephone
        }
    });
    
    if (!error) {
        // Créer entrée dans table IPT
        await supabase.from('gestion_users_ipt').insert([userData]);
    }
    
    return { data, error };
}
```

### 🔄 Modification des Rôles

#### Rôles Disponibles
```javascript
const ROLES_IPT = {
    'demandeur': 'Demandeur',
    'responsable_approvisionnement': 'Responsable Approvisionnement',
    'magasinier': 'Magasinier',
    'transitaire': 'Transitaire',
    'receptionniste': 'Réceptionniste',
    'rve': 'Responsable Volet Équipement',
    'sous_direction_achats': 'Sous-Direction Achats',
    'admin_systeme': 'Administrateur Système'
};
```

#### Changement de Rôle
```sql
-- Mettre à jour le rôle utilisateur
UPDATE auth.users 
SET raw_user_meta_data = raw_user_meta_data || jsonb_build_object('role_ipt', 'nouveau_role')
WHERE email = '<EMAIL>';

-- Synchroniser avec table IPT
UPDATE gestion_users_ipt 
SET role_ipt = 'nouveau_role', updated_at = now()
WHERE email = '<EMAIL>';
```

### 🚫 Désactivation d'Utilisateur

```sql
-- Désactiver sans supprimer (recommandé)
UPDATE gestion_users_ipt 
SET actif = false, updated_at = now()
WHERE email = '<EMAIL>';

-- Désactiver dans Auth
UPDATE auth.users 
SET banned_until = '2099-12-31'::timestamp
WHERE email = '<EMAIL>';
```

---

## 🔧 Maintenance et Monitoring

### 📊 Requêtes de Monitoring

#### Performance du Système
```sql
-- Statistiques générales
SELECT 
    COUNT(*) as total_dons,
    COUNT(*) FILTER (WHERE statut = 'archive') as dons_termines,
    AVG(EXTRACT(days FROM updated_at - created_at)) as duree_moyenne_jours
FROM gestion_donations_ipt
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days';

-- Dons en retard
SELECT numero_don, demandeur_nom, statut, 
       EXTRACT(days FROM now() - created_at) as jours_depuis_creation
FROM gestion_donations_ipt 
WHERE statut NOT IN ('archive', 'refuse') 
  AND created_at < CURRENT_DATE - INTERVAL '15 days'
ORDER BY created_at;

-- Utilisation par service
SELECT demandeur_service, COUNT(*) as nb_dons
FROM gestion_donations_ipt
WHERE created_at >= CURRENT_DATE - INTERVAL '90 days'
GROUP BY demandeur_service
ORDER BY nb_dons DESC;
```

#### Monitoring des Erreurs
```sql
-- Logs d'erreurs (si table de logs existe)
SELECT error_type, COUNT(*) as occurrences, MAX(created_at) as derniere_occurrence
FROM system_logs 
WHERE level = 'ERROR' 
  AND created_at >= CURRENT_DATE - INTERVAL '7 days'
GROUP BY error_type
ORDER BY occurrences DESC;
```

### 🔄 Maintenance Préventive

#### Nettoyage Hebdomadaire
```sql
-- Nettoyer les brouillons anciens (> 30 jours)
DELETE FROM gestion_donations_ipt 
WHERE statut = 'brouillon' 
  AND created_at < CURRENT_DATE - INTERVAL '30 days';

-- Nettoyer les sessions expirées
DELETE FROM auth.sessions 
WHERE expires_at < now();

-- Optimiser les tables
VACUUM ANALYZE gestion_donations_ipt;
VACUUM ANALYZE gestion_donation_workflow;
```

#### Sauvegarde Quotidienne
```bash
#!/bin/bash
# Script de sauvegarde automatique

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/donations_ipt"

# Sauvegarde base de données
pg_dump -h localhost -U postgres -d supabase > "$BACKUP_DIR/db_backup_$DATE.sql"

# Sauvegarde Storage (documents)
aws s3 sync s3://supabase-storage/donation-documents "$BACKUP_DIR/documents_$DATE/"

# Compression
tar -czf "$BACKUP_DIR/complete_backup_$DATE.tar.gz" "$BACKUP_DIR/db_backup_$DATE.sql" "$BACKUP_DIR/documents_$DATE/"

# Nettoyage (garder 30 jours)
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete
```

---

## 🚨 Résolution de Problèmes

### 🔍 Diagnostic des Problèmes Courants

#### Problème : "Don bloqué dans une étape"
```sql
-- Identifier le problème
SELECT d.numero_don, d.statut, w.nom_etape, w.responsable_role, w.date_debut
FROM gestion_donations_ipt d
JOIN gestion_donation_workflow w ON d.id = w.donation_id
WHERE d.numero_don = 'DON-2024-XXXX'
  AND w.statut = 'en_cours'
ORDER BY w.numero_etape;

-- Solution : Forcer le passage à l'étape suivante
UPDATE gestion_donation_workflow 
SET statut = 'termine', date_fin = now(), commentaires = 'Déblocage administratif'
WHERE donation_id = (SELECT id FROM gestion_donations_ipt WHERE numero_don = 'DON-2024-XXXX')
  AND statut = 'en_cours';
```

#### Problème : "Document corrompu"
```sql
-- Identifier les documents problématiques
SELECT id, nom_original, taille_fichier, type_mime, uploaded_at
FROM gestion_donation_documents
WHERE taille_fichier = 0 OR type_mime IS NULL;

-- Marquer pour re-upload
UPDATE gestion_donation_documents 
SET statut = 'erreur', commentaires = 'Document corrompu - re-upload requis'
WHERE id = 'document_id';
```

#### Problème : "Permissions incorrectes"
```sql
-- Vérifier les permissions utilisateur
SELECT u.email, u.role_ipt, u.actif, u.service
FROM gestion_users_ipt u
WHERE u.email = '<EMAIL>';

-- Corriger les permissions
UPDATE gestion_users_ipt 
SET role_ipt = 'role_correct', updated_at = now()
WHERE email = '<EMAIL>';
```

### 🔧 Outils de Diagnostic

#### Script de Vérification Système
```javascript
// Vérification complète du système
async function systemHealthCheck() {
    const checks = {
        database: await checkDatabaseConnection(),
        storage: await checkStorageAccess(),
        auth: await checkAuthService(),
        functions: await checkDatabaseFunctions(),
        rls: await checkRLSPolicies()
    };
    
    console.log('System Health Check:', checks);
    return checks;
}

async function checkDatabaseConnection() {
    try {
        const { data, error } = await supabase.from('gestion_donations_ipt').select('count').limit(1);
        return { status: 'OK', error: null };
    } catch (error) {
        return { status: 'ERROR', error: error.message };
    }
}
```

---

## 📈 Optimisation des Performances

### 🚀 Optimisations Base de Données

#### Index Recommandés
```sql
-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_donations_statut ON gestion_donations_ipt(statut);
CREATE INDEX IF NOT EXISTS idx_donations_created_at ON gestion_donations_ipt(created_at);
CREATE INDEX IF NOT EXISTS idx_donations_demandeur ON gestion_donations_ipt(demandeur_email);
CREATE INDEX IF NOT EXISTS idx_workflow_donation_id ON gestion_donation_workflow(donation_id);
CREATE INDEX IF NOT EXISTS idx_documents_donation_id ON gestion_donation_documents(donation_id);
```

#### Optimisation des Requêtes
```sql
-- Vue matérialisée pour les statistiques (rafraîchie quotidiennement)
CREATE MATERIALIZED VIEW donations_stats_daily AS
SELECT 
    DATE(created_at) as date_creation,
    COUNT(*) as nb_dons,
    COUNT(*) FILTER (WHERE type_don = 'article') as nb_articles,
    COUNT(*) FILTER (WHERE type_don = 'equipement') as nb_equipements,
    COUNT(*) FILTER (WHERE statut = 'archive') as nb_termines
FROM gestion_donations_ipt
GROUP BY DATE(created_at)
ORDER BY date_creation DESC;

-- Rafraîchissement automatique
CREATE OR REPLACE FUNCTION refresh_daily_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW donations_stats_daily;
END;
$$ LANGUAGE plpgsql;
```

### ⚡ Optimisations Frontend

#### Mise en Cache
```javascript
// Cache des données fréquemment utilisées
class DataCache {
    constructor() {
        this.cache = new Map();
        this.ttl = 5 * 60 * 1000; // 5 minutes
    }
    
    async get(key, fetchFunction) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.ttl) {
            return cached.data;
        }
        
        const data = await fetchFunction();
        this.cache.set(key, { data, timestamp: Date.now() });
        return data;
    }
}

const cache = new DataCache();
```

---

## 📚 Formation Utilisateurs

### 🎯 Programme de Formation par Rôle

#### Formation Demandeurs (2h)
1. **Introduction** (30min)
   - Présentation du système
   - Avantages et objectifs
   
2. **Création de demandes** (60min)
   - Interface d'enregistrement
   - Upload de documents
   - Suivi des demandes
   
3. **Pratique** (30min)
   - Exercices guidés
   - Questions/réponses

#### Formation Validateurs (3h)
1. **Workflow complet** (60min)
   - Compréhension du processus
   - Rôles et responsabilités
   
2. **Interface de validation** (90min)
   - Utilisation de l'interface
   - Validation/refus
   - Commentaires et signatures
   
3. **Cas pratiques** (30min)
   - Scénarios réels
   - Gestion des problèmes

#### Formation Administrateurs (1 jour)
1. **Architecture technique** (2h)
2. **Gestion des utilisateurs** (2h)
3. **Maintenance et monitoring** (2h)
4. **Résolution de problèmes** (2h)

### 📋 Checklist de Formation

#### Avant la Formation
- ✅ Environnement de test préparé
- ✅ Comptes utilisateurs créés
- ✅ Données d'exemple disponibles
- ✅ Documentation distribuée

#### Pendant la Formation
- ✅ Démonstration en direct
- ✅ Exercices pratiques
- ✅ Questions/réponses
- ✅ Évaluation des acquis

#### Après la Formation
- ✅ Support post-formation (1 semaine)
- ✅ Documentation de référence
- ✅ Contact support technique
- ✅ Évaluation de satisfaction

---

## 📞 Support et Escalade

### 🆘 Niveaux de Support

#### Niveau 1 - Support Utilisateur
- **Problèmes** : Interface, utilisation basique
- **Résolution** : Guide utilisateur, FAQ
- **Délai** : Immédiat à 2h

#### Niveau 2 - Support Technique
- **Problèmes** : Bugs, erreurs système
- **Résolution** : Diagnostic technique, corrections
- **Délai** : 2h à 1 jour

#### Niveau 3 - Support Développement
- **Problèmes** : Bugs complexes, modifications
- **Résolution** : Développement, déploiement
- **Délai** : 1 à 5 jours

### 📋 Procédure d'Escalade

```
Utilisateur → Support N1 → Support N2 → Support N3 → Développement
    ↓           ↓           ↓           ↓           ↓
  2h max     4h max     1j max     3j max     5j max
```

---

**🎓 Formation Administrateurs - Système de Gestion des Dons IPT**  
*Version 1.0 - Institut Pasteur de Tunis*  
*Guide complet pour l'administration et la maintenance du système*
