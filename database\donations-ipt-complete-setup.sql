-- =====================================================
-- INSTALLATION COMPLÈTE - SYSTÈME DONS IPT
-- Institut Pasteur de Tunis
-- =====================================================

-- Ce script installe complètement le système de gestion des dons
-- avec toutes les tables, fonctions, vues et politiques de sécurité

BEGIN;

-- =====================================================
-- 1. SCHÉMA DE BASE DE DONNÉES
-- =====================================================

\echo 'Installation du schéma de base de données...'
\i donations-ipt-schema.sql

-- =====================================================
-- 2. FONCTIONS DE GESTION
-- =====================================================

\echo 'Installation des fonctions de gestion...'
\i donations-ipt-functions.sql
\i donations-ipt-functions-reception.sql

-- =====================================================
-- 3. VUES OPTIMISÉES
-- =====================================================

\echo 'Création des vues optimisées...'
\i donations-ipt-views.sql

-- =====================================================
-- 4. POLITIQUES DE SÉCURITÉ RLS
-- =====================================================

\echo 'Configuration des politiques de sécurité...'
\i donations-ipt-rls-policies.sql

-- =====================================================
-- 5. DONNÉES D'EXEMPLE ET CONFIGURATION
-- =====================================================

\echo 'Insertion des données de configuration...'

-- Utilisateurs de test IPT
INSERT INTO gestion_users_ipt (
    email, nom, prenom, role_ipt, service, laboratoire, fonction,
    peut_valider_dons, peut_gerer_inventaire, peut_voir_tous_dons, peut_generer_rapports,
    is_active, created_by
) VALUES 
    -- Admin système
    ('<EMAIL>', 'Admin', 'Système', 'admin_systeme', 'Informatique', NULL, 'Administrateur Système',
     true, true, true, true, true, 'system'),
    
    -- Responsable Approvisionnement
    ('<EMAIL>', 'Responsable', 'Approvisionnement', 'responsable_approvisionnement', 'Approvisionnement', NULL, 'Responsable Approvisionnement',
     true, false, true, true, true, 'system'),
    
    -- Direction Technique
    ('<EMAIL>', 'Direction', 'Technique', 'direction_technique', 'Direction Technique', NULL, 'Directeur Technique',
     true, false, true, true, true, 'system'),
    
    -- Directeur Général
    ('<EMAIL>', 'Directeur', 'Général', 'directeur_general', 'Direction Générale', NULL, 'Directeur Général',
     true, false, true, true, true, 'system'),
    
    -- Sous-Direction Achats
    ('<EMAIL>', 'Sous-Direction', 'Achats', 'sous_direction_achats', 'Achats', NULL, 'Responsable Achats',
     true, false, true, true, true, 'system'),
    
    -- Réceptionniste
    ('<EMAIL>', 'Réceptionniste', 'Principal', 'receptionniste', 'Réception', NULL, 'Réceptionniste',
     false, false, false, false, true, 'system'),
    
    -- RVE (Responsable Volet Équipement)
    ('<EMAIL>', 'Responsable', 'Équipement', 'rve', 'Équipement', NULL, 'Responsable Volet Équipement',
     false, true, true, true, true, 'system'),
    
    -- Bénéficiaires de test
    ('<EMAIL>', 'Chef', 'Laboratoire1', 'beneficiaire', 'Laboratoire Microbiologie', 'Microbiologie', 'Chef de Laboratoire',
     false, false, false, false, true, 'system'),
    
    ('<EMAIL>', 'Chef', 'Laboratoire2', 'beneficiaire', 'Laboratoire Virologie', 'Virologie', 'Chef de Laboratoire',
     false, false, false, false, true, 'system'),
    
    -- Demandeur de test
    ('<EMAIL>', 'Demandeur', 'Test', 'demandeur', 'Laboratoire Microbiologie', 'Microbiologie', 'Chercheur',
     false, false, false, false, true, 'system');

-- Dons d'exemple
INSERT INTO gestion_donations_ipt (
    numero_don, type_don, demandeur_nom, demandeur_prenom, demandeur_email, 
    demandeur_service, demandeur_laboratoire, demandeur_telephone,
    motif_don, lieu_affectation, description_don, specifications_techniques,
    donateur_nom, donateur_contact, donateur_adresse,
    valeur_estimee, devise, quantite, unite,
    statut, etape_actuelle, priorite,
    date_demande, date_livraison_prevue,
    created_by
) VALUES 
    -- Don d'équipement en cours de validation
    ('DON-2024-0001', 'equipement', 'Dr. Ahmed', 'Benali', '<EMAIL>',
     'Laboratoire Microbiologie', 'Microbiologie', '+216 71 123 456',
     'Remplacement équipement défaillant', 'Laboratoire Microbiologie - Salle 101',
     'Microscope électronique à balayage haute résolution pour analyse microbiologique',
     '{"resolution": "1nm", "voltage": "30kV", "grossissement": "x1000000"}',
     'Société MedEquip International', '<EMAIL>', '123 Rue de la Science, Tunis',
     150000.00, 'TND', 1, 'unité',
     'soumis', 1, 'elevee',
     '2024-01-15', '2024-03-15',
     '<EMAIL>'),
    
    -- Don d'articles consommables
    ('DON-2024-0002', 'article', 'Dr. Fatma', 'Karray', '<EMAIL>',
     'Laboratoire Virologie', 'Virologie', '+216 71 123 457',
     'Stock de consommables pour recherche COVID-19', 'Laboratoire Virologie - Stock',
     'Lot de réactifs PCR et kits de détection virale',
     NULL,
     'Laboratoires Roche Tunisie', '<EMAIL>', '456 Avenue Habib Bourguiba, Tunis',
     25000.00, 'TND', 100, 'kits',
     'valide_appro', 2, 'normale',
     '2024-01-20', '2024-02-20',
     '<EMAIL>'),
    
    -- Don approuvé en attente de réception
    ('DON-2024-0003', 'equipement', 'Prof. Mohamed', 'Trabelsi', '<EMAIL>',
     'Laboratoire Immunologie', 'Immunologie', '+216 71 123 458',
     'Modernisation du laboratoire d''immunologie', 'Laboratoire Immunologie - Salle principale',
     'Cytomètre en flux pour analyse cellulaire avancée',
     '{"canaux": 8, "lasers": 3, "debit": "10000 cellules/sec"}',
     'BD Biosciences MENA', '<EMAIL>', '789 Boulevard de l''Environnement, Tunis',
     200000.00, 'TND', 1, 'unité',
     'approuve_ms', 7, 'urgente',
     '2024-01-10', '2024-02-10',
     '<EMAIL>');

-- Initialiser les workflows pour les dons d'exemple
SELECT initialize_donation_workflow(
    (SELECT id FROM gestion_donations_ipt WHERE numero_don = 'DON-2024-0001'),
    'equipement',
    '<EMAIL>'
);

SELECT initialize_donation_workflow(
    (SELECT id FROM gestion_donations_ipt WHERE numero_don = 'DON-2024-0002'),
    'article',
    '<EMAIL>'
);

SELECT initialize_donation_workflow(
    (SELECT id FROM gestion_donations_ipt WHERE numero_don = 'DON-2024-0003'),
    'equipement',
    '<EMAIL>'
);

-- Simuler quelques validations pour les exemples
SELECT validate_donation_approvisionnement(
    (SELECT id FROM gestion_donations_ipt WHERE numero_don = 'DON-2024-0002'),
    '<EMAIL>',
    'Don validé - conforme aux besoins du laboratoire',
    true
);

-- Simuler l'avancement du don DON-2024-0003 jusqu'à l'approbation MS
DO $$
DECLARE
    don_id UUID;
BEGIN
    SELECT id INTO don_id FROM gestion_donations_ipt WHERE numero_don = 'DON-2024-0003';
    
    -- Validation approvisionnement
    PERFORM validate_donation_approvisionnement(don_id, '<EMAIL>', 'Validé', true);
    
    -- Avis technique favorable
    PERFORM provide_technical_advice(don_id, '<EMAIL>', 'Équipement conforme aux standards', true);
    
    -- Validation DG
    PERFORM validate_donation_dg(don_id, '<EMAIL>', 'Approuvé par la direction', true);
    
    -- Validation SD Achats
    PERFORM validate_donation_sd_achats(don_id, '<EMAIL>', 'Dossier complet pour soumission MS', true);
    
    -- Soumission au MS
    PERFORM submit_to_ministry(don_id, '<EMAIL>', 'Dossier soumis au Ministère de la Santé');
    
    -- Décision MS favorable
    PERFORM record_ministry_decision(don_id, 'approuve', 'MS-2024-001', 'Don approuvé par le Ministère', '<EMAIL>');
END $$;

-- =====================================================
-- 6. CONFIGURATION FINALE
-- =====================================================

\echo 'Configuration finale...'

-- Créer des index supplémentaires pour les performances
CREATE INDEX IF NOT EXISTS idx_donations_workflow_active 
ON gestion_donation_workflow(donation_id, numero_etape) 
WHERE statut_etape IN ('en_cours', 'en_attente');

CREATE INDEX IF NOT EXISTS idx_donations_by_service 
ON gestion_donations_ipt(demandeur_service, statut) 
WHERE deleted_at IS NULL;

CREATE INDEX IF NOT EXISTS idx_donations_by_date_range 
ON gestion_donations_ipt(date_demande, statut) 
WHERE deleted_at IS NULL;

-- Créer une séquence pour les numéros de don (backup)
CREATE SEQUENCE IF NOT EXISTS donation_number_seq START 1;

-- Créer une séquence pour les numéros d'inventaire (backup)
CREATE SEQUENCE IF NOT EXISTS inventory_number_seq START 1;

-- Fonction de nettoyage des données anciennes (à exécuter périodiquement)
CREATE OR REPLACE FUNCTION cleanup_old_donations()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Supprimer les brouillons de plus de 30 jours
    UPDATE gestion_donations_ipt 
    SET deleted_at = NOW()
    WHERE statut = 'brouillon' 
    AND created_at < NOW() - INTERVAL '30 days'
    AND deleted_at IS NULL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 7. VÉRIFICATIONS FINALES
-- =====================================================

\echo 'Vérifications finales...'

-- Vérifier que toutes les tables sont créées
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name LIKE 'gestion_donation%';
    
    IF table_count < 7 THEN
        RAISE EXCEPTION 'Erreur: Toutes les tables ne sont pas créées (% trouvées)', table_count;
    END IF;
    
    RAISE NOTICE 'Succès: % tables créées', table_count;
END $$;

-- Vérifier que les fonctions sont créées
DO $$
DECLARE
    function_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines 
    WHERE routine_schema = 'public' 
    AND routine_name LIKE '%donation%';
    
    IF function_count < 10 THEN
        RAISE EXCEPTION 'Erreur: Toutes les fonctions ne sont pas créées (% trouvées)', function_count;
    END IF;
    
    RAISE NOTICE 'Succès: % fonctions créées', function_count;
END $$;

-- Vérifier que les vues sont créées
DO $$
DECLARE
    view_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO view_count
    FROM information_schema.views 
    WHERE table_schema = 'public' 
    AND table_name LIKE 'donations_ipt%';
    
    IF view_count < 5 THEN
        RAISE EXCEPTION 'Erreur: Toutes les vues ne sont pas créées (% trouvées)', view_count;
    END IF;
    
    RAISE NOTICE 'Succès: % vues créées', view_count;
END $$;

-- Vérifier les données d'exemple
DO $$
DECLARE
    donation_count INTEGER;
    user_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO donation_count FROM gestion_donations_ipt WHERE deleted_at IS NULL;
    SELECT COUNT(*) INTO user_count FROM gestion_users_ipt WHERE is_active = true;
    
    RAISE NOTICE 'Données d''exemple: % dons, % utilisateurs', donation_count, user_count;
END $$;

COMMIT;

-- =====================================================
-- RÉSUMÉ DE L'INSTALLATION
-- =====================================================

\echo ''
\echo '=========================================='
\echo 'INSTALLATION TERMINÉE AVEC SUCCÈS!'
\echo '=========================================='
\echo ''
\echo 'Système de Gestion des Dons IPT installé:'
\echo '- 7 tables principales créées'
\echo '- 15+ fonctions de gestion installées'
\echo '- 6 vues optimisées créées'
\echo '- Politiques RLS configurées'
\echo '- 10 utilisateurs de test créés'
\echo '- 3 dons d''exemple avec workflows'
\echo ''
\echo 'Utilisateurs de test disponibles:'
\echo '- <EMAIL> (Admin Système)'
\echo '- <EMAIL> (Responsable Approvisionnement)'
\echo '- <EMAIL> (Direction Technique)'
\echo '- <EMAIL> (Directeur Général)'
\echo '- <EMAIL> (Sous-Direction Achats)'
\echo '- <EMAIL> (Réceptionniste)'
\echo '- <EMAIL> (Responsable Volet Équipement)'
\echo '- <EMAIL> (Demandeur)'
\echo ''
\echo 'Le système est prêt à être utilisé!'
\echo '=========================================='
