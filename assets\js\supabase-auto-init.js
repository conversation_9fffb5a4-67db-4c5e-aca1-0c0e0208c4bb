// Script d'initialisation automatique de Supabase
// Gère la création des tables, politiques RLS et données initiales

class SupabaseAutoInit {
    constructor(config) {
        this.config = config;
        this.client = null;
        this.initStatus = {
            connected: false,
            tablesCreated: false,
            rlsEnabled: false,
            dataSeeded: false,
            errors: []
        };
    }

    // Initialisation complète automatique
    async initialize() {
        console.log('🚀 Démarrage de l\'initialisation automatique Supabase...');
        
        try {
            // Étape 1: Connexion
            await this.connect();
            
            // Étape 2: Vérification et création des tables
            if (this.config.autoInit) {
                await this.ensureTables();
            }
            
            // Étape 3: Configuration RLS
            if (this.config.enableRLS) {
                await this.setupRLS();
            }
            
            // Étape 4: Migration des données
            if (this.config.autoMigrate) {
                await this.migrateData();
            }
            
            // Étape 5: Données initiales
            await this.seedInitialData();
            
            console.log('✅ Initialisation Supabase terminée avec succès');
            return { success: true, status: this.initStatus };
            
        } catch (error) {
            console.error('❌ Erreur lors de l\'initialisation Supabase:', error);
            this.initStatus.errors.push(error.message);
            return { success: false, error: error.message, status: this.initStatus };
        }
    }

    // Connexion au client Supabase
    async connect() {
        try {
            const supabaseLib = typeof supabase !== 'undefined' ? supabase :
                               typeof window.supabase !== 'undefined' ? window.supabase : null;

            if (!supabaseLib) {
                throw new Error('Supabase library not loaded');
            }

            this.client = supabaseLib.createClient(this.config.url, this.config.anonKey);
            
            // Test de connexion
            const { data, error } = await this.client.auth.getSession();
            if (error && error.message !== 'Invalid session') {
                throw error;
            }

            this.initStatus.connected = true;
            console.log('✅ Connexion Supabase établie');
            
        } catch (error) {
            console.error('❌ Erreur de connexion Supabase:', error);
            throw error;
        }
    }

    // Vérification et création des tables
    async ensureTables() {
        console.log('🔧 Vérification des tables...');
        
        const tables = Object.keys(this.config.tables);
        const tableStatus = {};
        
        for (const table of tables) {
            try {
                const tableName = this.config.tables[table];
                
                // Tester l'existence de la table
                const { data, error } = await this.client
                    .from(tableName)
                    .select('count')
                    .limit(1);
                
                if (error && error.code === 'PGRST116') {
                    // Table n'existe pas, la créer
                    console.log(`📋 Création de la table ${tableName}...`);
                    await this.createTable(table, tableName);
                    tableStatus[table] = 'created';
                } else if (error) {
                    console.warn(`⚠️ Erreur lors de la vérification de ${tableName}:`, error);
                    tableStatus[table] = 'error';
                } else {
                    console.log(`✅ Table ${tableName} existe déjà`);
                    tableStatus[table] = 'exists';
                }
                
            } catch (error) {
                console.error(`❌ Erreur pour la table ${table}:`, error);
                tableStatus[table] = 'error';
                this.initStatus.errors.push(`Table ${table}: ${error.message}`);
            }
        }
        
        this.initStatus.tablesCreated = true;
        console.log('📊 État des tables:', tableStatus);
    }

    // Création d'une table spécifique
    async createTable(tableKey, tableName) {
        const schemas = {
            users: `
                CREATE TABLE IF NOT EXISTS ${tableName} (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    username TEXT UNIQUE NOT NULL,
                    email TEXT UNIQUE NOT NULL,
                    nom TEXT NOT NULL,
                    prenom TEXT NOT NULL,
                    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user', 'chef_service', 'responsable_hygiene')),
                    service TEXT,
                    is_active BOOLEAN DEFAULT true,
                    last_login TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            `,
            messages: `
                CREATE TABLE IF NOT EXISTS ${tableName} (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    sender_username TEXT NOT NULL,
                    sender_name TEXT NOT NULL,
                    recipient TEXT NOT NULL,
                    content TEXT NOT NULL,
                    file_name TEXT,
                    file_data TEXT,
                    file_type TEXT,
                    is_read BOOLEAN DEFAULT false,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            `,
            commands: `
                CREATE TABLE IF NOT EXISTS ${tableName} (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    numero_commande TEXT UNIQUE NOT NULL,
                    fournisseur TEXT NOT NULL,
                    date_commande DATE NOT NULL,
                    statut TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'soumise', 'validee', 'livree', 'annulee')),
                    produits JSONB DEFAULT '[]'::jsonb,
                    total_ht DECIMAL(10,2),
                    total_ttc DECIMAL(10,2),
                    observations TEXT,
                    created_by TEXT NOT NULL,
                    validated_by TEXT,
                    validated_at TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            `,
            pv: `
                CREATE TABLE IF NOT EXISTS ${tableName} (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    numero_pv TEXT UNIQUE NOT NULL,
                    type_pv TEXT NOT NULL DEFAULT 'depot' CHECK (type_pv IN ('depot', 'controle_qualite', 'inventaire', 'reception')),
                    depot TEXT NOT NULL,
                    date_pv DATE NOT NULL,
                    responsable TEXT NOT NULL,
                    statut TEXT NOT NULL DEFAULT 'brouillon' CHECK (statut IN ('brouillon', 'en_cours', 'finalise', 'archive')),
                    produits JSONB DEFAULT '[]'::jsonb,
                    observations TEXT,
                    created_by TEXT NOT NULL,
                    validated_by TEXT,
                    validated_at TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            `,
            products: `
                CREATE TABLE IF NOT EXISTS ${tableName} (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    nom TEXT NOT NULL,
                    categorie TEXT NOT NULL,
                    reference TEXT UNIQUE,
                    fournisseur TEXT,
                    prix_unitaire DECIMAL(10,2),
                    unite TEXT DEFAULT 'unité',
                    description TEXT,
                    is_active BOOLEAN DEFAULT true,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            `,
            sessions: `
                CREATE TABLE IF NOT EXISTS ${tableName} (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID REFERENCES ${this.config.tables.users}(id) ON DELETE CASCADE,
                    username TEXT NOT NULL,
                    session_token TEXT UNIQUE NOT NULL,
                    ip_address INET,
                    user_agent TEXT,
                    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
            `
        };

        if (schemas[tableKey]) {
            try {
                // Note: En réalité, nous ne pouvons pas exécuter du DDL via l'API REST
                // Cette fonction simule la création et log les schémas
                console.log(`📋 Schéma pour ${tableName}:`, schemas[tableKey]);
                console.log(`⚠️ Veuillez exécuter ce schéma manuellement dans l'éditeur SQL de Supabase`);
                return true;
            } catch (error) {
                console.error(`❌ Erreur création table ${tableName}:`, error);
                throw error;
            }
        } else {
            console.warn(`⚠️ Schéma non défini pour la table ${tableKey}`);
        }
    }

    // Configuration des politiques RLS
    async setupRLS() {
        console.log('🔒 Configuration des politiques RLS...');
        
        try {
            // Note: Les politiques RLS doivent être configurées manuellement
            // via l'interface Supabase ou l'éditeur SQL
            const rlsPolicies = this.generateRLSPolicies();
            console.log('🔒 Politiques RLS générées:', rlsPolicies);
            console.log('⚠️ Veuillez appliquer ces politiques manuellement dans Supabase');
            
            this.initStatus.rlsEnabled = true;
            
        } catch (error) {
            console.error('❌ Erreur configuration RLS:', error);
            this.initStatus.errors.push(`RLS: ${error.message}`);
        }
    }

    // Génération des politiques RLS
    generateRLSPolicies() {
        return {
            users: [
                "ALTER TABLE gestion_users ENABLE ROW LEVEL SECURITY;",
                "CREATE POLICY \"Users can view all users\" ON gestion_users FOR SELECT USING (true);",
                "CREATE POLICY \"Users can update own profile\" ON gestion_users FOR UPDATE USING (auth.uid()::text = id::text);"
            ],
            messages: [
                "ALTER TABLE gestion_messages ENABLE ROW LEVEL SECURITY;",
                "CREATE POLICY \"Users can view their messages\" ON gestion_messages FOR SELECT USING (sender_username = current_user OR recipient = current_user);",
                "CREATE POLICY \"Users can send messages\" ON gestion_messages FOR INSERT WITH CHECK (sender_username = current_user);"
            ],
            commands: [
                "ALTER TABLE gestion_commands ENABLE ROW LEVEL SECURITY;",
                "CREATE POLICY \"Users can view commands\" ON gestion_commands FOR SELECT USING (true);",
                "CREATE POLICY \"Users can create commands\" ON gestion_commands FOR INSERT WITH CHECK (created_by = current_user);"
            ],
            pv: [
                "ALTER TABLE gestion_pv ENABLE ROW LEVEL SECURITY;",
                "CREATE POLICY \"Users can view PV\" ON gestion_pv FOR SELECT USING (true);",
                "CREATE POLICY \"Users can create PV\" ON gestion_pv FOR INSERT WITH CHECK (created_by = current_user);"
            ]
        };
    }

    // Migration des données existantes
    async migrateData() {
        console.log('🔄 Migration des données locales vers Supabase...');
        
        try {
            const localData = this.getLocalData();
            let migratedCount = 0;
            
            for (const [table, data] of Object.entries(localData)) {
                if (data && data.length > 0) {
                    console.log(`📤 Migration de ${data.length} éléments pour ${table}...`);
                    
                    const result = await this.migrateTableData(table, data);
                    if (result.success) {
                        migratedCount += result.count;
                        console.log(`✅ ${result.count} éléments migrés pour ${table}`);
                    }
                }
            }
            
            console.log(`✅ Migration terminée: ${migratedCount} éléments au total`);
            this.initStatus.dataSeeded = true;
            
        } catch (error) {
            console.error('❌ Erreur lors de la migration:', error);
            this.initStatus.errors.push(`Migration: ${error.message}`);
        }
    }

    // Récupération des données locales
    getLocalData() {
        const data = {};
        
        try {
            data.messages = JSON.parse(localStorage.getItem('gestion_messages') || '[]');
            data.commands = JSON.parse(localStorage.getItem('gestion_commands') || '[]');
            data.pv = JSON.parse(localStorage.getItem('gestion_pv') || '[]');
        } catch (error) {
            console.warn('⚠️ Erreur lecture données locales:', error);
        }
        
        return data;
    }

    // Migration d'une table spécifique
    async migrateTableData(table, data) {
        try {
            const tableName = this.config.tables[table];
            if (!tableName) {
                throw new Error(`Table ${table} non configurée`);
            }

            // Migrer par petits lots
            const batchSize = 50;
            let totalMigrated = 0;
            
            for (let i = 0; i < data.length; i += batchSize) {
                const batch = data.slice(i, i + batchSize);
                
                const { data: result, error } = await this.client
                    .from(tableName)
                    .upsert(batch, { onConflict: 'id' });
                
                if (error) {
                    console.warn(`⚠️ Erreur migration batch ${table}:`, error);
                } else {
                    totalMigrated += batch.length;
                }
            }
            
            return { success: true, count: totalMigrated };
            
        } catch (error) {
            console.error(`❌ Erreur migration ${table}:`, error);
            return { success: false, error: error.message };
        }
    }

    // Insertion des données initiales
    async seedInitialData() {
        console.log('🌱 Insertion des données initiales...');
        
        try {
            // Vérifier si des utilisateurs existent déjà
            const { data: existingUsers } = await this.client
                .from(this.config.tables.users)
                .select('count')
                .limit(1);
            
            if (!existingUsers || existingUsers.length === 0) {
                await this.createInitialUsers();
            }
            
            // Vérifier si des produits existent déjà
            const { data: existingProducts } = await this.client
                .from(this.config.tables.products)
                .select('count')
                .limit(1);
            
            if (!existingProducts || existingProducts.length === 0) {
                await this.createInitialProducts();
            }
            
        } catch (error) {
            console.error('❌ Erreur insertion données initiales:', error);
            this.initStatus.errors.push(`Seed data: ${error.message}`);
        }
    }

    // Création des utilisateurs initiaux
    async createInitialUsers() {
        const initialUsers = [
            {
                username: 'admin',
                email: '<EMAIL>',
                nom: 'Administrateur',
                prenom: 'Système',
                role: 'admin',
                service: 'Direction'
            },
            {
                username: 'moncef',
                email: '<EMAIL>',
                nom: 'Moncef',
                prenom: 'Chef',
                role: 'chef_service',
                service: 'Service Principal'
            }
        ];

        try {
            const { error } = await this.client
                .from(this.config.tables.users)
                .insert(initialUsers);
            
            if (error) throw error;
            console.log('✅ Utilisateurs initiaux créés');
            
        } catch (error) {
            console.warn('⚠️ Erreur création utilisateurs initiaux:', error);
        }
    }

    // Création des produits initiaux
    async createInitialProducts() {
        const initialProducts = [
            {
                nom: 'Acide sulfurique 98%',
                categorie: 'Acides',
                reference: 'AS-98-1L',
                fournisseur: 'Sigma-Aldrich',
                prix_unitaire: 45.50,
                unite: 'litre'
            },
            {
                nom: 'Hydroxyde de sodium',
                categorie: 'Bases',
                reference: 'NaOH-500G',
                fournisseur: 'Merck',
                prix_unitaire: 25.30,
                unite: 'gramme'
            }
        ];

        try {
            const { error } = await this.client
                .from(this.config.tables.products)
                .insert(initialProducts);
            
            if (error) throw error;
            console.log('✅ Produits initiaux créés');
            
        } catch (error) {
            console.warn('⚠️ Erreur création produits initiaux:', error);
        }
    }

    // Obtenir le statut d'initialisation
    getStatus() {
        return this.initStatus;
    }
}

// Export de la classe
if (typeof window !== 'undefined') {
    window.SupabaseAutoInit = SupabaseAutoInit;
}
