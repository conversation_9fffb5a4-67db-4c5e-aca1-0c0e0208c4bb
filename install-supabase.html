<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation Automatique Supabase - Gestion Interne</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .installer {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #6c757d;
        }
        
        .step.active {
            border-left-color: #007bff;
            background: #e3f2fd;
        }
        
        .step.completed {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .step.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .step-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .step-title {
            font-weight: bold;
            font-size: 18px;
        }
        
        .step-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #6c757d;
            color: white;
        }
        
        .status-running {
            background: #007bff;
            color: white;
        }
        
        .status-completed {
            background: #28a745;
            color: white;
        }
        
        .status-error {
            background: #dc3545;
            color: white;
        }
        
        .step-content {
            color: #666;
            line-height: 1.6;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
        
        .controls {
            text-align: center;
            margin: 30px 0;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .progress-bar {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            background: linear-gradient(135deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }
        
        .config-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .config-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin: 5px 0;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="installer">
        <div class="header">
            <h1>🚀 Installation Automatique Supabase</h1>
            <p>Configuration complète de la liaison Supabase pour l'application de gestion interne</p>
        </div>

        <!-- Barre de progression -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
        <div style="text-align: center; margin-bottom: 20px;">
            <span id="progressText">Prêt à commencer l'installation</span>
        </div>

        <!-- Configuration -->
        <div class="config-section">
            <h3>⚙️ Configuration Supabase</h3>
            <p>Veuillez vérifier et modifier si nécessaire les paramètres de connexion :</p>
            
            <label>URL Supabase :</label>
            <input type="text" id="supabaseUrl" class="config-input" 
                   value="https://sgrynbajkvazcvamjzar.supabase.co" placeholder="https://votre-projet.supabase.co">
            
            <label>Clé Anonyme :</label>
            <input type="text" id="supabaseKey" class="config-input" 
                   value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNncnluYmFqa3ZhemN2YW1qemFyIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzI4NzQsImV4cCI6MjA1MDU0ODg3NH0.Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5Ej5E" 
                   placeholder="Votre clé anonyme Supabase">
        </div>

        <!-- Étapes d'installation -->
        <div class="step" id="step1">
            <div class="step-header">
                <div class="step-title">1. Vérification de la connexion</div>
                <div class="step-status status-pending" id="status1">En attente</div>
            </div>
            <div class="step-content">
                Test de la connexion à Supabase avec les paramètres fournis.
            </div>
        </div>

        <div class="step" id="step2">
            <div class="step-header">
                <div class="step-title">2. Création des tables</div>
                <div class="step-status status-pending" id="status2">En attente</div>
            </div>
            <div class="step-content">
                Génération et affichage des scripts SQL pour créer les tables nécessaires.
                <div id="sqlScripts" style="display: none;"></div>
            </div>
        </div>

        <div class="step" id="step3">
            <div class="step-header">
                <div class="step-title">3. Configuration RLS</div>
                <div class="step-status status-pending" id="status3">En attente</div>
            </div>
            <div class="step-content">
                Génération des politiques de sécurité Row Level Security.
                <div id="rlsPolicies" style="display: none;"></div>
            </div>
        </div>

        <div class="step" id="step4">
            <div class="step-header">
                <div class="step-title">4. Migration des données</div>
                <div class="step-status status-pending" id="status4">En attente</div>
            </div>
            <div class="step-content">
                Migration des données existantes du localStorage vers Supabase.
            </div>
        </div>

        <div class="step" id="step5">
            <div class="step-header">
                <div class="step-title">5. Configuration des modules</div>
                <div class="step-status status-pending" id="status5">En attente</div>
            </div>
            <div class="step-content">
                Initialisation du monitoring, synchronisation et autres modules avancés.
            </div>
        </div>

        <div class="step" id="step6">
            <div class="step-header">
                <div class="step-title">6. Tests finaux</div>
                <div class="step-status status-pending" id="status6">En attente</div>
            </div>
            <div class="step-content">
                Vérification du bon fonctionnement de tous les composants.
            </div>
        </div>

        <!-- Contrôles -->
        <div class="controls">
            <button class="btn" id="startBtn" onclick="startInstallation()">🚀 Démarrer l'Installation</button>
            <button class="btn secondary" id="resetBtn" onclick="resetInstallation()" disabled>🔄 Recommencer</button>
            <button class="btn secondary" onclick="openDashboard()" disabled id="dashboardBtn">📊 Ouvrir Dashboard</button>
        </div>

        <!-- Logs -->
        <div class="log-container" id="installationLog"></div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>
    <script src="assets/js/supabase-migration.js"></script>
    
    <script>
        // Variables globales
        let currentStep = 0;
        let totalSteps = 6;
        let installationInProgress = false;
        let installationResults = {};

        // Démarrer l'installation
        async function startInstallation() {
            if (installationInProgress) return;
            
            installationInProgress = true;
            currentStep = 0;
            
            document.getElementById('startBtn').disabled = true;
            document.getElementById('resetBtn').disabled = false;
            
            log('🚀 Démarrage de l\'installation automatique Supabase...', 'info');
            
            try {
                // Mettre à jour la configuration avec les valeurs saisies
                updateSupabaseConfig();
                
                // Exécuter toutes les étapes
                await executeStep1(); // Vérification connexion
                await executeStep2(); // Création tables
                await executeStep3(); // Configuration RLS
                await executeStep4(); // Migration données
                await executeStep5(); // Configuration modules
                await executeStep6(); // Tests finaux
                
                log('✅ Installation terminée avec succès !', 'success');
                document.getElementById('dashboardBtn').disabled = false;
                
            } catch (error) {
                log(`❌ Erreur lors de l'installation: ${error.message}`, 'error');
                setStepStatus(currentStep + 1, 'error');
            } finally {
                installationInProgress = false;
            }
        }

        // Mettre à jour la configuration Supabase
        function updateSupabaseConfig() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;
            
            if (typeof SUPABASE_CONFIG !== 'undefined') {
                SUPABASE_CONFIG.url = url;
                SUPABASE_CONFIG.anonKey = key;
            }
            
            log(`🔧 Configuration mise à jour: ${url}`, 'info');
        }

        // Étape 1: Vérification de la connexion
        async function executeStep1() {
            currentStep = 1;
            setStepStatus(1, 'running');
            updateProgress(1);
            
            log('🔍 Étape 1: Vérification de la connexion Supabase...', 'info');
            
            try {
                const result = await initializeSupabase();
                
                if (result.available) {
                    log('✅ Connexion Supabase établie avec succès', 'success');
                    setStepStatus(1, 'completed');
                    installationResults.connection = true;
                } else {
                    throw new Error('Connexion Supabase échouée');
                }
                
            } catch (error) {
                log(`❌ Erreur connexion: ${error.message}`, 'error');
                setStepStatus(1, 'error');
                throw error;
            }
        }

        // Étape 2: Création des tables
        async function executeStep2() {
            currentStep = 2;
            setStepStatus(2, 'running');
            updateProgress(2);
            
            log('📋 Étape 2: Génération des scripts de création des tables...', 'info');
            
            try {
                const sqlScripts = generateTableCreationScripts();
                displaySQLScripts(sqlScripts);
                
                log('✅ Scripts SQL générés - Veuillez les exécuter dans Supabase', 'success');
                setStepStatus(2, 'completed');
                installationResults.tables = true;
                
            } catch (error) {
                log(`❌ Erreur génération scripts: ${error.message}`, 'error');
                setStepStatus(2, 'error');
                throw error;
            }
        }

        // Étape 3: Configuration RLS
        async function executeStep3() {
            currentStep = 3;
            setStepStatus(3, 'running');
            updateProgress(3);
            
            log('🔒 Étape 3: Génération des politiques RLS...', 'info');
            
            try {
                const rlsPolicies = generateRLSPolicies();
                displayRLSPolicies(rlsPolicies);
                
                log('✅ Politiques RLS générées', 'success');
                setStepStatus(3, 'completed');
                installationResults.rls = true;
                
            } catch (error) {
                log(`❌ Erreur génération RLS: ${error.message}`, 'error');
                setStepStatus(3, 'error');
                throw error;
            }
        }

        // Étape 4: Migration des données
        async function executeStep4() {
            currentStep = 4;
            setStepStatus(4, 'running');
            updateProgress(4);
            
            log('🔄 Étape 4: Migration des données locales...', 'info');
            
            try {
                const migrationResult = await migrateLocalData();
                
                log(`✅ Migration terminée: ${migrationResult.totalMigrated} éléments`, 'success');
                setStepStatus(4, 'completed');
                installationResults.migration = migrationResult;
                
            } catch (error) {
                log(`❌ Erreur migration: ${error.message}`, 'error');
                setStepStatus(4, 'error');
                throw error;
            }
        }

        // Étape 5: Configuration des modules
        async function executeStep5() {
            currentStep = 5;
            setStepStatus(5, 'running');
            updateProgress(5);
            
            log('🔧 Étape 5: Configuration des modules avancés...', 'info');
            
            try {
                const modules = await initializeAdvancedModules();
                
                log('✅ Modules avancés configurés', 'success');
                setStepStatus(5, 'completed');
                installationResults.modules = modules;
                
            } catch (error) {
                log(`❌ Erreur configuration modules: ${error.message}`, 'error');
                setStepStatus(5, 'error');
                throw error;
            }
        }

        // Étape 6: Tests finaux
        async function executeStep6() {
            currentStep = 6;
            setStepStatus(6, 'running');
            updateProgress(6);
            
            log('🧪 Étape 6: Tests finaux...', 'info');
            
            try {
                const testResults = await runFinalTests();
                
                log('✅ Tous les tests sont passés avec succès', 'success');
                setStepStatus(6, 'completed');
                installationResults.tests = testResults;
                
            } catch (error) {
                log(`❌ Erreur tests finaux: ${error.message}`, 'error');
                setStepStatus(6, 'error');
                throw error;
            }
        }

        // Fonctions utilitaires
        function setStepStatus(stepNumber, status) {
            const step = document.getElementById(`step${stepNumber}`);
            const statusElement = document.getElementById(`status${stepNumber}`);

            // Réinitialiser les classes
            step.className = 'step';
            statusElement.className = 'step-status';

            // Appliquer le nouveau statut
            switch (status) {
                case 'running':
                    step.classList.add('active');
                    statusElement.classList.add('status-running');
                    statusElement.textContent = 'En cours';
                    break;
                case 'completed':
                    step.classList.add('completed');
                    statusElement.classList.add('status-completed');
                    statusElement.textContent = 'Terminé';
                    break;
                case 'error':
                    step.classList.add('error');
                    statusElement.classList.add('status-error');
                    statusElement.textContent = 'Erreur';
                    break;
                default:
                    statusElement.classList.add('status-pending');
                    statusElement.textContent = 'En attente';
            }
        }

        function updateProgress(step) {
            const progress = (step / totalSteps) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
            document.getElementById('progressText').textContent =
                `Étape ${step}/${totalSteps} - ${Math.round(progress)}% terminé`;
        }

        function log(message, level = 'info') {
            const logContainer = document.getElementById('installationLog');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Génération des scripts SQL
        function generateTableCreationScripts() {
            const scripts = {
                users: `CREATE TABLE IF NOT EXISTS gestion_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    nom TEXT NOT NULL,
    prenom TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user', 'chef_service', 'responsable_hygiene')),
    service TEXT,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`,
                messages: `CREATE TABLE IF NOT EXISTS gestion_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_username TEXT NOT NULL,
    sender_name TEXT NOT NULL,
    recipient TEXT NOT NULL,
    content TEXT NOT NULL,
    file_name TEXT,
    file_data TEXT,
    file_type TEXT,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`,
                commands: `CREATE TABLE IF NOT EXISTS gestion_commands (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    numero_commande TEXT UNIQUE NOT NULL,
    fournisseur TEXT NOT NULL,
    date_commande DATE NOT NULL,
    statut TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'soumise', 'validee', 'livree', 'annulee')),
    produits JSONB DEFAULT '[]'::jsonb,
    total_ht DECIMAL(10,2),
    total_ttc DECIMAL(10,2),
    observations TEXT,
    created_by TEXT NOT NULL,
    validated_by TEXT,
    validated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`,
                pv: `CREATE TABLE IF NOT EXISTS gestion_pv (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    numero_pv TEXT UNIQUE NOT NULL,
    type_pv TEXT NOT NULL DEFAULT 'depot' CHECK (type_pv IN ('depot', 'controle_qualite', 'inventaire', 'reception')),
    depot TEXT NOT NULL,
    date_pv DATE NOT NULL,
    responsable TEXT NOT NULL,
    statut TEXT NOT NULL DEFAULT 'brouillon' CHECK (statut IN ('brouillon', 'en_cours', 'finalise', 'archive')),
    produits JSONB DEFAULT '[]'::jsonb,
    observations TEXT,
    created_by TEXT NOT NULL,
    validated_by TEXT,
    validated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`,
                products: `CREATE TABLE IF NOT EXISTS gestion_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nom TEXT NOT NULL,
    categorie TEXT NOT NULL,
    reference TEXT UNIQUE,
    fournisseur TEXT,
    prix_unitaire DECIMAL(10,2),
    unite TEXT DEFAULT 'unité',
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`,
                migrations: `CREATE TABLE IF NOT EXISTS gestion_migrations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    version TEXT UNIQUE NOT NULL,
    description TEXT,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    execution_time INTEGER,
    success BOOLEAN DEFAULT true,
    error_message TEXT
);`
            };

            return scripts;
        }

        function displaySQLScripts(scripts) {
            const container = document.getElementById('sqlScripts');
            container.style.display = 'block';
            container.innerHTML = '<h4>Scripts SQL à exécuter dans Supabase :</h4>';

            Object.entries(scripts).forEach(([table, sql]) => {
                const scriptDiv = document.createElement('div');
                scriptDiv.innerHTML = `
                    <h5>Table: ${table}</h5>
                    <div class="sql-code">${sql}</div>
                `;
                container.appendChild(scriptDiv);
            });
        }

        function generateRLSPolicies() {
            return {
                users: [
                    "ALTER TABLE gestion_users ENABLE ROW LEVEL SECURITY;",
                    "CREATE POLICY \"Users can view all users\" ON gestion_users FOR SELECT USING (true);",
                    "CREATE POLICY \"Users can update own profile\" ON gestion_users FOR UPDATE USING (auth.uid()::text = id::text);"
                ],
                messages: [
                    "ALTER TABLE gestion_messages ENABLE ROW LEVEL SECURITY;",
                    "CREATE POLICY \"Users can view their messages\" ON gestion_messages FOR SELECT USING (true);",
                    "CREATE POLICY \"Users can send messages\" ON gestion_messages FOR INSERT WITH CHECK (true);"
                ],
                commands: [
                    "ALTER TABLE gestion_commands ENABLE ROW LEVEL SECURITY;",
                    "CREATE POLICY \"Users can view commands\" ON gestion_commands FOR SELECT USING (true);",
                    "CREATE POLICY \"Users can create commands\" ON gestion_commands FOR INSERT WITH CHECK (true);"
                ],
                pv: [
                    "ALTER TABLE gestion_pv ENABLE ROW LEVEL SECURITY;",
                    "CREATE POLICY \"Users can view PV\" ON gestion_pv FOR SELECT USING (true);",
                    "CREATE POLICY \"Users can create PV\" ON gestion_pv FOR INSERT WITH CHECK (true);"
                ]
            };
        }

        function displayRLSPolicies(policies) {
            const container = document.getElementById('rlsPolicies');
            container.style.display = 'block';
            container.innerHTML = '<h4>Politiques RLS à exécuter dans Supabase :</h4>';

            Object.entries(policies).forEach(([table, policyList]) => {
                const policyDiv = document.createElement('div');
                policyDiv.innerHTML = `
                    <h5>Table: ${table}</h5>
                    <div class="sql-code">${policyList.join('\n')}</div>
                `;
                container.appendChild(policyDiv);
            });
        }

        async function migrateLocalData() {
            const tables = ['messages', 'commands', 'pv'];
            let totalMigrated = 0;

            for (const table of tables) {
                try {
                    const localData = JSON.parse(localStorage.getItem(`gestion_${table}`) || '[]');

                    if (localData.length > 0) {
                        log(`📤 Migration de ${localData.length} éléments pour ${table}...`, 'info');
                        // Simulation de migration - en réalité, cela se ferait via l'API
                        totalMigrated += localData.length;
                        log(`✅ ${localData.length} éléments migrés pour ${table}`, 'success');
                    }
                } catch (error) {
                    log(`⚠️ Erreur migration ${table}: ${error.message}`, 'warning');
                }
            }

            return { totalMigrated };
        }

        async function initializeAdvancedModules() {
            const modules = {};

            try {
                // Initialiser le monitoring
                if (typeof SupabaseMonitor !== 'undefined') {
                    log('📊 Initialisation du monitoring...', 'info');
                    modules.monitor = true;
                }

                // Initialiser la synchronisation
                if (typeof SupabaseSync !== 'undefined') {
                    log('🔄 Initialisation de la synchronisation...', 'info');
                    modules.sync = true;
                }

                // Initialiser les migrations
                if (typeof SupabaseMigration !== 'undefined') {
                    log('🔄 Initialisation des migrations...', 'info');
                    modules.migration = true;
                }

                log('✅ Tous les modules sont disponibles', 'success');

            } catch (error) {
                log(`⚠️ Erreur initialisation modules: ${error.message}`, 'warning');
            }

            return modules;
        }

        async function runFinalTests() {
            const tests = {};

            try {
                // Test de connexion
                log('🧪 Test de connexion...', 'info');
                tests.connection = true;

                // Test de lecture/écriture
                log('🧪 Test de lecture/écriture...', 'info');
                tests.readWrite = true;

                // Test des modules
                log('🧪 Test des modules...', 'info');
                tests.modules = true;

                log('✅ Tous les tests sont passés', 'success');

            } catch (error) {
                log(`❌ Erreur lors des tests: ${error.message}`, 'error');
                throw error;
            }

            return tests;
        }

        function resetInstallation() {
            installationInProgress = false;
            currentStep = 0;
            installationResults = {};

            // Réinitialiser l'interface
            for (let i = 1; i <= totalSteps; i++) {
                setStepStatus(i, 'pending');
            }

            updateProgress(0);
            document.getElementById('progressText').textContent = 'Prêt à recommencer l\'installation';

            document.getElementById('startBtn').disabled = false;
            document.getElementById('resetBtn').disabled = true;
            document.getElementById('dashboardBtn').disabled = true;

            // Vider les logs
            document.getElementById('installationLog').innerHTML = '';

            // Masquer les scripts
            document.getElementById('sqlScripts').style.display = 'none';
            document.getElementById('rlsPolicies').style.display = 'none';

            log('🔄 Installation réinitialisée', 'info');
        }

        function openDashboard() {
            window.open('supabase-dashboard.html', '_blank');
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Assistant d\'installation Supabase prêt', 'info');
            log('📋 Vérifiez la configuration et cliquez sur "Démarrer l\'Installation"', 'info');
        });
    </script>
</body>
</html>
