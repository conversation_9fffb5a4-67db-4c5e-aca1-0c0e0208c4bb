# 🎁 Système de Gestion des Dons IPT

## 📋 Vue d'ensemble

Le système de gestion des dons de l'Institut Pasteur de Tunis (IPT) automatise le processus complet de gestion des dons selon la procédure officielle de l'institut. Il gère deux types de dons : **Articles** et **Équipements**, chacun suivant un workflow spécifique d'approbation.

## ✨ Fonctionnalités Principales

### 🔄 Workflow Automatisé
- **Articles** : Demandeur → DG → MS → Réception
- **Équipements** : Demandeur → DG → DT (avis technique) → MS → Réception → Inventaire (RVE)

### 📄 Gestion Documentaire
- Upload et stockage sécurisé des documents
- Types supportés : PDF, Word, Images
- Documents requis : Demande explicative, Lettre de don, BP, Factures, BL, etc.

### 🔔 Notifications Automatiques
- Notifications en temps réel pour chaque étape
- Alertes par email (configurable)
- Suivi des délais de traitement

### 📊 Tableau de Bord et Statistiques
- Vue d'ensemble des dons en cours
- Statistiques par type, statut, période
- Rapports de performance
- Métriques de temps de traitement

## 🚀 Installation Rapide

### 1. Prérequis
```bash
✅ Projet Supabase configuré
✅ Tables utilisateurs existantes
✅ Système d'authentification fonctionnel
```

### 2. Installation Base de Données
Exécutez dans l'ordre dans l'éditeur SQL Supabase :

```sql
-- 1. Structure de base
\i donations-complete-setup.sql

-- 2. Fonctions de workflow
\i donations-functions.sql
\i donations-functions-part2.sql

-- 3. Politiques de sécurité
\i donations-rls-policies.sql

-- 4. Données de test (optionnel)
\i donations-test-data.sql

-- 5. Validation
\i validate-donations-installation.sql
```

### 3. Configuration Stockage
```sql
-- Créer le bucket documents
INSERT INTO storage.buckets (id, name, public) 
VALUES ('documents', 'documents', false);
```

### 4. Accès Interface
- **Interface principale** : `donations-management.html`
- **Tests système** : `test-donations-system.html`

## 📱 Utilisation

### Créer un Nouveau Don

1. **Accéder à l'interface** : Ouvrir `donations-management.html`
2. **Nouveau don** : Cliquer sur "➕ Nouveau Don"
3. **Type** : Sélectionner Article ou Équipement
4. **Informations** : Remplir donneur, demandeur, justification
5. **Items** : Ajouter les articles/équipements
6. **Documents** : Joindre les pièces requises
7. **Soumettre** : Envoyer au DG pour validation

### Workflow d'Approbation

#### Articles 📦
```
Demandeur → DG → MS → Réception
    ↓        ↓     ↓       ↓
  Création  Valid. Autor. Stockage
```

#### Équipements 🔧
```
Demandeur → DG → DT → MS → Réception → RVE
    ↓        ↓     ↓    ↓       ↓        ↓
  Création  Valid. Avis Autor. Stockage Inventaire
```

### Rôles et Permissions

| Rôle | Créer | Valider | Avis Tech | Autoriser | Recevoir | Inventaire |
|------|-------|---------|-----------|-----------|----------|------------|
| **Demandeur** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **DG** | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **DT** | ✅ | ❌ | ✅ | ❌ | ❌ | ❌ |
| **MS** | ✅ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **Réceptionniste** | ✅ | ❌ | ❌ | ❌ | ✅ | ❌ |
| **RVE** | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ |
| **Admin** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

## 🔧 Configuration Technique

### Tables Principales
```sql
gestion_donations          -- Dons principaux
gestion_donation_items     -- Articles/équipements
gestion_donation_documents -- Documents joints
gestion_donation_workflow  -- Historique workflow
gestion_donation_approvals -- Approbations
```

### Fonctions Clés
```sql
generate_donation_number()           -- DON-2024-0001
generate_inventory_number()          -- NI-2024-000001
submit_donation_to_dg()              -- Soumettre au DG
validate_donation_by_dg()            -- Validation DG
provide_technical_advice()           -- Avis technique DT
authorize_donation_by_ms()           -- Autorisation MS
mark_donation_received()             -- Marquer reçu
assign_inventory_number()            -- Attribuer NI
```

### Vues Disponibles
```sql
donations_complete    -- Vue complète avec détails
donations_stats       -- Statistiques globales
```

## 🧪 Tests et Validation

### Tests Automatisés
Utilisez `test-donations-system.html` pour :
- ✅ Connexion Supabase
- ✅ Existence des tables
- ✅ Fonctions SQL
- ✅ Opérations CRUD
- ✅ Politiques RLS
- ✅ Workflow complet

### Validation Installation
```sql
-- Exécuter le script de validation
\i validate-donations-installation.sql
```

## 📊 Métriques et Monitoring

### Statistiques Disponibles
- **Total dons** : Nombre total de dons
- **Par type** : Articles vs Équipements
- **Par statut** : En cours, Approuvés, Refusés
- **Valeur totale** : Estimation financière
- **Temps moyen** : Durée de traitement
- **Taux d'approbation** : Pourcentage de réussite

### Tableaux de Bord
- Vue d'ensemble en temps réel
- Filtres par statut, type, période
- Recherche avancée
- Export des données

## 🔒 Sécurité

### Row Level Security (RLS)
- **Isolation des données** : Chaque utilisateur voit ses dons
- **Permissions par rôle** : Accès contrôlé selon le workflow
- **Audit trail** : Historique complet des actions

### Stockage Sécurisé
- **Documents chiffrés** : Stockage Supabase sécurisé
- **Accès contrôlé** : Permissions par utilisateur
- **Backup automatique** : Sauvegarde des fichiers

## 🐛 Dépannage

### Problèmes Courants

#### Tables manquantes
```sql
-- Réexécuter l'installation
\i donations-complete-setup.sql
```

#### Fonctions manquantes
```sql
-- Réinstaller les fonctions
\i donations-functions.sql
\i donations-functions-part2.sql
```

#### Erreurs de permissions
```sql
-- Vérifier les politiques RLS
\i donations-rls-policies.sql
```

#### Upload de fichiers
```sql
-- Vérifier le bucket documents
SELECT * FROM storage.buckets WHERE id = 'documents';
```

### Debug Mode
```javascript
// Activer les logs détaillés
window.donationsManager.debug = true;
console.log('Debug mode activé');
```

## 📞 Support

### Ressources
- 📖 **Documentation** : `DONATIONS_SYSTEM_GUIDE.md`
- 🧪 **Tests** : `test-donations-system.html`
- 🔧 **Validation** : `validate-donations-installation.sql`

### Contact
Pour assistance technique :
1. Vérifier les logs console
2. Exécuter les tests automatisés
3. Consulter la documentation Supabase
4. Contacter l'équipe de développement

## 🔄 Roadmap

### Version Actuelle (1.0.0)
- ✅ Workflow complet articles/équipements
- ✅ Gestion documentaire
- ✅ Interface utilisateur
- ✅ Tests automatisés
- ✅ Sécurité RLS

### Prochaines Versions
- 📧 Notifications email automatiques
- 📄 Export PDF des bons de sortie
- 📱 Application mobile
- 🔗 API REST pour intégrations
- 📈 Analytics avancés

---

**Développé pour l'Institut Pasteur de Tunis** 🧬  
*Système de gestion des dons - Version 1.0.0*
