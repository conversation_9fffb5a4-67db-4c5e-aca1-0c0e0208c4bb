<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Documents - Dons IPT</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    <link rel="stylesheet" href="assets/css/donations-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    <script src="assets/js/supabase-database.js"></script>
    
    <style>
        .documents-manager {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .documents-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .documents-tabs {
            display: flex;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }
        
        .tab-button {
            flex: 1;
            padding: 20px;
            border: none;
            background: #f8fafc;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }
        
        .tab-button.active {
            background: white;
            border-bottom-color: #3b82f6;
            color: #3b82f6;
        }
        
        .tab-content {
            display: none;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .document-form {
            background: #f8fafc;
            padding: 25px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 8px;
        }
        
        .form-input {
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            min-height: 100px;
            resize: vertical;
        }
        
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-success {
            background: #10b981;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .documents-list {
            margin-top: 30px;
        }
        
        .document-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.2s;
        }
        
        .document-card:hover {
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .document-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .document-title {
            font-weight: bold;
            color: #1f2937;
            font-size: 16px;
        }
        
        .document-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-cree {
            background: #dbeafe;
            color: #2563eb;
        }
        
        .status-valide {
            background: #d1fae5;
            color: #059669;
        }
        
        .status-termine {
            background: #dcfce7;
            color: #16a34a;
        }
        
        .status-annule {
            background: #fee2e2;
            color: #dc2626;
        }
        
        .document-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            flex-direction: column;
        }
        
        .info-label {
            font-size: 12px;
            color: #6b7280;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 4px;
        }
        
        .info-value {
            color: #1f2937;
            font-weight: 500;
        }
        
        .document-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .items-table th,
        .items-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .items-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .items-table tr:hover {
            background: #f8fafc;
        }
        
        .add-item-btn {
            background: #10b981;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-top: 10px;
        }
        
        .remove-item-btn {
            background: #ef4444;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
        }
        
        .search-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .search-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
        }
        
        .modal-header {
            background: #f8fafc;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .close {
            color: #6b7280;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }
        
        .close:hover {
            color: #1f2937;
        }
        
        .print-section {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            .document-card {
                break-inside: avoid;
                box-shadow: none;
                border: 1px solid #000;
            }
        }
    </style>
</head>
<body>
    <div class="documents-manager">
        <!-- Header -->
        <div class="documents-header">
            <h1>📄 Gestion des Documents - Dons IPT</h1>
            <p>Création et gestion des Bons de Prélèvement (BP) et Bons de Sortie (BS)</p>
        </div>

        <!-- Onglets -->
        <div class="documents-tabs">
            <button class="tab-button active" onclick="switchTab('bp')">
                📋 Bons de Prélèvement (BP)
            </button>
            <button class="tab-button" onclick="switchTab('bs')">
                📦 Bons de Sortie (BS)
            </button>
            <button class="tab-button" onclick="switchTab('recap')">
                📊 Tableaux Récapitulatifs
            </button>
        </div>

        <!-- Contenu BP -->
        <div id="tab-bp" class="tab-content active">
            <h2>📋 Bons de Prélèvement</h2>
            
            <!-- Recherche BP -->
            <div class="search-section">
                <h3>🔍 Rechercher des BP</h3>
                <div class="search-row">
                    <div class="form-group">
                        <label class="form-label">Numéro de don</label>
                        <input type="text" id="searchDonationBP" class="form-input" placeholder="DON-2024-0001">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Statut BP</label>
                        <select id="searchStatusBP" class="form-input">
                            <option value="">Tous les statuts</option>
                            <option value="cree">Créé</option>
                            <option value="valide">Validé</option>
                            <option value="en_cours">En cours</option>
                            <option value="termine">Terminé</option>
                            <option value="annule">Annulé</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Service</label>
                        <input type="text" id="searchServiceBP" class="form-input" placeholder="Service demandeur">
                    </div>
                    <div class="form-group">
                        <button onclick="searchBP()" class="btn btn-primary">🔍 Rechercher</button>
                    </div>
                </div>
            </div>

            <!-- Formulaire création BP -->
            <div class="document-form">
                <h3>➕ Créer un nouveau Bon de Prélèvement</h3>
                <form id="formCreateBP">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Don concerné *</label>
                            <select id="bpDonationId" class="form-input" required>
                                <option value="">Sélectionner un don</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Motif du prélèvement *</label>
                            <input type="text" id="bpMotif" class="form-input" required 
                                   placeholder="Ex: Besoin urgent pour analyses">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Lieu de prélèvement *</label>
                            <input type="text" id="bpLieu" class="form-input" required 
                                   placeholder="Ex: Magasin central IPT">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Date prélèvement prévue</label>
                            <input type="date" id="bpDatePrevue" class="form-input">
                        </div>
                    </div>
                    
                    <h4>Articles demandés</h4>
                    <table class="items-table" id="bpItemsTable">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Quantité</th>
                                <th>Unité</th>
                                <th>Observations</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="bpItemsBody">
                            <!-- Items seront ajoutés ici -->
                        </tbody>
                    </table>
                    <button type="button" onclick="addBPItem()" class="add-item-btn">
                        ➕ Ajouter un article
                    </button>
                    
                    <div class="form-actions">
                        <button type="button" onclick="resetBPForm()" class="btn btn-secondary">
                            🔄 Réinitialiser
                        </button>
                        <button type="submit" class="btn btn-primary">
                            💾 Créer le BP
                        </button>
                    </div>
                </form>
            </div>

            <!-- Liste des BP -->
            <div class="documents-list">
                <h3>📋 Liste des Bons de Prélèvement</h3>
                <div id="bpList">
                    <!-- Les BP seront affichés ici -->
                </div>
            </div>
        </div>

        <!-- Contenu BS -->
        <div id="tab-bs" class="tab-content">
            <h2>📦 Bons de Sortie</h2>
            
            <!-- Recherche BS -->
            <div class="search-section">
                <h3>🔍 Rechercher des BS</h3>
                <div class="search-row">
                    <div class="form-group">
                        <label class="form-label">Numéro BP</label>
                        <input type="text" id="searchBPNumber" class="form-input" placeholder="BP-2024-0001">
                    </div>
                    <div class="form-group">
                        <label class="form-label">Statut BS</label>
                        <select id="searchStatusBS" class="form-input">
                            <option value="">Tous les statuts</option>
                            <option value="cree">Créé</option>
                            <option value="valide">Validé</option>
                            <option value="sorti">Sorti</option>
                            <option value="recu">Reçu</option>
                            <option value="archive">Archivé</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Magasinier</label>
                        <input type="text" id="searchMagasinier" class="form-input" placeholder="Nom du magasinier">
                    </div>
                    <div class="form-group">
                        <button onclick="searchBS()" class="btn btn-primary">🔍 Rechercher</button>
                    </div>
                </div>
            </div>

            <!-- Formulaire création BS -->
            <div class="document-form">
                <h3>➕ Créer un nouveau Bon de Sortie</h3>
                <form id="formCreateBS">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">BP de référence *</label>
                            <select id="bsBPId" class="form-input" required>
                                <option value="">Sélectionner un BP</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Magasinier responsable *</label>
                            <input type="text" id="bsMagasinier" class="form-input" required 
                                   placeholder="Nom du magasinier">
                        </div>
                        <div class="form-group">
                            <label class="form-label">Service destination</label>
                            <input type="text" id="bsDestinationService" class="form-input" readonly>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Responsable destination</label>
                            <input type="text" id="bsDestinationResponsable" class="form-input" readonly>
                        </div>
                    </div>
                    
                    <h4>Articles sortis</h4>
                    <table class="items-table" id="bsItemsTable">
                        <thead>
                            <tr>
                                <th>Article</th>
                                <th>Quantité demandée</th>
                                <th>Quantité sortie</th>
                                <th>Unité</th>
                                <th>État</th>
                                <th>Observations</th>
                            </tr>
                        </thead>
                        <tbody id="bsItemsBody">
                            <!-- Items seront chargés depuis le BP -->
                        </tbody>
                    </table>
                    
                    <div class="form-actions">
                        <button type="button" onclick="resetBSForm()" class="btn btn-secondary">
                            🔄 Réinitialiser
                        </button>
                        <button type="submit" class="btn btn-success">
                            📦 Créer le BS
                        </button>
                    </div>
                </form>
            </div>

            <!-- Liste des BS -->
            <div class="documents-list">
                <h3>📦 Liste des Bons de Sortie</h3>
                <div id="bsList">
                    <!-- Les BS seront affichés ici -->
                </div>
            </div>
        </div>

        <!-- Contenu Récapitulatifs -->
        <div id="tab-recap" class="tab-content">
            <h2>📊 Tableaux Récapitulatifs</h2>
            
            <!-- Formulaire création récapitulatif -->
            <div class="document-form">
                <h3>➕ Créer un nouveau Tableau Récapitulatif</h3>
                <form id="formCreateRecap">
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Période début *</label>
                            <input type="date" id="recapDateDebut" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Période fin *</label>
                            <input type="date" id="recapDateFin" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Titre du récapitulatif</label>
                            <input type="text" id="recapTitre" class="form-input" 
                                   placeholder="Sera généré automatiquement">
                        </div>
                        <div class="form-group">
                            <label class="form-label">RVE responsable</label>
                            <input type="text" id="recapRVE" class="form-input" 
                                   placeholder="Nom du RVE">
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            📊 Générer le récapitulatif
                        </button>
                    </div>
                </form>
            </div>

            <!-- Liste des récapitulatifs -->
            <div class="documents-list">
                <h3>📊 Liste des Tableaux Récapitulatifs</h3>
                <div id="recapList">
                    <!-- Les récapitulatifs seront affichés ici -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal d'impression -->
    <div id="modalPrint" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="printModalTitle">Aperçu avant impression</h2>
                <span class="close" onclick="closePrintModal()">&times;</span>
            </div>
            <div class="modal-body" id="printModalBody">
                <!-- Contenu à imprimer -->
            </div>
            <div class="print-section no-print">
                <button onclick="printDocument()" class="btn btn-primary">
                    🖨️ Imprimer
                </button>
                <button onclick="closePrintModal()" class="btn btn-secondary">
                    Fermer
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/donations-documents-manager.js"></script>
    
    <script>
        // Initialisation
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📄 Initialisation du gestionnaire de documents...');
            
            try {
                // Vérifier l'authentification
                if (typeof SupabaseAuth !== 'undefined' && authSystem) {
                    const user = await authSystem.getCurrentUser();
                    if (!user) {
                        window.location.href = 'login.html';
                        return;
                    }
                }
                
                // Initialiser le gestionnaire
                if (typeof DocumentsManager !== 'undefined') {
                    await DocumentsManager.initialize();
                    console.log('✅ Gestionnaire de documents initialisé');
                }
                
            } catch (error) {
                console.error('❌ Erreur initialisation:', error);
            }
        });
        
        // Fonctions utilitaires
        function switchTab(tabName) {
            // Masquer tous les contenus
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Désactiver tous les boutons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Activer le contenu et bouton sélectionnés
            document.getElementById(`tab-${tabName}`).classList.add('active');
            event.target.classList.add('active');
        }
        
        function closePrintModal() {
            document.getElementById('modalPrint').style.display = 'none';
        }
        
        function printDocument() {
            window.print();
        }
        
        // Fermer modal en cliquant à l'extérieur
        window.onclick = function(event) {
            const modal = document.getElementById('modalPrint');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
