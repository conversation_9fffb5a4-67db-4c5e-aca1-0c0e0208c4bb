// Fix pour DatabaseManager - Assure la disponibilité du gestionnaire de base de données
// Ce script garantit que DatabaseManager est toujours disponible

(function() {
    'use strict';
    
    console.log('🔧 Initialisation du fix DatabaseManager...');
    
    // Fonction pour créer un DatabaseManager robuste
    function createRobustDatabaseManager() {
        return {
            // Vérifier la disponibilité
            isAvailable() {
                return true; // Toujours disponible avec fallback
            },

            // Sauvegarder des données
            async save(table, data) {
                try {
                    console.log(`💾 [DatabaseManager] Sauvegarde ${table}...`);
                    
                    // Ajouter les champs requis
                    const enrichedData = {
                        ...data,
                        id: data.id || this.generateId(),
                        created_at: data.created_at || new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };
                    
                    // Essayer Supabase d'abord
                    if (await this.trySupabaseSave(table, enrichedData)) {
                        return enrichedData;
                    }
                    
                    // Fallback localStorage
                    return this.saveToLocal(table, enrichedData);
                    
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur sauvegarde ${table}:`, error);
                    return this.saveToLocal(table, data);
                }
            },

            // Tentative de sauvegarde Supabase
            async trySupabaseSave(table, data) {
                try {
                    // Vérifier SupabaseUtils
                    const supabaseUtils = window.SupabaseUtils || 
                                         (typeof SupabaseUtils !== 'undefined' ? SupabaseUtils : null);

                    if (supabaseUtils && typeof supabaseUtils.isAvailable === 'function' && supabaseUtils.isAvailable()) {
                        console.log(`📤 [DatabaseManager] Tentative Supabase pour ${table}...`);
                        
                        const result = await supabaseUtils.saveData(table, data);
                        
                        if (result && result.success) {
                            console.log(`✅ [DatabaseManager] ${table} sauvegardé sur Supabase`);
                            return true;
                        } else {
                            console.warn(`⚠️ [DatabaseManager] Échec Supabase ${table}:`, result?.error);
                            return false;
                        }
                    } else {
                        console.log(`💾 [DatabaseManager] Supabase non disponible pour ${table}`);
                        return false;
                    }
                } catch (error) {
                    console.warn(`⚠️ [DatabaseManager] Erreur Supabase ${table}:`, error);
                    return false;
                }
            },

            // Sauvegarder en local
            saveToLocal(table, data) {
                try {
                    const key = `gestion_${table}`;
                    const existing = JSON.parse(localStorage.getItem(key) || '[]');
                    
                    // Assurer l'ID et les timestamps
                    const finalData = {
                        ...data,
                        id: data.id || this.generateId(),
                        created_at: data.created_at || new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };
                    
                    existing.push(finalData);
                    localStorage.setItem(key, JSON.stringify(existing));
                    
                    console.log(`💾 [DatabaseManager] ${table} sauvegardé en local`);
                    return finalData;
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur sauvegarde locale ${table}:`, error);
                    return null;
                }
            },

            // Charger des données
            async load(table, filters = {}) {
                try {
                    console.log(`📥 [DatabaseManager] Chargement ${table}...`);
                    
                    // Essayer Supabase d'abord
                    const supabaseData = await this.trySupabaseLoad(table, filters);
                    if (supabaseData) {
                        return supabaseData;
                    }
                    
                    // Fallback localStorage
                    return this.loadFromLocal(table, filters);
                    
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur chargement ${table}:`, error);
                    return this.loadFromLocal(table, filters);
                }
            },

            // Tentative de chargement Supabase
            async trySupabaseLoad(table, filters) {
                try {
                    const supabaseUtils = window.SupabaseUtils || 
                                         (typeof SupabaseUtils !== 'undefined' ? SupabaseUtils : null);

                    if (supabaseUtils && typeof supabaseUtils.isAvailable === 'function' && supabaseUtils.isAvailable()) {
                        console.log(`📤 [DatabaseManager] Chargement Supabase ${table}...`);
                        
                        const result = await supabaseUtils.loadData(table, filters);
                        
                        if (result && result.success) {
                            console.log(`✅ [DatabaseManager] ${table} chargé depuis Supabase: ${result.data.length} éléments`);
                            return result.data;
                        } else {
                            console.warn(`⚠️ [DatabaseManager] Échec chargement Supabase ${table}:`, result?.error);
                            return null;
                        }
                    } else {
                        console.log(`💾 [DatabaseManager] Supabase non disponible pour chargement ${table}`);
                        return null;
                    }
                } catch (error) {
                    console.warn(`⚠️ [DatabaseManager] Erreur chargement Supabase ${table}:`, error);
                    return null;
                }
            },

            // Charger depuis localStorage
            loadFromLocal(table, filters = {}) {
                try {
                    const key = `gestion_${table}`;
                    const data = JSON.parse(localStorage.getItem(key) || '[]');
                    
                    // Appliquer les filtres si nécessaire
                    let filteredData = data;
                    if (Object.keys(filters).length > 0) {
                        filteredData = data.filter(item => {
                            return Object.entries(filters).every(([key, value]) => {
                                return item[key] === value;
                            });
                        });
                    }
                    
                    console.log(`💾 [DatabaseManager] ${table} chargé depuis local: ${filteredData.length} éléments`);
                    return filteredData;
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur chargement local ${table}:`, error);
                    return [];
                }
            },

            // Mettre à jour des données
            async update(table, id, updates) {
                try {
                    console.log(`✏️ [DatabaseManager] Mise à jour ${table}/${id}...`);
                    
                    // Essayer Supabase d'abord
                    if (await this.trySupabaseUpdate(table, id, updates)) {
                        return true;
                    }
                    
                    // Fallback localStorage
                    return this.updateLocal(table, id, updates);
                    
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur mise à jour ${table}:`, error);
                    return this.updateLocal(table, id, updates);
                }
            },

            // Tentative de mise à jour Supabase
            async trySupabaseUpdate(table, id, updates) {
                try {
                    const supabaseUtils = window.SupabaseUtils || 
                                         (typeof SupabaseUtils !== 'undefined' ? SupabaseUtils : null);

                    if (supabaseUtils && typeof supabaseUtils.updateData === 'function' && supabaseUtils.isAvailable()) {
                        const result = await supabaseUtils.updateData(table, id, {
                            ...updates,
                            updated_at: new Date().toISOString()
                        });
                        
                        if (result && result.success) {
                            console.log(`✅ [DatabaseManager] ${table}/${id} mis à jour sur Supabase`);
                            return true;
                        }
                    }
                    return false;
                } catch (error) {
                    console.warn(`⚠️ [DatabaseManager] Erreur mise à jour Supabase:`, error);
                    return false;
                }
            },

            // Mise à jour locale
            updateLocal(table, id, updates) {
                try {
                    const key = `gestion_${table}`;
                    const data = JSON.parse(localStorage.getItem(key) || '[]');
                    const index = data.findIndex(item => item.id === id);
                    
                    if (index !== -1) {
                        data[index] = {
                            ...data[index],
                            ...updates,
                            updated_at: new Date().toISOString()
                        };
                        localStorage.setItem(key, JSON.stringify(data));
                        console.log(`✅ [DatabaseManager] ${table}/${id} mis à jour en local`);
                        return true;
                    } else {
                        console.warn(`⚠️ [DatabaseManager] Élément ${id} non trouvé dans ${table}`);
                        return false;
                    }
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur mise à jour locale:`, error);
                    return false;
                }
            },

            // Supprimer des données
            async delete(table, id) {
                try {
                    console.log(`🗑️ [DatabaseManager] Suppression ${table}/${id}...`);
                    
                    // Essayer Supabase d'abord
                    if (await this.trySupabaseDelete(table, id)) {
                        return true;
                    }
                    
                    // Fallback localStorage
                    return this.deleteLocal(table, id);
                    
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur suppression ${table}:`, error);
                    return this.deleteLocal(table, id);
                }
            },

            // Tentative de suppression Supabase
            async trySupabaseDelete(table, id) {
                try {
                    const supabaseUtils = window.SupabaseUtils || 
                                         (typeof SupabaseUtils !== 'undefined' ? SupabaseUtils : null);

                    if (supabaseUtils && typeof supabaseUtils.deleteData === 'function' && supabaseUtils.isAvailable()) {
                        const result = await supabaseUtils.deleteData(table, id);
                        
                        if (result && result.success) {
                            console.log(`✅ [DatabaseManager] ${table}/${id} supprimé de Supabase`);
                            return true;
                        }
                    }
                    return false;
                } catch (error) {
                    console.warn(`⚠️ [DatabaseManager] Erreur suppression Supabase:`, error);
                    return false;
                }
            },

            // Suppression locale
            deleteLocal(table, id) {
                try {
                    const key = `gestion_${table}`;
                    const data = JSON.parse(localStorage.getItem(key) || '[]');
                    const filtered = data.filter(item => item.id !== id);
                    
                    localStorage.setItem(key, JSON.stringify(filtered));
                    console.log(`✅ [DatabaseManager] ${table}/${id} supprimé en local`);
                    return true;
                } catch (error) {
                    console.error(`❌ [DatabaseManager] Erreur suppression locale:`, error);
                    return false;
                }
            },

            // Synchroniser tout
            async syncAll() {
                try {
                    console.log('🔄 [DatabaseManager] Synchronisation complète...');
                    
                    const supabaseUtils = window.SupabaseUtils || 
                                         (typeof SupabaseUtils !== 'undefined' ? SupabaseUtils : null);

                    if (supabaseUtils && typeof supabaseUtils.syncToSupabase === 'function' && supabaseUtils.isAvailable()) {
                        const result = await supabaseUtils.syncToSupabase();
                        console.log('✅ [DatabaseManager] Synchronisation terminée:', result);
                        return result;
                    } else {
                        console.log('💾 [DatabaseManager] Synchronisation non disponible (mode local)');
                        return { success: false, error: 'Supabase not available' };
                    }
                } catch (error) {
                    console.error('❌ [DatabaseManager] Erreur synchronisation:', error);
                    return { success: false, error: error.message };
                }
            },

            // Générer un ID unique
            generateId() {
                return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }
        };
    }

    // Fonction pour assurer la disponibilité du DatabaseManager
    function ensureDatabaseManager() {
        // Vérifier si DatabaseManager existe déjà
        if (typeof window.DatabaseManager === 'undefined' || window.DatabaseManager === null) {
            console.log('🔧 [Fix] Création du DatabaseManager...');
            window.DatabaseManager = createRobustDatabaseManager();
        } else {
            console.log('✅ [Fix] DatabaseManager déjà disponible');
        }

        // Ajouter une fonction de vérification globale
        window.getDatabaseManager = function() {
            if (typeof window.DatabaseManager !== 'undefined' && window.DatabaseManager !== null) {
                return window.DatabaseManager;
            } else {
                console.warn('⚠️ [Fix] DatabaseManager non trouvé, création d\'urgence...');
                window.DatabaseManager = createRobustDatabaseManager();
                return window.DatabaseManager;
            }
        };

        // Ajouter une fonction de test
        window.testDatabaseManager = function() {
            const dbManager = window.getDatabaseManager();
            if (dbManager && typeof dbManager.isAvailable === 'function') {
                console.log('✅ [Test] DatabaseManager fonctionnel');
                return true;
            } else {
                console.error('❌ [Test] DatabaseManager non fonctionnel');
                return false;
            }
        };
    }

    // Initialisation immédiate
    ensureDatabaseManager();

    // Réinitialisation périodique pour s'assurer que DatabaseManager reste disponible
    setInterval(() => {
        if (typeof window.DatabaseManager === 'undefined' || window.DatabaseManager === null) {
            console.warn('⚠️ [Fix] DatabaseManager perdu, recréation...');
            ensureDatabaseManager();
        }
    }, 5000); // Vérification toutes les 5 secondes

    console.log('✅ Fix DatabaseManager initialisé avec succès');

})();
