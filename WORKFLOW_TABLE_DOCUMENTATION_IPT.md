# 📋 Table de Documentation Workflow - Gestion des Dons IPT

## 🎯 Vue d'Ensemble

Cette table de documentation présente le **processus complet en 10 étapes** pour la gestion des dons à l'Institut Pasteur de Tunis, avec un mapping détaillé des responsabilités, documents et actions à chaque étape.

## 📊 Table Complète du Workflow

| Étape | Nom / Description | Responsable | Documents d'Entrée | Actions Réalisées | Documents de Sortie | Responsable Suivant |
|-------|-------------------|-------------|---------------------|-------------------|---------------------|---------------------|
| **1** | **Création de la Demande Initiale**<br>*Le donneur/bénéficiaire crée la demande avec tous les documents justificatifs* | **Bénéficiaire** | • Facture<br>• BL (<PERSON>)<br>• Lettre du don | • Saisie des informations du don<br>• Upload des documents justificatifs<br>• Validation des données saisies<br>• Soumission de la demande | • Accusé de réception<br>• Numéro de dossier | **Bénéficiaire** |
| **2** | **Création du BP Bénéficiaire**<br>*Le bénéficiaire génère le Bon de Prélèvement pour validation* | **Bénéficiaire** | • Dossier de demande<br>• Documents justificatifs | • Analyse de la demande<br>• Génération du BP du bénéficiaire<br>• Validation des spécifications<br>• Transmission au magasinier | • BP du bénéficiaire<br>• Dossier validé | **Magasinier** |
| **3** | **Vérification Magasinier**<br>*Le magasinier vérifie physiquement les articles/équipements* | **Magasinier** | • BP du bénéficiaire<br>• Facture<br>• BL<br>• Lettre du don | • Localisation physique des articles<br>• Vérification de la conformité<br>• Contrôle de l'état physique<br>• Documentation des observations<br>• Validation de la disponibilité | • Rapport de vérification<br>• Photos des articles<br>• BP validé | **Transitaire** |
| **4** | **Validation Transitaire**<br>*Le transitaire valide les aspects logistiques et transport* | **Transitaire** | • Tous documents précédents<br>• Rapport de vérification | • Analyse des conditions de transport<br>• Évaluation des risques logistiques<br>• Planification de la livraison<br>• Validation des moyens de transport<br>• Génération des instructions | • Instructions de transport<br>• Planning de livraison<br>• Autorisation de transport | **Réceptionniste** |
| **5** | **Processus de Réception**<br>*Le réceptionniste effectue la réception physique avec documentation complète* | **Réceptionniste** | • Documentation complète<br>• Instructions de transport | • Contrôle de la livraison<br>• Vérification de la conformité<br>• Signature numérique du BL<br>• Documentation de l'état<br>• Confirmation de réception | • BL signé<br>• Rapport de réception<br>• Photos de réception | **RVE** |
| **6** | **Attribution d'Inventaire**<br>*Le RVE traite l'inventaire et génère le Bon de Sortie* | **RVE** | • Tous documents + BL signé | • Attribution du numéro d'inventaire<br>• Génération du BS (Bon de Sortie)<br>• Création des étiquettes code-barres<br>• Mise à jour de l'inventaire<br>• Préparation de l'affectation | • BS (Bon de Sortie)<br>• Étiquettes code-barres<br>• Fiche d'inventaire | **RVE** |
| **7** | **Gestion des Équipements**<br>*Le RVE gère l'allocation des équipements avec copie du dossier et BS* | **RVE** | • Copie du dossier complet<br>• BS | • Affectation au service destinataire<br>• Coordination avec le service<br>• Installation et configuration<br>• Formation des utilisateurs<br>• Tests de fonctionnement | • PV d'installation<br>• Certificat de formation<br>• Rapport d'affectation | **RVE** |
| **8** | **Traitement Final**<br>*Le RVE finalise le processus et prépare pour la comptabilité* | **RVE** | • Dossier complet<br>• PV d'installation | • Validation de l'installation<br>• Génération du tableau récapitulatif<br>• Vérification de la complétude<br>• Préparation des documents comptables<br>• Contrôle qualité final | • Tableau récap des dons<br>• Dossier finalisé<br>• Documents comptables | **RVE** |
| **9** | **Transmission Comptabilité**<br>*Le RVE transmet le tableau récapitulatif et BS à la comptabilité* | **RVE** | • Tableau récap des dons<br>• BS<br>• Dossier complet | • Vérification finale des documents<br>• Préparation du bordereau de transmission<br>• Transmission à la comptabilité<br>• Suivi de la réception<br>• Confirmation de l'intégration | • Bordereau de transmission<br>• Accusé de réception comptabilité | **RVE** |
| **10** | **Archivage/Clôture**<br>*Le RVE finalise et archive le processus complet de don* | **RVE** | • Dossier complet<br>• Confirmation comptabilité | • Constitution de l'archive complète<br>• Vérification de l'intégrité<br>• Archivage numérique sécurisé<br>• Génération du certificat<br>• Clôture définitive du dossier | • Archive complète<br>• Certificat d'archivage<br>• Rapport de clôture | **Archivage** |

## 🏷️ Légende des Documents

### 📥 Documents d'Entrée (Reçus)
- **Facture** : Document commercial du fournisseur
- **BL** : Bon de Livraison du transporteur
- **Lettre du don** : Courrier officiel du donateur
- **BP** : Bon de Prélèvement (généré en interne)
- **BS** : Bon de Sortie (généré en interne)

### 📤 Documents de Sortie (Générés/Transmis)
- **Accusé de réception** : Confirmation de réception de la demande
- **Rapport de vérification** : Document de contrôle qualité
- **Instructions de transport** : Directives logistiques
- **Tableau récap des dons** : Synthèse finale pour comptabilité
- **Certificat d'archivage** : Attestation de clôture

### 🔄 Documents Générés Automatiquement
- **Numéro de dossier** : Identifiant unique du don
- **Étiquettes code-barres** : Identification physique des équipements
- **PV d'installation** : Procès-verbal de mise en service
- **Archive complète** : Dossier numérique final

## 👥 Rôles et Responsabilités

### 🎯 Matrice des Responsabilités

| Rôle | Étapes | Responsabilités Principales |
|------|--------|----------------------------|
| **Bénéficiaire** | 1, 2 | Création demande, génération BP initial |
| **Magasinier** | 3 | Vérification physique, contrôle conformité |
| **Transitaire** | 4 | Validation logistique, planification transport |
| **Réceptionniste** | 5 | Réception physique, signature documents |
| **RVE** | 6, 7, 8, 9, 10 | Inventaire, affectation, finalisation, archivage |

### 📊 Charge de Travail par Rôle

| Rôle | Nombre d'Étapes | Pourcentage | Durée Moyenne |
|------|-----------------|-------------|---------------|
| **Bénéficiaire** | 2 | 20% | 2-3 jours |
| **Magasinier** | 1 | 10% | 1-2 jours |
| **Transitaire** | 1 | 10% | 1 jour |
| **Réceptionniste** | 1 | 10% | 1 jour |
| **RVE** | 5 | 50% | 5-8 jours |

## 📈 Métriques et Indicateurs

### ⏱️ Délais Standards

| Étape | Délai Standard | Délai Maximum | Criticité |
|-------|----------------|---------------|-----------|
| 1-2 | 2 jours | 3 jours | Normale |
| 3 | 1 jour | 2 jours | Élevée |
| 4 | 1 jour | 1 jour | Critique |
| 5 | 1 jour | 1 jour | Critique |
| 6-10 | 5 jours | 8 jours | Normale |
| **TOTAL** | **10 jours** | **15 jours** | - |

### 📊 Indicateurs de Performance

- **Taux de conformité** : > 95%
- **Délai moyen de traitement** : 10-12 jours
- **Taux de validation première fois** : > 85%
- **Satisfaction utilisateur** : > 90%

## 🔧 Fonctionnalités Administrateur

### 👨‍💼 Utilisateurs Autorisés
- **<EMAIL>** - Administrateur principal
- **<EMAIL>** - Gestionnaire workflow
- **<EMAIL>** - Responsable qualité
- **<EMAIL>** - Coordinateur processus
- **<EMAIL>** - Superviseur technique
- **<EMAIL>** - Contrôleur workflow

### ⚙️ Actions Administrateur Disponibles

| Action | Description | Permissions |
|--------|-------------|-------------|
| **➕ Ajouter Étape** | Créer une nouvelle étape dans le workflow | Admin uniquement |
| **✅ Valider Toutes les Étapes** | Contrôle de cohérence du workflow complet | Admin uniquement |
| **✏️ Modifier Workflow** | Activer le mode édition pour modifications | Admin uniquement |
| **🔄 Réinitialiser** | Restaurer le workflow aux valeurs par défaut | Admin uniquement |
| **📤 Exporter Documentation** | Générer un export PDF/Excel du workflow | Tous utilisateurs |

## 🎯 Utilisation de l'Interface

### 🌐 Accès à la Documentation
**URL** : `workflow-documentation-table.html`

### 🔑 Authentification
- Connexion via Supabase Auth
- Vérification automatique des permissions
- Affichage conditionnel des contrôles admin

### 📱 Fonctionnalités Interface
- **Table responsive** : Adaptation mobile/desktop
- **Recherche et filtrage** : Localisation rapide d'informations
- **Export/impression** : Génération de documents
- **Mode édition** : Modification en temps réel (admin)

## 📚 Documentation Complémentaire

### 🔗 Liens Utiles
- **Manuel utilisateur complet** : `MANUEL_UTILISATEUR_DONS_IPT.md`
- **Guide de référence rapide** : `GUIDE_REFERENCE_RAPIDE_DONS_IPT.md`
- **Formation administrateurs** : `FORMATION_ADMINISTRATEURS_DONS_IPT.md`

### 📞 Support et Assistance
- **Email support** : <EMAIL>
- **Téléphone** : +216 71 XXX XXX
- **Documentation en ligne** : Disponible 24h/7j

---

**📋 Table de Documentation Workflow - Gestion des Dons IPT**  
*Version 1.0 - Institut Pasteur de Tunis*  
*Référence définitive pour le processus complet de gestion des dons*
