// Configuration Supabase pour l'application de gestion interne
// Version complète avec authentification, base de données temps réel et stockage automatique
const SUPABASE_CONFIG = {
    // Configuration de connexion - NOUVEAU PROJET
    url: 'https://azoocjqlxduronwighwh.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6b29janFseGR1cm9ud2lnaHdoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2NDkzNjIsImV4cCI6MjA2NTIyNTM2Mn0.hUJEyPwxNW5HUHT33ZH4v0CmFxfzbv9bXPF3PzF-Pyw',
    serviceKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF6b29janFseGR1cm9ud2lnaHdoIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTY0OTM2MiwiZXhwIjoyMDY1MjI1MzYyfQ.rrdLSosx3n6cOTTcqE45Ad-A1dCdgAMe4lbvGp-6R-8',

    // Configuration avancée
    autoInit: true,
    autoMigrate: true,
    autoSync: true,
    enableRLS: true,
    
    // Configuration des tables - NOUVELLES TABLES ÉTENDUES
    tables: {
        // Tables principales
        users: 'user_profiles',
        messages: 'gestion_messages',
        commands: 'gestion_commands',
        command_items: 'gestion_command_items',
        pv: 'gestion_pv',
        products: 'gestion_products',
        sessions: 'user_sessions',
        notifications: 'notifications',
        activity_logs: 'activity_logs',

        // Tables étendues/alternatives
        users_legacy: 'gestion_users',
        messages_v2: 'gestion_messages_v2',
        commands_extended: 'gestion_commands_extended',
        pv_detailed: 'gestion_pv_detailed',
        products_detailed: 'gestion_products_detailed',
        migrations: 'gestion_migrations',

        // Tables pour la gestion des dons
        donations: 'gestion_donations',
        donation_documents: 'gestion_donation_documents',
        donation_workflow: 'gestion_donation_workflow',
        donation_equipment: 'gestion_donation_equipment',
        donation_approvals: 'gestion_donation_approvals',
        donation_inventory: 'gestion_donation_inventory'
    },

    // Configuration des vues
    views: {
        dashboard_stats: 'dashboard_stats',
        dashboard_stats_extended: 'dashboard_stats_extended',
        commands_with_details: 'commands_with_details',
        commands_extended_with_details: 'commands_extended_with_details',
        pv_detailed_with_stats: 'pv_detailed_with_stats',
        messages_v2_with_status: 'messages_v2_with_status',
        products_detailed_categorized: 'products_detailed_categorized',
        low_stock_products: 'low_stock_products',

        // Vues pour la gestion des dons
        donations_with_workflow: 'donations_with_workflow',
        donations_pending_approval: 'donations_pending_approval',
        donations_by_status: 'donations_by_status',
        donation_equipment_inventory: 'donation_equipment_inventory',
        donation_statistics: 'donation_statistics'
    },

    // Configuration du stockage (Storage)
    storage: {
        buckets: {
            avatars: 'avatars',
            documents: 'documents',
            images: 'images',
            attachments: 'attachments',
            donations: 'donations',
            donation_documents: 'donation-documents'
        },
        maxFileSize: {
            avatars: 5 * 1024 * 1024, // 5MB
            documents: 50 * 1024 * 1024, // 50MB
            images: 10 * 1024 * 1024, // 10MB
            attachments: 100 * 1024 * 1024, // 100MB
            donations: 100 * 1024 * 1024, // 100MB
            donation_documents: 200 * 1024 * 1024 // 200MB
        },
        allowedTypes: {
            avatars: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
            documents: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            images: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
            attachments: null, // Tous types autorisés
            donations: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'],
            donation_documents: null // Tous types autorisés pour les documents de don
        }
    },

    // Configuration de l'authentification
    auth: {
        enableSignUp: true,
        requireEmailConfirmation: true,
        passwordMinLength: 8,
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 heures
        autoRefreshToken: true,
        providers: {
            email: true,
            google: true,
            github: true
        },
        redirectUrls: {
            signIn: '/dashboard',
            signUp: '/welcome',
            signOut: '/login',
            passwordReset: '/reset-password'
        }
    },
    
    // Configuration des fonctionnalités - AMÉLIORÉES
    features: {
        realTimeSync: true,
        offlineMode: true,
        autoBackup: true,
        encryption: false,
        notifications: true,
        activityLogging: true,
        fileUpload: true,
        userPresence: true,
        collaboration: true
    },

    // Configuration temps réel
    realtime: {
        enabled: true,
        channels: {
            messages: 'realtime:gestion_messages',
            commands: 'realtime:gestion_commands',
            notifications: 'realtime:notifications',
            presence: 'realtime:user_presence',
            donations: 'realtime:gestion_donations',
            donation_workflow: 'realtime:gestion_donation_workflow'
        },
        heartbeatInterval: 30000, // 30 secondes
        reconnectDelay: 5000,
        maxReconnectAttempts: 10
    },
    
    // Configuration de la synchronisation
    sync: {
        interval: 30000, // 30 secondes
        batchSize: 100,
        retryAttempts: 3,
        retryDelay: 1000,
        autoReconnect: true,
        maxReconnectAttempts: 10,
        reconnectDelay: 5000
    },

    // Configuration du cache
    cache: {
        enabled: true,
        duration: 300000, // 5 minutes
        maxSize: 1000
    },

    // Configuration du monitoring
    monitoring: {
        enabled: true,
        logLevel: 'info', // 'debug', 'info', 'warn', 'error'
        metricsInterval: 60000, // 1 minute
        healthCheckInterval: 30000, // 30 secondes
        alertThreshold: 5 // Nombre d'erreurs avant alerte
    },

    // Configuration des migrations
    migrations: {
        enabled: true,
        autoRun: true,
        version: '1.0.0',
        backupBeforeMigration: true
    }
};

// Client Supabase global
let supabaseClient = null;
let isSupabaseAvailable = false;
let connectionStatus = 'disconnected';

// Instances des modules avancés
let autoInit = null;
let monitor = null;
let syncManager = null;
let migrationManager = null;

// Instances des modules temps réel
let realtimeCoordinator = null;
let notificationSystem = null;
let realtimeUIManager = null;
let userPresence = null;

// Instance du système de mصادقة
let authSystem = null;

// Initialisation complète de Supabase avec tous les modules
async function initializeSupabase() {
    try {
        console.log('🚀 Initialisation complète de Supabase...');

        // Vérifier si Supabase est disponible
        const supabaseLib = typeof supabase !== 'undefined' ? supabase :
                           typeof window.supabase !== 'undefined' ? window.supabase : null;

        if (!supabaseLib) {
            throw new Error('Supabase library not loaded');
        }

        // Créer le client Supabase
        supabaseClient = supabaseLib.createClient(
            SUPABASE_CONFIG.url,
            SUPABASE_CONFIG.anonKey
        );

        // Tester la connexion
        const { data, error } = await supabaseClient.auth.getSession();

        if (error && error.message !== 'Invalid session') {
            throw error;
        }

        // Vérifier la connectivité avec une requête simple
        const { data: testData, error: testError } = await supabaseClient
            .from('gestion_users')
            .select('count')
            .limit(1);

        // Si l'erreur est PGRST116 (table doesn't exist), c'est normal
        if (testError && testError.code !== 'PGRST116') {
            console.warn('Test de connexion Supabase:', testError);
        }

        isSupabaseAvailable = true;
        connectionStatus = 'connected';

        console.log('✅ Connexion Supabase établie');

        // Initialiser les modules avancés si disponibles
        await initializeAdvancedModules();

        // Configurer les listeners en temps réel si activé
        if (SUPABASE_CONFIG.features.realTimeSync) {
            setupRealTimeListeners();
        }

        console.log('✅ Supabase initialisé avec succès (mode complet)');
        return { client: supabaseClient, available: true, modules: getModuleStatus() };

    } catch (error) {
        console.warn('⚠️ Supabase non disponible, mode local activé:', error.message);
        isSupabaseAvailable = false;
        connectionStatus = 'disconnected';

        return { client: null, available: false, error: error.message };
    }
}

// Initialiser les modules avancés
async function initializeAdvancedModules() {
    console.log('🔧 Initialisation des modules avancés...');

    try {
        // 1. Auto-initialisation
        if (typeof SupabaseAutoInit !== 'undefined' && SUPABASE_CONFIG.autoInit) {
            console.log('🔄 Initialisation du module auto-init...');
            autoInit = new SupabaseAutoInit(SUPABASE_CONFIG);
            const initResult = await autoInit.initialize();

            if (initResult.success) {
                console.log('✅ Auto-initialisation réussie');
            } else {
                console.warn('⚠️ Auto-initialisation échouée:', initResult.error);
            }
        }

        // 2. Monitoring
        if (typeof SupabaseMonitor !== 'undefined' && SUPABASE_CONFIG.monitoring?.enabled) {
            console.log('📊 Initialisation du monitoring...');
            monitor = new SupabaseMonitor(SUPABASE_CONFIG);
            await monitor.startMonitoring(supabaseClient);
            console.log('✅ Monitoring actif');
        }

        // 3. Synchronisation
        if (typeof SupabaseSync !== 'undefined' && SUPABASE_CONFIG.autoSync) {
            console.log('🔄 Initialisation de la synchronisation...');
            syncManager = new SupabaseSync(SUPABASE_CONFIG, supabaseClient);

            if (SUPABASE_CONFIG.features.realTimeSync) {
                await syncManager.startSync();
                console.log('✅ Synchronisation automatique active');
            }
        }

        // 4. Migrations
        if (typeof SupabaseMigration !== 'undefined' && SUPABASE_CONFIG.migrations?.enabled) {
            console.log('🔄 Initialisation des migrations...');
            migrationManager = new SupabaseMigration(SUPABASE_CONFIG, supabaseClient);

            if (SUPABASE_CONFIG.migrations.autoRun) {
                const migrationResult = await migrationManager.runMigrations();
                console.log(`✅ Migrations terminées: ${migrationResult.migrationsRun} exécutées`);
            }
        }

        // 5. Système de mصادقة
        await initializeAuthSystem();

        // 6. Modules temps réel
        await initializeRealtimeModules();

        console.log('✅ Tous les modules avancés initialisés');

    } catch (error) {
        console.warn('⚠️ Erreur initialisation modules avancés:', error);
    }
}

// Initialiser le système de mصادقة
async function initializeAuthSystem() {
    console.log('🔐 Initialisation du système de mصادقة...');

    try {
        if (typeof SupabaseAuth !== 'undefined') {
            authSystem = new SupabaseAuth(SUPABASE_CONFIG, supabaseClient);
            await authSystem.initialize();

            // Configurer les callbacks d'authentification
            setupAuthCallbacks();

            console.log('✅ Système de mصادقة initialisé');
        } else {
            console.warn('⚠️ SupabaseAuth non disponible');
        }
    } catch (error) {
        console.error('❌ Erreur initialisation système de mصادقة:', error);
    }
}

// Configurer les callbacks d'authentification
function setupAuthCallbacks() {
    if (!authSystem) return;

    authSystem.on('onSignIn', (session) => {
        console.log('✅ Utilisateur connecté:', session.user.email);

        // Mettre à jour l'interface utilisateur
        updateUIForAuthenticatedUser(session.user);

        // Démarrer les modules temps réel avec l'utilisateur connecté
        if (realtimeCoordinator) {
            realtimeCoordinator.initialize(getCurrentUser());
        }
    });

    authSystem.on('onSignOut', () => {
        console.log('👋 Utilisateur déconnecté');

        // Mettre à jour l'interface utilisateur
        updateUIForUnauthenticatedUser();

        // Arrêter les modules temps réel
        if (realtimeCoordinator) {
            realtimeCoordinator.stop();
        }

        // Rediriger vers la page de connexion si nécessaire
        if (window.location.pathname !== '/login.html' && window.location.pathname !== '/') {
            window.location.href = 'login.html';
        }
    });

    authSystem.on('onError', (error) => {
        console.error('🚨 Erreur d\'authentification:', error);

        if (typeof showNotification === 'function') {
            showNotification('Erreur d\'authentification: ' + error.message, 'error');
        }
    });
}

// Initialiser les modules temps réel
async function initializeRealtimeModules() {
    if (!SUPABASE_CONFIG.features.realTimeSync) {
        console.log('⚠️ Temps réel désactivé dans la configuration');
        return;
    }

    console.log('🚀 Initialisation des modules temps réel...');

    try {
        // Obtenir l'utilisateur actuel
        const currentUser = getCurrentUser();

        // Initialiser le coordinateur temps réel
        if (typeof RealtimeCoordinator !== 'undefined') {
            console.log('🎯 Initialisation du coordinateur temps réel...');
            realtimeCoordinator = new RealtimeCoordinator(SUPABASE_CONFIG, supabaseClient);
            await realtimeCoordinator.initialize(currentUser);

            // Configurer les callbacks globaux
            setupRealtimeCallbacks();

            console.log('✅ Coordinateur temps réel initialisé');
        }

        console.log('✅ Modules temps réel initialisés');

    } catch (error) {
        console.error('❌ Erreur initialisation modules temps réel:', error);
    }
}

// Configurer les callbacks temps réel
function setupRealtimeCallbacks() {
    if (!realtimeCoordinator) return;

    // Callback de prêt
    realtimeCoordinator.on('onRealtimeReady', (data) => {
        console.log('🎉 Système temps réel prêt:', data);

        // Notifier l'interface
        if (typeof showNotification === 'function') {
            showNotification('Synchronisation temps réel active', 'success');
        }
    });

    // Callback d'erreur
    realtimeCoordinator.on('onError', (error) => {
        console.error('🚨 Erreur temps réel:', error);

        // Notifier l'interface
        if (typeof showNotification === 'function') {
            showNotification('Erreur de synchronisation temps réel', 'error');
        }
    });

    // Callback de synchronisation
    realtimeCoordinator.on('onSyncComplete', (data) => {
        console.log('✅ Synchronisation terminée:', data);
    });
}

// Obtenir l'utilisateur actuel
function getCurrentUser() {
    // Essayer de récupérer depuis le système d'authentification
    if (authSystem && authSystem.isUserAuthenticated()) {
        const currentUser = authSystem.getCurrentUser();
        if (currentUser) {
            const userData = currentUser.user_metadata || {};
            return {
                id: currentUser.id,
                username: userData.username || currentUser.email,
                email: currentUser.email,
                nom: userData.nom || '',
                prenom: userData.prenom || '',
                role: userData.role || 'user',
                service: userData.service || ''
            };
        }
    }

    // Essayer de récupérer depuis le localStorage
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
        try {
            return JSON.parse(storedUser);
        } catch (error) {
            console.warn('⚠️ Erreur parsing utilisateur stocké:', error);
        }
    }

    // Retourner null si aucun utilisateur trouvé
    return null;
}

// Mettre à jour l'interface pour utilisateur connecté
function updateUIForAuthenticatedUser(user) {
    // Cacher les formulaires de connexion
    const authForms = document.querySelectorAll('.auth-form, .login-form, .auth-container');
    authForms.forEach(form => {
        if (form) form.style.display = 'none';
    });

    // Afficher le contenu principal
    const mainContent = document.querySelector('.main-content, #main-app, .container');
    if (mainContent) {
        mainContent.style.display = 'block';
    }

    // Mettre à jour les informations utilisateur dans l'interface
    updateUserInfoInUI(user);
}

// Mettre à jour l'interface pour utilisateur non connecté
function updateUIForUnauthenticatedUser() {
    // Afficher les formulaires de connexion
    const authForms = document.querySelectorAll('.auth-form, .login-form, .auth-container');
    authForms.forEach(form => {
        if (form) form.style.display = 'block';
    });

    // Cacher le contenu principal
    const mainContent = document.querySelector('.main-content, #main-app, .container');
    if (mainContent) {
        mainContent.style.display = 'none';
    }
}

// Mettre à jour les informations utilisateur dans l'interface
function updateUserInfoInUI(user) {
    if (!user) return;

    const userNameElements = document.querySelectorAll('.user-name, #user-name, .username');
    const userEmailElements = document.querySelectorAll('.user-email, #user-email');
    const userRoleElements = document.querySelectorAll('.user-role, #user-role');

    const displayName = user.prenom && user.nom
        ? `${user.prenom} ${user.nom}`
        : user.username || user.email;

    userNameElements.forEach(el => {
        if (el) el.textContent = displayName;
    });

    userEmailElements.forEach(el => {
        if (el) el.textContent = user.email || '';
    });

    userRoleElements.forEach(el => {
        if (el) el.textContent = user.role || 'مستخدم';
    });
}

// Obtenir le statut des modules
function getModuleStatus() {
    return {
        autoInit: autoInit !== null,
        monitor: monitor !== null,
        sync: syncManager !== null,
        migration: migrationManager !== null,
        realtime: SUPABASE_CONFIG.features.realTimeSync,
        auth: authSystem !== null,
        realtimeCoordinator: realtimeCoordinator !== null,
        notificationSystem: notificationSystem !== null,
        realtimeUIManager: realtimeUIManager !== null,
        userPresence: userPresence !== null
    };
}

// Configuration des listeners en temps réel
function setupRealTimeListeners() {
    if (!supabaseClient || !isSupabaseAvailable) return;

    try {
        // Listener pour les messages
        supabaseClient
            .channel('gestion_messages')
            .on('postgres_changes', 
                { event: '*', schema: 'public', table: SUPABASE_CONFIG.tables.messages },
                (payload) => {
                    console.log('Message en temps réel:', payload);
                    handleRealTimeMessage(payload);
                }
            )
            .subscribe();

        // Listener pour les commandes
        supabaseClient
            .channel('gestion_commands')
            .on('postgres_changes',
                { event: '*', schema: 'public', table: SUPABASE_CONFIG.tables.commands },
                (payload) => {
                    console.log('Commande en temps réel:', payload);
                    handleRealTimeCommand(payload);
                }
            )
            .subscribe();

        // Listener pour les PV
        supabaseClient
            .channel('gestion_pv')
            .on('postgres_changes',
                { event: '*', schema: 'public', table: SUPABASE_CONFIG.tables.pv },
                (payload) => {
                    console.log('PV en temps réel:', payload);
                    handleRealTimePV(payload);
                }
            )
            .subscribe();

        console.log('🔄 Listeners temps réel configurés');

    } catch (error) {
        console.warn('Erreur configuration listeners temps réel:', error);
    }
}

// Gestionnaires d'événements temps réel
function handleRealTimeMessage(payload) {
    // Traiter les nouveaux messages en temps réel
    if (typeof window.handleNewMessage === 'function') {
        window.handleNewMessage(payload.new);
    }
}

function handleRealTimeCommand(payload) {
    // Traiter les nouvelles commandes en temps réel
    if (typeof window.handleNewCommand === 'function') {
        window.handleNewCommand(payload.new);
    }
}

function handleRealTimePV(payload) {
    // Traiter les nouveaux PV en temps réel
    if (typeof window.handleNewPV === 'function') {
        window.handleNewPV(payload.new);
    }
}

// Fonctions utilitaires Supabase
const SupabaseUtils = {
    // Vérifier si Supabase est disponible
    isAvailable() {
        return isSupabaseAvailable;
    },

    // Obtenir le client Supabase
    getClient() {
        return supabaseClient;
    },

    // Obtenir le statut de connexion
    getConnectionStatus() {
        return connectionStatus;
    },

    // Sauvegarder des données
    async saveData(table, data) {
        if (!isSupabaseAvailable) {
            return this.saveToLocalStorage(table, data);
        }

        try {
            const { data: result, error } = await supabaseClient
                .from(SUPABASE_CONFIG.tables[table])
                .insert(data)
                .select();

            if (error) throw error;

            // Sauvegarder aussi en local comme backup
            this.saveToLocalStorage(table, data);

            return { success: true, data: result };

        } catch (error) {
            console.warn(`Erreur sauvegarde Supabase ${table}:`, error);
            // Fallback vers localStorage
            return this.saveToLocalStorage(table, data);
        }
    },

    // Charger des données
    async loadData(table, filters = {}) {
        if (!isSupabaseAvailable) {
            return this.loadFromLocalStorage(table);
        }

        try {
            let query = supabaseClient.from(SUPABASE_CONFIG.tables[table]).select('*');

            // Appliquer les filtres
            Object.entries(filters).forEach(([key, value]) => {
                query = query.eq(key, value);
            });

            const { data, error } = await query.order('created_at', { ascending: false });

            if (error) throw error;

            // Mettre à jour le cache local
            this.saveToLocalStorage(table, data);

            return { success: true, data: data || [] };

        } catch (error) {
            console.warn(`Erreur chargement Supabase ${table}:`, error);
            // Fallback vers localStorage
            return this.loadFromLocalStorage(table);
        }
    },

    // Mettre à jour des données
    async updateData(table, id, updates) {
        if (!isSupabaseAvailable) {
            return this.updateLocalStorage(table, id, updates);
        }

        try {
            const { data, error } = await supabaseClient
                .from(SUPABASE_CONFIG.tables[table])
                .update(updates)
                .eq('id', id)
                .select();

            if (error) throw error;

            // Mettre à jour aussi en local
            this.updateLocalStorage(table, id, updates);

            return { success: true, data };

        } catch (error) {
            console.warn(`Erreur mise à jour Supabase ${table}:`, error);
            return this.updateLocalStorage(table, id, updates);
        }
    },

    // Supprimer des données
    async deleteData(table, id) {
        if (!isSupabaseAvailable) {
            return this.deleteFromLocalStorage(table, id);
        }

        try {
            const { error } = await supabaseClient
                .from(SUPABASE_CONFIG.tables[table])
                .delete()
                .eq('id', id);

            if (error) throw error;

            // Supprimer aussi en local
            this.deleteFromLocalStorage(table, id);

            return { success: true };

        } catch (error) {
            console.warn(`Erreur suppression Supabase ${table}:`, error);
            return this.deleteFromLocalStorage(table, id);
        }
    },

    // Fonctions localStorage (fallback)
    saveToLocalStorage(table, data) {
        try {
            const key = `gestion_${table}`;
            const existing = JSON.parse(localStorage.getItem(key) || '[]');
            
            if (Array.isArray(data)) {
                existing.push(...data);
            } else {
                existing.push(data);
            }
            
            localStorage.setItem(key, JSON.stringify(existing));
            return { success: true, data, source: 'localStorage' };

        } catch (error) {
            console.error('Erreur localStorage save:', error);
            return { success: false, error: error.message };
        }
    },

    loadFromLocalStorage(table) {
        try {
            const key = `gestion_${table}`;
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            return { success: true, data, source: 'localStorage' };

        } catch (error) {
            console.error('Erreur localStorage load:', error);
            return { success: false, data: [], error: error.message };
        }
    },

    updateLocalStorage(table, id, updates) {
        try {
            const key = `gestion_${table}`;
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            const index = data.findIndex(item => item.id === id);
            
            if (index !== -1) {
                data[index] = { ...data[index], ...updates, updated_at: new Date().toISOString() };
                localStorage.setItem(key, JSON.stringify(data));
                return { success: true, data: data[index], source: 'localStorage' };
            }
            
            return { success: false, error: 'Item not found' };

        } catch (error) {
            console.error('Erreur localStorage update:', error);
            return { success: false, error: error.message };
        }
    },

    deleteFromLocalStorage(table, id) {
        try {
            const key = `gestion_${table}`;
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            const filtered = data.filter(item => item.id !== id);
            
            localStorage.setItem(key, JSON.stringify(filtered));
            return { success: true, source: 'localStorage' };

        } catch (error) {
            console.error('Erreur localStorage delete:', error);
            return { success: false, error: error.message };
        }
    },

    // Synchroniser les données locales vers Supabase
    async syncToSupabase() {
        if (!isSupabaseAvailable) {
            console.log('Supabase non disponible pour la synchronisation');
            return { success: false, error: 'Supabase not available' };
        }

        try {
            const tables = ['messages', 'commands', 'pv'];
            const results = {};

            for (const table of tables) {
                const localData = this.loadFromLocalStorage(table);
                if (localData.success && localData.data.length > 0) {
                    // Synchroniser par petits lots
                    const batchSize = SUPABASE_CONFIG.sync.batchSize;
                    const batches = [];
                    
                    for (let i = 0; i < localData.data.length; i += batchSize) {
                        batches.push(localData.data.slice(i, i + batchSize));
                    }

                    let syncedCount = 0;
                    for (const batch of batches) {
                        try {
                            const { error } = await supabaseClient
                                .from(SUPABASE_CONFIG.tables[table])
                                .upsert(batch, { onConflict: 'id' });

                            if (!error) {
                                syncedCount += batch.length;
                            }
                        } catch (batchError) {
                            console.warn(`Erreur sync batch ${table}:`, batchError);
                        }
                    }

                    results[table] = { total: localData.data.length, synced: syncedCount };
                }
            }

            console.log('📤 Synchronisation terminée:', results);
            return { success: true, results };

        } catch (error) {
            console.error('Erreur synchronisation:', error);
            return { success: false, error: error.message };
        }
    }
};

// Fonctions utilitaires avancées
const SupabaseAdvanced = {
    // Obtenir toutes les instances des modules
    getModules() {
        return {
            autoInit,
            monitor,
            syncManager,
            migrationManager,
            realtimeCoordinator,
            notificationSystem,
            realtimeUIManager,
            userPresence,
            client: supabaseClient
        };
    },

    // Obtenir un rapport de santé complet
    async getHealthReport() {
        const report = {
            timestamp: new Date().toISOString(),
            connection: {
                status: connectionStatus,
                available: isSupabaseAvailable
            },
            modules: getModuleStatus()
        };

        if (monitor) {
            report.monitoring = monitor.getHealthReport();
        }

        if (syncManager) {
            report.sync = {
                stats: syncManager.getStats(),
                conflicts: syncManager.getConflicts()
            };
        }

        if (migrationManager) {
            report.migrations = {
                history: migrationManager.getMigrationHistory(),
                available: migrationManager.getAvailableMigrations()
            };
        }

        return report;
    },

    // Forcer une synchronisation complète
    async forceFullSync() {
        if (syncManager) {
            return await syncManager.forceSync();
        }
        throw new Error('Gestionnaire de synchronisation non disponible');
    },

    // Exécuter les migrations manuellement
    async runMigrations() {
        if (migrationManager) {
            return await migrationManager.runMigrations();
        }
        throw new Error('Gestionnaire de migrations non disponible');
    },

    // Réinitialiser complètement Supabase
    async reinitialize() {
        console.log('🔄 Réinitialisation complète de Supabase...');

        // Arrêter tous les modules
        if (monitor) {
            monitor.stopMonitoring();
        }

        if (syncManager) {
            syncManager.stopSync();
        }

        // Arrêter les modules temps réel
        if (realtimeCoordinator) {
            await realtimeCoordinator.stop();
        }

        // Réinitialiser les variables
        autoInit = null;
        monitor = null;
        syncManager = null;
        migrationManager = null;
        realtimeCoordinator = null;
        notificationSystem = null;
        realtimeUIManager = null;
        userPresence = null;
        supabaseClient = null;
        isSupabaseAvailable = false;
        connectionStatus = 'disconnected';

        // Relancer l'initialisation
        return await initializeSupabase();
    },

    // Obtenir les statistiques détaillées
    getDetailedStats() {
        const stats = {
            connection: {
                status: connectionStatus,
                available: isSupabaseAvailable
            },
            modules: getModuleStatus()
        };

        if (monitor) {
            stats.monitoring = monitor.getMetrics();
        }

        if (syncManager) {
            stats.sync = syncManager.getStats();
        }

        return stats;
    },

    // Configurer les callbacks globaux
    setupGlobalCallbacks() {
        if (monitor) {
            monitor.on('onError', (error) => {
                console.error('🚨 Erreur Supabase globale:', error);
                // Déclencher des notifications ou alertes
                if (typeof showNotification === 'function') {
                    showNotification('Erreur de connexion Supabase', 'error');
                }
            });

            monitor.on('onReconnect', () => {
                console.log('🔗 Reconnexion Supabase réussie');
                if (typeof showNotification === 'function') {
                    showNotification('Connexion Supabase restaurée', 'success');
                }
            });
        }

        if (syncManager) {
            syncManager.on('onConflict', (conflict) => {
                console.warn('⚠️ Conflit de synchronisation:', conflict);
                if (typeof showNotification === 'function') {
                    showNotification(`Conflit détecté: ${conflict.table}`, 'warning');
                }
            });
        }
    },

    // Exporter les données pour backup
    async exportAllData() {
        const exportData = {
            timestamp: new Date().toISOString(),
            version: SUPABASE_CONFIG.migrations?.version || '1.0.0',
            data: {}
        };

        const tables = Object.keys(SUPABASE_CONFIG.tables);

        for (const table of tables) {
            try {
                if (isSupabaseAvailable) {
                    const { data, error } = await supabaseClient
                        .from(SUPABASE_CONFIG.tables[table])
                        .select('*');

                    if (!error) {
                        exportData.data[table] = data;
                    }
                } else {
                    // Fallback vers localStorage
                    const localData = JSON.parse(localStorage.getItem(`gestion_${table}`) || '[]');
                    exportData.data[table] = localData;
                }
            } catch (error) {
                console.warn(`Erreur export ${table}:`, error);
                exportData.data[table] = [];
            }
        }

        return exportData;
    },

    // Importer des données depuis un backup
    async importData(importData) {
        if (!importData || !importData.data) {
            throw new Error('Données d\'import invalides');
        }

        const results = {};

        for (const [table, data] of Object.entries(importData.data)) {
            try {
                if (isSupabaseAvailable && Array.isArray(data) && data.length > 0) {
                    const { error } = await supabaseClient
                        .from(SUPABASE_CONFIG.tables[table])
                        .upsert(data, { onConflict: 'id' });

                    if (error) {
                        throw error;
                    }

                    results[table] = { success: true, count: data.length };
                } else {
                    // Sauvegarder en localStorage
                    localStorage.setItem(`gestion_${table}`, JSON.stringify(data));
                    results[table] = { success: true, count: data.length, source: 'localStorage' };
                }
            } catch (error) {
                console.error(`Erreur import ${table}:`, error);
                results[table] = { success: false, error: error.message };
            }
        }

        return results;
    },

    // Fonctions temps réel avancées

    // Démarrer le système temps réel
    async startRealtime(currentUser = null) {
        if (realtimeCoordinator) {
            return await realtimeCoordinator.initialize(currentUser);
        }
        throw new Error('Coordinateur temps réel non disponible');
    },

    // Arrêter le système temps réel
    async stopRealtime() {
        if (realtimeCoordinator) {
            return await realtimeCoordinator.stop();
        }
    },

    // Forcer une synchronisation temps réel
    async forceRealtimeSync() {
        if (realtimeCoordinator) {
            return await realtimeCoordinator.forceFullSync();
        }
        throw new Error('Coordinateur temps réel non disponible');
    },

    // Obtenir les statistiques temps réel
    getRealtimeStats() {
        const stats = {
            coordinator: realtimeCoordinator ? realtimeCoordinator.getStats() : null,
            notifications: notificationSystem ? notificationSystem.getStats() : null,
            uiManager: realtimeUIManager ? realtimeUIManager.getStats() : null,
            userPresence: userPresence ? userPresence.getStats() : null
        };

        return stats;
    },

    // Envoyer une notification temps réel
    sendRealtimeNotification(options) {
        if (notificationSystem) {
            return notificationSystem.showNotification(options);
        }
        console.warn('⚠️ Système de notifications non disponible');
    },

    // Obtenir les utilisateurs connectés
    getConnectedUsers() {
        if (userPresence) {
            return userPresence.getConnectedUsers();
        }
        return [];
    },

    // Mettre à jour le statut utilisateur
    async updateUserStatus(status) {
        if (userPresence) {
            return await userPresence.updateUserStatus(status);
        }
    },

    // Configurer les callbacks temps réel
    onRealtimeEvent(event, callback) {
        if (realtimeCoordinator) {
            realtimeCoordinator.on(event, callback);
        }
    },

    // Obtenir l'état de santé temps réel
    getRealtimeHealth() {
        return {
            isActive: realtimeCoordinator?.isActive || false,
            modules: getModuleStatus(),
            lastSync: realtimeCoordinator?.syncState?.lastSync || null,
            errorCount: realtimeCoordinator?.syncState?.errorCount || 0,
            successCount: realtimeCoordinator?.syncState?.successCount || 0
        };
    }
};

// Export des configurations et fonctions
if (typeof window !== 'undefined') {
    window.SUPABASE_CONFIG = SUPABASE_CONFIG;
    window.SupabaseUtils = SupabaseUtils;
    window.SupabaseAdvanced = SupabaseAdvanced;
    window.initializeSupabase = initializeSupabase;

    // Exposer les instances des modules
    window.getSupabaseModules = () => SupabaseAdvanced.getModules();
}
