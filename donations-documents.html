<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Documents - Institut Pasteur <PERSON></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            color: #2d3748;
            font-size: 28px;
            font-weight: 700;
        }

        .donation-info {
            background: #f7fafc;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 12px;
            color: #718096;
            font-weight: 600;
            text-transform: uppercase;
            margin-bottom: 4px;
        }

        .info-value {
            color: #2d3748;
            font-weight: 500;
        }

        .documents-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-content {
            padding: 30px;
        }

        .document-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .document-type-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            transition: all 0.2s;
        }

        .document-type-card:hover {
            border-color: #4299e1;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .document-type-card.required {
            border-color: #f56565;
            background: #fed7d7;
        }

        .document-type-card.uploaded {
            border-color: #48bb78;
            background: #f0fff4;
        }

        .document-type-header {
            display: flex;
            align-items: center;
            justify-content: between;
            margin-bottom: 15px;
        }

        .document-type-icon {
            font-size: 24px;
            margin-right: 10px;
        }

        .document-type-title {
            font-weight: 600;
            color: #2d3748;
            flex: 1;
        }

        .document-type-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-required {
            background: #fed7d7;
            color: #c53030;
        }

        .status-optional {
            background: #e2e8f0;
            color: #718096;
        }

        .status-uploaded {
            background: #c6f6d5;
            color: #22543d;
        }

        .document-type-description {
            color: #718096;
            font-size: 14px;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .upload-area:hover {
            border-color: #4299e1;
            background: #f7fafc;
        }

        .upload-area.dragover {
            border-color: #4299e1;
            background: #ebf8ff;
        }

        .upload-icon {
            font-size: 32px;
            color: #a0aec0;
            margin-bottom: 10px;
        }

        .upload-text {
            color: #4a5568;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .upload-hint {
            color: #718096;
            font-size: 12px;
        }

        .document-list {
            margin-top: 15px;
        }

        .document-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .document-info {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .document-icon {
            font-size: 20px;
            color: #4299e1;
        }

        .document-details {
            display: flex;
            flex-direction: column;
        }

        .document-name {
            font-weight: 500;
            color: #2d3748;
            font-size: 14px;
        }

        .document-meta {
            font-size: 12px;
            color: #718096;
        }

        .document-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: #4299e1;
            color: white;
        }

        .btn-success {
            background: #48bb78;
            color: white;
        }

        .btn-danger {
            background: #f56565;
            color: white;
        }

        .btn-secondary {
            background: #edf2f7;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }

        .signature-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .signature-pad {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin: 20px 0;
            cursor: crosshair;
        }

        .signature-controls {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .progress-indicator {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .progress-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .progress-bar {
            background: #e2e8f0;
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            background: linear-gradient(90deg, #4299e1, #48bb78);
            height: 100%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 14px;
            color: #718096;
            text-align: center;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .alert-info {
            background: #ebf8ff;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .alert-warning {
            background: #fffbeb;
            color: #744210;
            border: 1px solid #f6e05e;
        }

        .loading {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Modal styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 30px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .document-types {
                grid-template-columns: 1fr;
            }

            .donation-info {
                grid-template-columns: 1fr;
            }

            .document-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .document-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }

        /* Styles pour la signature */
        .signature-canvas {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: crosshair;
            touch-action: none;
        }

        .signature-canvas:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        /* Styles pour le viewer de documents */
        .document-viewer {
            width: 100%;
            height: 500px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            overflow: hidden;
        }

        .document-viewer iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        .document-viewer img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        /* Styles pour les étapes de validation */
        .validation-steps {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            padding: 0 20px;
        }

        .validation-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .validation-step::after {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: #e2e8f0;
            z-index: -1;
        }

        .validation-step:last-child::after {
            display: none;
        }

        .validation-step.completed::after {
            background: #48bb78;
        }

        .validation-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            color: #718096;
            margin-bottom: 8px;
        }

        .validation-step.completed .validation-circle {
            background: #48bb78;
            color: white;
        }

        .validation-step.current .validation-circle {
            background: #4299e1;
            color: white;
        }

        .validation-label {
            font-size: 12px;
            color: #718096;
            text-align: center;
        }

        .validation-step.current .validation-label {
            color: #4299e1;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📄 Gestion des Documents</h1>
            <div class="donation-info" id="donationInfo">
                <div class="info-item">
                    <div class="info-label">Numéro de Don</div>
                    <div class="info-value" id="donationNumber">Chargement...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Type</div>
                    <div class="info-value" id="donationType">Chargement...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Demandeur</div>
                    <div class="info-value" id="donationRequester">Chargement...</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Statut</div>
                    <div class="info-value" id="donationStatus">Chargement...</div>
                </div>
            </div>
        </div>

        <!-- Indicateur de progression -->
        <div class="progress-indicator">
            <div class="progress-title">📊 Progression des Documents</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <div class="progress-text" id="progressText">0 document(s) sur 4 requis</div>
        </div>

        <!-- Messages d'alerte -->
        <div id="alertContainer"></div>

        <!-- Section des types de documents -->
        <div class="documents-section">
            <div class="section-header">
                <div class="section-title">
                    <span>📁</span>
                    Documents Requis
                </div>
            </div>
            <div class="section-content">
                <div class="document-types" id="documentTypes">
                    <!-- Les types de documents seront générés dynamiquement -->
                </div>
            </div>
        </div>

        <!-- Section de signature numérique -->
        <div class="signature-section" id="signatureSection" style="display: none;">
            <div class="section-header">
                <div class="section-title">
                    <span>✍️</span>
                    Signature Numérique
                </div>
            </div>
            <div class="section-content">
                <div class="alert alert-info">
                    <span>ℹ️</span>
                    <div>Signez numériquement le bon de livraison pour confirmer la réception des articles/équipements.</div>
                </div>
                
                <canvas id="signatureCanvas" class="signature-canvas" width="600" height="200"></canvas>
                
                <div class="signature-controls">
                    <button class="btn btn-secondary" onclick="clearSignature()">
                        🗑️ Effacer
                    </button>
                    <button class="btn btn-primary" onclick="saveSignature()">
                        💾 Sauvegarder Signature
                    </button>
                </div>
            </div>
        </div>

        <!-- Section des documents uploadés -->
        <div class="documents-section">
            <div class="section-header">
                <div class="section-title">
                    <span>📋</span>
                    Documents Uploadés
                </div>
            </div>
            <div class="section-content">
                <div id="uploadedDocuments">
                    <div class="loading">
                        <div class="spinner"></div>
                        Chargement des documents...
                    </div>
                </div>
            </div>
        </div>

        <!-- Étapes de validation -->
        <div class="documents-section">
            <div class="section-header">
                <div class="section-title">
                    <span>🔄</span>
                    Étapes de Validation
                </div>
            </div>
            <div class="section-content">
                <div class="validation-steps" id="validationSteps">
                    <!-- Les étapes seront générées dynamiquement -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de visualisation de document -->
    <div id="documentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Visualisation du Document</h3>
                <button class="modal-close" onclick="closeDocumentModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="documentViewer" class="document-viewer">
                    <!-- Le contenu du document sera affiché ici -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/donations-documents.js"></script>
    
    <script>
        // Initialisation de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📄 Initialisation de la gestion des documents...');
            
            try {
                // Récupérer l'ID du don depuis l'URL
                const urlParams = new URLSearchParams(window.location.search);
                const donationId = urlParams.get('id');
                
                if (!donationId) {
                    showAlert('ID du don manquant dans l\'URL', 'error');
                    return;
                }

                // Initialiser Supabase
                if (typeof initializeSupabase === 'function') {
                    const { client, available } = await initializeSupabase();
                    
                    if (available) {
                        console.log('✅ Supabase connecté pour la gestion des documents');
                        
                        // Initialiser le gestionnaire de documents
                        if (typeof DonationsDocuments !== 'undefined') {
                            await donationsDocuments.initialize(donationId);
                            console.log('✅ Gestionnaire de documents initialisé');
                        }
                    } else {
                        console.warn('⚠️ Supabase non disponible - mode dégradé');
                        showAlert('Connexion à la base de données indisponible.', 'error');
                    }
                } else {
                    console.error('❌ Fonction initializeSupabase non trouvée');
                }
                
            } catch (error) {
                console.error('❌ Erreur initialisation gestion documents:', error);
                showAlert('Erreur lors de l\'initialisation. Veuillez rafraîchir la page.', 'error');
            }
        });

        // Fonctions globales pour l'interface
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            alert.innerHTML = `<span>${icon}</span><div>${message}</div>`;
            
            container.appendChild(alert);
            
            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        function openDocumentModal(documentId) {
            if (window.donationsDocuments) {
                donationsDocuments.openDocumentModal(documentId);
            }
        }

        function closeDocumentModal() {
            document.getElementById('documentModal').style.display = 'none';
        }

        function clearSignature() {
            if (window.donationsDocuments) {
                donationsDocuments.clearSignature();
            }
        }

        function saveSignature() {
            if (window.donationsDocuments) {
                donationsDocuments.saveSignature();
            }
        }

        function downloadDocument(documentId) {
            if (window.donationsDocuments) {
                donationsDocuments.downloadDocument(documentId);
            }
        }

        function deleteDocument(documentId) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce document ?')) {
                if (window.donationsDocuments) {
                    donationsDocuments.deleteDocument(documentId);
                }
            }
        }
    </script>
</body>
</html>
