/**
 * Interface de Gestion des Dons pour Utilisateurs
 * Accessible à tous les utilisateurs authentifiés (contrairement au système de commandes admin-only)
 */
class DonationsUserInterface {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.donations = [];
        this.filteredDonations = [];
        this.currentPage = 1;
        this.itemsPerPage = 10;
        this.sortColumn = 'created_at';
        this.sortDirection = 'desc';
        this.filters = {
            search: '',
            statut: '',
            type_don: '',
            donneur: '',
            date_debut: '',
            date_fin: ''
        };
        
        // Statuts des dons avec leurs libellés
        this.statusLabels = {
            'brouillon': 'Brouillon',
            'soumis': 'Soumis',
            'bp_cree': 'BP Créé',
            'chez_magasinier': 'Chez Magasinier',
            'chez_transitaire': 'Chez Transitaire',
            'chez_receptionniste': 'Chez Réceptionniste',
            'bs_cree': 'BS Créé',
            'chez_rve': 'Chez RVE',
            'recap_cree': 'Récap Créé',
            'envoye_comptabilite': 'Envoyé Comptabilité',
            'archive': 'Archivé'
        };
        
        // Types de dons
        this.typeLabels = {
            'article': 'Article',
            'equipement': 'Équipement'
        };
    }

    /**
     * Initialiser l'interface
     */
    async initialize() {
        try {
            console.log('🎁 Initialisation interface dons utilisateur...');
            
            // Vérifier Supabase
            if (!window.supabaseClient) {
                throw new Error('Client Supabase non disponible');
            }
            
            this.supabase = window.supabaseClient;
            
            // Vérifier l'authentification
            const { data: { user } } = await this.supabase.auth.getUser();
            if (!user) {
                this.showAccessDenied('Vous devez être connecté pour accéder à la gestion des dons.');
                return;
            }
            
            this.currentUser = user;
            console.log('✅ Utilisateur connecté:', user.email);
            
            // Rendre l'interface
            this.renderInterface();
            
            // Charger les données
            await this.loadData();
            
            // Configurer les événements
            this.setupEventListeners();
            
            console.log('✅ Interface dons utilisateur initialisée');
            
        } catch (error) {
            console.error('❌ Erreur initialisation interface dons:', error);
            this.showError('Erreur lors de l\'initialisation: ' + error.message);
        }
    }

    /**
     * Afficher message d'accès refusé
     */
    showAccessDenied(message) {
        const container = document.getElementById('donationsUserContainer');
        if (container) {
            container.innerHTML = `
                <div class="donations-access-denied">
                    <div class="access-denied-icon">🔒</div>
                    <h3>Accès Requis</h3>
                    <p>${message}</p>
                    <button onclick="window.location.reload()" class="btn btn-primary">
                        🔄 Actualiser
                    </button>
                </div>
            `;
        }
    }

    /**
     * Afficher message d'erreur
     */
    showError(message) {
        const container = document.getElementById('donationsUserContainer');
        if (container) {
            container.innerHTML = `
                <div class="donations-error">
                    <div class="error-icon">⚠️</div>
                    <h3>Erreur</h3>
                    <p>${message}</p>
                    <button onclick="window.location.reload()" class="btn btn-primary">
                        🔄 Réessayer
                    </button>
                </div>
            `;
        }
    }

    /**
     * Rendre l'interface principale
     */
    renderInterface() {
        const container = document.getElementById('donationsUserContainer');
        if (!container) return;

        container.innerHTML = `
            <div class="donations-user-interface">
                <!-- En-tête -->
                <div class="donations-header">
                    <div class="header-title">
                        <h2>🎁 Gestion des Dons</h2>
                        <p>Gérez vos demandes de dons et suivez leur progression</p>
                    </div>
                    <div class="header-actions">
                        <button id="btnNouveauDon" class="btn btn-primary">
                            ➕ Nouveau Don
                        </button>
                        <button id="btnExportDons" class="btn btn-secondary">
                            📊 Exporter
                        </button>
                        <button id="btnActualiser" class="btn btn-secondary">
                            🔄 Actualiser
                        </button>
                    </div>
                </div>

                <!-- Statistiques rapides -->
                <div class="donations-stats" id="donationsStats">
                    <!-- Les statistiques seront chargées ici -->
                </div>

                <!-- Filtres et recherche -->
                <div class="donations-filters">
                    <div class="filters-row">
                        <div class="filter-group">
                            <label>🔍 Recherche</label>
                            <input type="text" id="searchInput" placeholder="Rechercher par numéro, donneur, description...">
                        </div>
                        <div class="filter-group">
                            <label>📊 Statut</label>
                            <select id="statutFilter">
                                <option value="">Tous les statuts</option>
                                ${Object.entries(this.statusLabels).map(([key, label]) => 
                                    `<option value="${key}">${label}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>🏷️ Type</label>
                            <select id="typeFilter">
                                <option value="">Tous les types</option>
                                ${Object.entries(this.typeLabels).map(([key, label]) => 
                                    `<option value="${key}">${label}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>👤 Donneur</label>
                            <input type="text" id="donneurFilter" placeholder="Nom du donneur">
                        </div>
                    </div>
                    <div class="filters-row">
                        <div class="filter-group">
                            <label>📅 Date début</label>
                            <input type="date" id="dateDebutFilter">
                        </div>
                        <div class="filter-group">
                            <label>📅 Date fin</label>
                            <input type="date" id="dateFinFilter">
                        </div>
                        <div class="filter-actions">
                            <button id="btnAppliquerFiltres" class="btn btn-primary">🔍 Filtrer</button>
                            <button id="btnEffacerFiltres" class="btn btn-secondary">🗑️ Effacer</button>
                        </div>
                    </div>
                </div>

                <!-- Table des dons -->
                <div class="donations-table-container">
                    <div class="table-header">
                        <div class="table-info">
                            <span id="tableCount">0 dons</span>
                            <span id="tableFilter"></span>
                        </div>
                        <div class="table-controls">
                            <select id="itemsPerPage">
                                <option value="10">10 par page</option>
                                <option value="25">25 par page</option>
                                <option value="50">50 par page</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="table-wrapper">
                        <table class="donations-table">
                            <thead>
                                <tr>
                                    <th data-column="numero_don" class="sortable">N° Don</th>
                                    <th data-column="type_don" class="sortable">Type</th>
                                    <th data-column="donneur_nom" class="sortable">Donneur</th>
                                    <th data-column="demandeur_service" class="sortable">Service</th>
                                    <th data-column="statut" class="sortable">Statut</th>
                                    <th data-column="created_at" class="sortable">Date Création</th>
                                    <th data-column="valeur_estimee" class="sortable">Valeur</th>
                                    <th class="actions-column">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="donationsTableBody">
                                <!-- Les données seront chargées ici -->
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="table-footer">
                        <div class="pagination-info" id="paginationInfo">
                            <!-- Info pagination -->
                        </div>
                        <div class="pagination-controls" id="paginationControls">
                            <!-- Contrôles pagination -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Charger les données
     */
    async loadData() {
        try {
            console.log('📥 Chargement des dons...');
            
            const { data, error } = await this.supabase
                .from('donations_table_view')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;

            this.donations = data || [];
            console.log(`✅ ${this.donations.length} dons chargés`);

            this.applyFilters();
            this.renderTable();
            this.renderStats();

        } catch (error) {
            console.error('❌ Erreur chargement dons:', error);
            this.showError('Erreur lors du chargement des données: ' + error.message);
        }
    }

    /**
     * Rendre les statistiques
     */
    renderStats() {
        const container = document.getElementById('donationsStats');
        if (!container) return;

        const total = this.donations.length;
        const parStatut = {};
        const parType = {};

        this.donations.forEach(don => {
            parStatut[don.statut] = (parStatut[don.statut] || 0) + 1;
            parType[don.type_don] = (parType[don.type_don] || 0) + 1;
        });

        container.innerHTML = `
            <div class="stats-grid">
                <div class="stat-card total">
                    <div class="stat-icon">📊</div>
                    <div class="stat-content">
                        <div class="stat-number">${total}</div>
                        <div class="stat-label">Total Dons</div>
                    </div>
                </div>
                <div class="stat-card articles">
                    <div class="stat-icon">📦</div>
                    <div class="stat-content">
                        <div class="stat-number">${parType.article || 0}</div>
                        <div class="stat-label">Articles</div>
                    </div>
                </div>
                <div class="stat-card equipements">
                    <div class="stat-icon">🔧</div>
                    <div class="stat-content">
                        <div class="stat-number">${parType.equipement || 0}</div>
                        <div class="stat-label">Équipements</div>
                    </div>
                </div>
                <div class="stat-card en-cours">
                    <div class="stat-icon">⏳</div>
                    <div class="stat-content">
                        <div class="stat-number">${(parStatut.soumis || 0) + (parStatut.bp_cree || 0) + (parStatut.chez_magasinier || 0)}</div>
                        <div class="stat-label">En Cours</div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Appliquer les filtres
     */
    applyFilters() {
        this.filteredDonations = this.donations.filter(don => {
            // Recherche globale
            if (this.filters.search) {
                const searchTerm = this.filters.search.toLowerCase();
                const searchableText = [
                    don.numero_don,
                    don.donneur_nom,
                    don.demandeur_service,
                    don.raison_don,
                    don.lieu_affectation
                ].join(' ').toLowerCase();

                if (!searchableText.includes(searchTerm)) return false;
            }

            // Filtre par statut
            if (this.filters.statut && don.statut !== this.filters.statut) return false;

            // Filtre par type
            if (this.filters.type_don && don.type_don !== this.filters.type_don) return false;

            // Filtre par donneur
            if (this.filters.donneur) {
                const donneurTerm = this.filters.donneur.toLowerCase();
                if (!don.donneur_nom.toLowerCase().includes(donneurTerm)) return false;
            }

            // Filtre par date
            if (this.filters.date_debut) {
                const dateDebut = new Date(this.filters.date_debut);
                const dateCreation = new Date(don.created_at);
                if (dateCreation < dateDebut) return false;
            }

            if (this.filters.date_fin) {
                const dateFin = new Date(this.filters.date_fin);
                const dateCreation = new Date(don.created_at);
                if (dateCreation > dateFin) return false;
            }

            return true;
        });

        // Tri
        this.filteredDonations.sort((a, b) => {
            let aVal = a[this.sortColumn];
            let bVal = b[this.sortColumn];

            // Gestion des valeurs nulles
            if (aVal === null || aVal === undefined) aVal = '';
            if (bVal === null || bVal === undefined) bVal = '';

            // Conversion pour les dates
            if (this.sortColumn.includes('date') || this.sortColumn === 'created_at') {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }

            // Conversion pour les nombres
            if (this.sortColumn === 'valeur_estimee') {
                aVal = parseFloat(aVal) || 0;
                bVal = parseFloat(bVal) || 0;
            }

            if (aVal < bVal) return this.sortDirection === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

        // Réinitialiser à la première page
        this.currentPage = 1;
    }

    /**
     * Rendre la table
     */
    renderTable() {
        const tbody = document.getElementById('donationsTableBody');
        if (!tbody) return;

        // Calculer la pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const pageData = this.filteredDonations.slice(startIndex, endIndex);

        // Rendre les lignes
        tbody.innerHTML = pageData.map(don => this.renderTableRow(don)).join('');

        // Mettre à jour les informations
        this.updateTableInfo();
        this.renderPagination();
    }

    /**
     * Rendre une ligne de table
     */
    renderTableRow(don) {
        const statusClass = `status-${don.statut}`;
        const typeClass = `type-${don.type_don}`;

        return `
            <tr data-id="${don.id}">
                <td>
                    <strong>${don.code_don || don.numero_don}</strong>
                </td>
                <td>
                    <span class="table-badge ${typeClass}">
                        ${this.typeLabels[don.type_don] || don.type_don}
                    </span>
                </td>
                <td>
                    <div class="donneur-info">
                        <strong>${don.donneur_nom}</strong>
                        ${don.donneur_contact ? `<br><small>${don.donneur_contact}</small>` : ''}
                    </div>
                </td>
                <td>${don.demandeur_service}</td>
                <td>
                    <span class="table-status ${statusClass}">
                        ${this.statusLabels[don.statut] || don.statut}
                    </span>
                </td>
                <td>${this.formatDate(don.created_at)}</td>
                <td>
                    ${don.valeur_estimee ?
                        `${this.formatCurrency(don.valeur_estimee)} ${don.devise || 'TND'}` :
                        '<span class="text-muted">Non spécifiée</span>'
                    }
                </td>
                <td class="actions-column">
                    <div class="table-actions">
                        <button onclick="donationsUserInterface.viewDonation('${don.id}')"
                                class="btn-table btn-view" title="Voir détails">
                            👁️
                        </button>
                        ${this.canEdit(don) ? `
                            <button onclick="donationsUserInterface.editDonation('${don.id}')"
                                    class="btn-table btn-edit" title="Modifier">
                                ✏️
                            </button>
                        ` : ''}
                        ${this.canDelete(don) ? `
                            <button onclick="donationsUserInterface.deleteDonation('${don.id}')"
                                    class="btn-table btn-delete" title="Supprimer">
                                🗑️
                            </button>
                        ` : ''}
                        <button onclick="donationsUserInterface.trackDonation('${don.id}')"
                                class="btn-table btn-track" title="Suivre progression">
                            📍
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Vérifier si l'utilisateur peut modifier un don
     */
    canEdit(don) {
        // L'utilisateur peut modifier ses propres dons s'ils sont en brouillon ou soumis
        return don.created_by === this.currentUser.email &&
               ['brouillon', 'soumis'].includes(don.statut);
    }

    /**
     * Vérifier si l'utilisateur peut supprimer un don
     */
    canDelete(don) {
        // L'utilisateur peut supprimer ses propres dons s'ils sont en brouillon
        return don.created_by === this.currentUser.email && don.statut === 'brouillon';
    }

    /**
     * Mettre à jour les informations de la table
     */
    updateTableInfo() {
        const countElement = document.getElementById('tableCount');
        const filterElement = document.getElementById('tableFilter');

        if (countElement) {
            const total = this.filteredDonations.length;
            countElement.textContent = `${total} don${total > 1 ? 's' : ''}`;
        }

        if (filterElement) {
            const hasFilters = Object.values(this.filters).some(filter => filter !== '');
            filterElement.textContent = hasFilters ? ' (filtrés)' : '';
        }
    }

    /**
     * Rendre la pagination
     */
    renderPagination() {
        const container = document.getElementById('paginationControls');
        const infoContainer = document.getElementById('paginationInfo');

        if (!container || !infoContainer) return;

        const totalItems = this.filteredDonations.length;
        const totalPages = Math.ceil(totalItems / this.itemsPerPage);
        const startItem = (this.currentPage - 1) * this.itemsPerPage + 1;
        const endItem = Math.min(this.currentPage * this.itemsPerPage, totalItems);

        // Information de pagination
        infoContainer.textContent = totalItems > 0 ?
            `Affichage de ${startItem} à ${endItem} sur ${totalItems} résultats` :
            'Aucun résultat';

        // Contrôles de pagination
        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = `
            <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''}
                    onclick="donationsUserInterface.goToPage(${this.currentPage - 1})">
                ‹ Précédent
            </button>
        `;

        // Pages
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            paginationHTML += `<button class="pagination-btn" onclick="donationsUserInterface.goToPage(1)">1</button>`;
            if (startPage > 2) paginationHTML += `<span class="pagination-ellipsis">...</span>`;
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}"
                        onclick="donationsUserInterface.goToPage(${i})">
                    ${i}
                </button>
            `;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) paginationHTML += `<span class="pagination-ellipsis">...</span>`;
            paginationHTML += `<button class="pagination-btn" onclick="donationsUserInterface.goToPage(${totalPages})">${totalPages}</button>`;
        }

        paginationHTML += `
            <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''}
                    onclick="donationsUserInterface.goToPage(${this.currentPage + 1})">
                Suivant ›
            </button>
        `;

        container.innerHTML = paginationHTML;
    }

    /**
     * Aller à une page spécifique
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredDonations.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderTable();
        }
    }

    /**
     * Configurer les événements
     */
    setupEventListeners() {
        // Boutons d'action
        document.getElementById('btnNouveauDon')?.addEventListener('click', () => this.showNewDonationForm());
        document.getElementById('btnExportDons')?.addEventListener('click', () => this.exportDonations());
        document.getElementById('btnActualiser')?.addEventListener('click', () => this.loadData());

        // Filtres
        document.getElementById('searchInput')?.addEventListener('input', (e) => {
            this.filters.search = e.target.value;
            this.applyFilters();
            this.renderTable();
        });

        document.getElementById('statutFilter')?.addEventListener('change', (e) => {
            this.filters.statut = e.target.value;
            this.applyFilters();
            this.renderTable();
        });

        document.getElementById('typeFilter')?.addEventListener('change', (e) => {
            this.filters.type_don = e.target.value;
            this.applyFilters();
            this.renderTable();
        });

        document.getElementById('donneurFilter')?.addEventListener('input', (e) => {
            this.filters.donneur = e.target.value;
            this.applyFilters();
            this.renderTable();
        });

        document.getElementById('dateDebutFilter')?.addEventListener('change', (e) => {
            this.filters.date_debut = e.target.value;
            this.applyFilters();
            this.renderTable();
        });

        document.getElementById('dateFinFilter')?.addEventListener('change', (e) => {
            this.filters.date_fin = e.target.value;
            this.applyFilters();
            this.renderTable();
        });

        // Boutons de filtres
        document.getElementById('btnAppliquerFiltres')?.addEventListener('click', () => {
            this.applyFilters();
            this.renderTable();
        });

        document.getElementById('btnEffacerFiltres')?.addEventListener('click', () => {
            this.clearFilters();
        });

        // Tri des colonnes
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => {
                const column = header.dataset.column;
                if (this.sortColumn === column) {
                    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    this.sortColumn = column;
                    this.sortDirection = 'asc';
                }
                this.applyFilters();
                this.renderTable();
                this.updateSortIndicators();
            });
        });

        // Nombre d'éléments par page
        document.getElementById('itemsPerPage')?.addEventListener('change', (e) => {
            this.itemsPerPage = parseInt(e.target.value);
            this.currentPage = 1;
            this.renderTable();
        });
    }

    /**
     * Effacer tous les filtres
     */
    clearFilters() {
        this.filters = {
            search: '',
            statut: '',
            type_don: '',
            donneur: '',
            date_debut: '',
            date_fin: ''
        };

        // Réinitialiser les champs
        document.getElementById('searchInput').value = '';
        document.getElementById('statutFilter').value = '';
        document.getElementById('typeFilter').value = '';
        document.getElementById('donneurFilter').value = '';
        document.getElementById('dateDebutFilter').value = '';
        document.getElementById('dateFinFilter').value = '';

        this.applyFilters();
        this.renderTable();
    }

    /**
     * Mettre à jour les indicateurs de tri
     */
    updateSortIndicators() {
        document.querySelectorAll('.sortable').forEach(header => {
            const indicator = header.querySelector('.sort-indicator');
            if (indicator) {
                indicator.className = 'sort-indicator';
                if (header.dataset.column === this.sortColumn) {
                    indicator.className += ` ${this.sortDirection}`;
                }
            }
        });
    }

    /**
     * Afficher le formulaire de nouveau don
     */
    showNewDonationForm() {
        // Rediriger vers la page de création de don
        window.location.href = 'donations-registration.html';
    }

    /**
     * Voir les détails d'un don
     */
    async viewDonation(donationId) {
        try {
            const donation = this.donations.find(d => d.id === donationId);
            if (!donation) {
                alert('Don non trouvé');
                return;
            }

            // Créer et afficher le modal de détails
            this.showDonationDetailsModal(donation);

        } catch (error) {
            console.error('❌ Erreur affichage détails:', error);
            alert('Erreur lors de l\'affichage des détails');
        }
    }

    /**
     * Modifier un don
     */
    async editDonation(donationId) {
        try {
            const donation = this.donations.find(d => d.id === donationId);
            if (!donation) {
                alert('Don non trouvé');
                return;
            }

            if (!this.canEdit(donation)) {
                alert('Vous ne pouvez pas modifier ce don');
                return;
            }

            // Rediriger vers la page d'édition avec l'ID
            window.location.href = `donations-registration.html?edit=${donationId}`;

        } catch (error) {
            console.error('❌ Erreur modification don:', error);
            alert('Erreur lors de la modification');
        }
    }

    /**
     * Supprimer un don
     */
    async deleteDonation(donationId) {
        try {
            const donation = this.donations.find(d => d.id === donationId);
            if (!donation) {
                alert('Don non trouvé');
                return;
            }

            if (!this.canDelete(donation)) {
                alert('Vous ne pouvez pas supprimer ce don');
                return;
            }

            if (!confirm(`Êtes-vous sûr de vouloir supprimer le don ${donation.numero_don || donation.code_don} ?`)) {
                return;
            }

            const { error } = await this.supabase
                .from('gestion_donations_ipt')
                .delete()
                .eq('id', donationId);

            if (error) throw error;

            alert('Don supprimé avec succès');
            await this.loadData();

        } catch (error) {
            console.error('❌ Erreur suppression don:', error);
            alert('Erreur lors de la suppression: ' + error.message);
        }
    }

    /**
     * Suivre la progression d'un don
     */
    async trackDonation(donationId) {
        try {
            const donation = this.donations.find(d => d.id === donationId);
            if (!donation) {
                alert('Don non trouvé');
                return;
            }

            // Rediriger vers la page de suivi
            window.location.href = `donations-workflow.html?id=${donationId}`;

        } catch (error) {
            console.error('❌ Erreur suivi don:', error);
            alert('Erreur lors du suivi');
        }
    }

    /**
     * Exporter les dons
     */
    async exportDonations() {
        try {
            const dataToExport = this.filteredDonations.map(don => ({
                'N° Don': don.code_don || don.numero_don,
                'Type': this.typeLabels[don.type_don] || don.type_don,
                'Donneur': don.donneur_nom,
                'Contact Donneur': don.donneur_contact || '',
                'Service Demandeur': don.demandeur_service,
                'Statut': this.statusLabels[don.statut] || don.statut,
                'Raison': don.raison_don || '',
                'Lieu Affectation': don.lieu_affectation || '',
                'Valeur Estimée': don.valeur_estimee || '',
                'Devise': don.devise || '',
                'Date Création': this.formatDate(don.created_at),
                'Dernière Mise à Jour': this.formatDate(don.updated_at)
            }));

            this.downloadCSV(dataToExport, `dons_export_${new Date().toISOString().split('T')[0]}.csv`);

        } catch (error) {
            console.error('❌ Erreur export:', error);
            alert('Erreur lors de l\'export');
        }
    }

    /**
     * Télécharger un fichier CSV
     */
    downloadCSV(data, filename) {
        if (data.length === 0) {
            alert('Aucune donnée à exporter');
            return;
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);

        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }

    /**
     * Formater une date
     */
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    }

    /**
     * Formater une devise
     */
    formatCurrency(amount) {
        if (!amount) return '0';
        return new Intl.NumberFormat('fr-FR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    }

    /**
     * Afficher le modal de détails d'un don
     */
    showDonationDetailsModal(donation) {
        // Créer le modal s'il n'existe pas
        let modal = document.getElementById('donationDetailsModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'donationDetailsModal';
            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        modal.innerHTML = `
            <div class="modal-content large">
                <div class="modal-header">
                    <h2>🎁 Détails du Don</h2>
                    <span class="close" onclick="document.getElementById('donationDetailsModal').style.display='none'">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="donation-details-grid">
                        <div class="detail-section">
                            <h3>📋 Informations Générales</h3>
                            <div class="detail-row">
                                <strong>N° Don:</strong> ${donation.code_don || donation.numero_don}
                            </div>
                            <div class="detail-row">
                                <strong>Type:</strong>
                                <span class="table-badge type-${donation.type_don}">
                                    ${this.typeLabels[donation.type_don] || donation.type_don}
                                </span>
                            </div>
                            <div class="detail-row">
                                <strong>Statut:</strong>
                                <span class="table-status status-${donation.statut}">
                                    ${this.statusLabels[donation.statut] || donation.statut}
                                </span>
                            </div>
                            <div class="detail-row">
                                <strong>Date Création:</strong> ${this.formatDate(donation.created_at)}
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>👤 Donneur</h3>
                            <div class="detail-row">
                                <strong>Nom:</strong> ${donation.donneur_nom}
                            </div>
                            <div class="detail-row">
                                <strong>Contact:</strong> ${donation.donneur_contact || 'Non spécifié'}
                            </div>
                            <div class="detail-row">
                                <strong>Adresse:</strong> ${donation.donneur_adresse || 'Non spécifiée'}
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>🏢 Demandeur</h3>
                            <div class="detail-row">
                                <strong>Service:</strong> ${donation.demandeur_service}
                            </div>
                            <div class="detail-row">
                                <strong>Email:</strong> ${donation.demandeur_email || 'Non spécifié'}
                            </div>
                        </div>

                        <div class="detail-section">
                            <h3>📦 Détails du Don</h3>
                            <div class="detail-row">
                                <strong>Raison:</strong> ${donation.raison_don || 'Non spécifiée'}
                            </div>
                            <div class="detail-row">
                                <strong>Lieu d'affectation:</strong> ${donation.lieu_affectation || 'Non spécifié'}
                            </div>
                            <div class="detail-row">
                                <strong>Valeur estimée:</strong>
                                ${donation.valeur_estimee ?
                                    `${this.formatCurrency(donation.valeur_estimee)} ${donation.devise || 'TND'}` :
                                    'Non spécifiée'
                                }
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button onclick="donationsUserInterface.trackDonation('${donation.id}')" class="btn btn-primary">
                        📍 Suivre Progression
                    </button>
                    ${this.canEdit(donation) ? `
                        <button onclick="donationsUserInterface.editDonation('${donation.id}')" class="btn btn-secondary">
                            ✏️ Modifier
                        </button>
                    ` : ''}
                    <button onclick="document.getElementById('donationDetailsModal').style.display='none'" class="btn btn-secondary">
                        Fermer
                    </button>
                </div>
            </div>
        `;

        modal.style.display = 'block';
    }
}

// Instance globale
window.donationsUserInterface = null;
