// Gestionnaire de stockage Supabase automatique
// Gestion complète des fichiers avec upload, download, et gestion des permissions

class SupabaseStorageManager {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.uploadQueue = [];
        this.isUploading = false;
        
        // Callbacks pour les événements
        this.callbacks = {
            onUploadStart: [],
            onUploadProgress: [],
            onUploadComplete: [],
            onUploadError: [],
            onDeleteComplete: [],
            onDeleteError: []
        };
    }

    // Initialiser le gestionnaire de stockage
    async initialize() {
        console.log('📁 Initialisation du gestionnaire de stockage...');
        
        try {
            // Vérifier les buckets disponibles
            await this.checkBuckets();
            
            // Configurer les listeners de drag & drop
            this.setupDragAndDrop();
            
            console.log('✅ Gestionnaire de stockage initialisé');
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation stockage:', error);
            return false;
        }
    }

    // Vérifier les buckets disponibles
    async checkBuckets() {
        try {
            const { data: buckets, error } = await this.client.storage.listBuckets();
            
            if (error) throw error;
            
            const availableBuckets = buckets.map(bucket => bucket.name);
            console.log('📦 Buckets disponibles:', availableBuckets);
            
            return availableBuckets;
            
        } catch (error) {
            console.warn('⚠️ Erreur vérification buckets:', error);
            return [];
        }
    }

    // Upload d'un fichier
    async uploadFile(file, bucket, path = null, options = {}) {
        try {
            console.log(`📤 Upload: ${file.name} vers ${bucket}`);
            
            // Validation du fichier
            const validation = this.validateFile(file, bucket);
            if (!validation.valid) {
                throw new Error(validation.error);
            }
            
            // Générer le chemin si non fourni
            if (!path) {
                path = this.generateFilePath(file, bucket);
            }
            
            // Déclencher le callback de début
            this.triggerCallback('onUploadStart', { file, bucket, path });
            
            // Upload avec suivi de progression
            const { data, error } = await this.client.storage
                .from(bucket)
                .upload(path, file, {
                    cacheControl: '3600',
                    upsert: options.upsert || false,
                    ...options
                });
            
            if (error) throw error;
            
            // Obtenir l'URL publique si le bucket est public
            const publicUrl = this.getPublicUrl(bucket, data.path);
            
            const result = {
                success: true,
                data: data,
                publicUrl: publicUrl,
                bucket: bucket,
                path: data.path,
                file: file
            };
            
            console.log('✅ Upload réussi:', data.path);
            this.triggerCallback('onUploadComplete', result);
            
            return result;
            
        } catch (error) {
            console.error('❌ Erreur upload:', error);
            this.triggerCallback('onUploadError', { file, bucket, error: error.message });
            return { success: false, error: error.message };
        }
    }

    // Upload multiple avec queue
    async uploadMultipleFiles(files, bucket, options = {}) {
        const results = [];
        
        for (const file of files) {
            const result = await this.uploadFile(file, bucket, null, options);
            results.push(result);
            
            // Petite pause entre les uploads
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        return results;
    }

    // Validation d'un fichier
    validateFile(file, bucket) {
        const bucketConfig = this.config.storage;
        
        // Vérifier la taille
        const maxSize = bucketConfig.maxFileSize[bucket];
        if (maxSize && file.size > maxSize) {
            return {
                valid: false,
                error: `Fichier trop volumineux. Taille max: ${this.formatFileSize(maxSize)}`
            };
        }
        
        // Vérifier le type MIME
        const allowedTypes = bucketConfig.allowedTypes[bucket];
        if (allowedTypes && !allowedTypes.includes(file.type)) {
            return {
                valid: false,
                error: `Type de fichier non autorisé. Types acceptés: ${allowedTypes.join(', ')}`
            };
        }
        
        return { valid: true };
    }

    // Générer un chemin de fichier unique
    generateFilePath(file, bucket) {
        const timestamp = Date.now();
        const randomId = Math.random().toString(36).substring(2, 15);
        const extension = file.name.split('.').pop();
        const baseName = file.name.replace(/\.[^/.]+$/, "").replace(/[^a-zA-Z0-9]/g, '_');
        
        // Organiser par utilisateur si authentifié
        const userId = this.getCurrentUserId();
        const userFolder = userId ? `${userId}/` : 'public/';
        
        return `${userFolder}${timestamp}_${randomId}_${baseName}.${extension}`;
    }

    // Obtenir l'ID utilisateur actuel
    getCurrentUserId() {
        try {
            const user = this.client.auth.getUser();
            return user?.data?.user?.id || null;
        } catch {
            return null;
        }
    }

    // Obtenir l'URL publique d'un fichier
    getPublicUrl(bucket, path) {
        try {
            const { data } = this.client.storage
                .from(bucket)
                .getPublicUrl(path);
            
            return data.publicUrl;
        } catch (error) {
            console.warn('⚠️ Erreur obtention URL publique:', error);
            return null;
        }
    }

    // Obtenir une URL signée (pour les buckets privés)
    async getSignedUrl(bucket, path, expiresIn = 3600) {
        try {
            const { data, error } = await this.client.storage
                .from(bucket)
                .createSignedUrl(path, expiresIn);
            
            if (error) throw error;
            
            return data.signedUrl;
            
        } catch (error) {
            console.error('❌ Erreur création URL signée:', error);
            return null;
        }
    }

    // Lister les fichiers d'un bucket
    async listFiles(bucket, folder = '', options = {}) {
        try {
            const { data, error } = await this.client.storage
                .from(bucket)
                .list(folder, {
                    limit: options.limit || 100,
                    offset: options.offset || 0,
                    sortBy: options.sortBy || { column: 'name', order: 'asc' }
                });
            
            if (error) throw error;
            
            return { success: true, files: data };
            
        } catch (error) {
            console.error('❌ Erreur listage fichiers:', error);
            return { success: false, error: error.message };
        }
    }

    // Supprimer un fichier
    async deleteFile(bucket, path) {
        try {
            console.log(`🗑️ Suppression: ${path} de ${bucket}`);
            
            const { data, error } = await this.client.storage
                .from(bucket)
                .remove([path]);
            
            if (error) throw error;
            
            console.log('✅ Fichier supprimé');
            this.triggerCallback('onDeleteComplete', { bucket, path });
            
            return { success: true, data };
            
        } catch (error) {
            console.error('❌ Erreur suppression:', error);
            this.triggerCallback('onDeleteError', { bucket, path, error: error.message });
            return { success: false, error: error.message };
        }
    }

    // Supprimer plusieurs fichiers
    async deleteMultipleFiles(bucket, paths) {
        try {
            const { data, error } = await this.client.storage
                .from(bucket)
                .remove(paths);
            
            if (error) throw error;
            
            return { success: true, data };
            
        } catch (error) {
            console.error('❌ Erreur suppression multiple:', error);
            return { success: false, error: error.message };
        }
    }

    // Télécharger un fichier
    async downloadFile(bucket, path) {
        try {
            const { data, error } = await this.client.storage
                .from(bucket)
                .download(path);
            
            if (error) throw error;
            
            return { success: true, blob: data };
            
        } catch (error) {
            console.error('❌ Erreur téléchargement:', error);
            return { success: false, error: error.message };
        }
    }

    // Déplacer un fichier
    async moveFile(bucket, fromPath, toPath) {
        try {
            const { data, error } = await this.client.storage
                .from(bucket)
                .move(fromPath, toPath);
            
            if (error) throw error;
            
            return { success: true, data };
            
        } catch (error) {
            console.error('❌ Erreur déplacement:', error);
            return { success: false, error: error.message };
        }
    }

    // Copier un fichier
    async copyFile(bucket, fromPath, toPath) {
        try {
            const { data, error } = await this.client.storage
                .from(bucket)
                .copy(fromPath, toPath);
            
            if (error) throw error;
            
            return { success: true, data };
            
        } catch (error) {
            console.error('❌ Erreur copie:', error);
            return { success: false, error: error.message };
        }
    }

    // Configurer le drag & drop
    setupDragAndDrop() {
        // Empêcher le comportement par défaut du navigateur
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, this.preventDefaults, false);
        });

        // Ajouter les styles visuels
        ['dragenter', 'dragover'].forEach(eventName => {
            document.addEventListener(eventName, this.highlight.bind(this), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, this.unhighlight.bind(this), false);
        });

        // Gérer le drop
        document.addEventListener('drop', this.handleDrop.bind(this), false);
    }

    // Empêcher les comportements par défaut
    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // Mettre en surbrillance lors du drag
    highlight(e) {
        document.body.classList.add('drag-over');
    }

    // Enlever la surbrillance
    unhighlight(e) {
        document.body.classList.remove('drag-over');
    }

    // Gérer le drop de fichiers
    async handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            // Déterminer le bucket par défaut
            const bucket = this.getDefaultBucket(files[0]);
            
            // Uploader les fichiers
            await this.uploadMultipleFiles(Array.from(files), bucket);
        }
    }

    // Déterminer le bucket par défaut selon le type de fichier
    getDefaultBucket(file) {
        if (file.type.startsWith('image/')) {
            return 'images';
        } else if (file.type.includes('pdf') || file.type.includes('document')) {
            return 'documents';
        } else {
            return 'attachments';
        }
    }

    // Formater la taille d'un fichier
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Créer un élément de prévisualisation
    createPreviewElement(file, result) {
        const preview = document.createElement('div');
        preview.className = 'file-preview';
        
        let content = '';
        if (file.type.startsWith('image/')) {
            content = `<img src="${result.publicUrl}" alt="${file.name}" style="max-width: 100px; max-height: 100px;">`;
        } else {
            content = `<div class="file-icon">📄</div>`;
        }
        
        preview.innerHTML = `
            ${content}
            <div class="file-info">
                <div class="file-name">${file.name}</div>
                <div class="file-size">${this.formatFileSize(file.size)}</div>
                <div class="file-url">
                    <input type="text" value="${result.publicUrl || result.data.path}" readonly>
                    <button onclick="navigator.clipboard.writeText('${result.publicUrl || result.data.path}')">📋</button>
                </div>
            </div>
        `;
        
        return preview;
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Déclencher un callback
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les statistiques de stockage
    async getStorageStats() {
        const stats = {};
        
        for (const bucket of Object.values(this.config.storage.buckets)) {
            try {
                const { files } = await this.listFiles(bucket);
                if (files) {
                    stats[bucket] = {
                        count: files.length,
                        totalSize: files.reduce((sum, file) => sum + (file.metadata?.size || 0), 0)
                    };
                }
            } catch (error) {
                stats[bucket] = { count: 0, totalSize: 0, error: error.message };
            }
        }
        
        return stats;
    }
}

// Export pour utilisation
if (typeof window !== 'undefined') {
    window.SupabaseStorageManager = SupabaseStorageManager;
}
