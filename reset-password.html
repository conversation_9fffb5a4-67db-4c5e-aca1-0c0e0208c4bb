<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة تعيين كلمة المرور - نظام الإدارة الداخلية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .reset-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            text-align: center;
        }
        
        .reset-header {
            margin-bottom: 30px;
        }
        
        .reset-header h1 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .reset-header p {
            color: #666;
            font-size: 1em;
            line-height: 1.6;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: right;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
            text-align: right;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
        
        .back-link {
            margin-top: 20px;
        }
        
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .reset-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-header">
            <h1>🔐 إعادة تعيين كلمة المرور</h1>
            <p>أدخل كلمة مرور جديدة قوية لحسابك</p>
        </div>
        
        <!-- رسائل التنبيه -->
        <div id="alertContainer"></div>
        
        <!-- شاشة التحميل -->
        <div class="loading" id="loadingScreen">
            <div class="spinner"></div>
            <p>جاري تحديث كلمة المرور...</p>
        </div>
        
        <!-- نموذج إعادة تعيين كلمة المرور -->
        <form id="resetForm">
            <div class="form-group">
                <label for="newPassword">كلمة المرور الجديدة</label>
                <input type="password" id="newPassword" name="password" required 
                       placeholder="أدخل كلمة مرور قوية" oninput="checkPasswordStrength(this.value)">
                <div class="password-strength" id="passwordStrength"></div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">تأكيد كلمة المرور</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required 
                       placeholder="أعد إدخال كلمة المرور" oninput="checkPasswordMatch()">
                <div class="password-strength" id="passwordMatch"></div>
            </div>
            
            <button type="submit" class="btn" id="resetBtn">
                تحديث كلمة المرور
            </button>
        </form>
        
        <div class="back-link">
            <a href="login.html">العودة لتسجيل الدخول</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    
    <script>
        // متغيرات عامة
        let authSystem = null;
        let accessToken = null;
        let refreshToken = null;
        
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 تهيئة صفحة إعادة تعيين كلمة المرور...');
            
            try {
                // استخراج الرموز من URL
                extractTokensFromURL();
                
                if (!accessToken) {
                    showAlert('رابط غير صحيح أو منتهي الصلاحية', 'error');
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);
                    return;
                }
                
                // تهيئة Supabase
                await initializeSupabase();
                
                // تهيئة نظام المصادقة
                if (typeof SupabaseAuth !== 'undefined' && window.supabaseClient) {
                    authSystem = new SupabaseAuth(SUPABASE_CONFIG, window.supabaseClient);
                    await authSystem.initialize();
                    
                    // تعيين الجلسة باستخدام الرموز
                    await setSessionWithTokens();
                    
                    console.log('✅ نظام المصادقة جاهز');
                } else {
                    showAlert('خطأ في تهيئة النظام', 'error');
                }
                
            } catch (error) {
                console.error('❌ خطأ في التهيئة:', error);
                showAlert('خطأ في تهيئة النظام: ' + error.message, 'error');
            }
        });
        
        // استخراج الرموز من URL
        function extractTokensFromURL() {
            const urlParams = new URLSearchParams(window.location.hash.substring(1));
            accessToken = urlParams.get('access_token');
            refreshToken = urlParams.get('refresh_token');
            
            console.log('🔑 استخراج الرموز من URL:', { 
                hasAccessToken: !!accessToken, 
                hasRefreshToken: !!refreshToken 
            });
        }
        
        // تعيين الجلسة باستخدام الرموز
        async function setSessionWithTokens() {
            if (!accessToken || !refreshToken) {
                throw new Error('الرموز المطلوبة غير موجودة');
            }
            
            try {
                const { data, error } = await window.supabaseClient.auth.setSession({
                    access_token: accessToken,
                    refresh_token: refreshToken
                });
                
                if (error) {
                    throw error;
                }
                
                console.log('✅ تم تعيين الجلسة بنجاح');
                showAlert('تم التحقق من هويتك بنجاح. يمكنك الآن تحديث كلمة المرور.', 'success');
                
            } catch (error) {
                console.error('❌ خطأ في تعيين الجلسة:', error);
                throw new Error('فشل في التحقق من الرابط');
            }
        }
        
        // معالج نموذج إعادة تعيين كلمة المرور
        document.getElementById('resetForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!authSystem) {
                showAlert('نظام المصادقة غير جاهز', 'error');
                return;
            }
            
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            // التحقق من صحة البيانات
            if (!newPassword || !confirmPassword) {
                showAlert('يرجى ملء جميع الحقول', 'error');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                showAlert('كلمات المرور غير متطابقة', 'error');
                return;
            }
            
            if (newPassword.length < 8) {
                showAlert('كلمة المرور يجب أن تكون 8 أحرف على الأقل', 'error');
                return;
            }
            
            showLoading();
            
            try {
                const result = await authSystem.updatePassword(newPassword);
                
                if (result.success) {
                    showAlert('تم تحديث كلمة المرور بنجاح!', 'success');
                    
                    // إعادة توجيه لصفحة تسجيل الدخول بعد 3 ثوان
                    setTimeout(() => {
                        window.location.href = 'login.html';
                    }, 3000);
                } else {
                    showAlert(result.error, 'error');
                }
                
            } catch (error) {
                console.error('خطأ في تحديث كلمة المرور:', error);
                showAlert('حدث خطأ غير متوقع', 'error');
            } finally {
                hideLoading();
            }
        });
        
        // فحص قوة كلمة المرور
        function checkPasswordStrength(password) {
            const strengthElement = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthElement.textContent = '';
                return;
            }
            
            let strength = 0;
            let feedback = [];
            
            // طول كلمة المرور
            if (password.length >= 8) strength++;
            else feedback.push('8 أحرف على الأقل');
            
            // أحرف كبيرة وصغيرة
            if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
            else feedback.push('أحرف كبيرة وصغيرة');
            
            // أرقام
            if (/\d/.test(password)) strength++;
            else feedback.push('أرقام');
            
            // رموز خاصة
            if (/[^a-zA-Z\d]/.test(password)) strength++;
            else feedback.push('رموز خاصة');
            
            // عرض النتيجة
            if (strength < 2) {
                strengthElement.className = 'password-strength strength-weak';
                strengthElement.textContent = 'ضعيفة - يحتاج: ' + feedback.join(', ');
            } else if (strength < 3) {
                strengthElement.className = 'password-strength strength-medium';
                strengthElement.textContent = 'متوسطة - يحتاج: ' + feedback.join(', ');
            } else {
                strengthElement.className = 'password-strength strength-strong';
                strengthElement.textContent = 'قوية ✓';
            }
            
            // فحص التطابق إذا كان هناك تأكيد
            checkPasswordMatch();
        }
        
        // فحص تطابق كلمات المرور
        function checkPasswordMatch() {
            const password = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const matchElement = document.getElementById('passwordMatch');
            
            if (confirmPassword.length === 0) {
                matchElement.textContent = '';
                return;
            }
            
            if (password === confirmPassword) {
                matchElement.className = 'password-strength strength-strong';
                matchElement.textContent = 'كلمات المرور متطابقة ✓';
            } else {
                matchElement.className = 'password-strength strength-weak';
                matchElement.textContent = 'كلمات المرور غير متطابقة';
            }
        }
        
        // إظهار رسالة تنبيه
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            container.innerHTML = '';
            container.appendChild(alert);
            
            // إزالة الرسالة بعد 5 ثوان (إلا إذا كانت رسالة نجاح)
            if (type !== 'success') {
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 5000);
            }
        }
        
        // إظهار شاشة التحميل
        function showLoading() {
            document.getElementById('loadingScreen').classList.add('show');
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
        }
        
        // إخفاء شاشة التحميل
        function hideLoading() {
            document.getElementById('loadingScreen').classList.remove('show');
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
        }
    </script>
</body>
</html>
