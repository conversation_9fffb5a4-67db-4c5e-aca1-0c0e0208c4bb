-- =====================================================
-- POLITIQUES RLS - SYSTÈME DONS IPT
-- =====================================================

-- Activer RLS sur toutes les tables
ALTER TABLE gestion_donations_ipt ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_workflow ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_inventaire ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_mouvements ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_users_ipt ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_historique ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- FONCTIONS UTILITAIRES POUR RLS
-- =====================================================

-- Fonction pour récupérer le rôle de l'utilisateur actuel
CREATE OR REPLACE FUNCTION get_current_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN COALESCE(
        (auth.jwt() ->> 'user_metadata')::jsonb ->> 'role_ipt',
        'demandeur'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour récupérer l'email de l'utilisateur actuel
CREATE OR REPLACE FUNCTION get_current_user_email()
RETURNS TEXT AS $$
BEGIN
    RETURN COALESCE(auth.email(), '');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour récupérer le service de l'utilisateur actuel
CREATE OR REPLACE FUNCTION get_current_user_service()
RETURNS TEXT AS $$
BEGIN
    RETURN COALESCE(
        (auth.jwt() ->> 'user_metadata')::jsonb ->> 'service',
        ''
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- POLITIQUES POUR gestion_donations_ipt
-- =====================================================

-- Lecture des dons
CREATE POLICY "donations_select_policy" ON gestion_donations_ipt
FOR SELECT USING (
    CASE get_current_user_role()
        -- Admin peut tout voir
        WHEN 'admin_systeme' THEN true
        
        -- Responsables peuvent voir tous les dons de leur domaine
        WHEN 'responsable_approvisionnement' THEN true
        WHEN 'direction_technique' THEN type_don = 'equipement'
        WHEN 'directeur_general' THEN true
        WHEN 'sous_direction_achats' THEN true
        
        -- RVE peut voir les dons réceptionnés et après
        WHEN 'rve' THEN statut IN ('receptionne', 'inventorie', 'affecte', 'archive')
        
        -- Réceptionniste peut voir les dons approuvés par MS
        WHEN 'receptionniste' THEN statut IN ('approuve_ms', 'receptionne', 'inventorie', 'affecte', 'archive')
        
        -- Bénéficiaires peuvent voir les dons de leur service
        WHEN 'beneficiaire' THEN 
            demandeur_service = get_current_user_service() 
            AND statut IN ('affecte', 'archive')
        
        -- Demandeurs peuvent voir leurs propres dons
        ELSE demandeur_email = get_current_user_email()
    END
);

-- Insertion de nouveaux dons
CREATE POLICY "donations_insert_policy" ON gestion_donations_ipt
FOR INSERT WITH CHECK (
    CASE get_current_user_role()
        -- Admin peut tout créer
        WHEN 'admin_systeme' THEN true
        
        -- Responsable approvisionnement peut créer pour son service
        WHEN 'responsable_approvisionnement' THEN true
        
        -- Demandeurs peuvent créer leurs propres dons
        ELSE demandeur_email = get_current_user_email()
    END
);

-- Mise à jour des dons
CREATE POLICY "donations_update_policy" ON gestion_donations_ipt
FOR UPDATE USING (
    CASE get_current_user_role()
        -- Admin peut tout modifier
        WHEN 'admin_systeme' THEN true
        
        -- Responsables peuvent modifier selon leur rôle et le statut
        WHEN 'responsable_approvisionnement' THEN 
            statut IN ('brouillon', 'soumis')
        
        WHEN 'direction_technique' THEN 
            type_don = 'equipement' AND statut = 'valide_appro'
        
        WHEN 'directeur_general' THEN 
            statut IN ('valide_appro', 'avis_technique')
        
        WHEN 'sous_direction_achats' THEN 
            statut IN ('valide_dg', 'soumis_ms', 'approuve_ms', 'refuse_ms')
        
        WHEN 'receptionniste' THEN 
            statut = 'approuve_ms'
        
        WHEN 'rve' THEN 
            statut IN ('receptionne', 'inventorie')
        
        -- Demandeurs peuvent modifier leurs brouillons
        ELSE demandeur_email = get_current_user_email() AND statut = 'brouillon'
    END
);

-- Suppression (soft delete uniquement)
CREATE POLICY "donations_delete_policy" ON gestion_donations_ipt
FOR UPDATE USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        ELSE demandeur_email = get_current_user_email() AND statut = 'brouillon'
    END
);

-- =====================================================
-- POLITIQUES POUR gestion_donation_workflow
-- =====================================================

-- Lecture du workflow
CREATE POLICY "workflow_select_policy" ON gestion_donation_workflow
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM gestion_donations_ipt d 
        WHERE d.id = donation_id 
        AND (
            CASE get_current_user_role()
                WHEN 'admin_systeme' THEN true
                WHEN 'responsable_approvisionnement' THEN true
                WHEN 'direction_technique' THEN d.type_don = 'equipement'
                WHEN 'directeur_general' THEN true
                WHEN 'sous_direction_achats' THEN true
                WHEN 'rve' THEN d.statut IN ('receptionne', 'inventorie', 'affecte', 'archive')
                WHEN 'receptionniste' THEN d.statut IN ('approuve_ms', 'receptionne', 'inventorie', 'affecte', 'archive')
                ELSE d.demandeur_email = get_current_user_email()
            END
        )
    )
);

-- Insertion dans le workflow (automatique via fonctions)
CREATE POLICY "workflow_insert_policy" ON gestion_donation_workflow
FOR INSERT WITH CHECK (
    get_current_user_role() IN ('admin_systeme', 'responsable_approvisionnement')
);

-- Mise à jour du workflow
CREATE POLICY "workflow_update_policy" ON gestion_donation_workflow
FOR UPDATE USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        WHEN 'responsable_approvisionnement' THEN role_responsable = 'responsable_approvisionnement'
        WHEN 'direction_technique' THEN role_responsable = 'direction_technique'
        WHEN 'directeur_general' THEN role_responsable = 'directeur_general'
        WHEN 'sous_direction_achats' THEN role_responsable = 'sous_direction_achats'
        WHEN 'receptionniste' THEN role_responsable = 'receptionniste'
        WHEN 'rve' THEN role_responsable = 'rve'
        ELSE false
    END
);

-- =====================================================
-- POLITIQUES POUR gestion_donation_documents
-- =====================================================

-- Lecture des documents
CREATE POLICY "documents_select_policy" ON gestion_donation_documents
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM gestion_donations_ipt d 
        WHERE d.id = donation_id 
        AND (
            CASE get_current_user_role()
                WHEN 'admin_systeme' THEN true
                WHEN 'responsable_approvisionnement' THEN true
                WHEN 'direction_technique' THEN d.type_don = 'equipement'
                WHEN 'directeur_general' THEN true
                WHEN 'sous_direction_achats' THEN true
                WHEN 'rve' THEN d.statut IN ('receptionne', 'inventorie', 'affecte', 'archive')
                WHEN 'receptionniste' THEN d.statut IN ('approuve_ms', 'receptionne', 'inventorie', 'affecte', 'archive')
                ELSE d.demandeur_email = get_current_user_email()
            END
        )
    )
);

-- Upload de documents
CREATE POLICY "documents_insert_policy" ON gestion_donation_documents
FOR INSERT WITH CHECK (
    EXISTS (
        SELECT 1 FROM gestion_donations_ipt d 
        WHERE d.id = donation_id 
        AND (
            CASE get_current_user_role()
                WHEN 'admin_systeme' THEN true
                WHEN 'responsable_approvisionnement' THEN true
                WHEN 'sous_direction_achats' THEN true
                WHEN 'receptionniste' THEN d.statut = 'approuve_ms'
                ELSE d.demandeur_email = get_current_user_email() AND d.statut = 'brouillon'
            END
        )
    )
);

-- Mise à jour des documents
CREATE POLICY "documents_update_policy" ON gestion_donation_documents
FOR UPDATE USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        ELSE uploaded_by = get_current_user_email()
    END
);

-- =====================================================
-- POLITIQUES POUR gestion_donation_inventaire
-- =====================================================

-- Lecture de l'inventaire
CREATE POLICY "inventaire_select_policy" ON gestion_donation_inventaire
FOR SELECT USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        WHEN 'rve' THEN true
        WHEN 'beneficiaire' THEN service_actuel = get_current_user_service()
        ELSE EXISTS (
            SELECT 1 FROM gestion_donations_ipt d 
            WHERE d.id = donation_id 
            AND d.demandeur_email = get_current_user_email()
        )
    END
);

-- Création d'entrées d'inventaire
CREATE POLICY "inventaire_insert_policy" ON gestion_donation_inventaire
FOR INSERT WITH CHECK (
    get_current_user_role() IN ('admin_systeme', 'rve')
);

-- Mise à jour de l'inventaire
CREATE POLICY "inventaire_update_policy" ON gestion_donation_inventaire
FOR UPDATE USING (
    get_current_user_role() IN ('admin_systeme', 'rve')
);

-- =====================================================
-- POLITIQUES POUR gestion_donation_mouvements
-- =====================================================

-- Lecture des mouvements
CREATE POLICY "mouvements_select_policy" ON gestion_donation_mouvements
FOR SELECT USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        WHEN 'rve' THEN true
        WHEN 'beneficiaire' THEN 
            service_origine = get_current_user_service() 
            OR service_destination = get_current_user_service()
        ELSE EXISTS (
            SELECT 1 FROM gestion_donations_ipt d 
            WHERE d.id = donation_id 
            AND d.demandeur_email = get_current_user_email()
        )
    END
);

-- Création de mouvements
CREATE POLICY "mouvements_insert_policy" ON gestion_donation_mouvements
FOR INSERT WITH CHECK (
    get_current_user_role() IN ('admin_systeme', 'rve') 
    OR demandeur = get_current_user_email()
);

-- Mise à jour des mouvements
CREATE POLICY "mouvements_update_policy" ON gestion_donation_mouvements
FOR UPDATE USING (
    get_current_user_role() IN ('admin_systeme', 'rve')
    OR created_by = get_current_user_email()
);

-- =====================================================
-- POLITIQUES POUR gestion_users_ipt
-- =====================================================

-- Lecture des utilisateurs
CREATE POLICY "users_select_policy" ON gestion_users_ipt
FOR SELECT USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        ELSE email = get_current_user_email()
    END
);

-- Création d'utilisateurs (admin uniquement)
CREATE POLICY "users_insert_policy" ON gestion_users_ipt
FOR INSERT WITH CHECK (
    get_current_user_role() = 'admin_systeme'
);

-- Mise à jour des utilisateurs
CREATE POLICY "users_update_policy" ON gestion_users_ipt
FOR UPDATE USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        ELSE email = get_current_user_email()
    END
);

-- =====================================================
-- POLITIQUES POUR gestion_donation_historique
-- =====================================================

-- Lecture de l'historique
CREATE POLICY "historique_select_policy" ON gestion_donation_historique
FOR SELECT USING (
    CASE get_current_user_role()
        WHEN 'admin_systeme' THEN true
        ELSE EXISTS (
            SELECT 1 FROM gestion_donations_ipt d 
            WHERE d.id = donation_id 
            AND (
                d.demandeur_email = get_current_user_email()
                OR get_current_user_role() IN (
                    'responsable_approvisionnement', 'direction_technique',
                    'directeur_general', 'sous_direction_achats', 'rve'
                )
            )
        )
    END
);

-- Insertion dans l'historique (automatique via triggers/fonctions)
CREATE POLICY "historique_insert_policy" ON gestion_donation_historique
FOR INSERT WITH CHECK (true); -- Contrôlé par les fonctions

-- =====================================================
-- COMMENTAIRES SUR LES POLITIQUES
-- =====================================================

COMMENT ON POLICY "donations_select_policy" ON gestion_donations_ipt IS 'Contrôle l''accès en lecture aux dons selon le rôle utilisateur';
COMMENT ON POLICY "donations_insert_policy" ON gestion_donations_ipt IS 'Contrôle la création de nouveaux dons';
COMMENT ON POLICY "donations_update_policy" ON gestion_donations_ipt IS 'Contrôle la modification des dons selon le rôle et le statut';

-- Accorder les permissions de base aux utilisateurs authentifiés
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;
