<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic DatabaseManager - Gestion Interne</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .diagnostic-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #6c757d;
        }
        
        .diagnostic-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .diagnostic-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .diagnostic-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .diagnostic-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .diagnostic-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .status-success {
            background: #28a745;
            color: white;
        }
        
        .status-error {
            background: #dc3545;
            color: white;
        }
        
        .status-warning {
            background: #ffc107;
            color: black;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            margin: 5px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .btn.success {
            background: linear-gradient(135deg, #28a745, #218838);
        }
        
        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .details {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Diagnostic DatabaseManager</h1>
            <p>Diagnostic et réparation du gestionnaire de base de données</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="runFullDiagnostic()">🔍 Diagnostic Complet</button>
            <button class="btn success" onclick="repairDatabaseManager()">🔧 Réparer DatabaseManager</button>
            <button class="btn secondary" onclick="testDatabaseOperations()">🧪 Tester Opérations</button>
            <button class="btn danger" onclick="resetEverything()">🔄 Reset Complet</button>
            <button class="btn secondary" onclick="clearLogs()">🗑️ Vider Logs</button>
        </div>

        <!-- Grille de diagnostic -->
        <div class="diagnostic-grid" id="diagnosticGrid">
            <!-- Les cartes de diagnostic seront générées dynamiquement -->
        </div>

        <!-- Logs -->
        <div class="log-container" id="diagnosticLog"></div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/app-config.js"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/database-manager-fix.js"></script>
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>
    <script src="assets/js/supabase-migration.js"></script>
    
    <script>
        // Variables globales
        let diagnosticResults = {};

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            generateDiagnosticCards();
            log('🚀 Diagnostic DatabaseManager prêt', 'info');
            
            // Diagnostic automatique au chargement
            setTimeout(() => {
                runFullDiagnostic();
            }, 1000);
        });

        // Générer les cartes de diagnostic
        function generateDiagnosticCards() {
            const diagnostics = [
                {
                    id: 'scripts_loaded',
                    name: 'Scripts Chargés',
                    description: 'Vérification du chargement des scripts'
                },
                {
                    id: 'database_manager',
                    name: 'DatabaseManager',
                    description: 'Disponibilité du gestionnaire de base de données'
                },
                {
                    id: 'supabase_utils',
                    name: 'SupabaseUtils',
                    description: 'Utilitaires Supabase'
                },
                {
                    id: 'local_storage',
                    name: 'LocalStorage',
                    description: 'Accès au stockage local'
                },
                {
                    id: 'supabase_connection',
                    name: 'Connexion Supabase',
                    description: 'État de la connexion Supabase'
                },
                {
                    id: 'data_operations',
                    name: 'Opérations Données',
                    description: 'Test des opérations CRUD'
                }
            ];

            const grid = document.getElementById('diagnosticGrid');
            
            diagnostics.forEach(diagnostic => {
                const card = document.createElement('div');
                card.className = 'diagnostic-card';
                card.id = `card-${diagnostic.id}`;
                
                card.innerHTML = `
                    <div class="diagnostic-title">${diagnostic.name}</div>
                    <div class="diagnostic-status status-warning" id="status-${diagnostic.id}">En attente</div>
                    <div class="diagnostic-description">${diagnostic.description}</div>
                    <div class="details" id="details-${diagnostic.id}" style="display: none;"></div>
                    <button class="btn" onclick="runSingleDiagnostic('${diagnostic.id}')">🔍 Tester</button>
                `;
                
                grid.appendChild(card);
            });
        }

        // Diagnostic complet
        async function runFullDiagnostic() {
            log('🔍 Démarrage du diagnostic complet...', 'info');
            
            const diagnostics = [
                'scripts_loaded',
                'database_manager',
                'supabase_utils',
                'local_storage',
                'supabase_connection',
                'data_operations'
            ];
            
            for (const diagnostic of diagnostics) {
                await runSingleDiagnostic(diagnostic);
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            log('✅ Diagnostic complet terminé', 'success');
            showSummary();
        }

        // Diagnostic individuel
        async function runSingleDiagnostic(diagnosticId) {
            log(`🔍 Test: ${diagnosticId}...`, 'info');
            
            try {
                let result;
                
                switch (diagnosticId) {
                    case 'scripts_loaded':
                        result = await testScriptsLoaded();
                        break;
                    case 'database_manager':
                        result = await testDatabaseManager();
                        break;
                    case 'supabase_utils':
                        result = await testSupabaseUtils();
                        break;
                    case 'local_storage':
                        result = await testLocalStorage();
                        break;
                    case 'supabase_connection':
                        result = await testSupabaseConnection();
                        break;
                    case 'data_operations':
                        result = await testDataOperations();
                        break;
                    default:
                        result = { success: false, error: 'Test non reconnu' };
                }
                
                updateDiagnosticCard(diagnosticId, result);
                diagnosticResults[diagnosticId] = result;
                
            } catch (error) {
                const result = { success: false, error: error.message };
                updateDiagnosticCard(diagnosticId, result);
                diagnosticResults[diagnosticId] = result;
                log(`❌ Erreur test ${diagnosticId}: ${error.message}`, 'error');
            }
        }

        // Tests individuels
        async function testScriptsLoaded() {
            const scripts = {
                'Supabase SDK': typeof supabase !== 'undefined',
                'APP_CONFIG': typeof APP_CONFIG !== 'undefined',
                'SUPABASE_CONFIG': typeof SUPABASE_CONFIG !== 'undefined',
                'DatabaseManager Fix': typeof window.getDatabaseManager === 'function',
                'SupabaseAutoInit': typeof SupabaseAutoInit !== 'undefined',
                'SupabaseMonitor': typeof SupabaseMonitor !== 'undefined',
                'SupabaseSync': typeof SupabaseSync !== 'undefined',
                'SupabaseMigration': typeof SupabaseMigration !== 'undefined'
            };
            
            const loaded = Object.values(scripts).filter(Boolean).length;
            const total = Object.keys(scripts).length;
            
            return {
                success: loaded === total,
                message: `${loaded}/${total} scripts chargés`,
                details: scripts
            };
        }

        async function testDatabaseManager() {
            try {
                // Test de base
                const dbManager = window.getDatabaseManager ? window.getDatabaseManager() : window.DatabaseManager;
                
                if (!dbManager) {
                    return {
                        success: false,
                        error: 'DatabaseManager non trouvé',
                        details: {
                            'window.DatabaseManager': typeof window.DatabaseManager,
                            'window.getDatabaseManager': typeof window.getDatabaseManager
                        }
                    };
                }
                
                // Test des méthodes
                const methods = ['save', 'load', 'update', 'delete', 'isAvailable'];
                const methodsAvailable = methods.map(method => ({
                    method,
                    available: typeof dbManager[method] === 'function'
                }));
                
                const allMethodsAvailable = methodsAvailable.every(m => m.available);
                
                return {
                    success: allMethodsAvailable,
                    message: allMethodsAvailable ? 'DatabaseManager fonctionnel' : 'Méthodes manquantes',
                    details: {
                        type: typeof dbManager,
                        methods: methodsAvailable,
                        isAvailable: typeof dbManager.isAvailable === 'function' ? dbManager.isAvailable() : 'N/A'
                    }
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testSupabaseUtils() {
            try {
                const supabaseUtils = window.SupabaseUtils || (typeof SupabaseUtils !== 'undefined' ? SupabaseUtils : null);
                
                if (!supabaseUtils) {
                    return {
                        success: false,
                        error: 'SupabaseUtils non trouvé',
                        details: {
                            'window.SupabaseUtils': typeof window.SupabaseUtils,
                            'global SupabaseUtils': typeof SupabaseUtils
                        }
                    };
                }
                
                const methods = ['isAvailable', 'saveData', 'loadData'];
                const methodsAvailable = methods.map(method => ({
                    method,
                    available: typeof supabaseUtils[method] === 'function'
                }));
                
                const allMethodsAvailable = methodsAvailable.every(m => m.available);
                
                return {
                    success: allMethodsAvailable,
                    message: allMethodsAvailable ? 'SupabaseUtils disponible' : 'Méthodes manquantes',
                    details: {
                        methods: methodsAvailable,
                        isAvailable: typeof supabaseUtils.isAvailable === 'function' ? supabaseUtils.isAvailable() : 'N/A'
                    }
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testLocalStorage() {
            try {
                const testKey = 'diagnostic_test_' + Date.now();
                const testData = { test: true, timestamp: new Date().toISOString() };
                
                // Test écriture
                localStorage.setItem(testKey, JSON.stringify(testData));
                
                // Test lecture
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                
                // Test suppression
                localStorage.removeItem(testKey);
                
                const success = retrieved && retrieved.test === true;
                
                return {
                    success,
                    message: success ? 'LocalStorage fonctionnel' : 'Erreur LocalStorage',
                    details: {
                        write: true,
                        read: retrieved !== null,
                        delete: localStorage.getItem(testKey) === null
                    }
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testSupabaseConnection() {
            try {
                if (typeof initializeSupabase === 'function') {
                    const result = await initializeSupabase();
                    return {
                        success: result.available,
                        message: result.available ? 'Connexion Supabase OK' : 'Connexion Supabase échouée',
                        details: result
                    };
                } else {
                    return {
                        success: false,
                        error: 'initializeSupabase non disponible'
                    };
                }
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testDataOperations() {
            try {
                const dbManager = window.getDatabaseManager ? window.getDatabaseManager() : window.DatabaseManager;
                
                if (!dbManager) {
                    return {
                        success: false,
                        error: 'DatabaseManager non disponible'
                    };
                }
                
                const testData = {
                    id: 'diagnostic_test_' + Date.now(),
                    content: 'Test diagnostic',
                    timestamp: new Date().toISOString()
                };
                
                // Test sauvegarde
                const saved = await dbManager.save('test_diagnostic', testData);
                
                if (!saved) {
                    return {
                        success: false,
                        error: 'Échec sauvegarde'
                    };
                }
                
                // Test chargement
                const loaded = await dbManager.load('test_diagnostic');
                
                // Nettoyer
                try {
                    localStorage.removeItem('gestion_test_diagnostic');
                } catch (e) {}
                
                return {
                    success: loaded && loaded.length > 0,
                    message: 'Opérations CRUD fonctionnelles',
                    details: {
                        saved: saved !== null,
                        loaded: loaded && loaded.length > 0,
                        dataCount: loaded ? loaded.length : 0
                    }
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // Mettre à jour une carte de diagnostic
        function updateDiagnosticCard(diagnosticId, result) {
            const card = document.getElementById(`card-${diagnosticId}`);
            const status = document.getElementById(`status-${diagnosticId}`);
            const details = document.getElementById(`details-${diagnosticId}`);
            
            // Réinitialiser les classes
            card.className = 'diagnostic-card';
            status.className = 'diagnostic-status';
            
            if (result.success) {
                card.classList.add('success');
                status.classList.add('status-success');
                status.textContent = '✅ OK';
                log(`✅ ${diagnosticId}: ${result.message || 'Réussi'}`, 'success');
            } else {
                card.classList.add('error');
                status.classList.add('status-error');
                status.textContent = '❌ Erreur';
                log(`❌ ${diagnosticId}: ${result.error || 'Échec'}`, 'error');
            }
            
            // Afficher les détails
            if (result.details) {
                details.style.display = 'block';
                details.textContent = JSON.stringify(result.details, null, 2);
            }
        }

        // Réparer DatabaseManager
        function repairDatabaseManager() {
            log('🔧 Réparation du DatabaseManager...', 'info');
            
            try {
                // Forcer la recréation
                delete window.DatabaseManager;
                
                // Recharger le script de fix
                if (typeof window.getDatabaseManager === 'function') {
                    const dbManager = window.getDatabaseManager();
                    log('✅ DatabaseManager réparé avec succès', 'success');
                    
                    // Retester
                    setTimeout(() => {
                        runSingleDiagnostic('database_manager');
                    }, 500);
                } else {
                    log('❌ Échec de la réparation', 'error');
                }
                
            } catch (error) {
                log(`❌ Erreur lors de la réparation: ${error.message}`, 'error');
            }
        }

        // Tester les opérations de base de données
        async function testDatabaseOperations() {
            log('🧪 Test des opérations de base de données...', 'info');
            await runSingleDiagnostic('data_operations');
        }

        // Reset complet
        function resetEverything() {
            if (confirm('Êtes-vous sûr de vouloir tout réinitialiser ?')) {
                log('🔄 Reset complet en cours...', 'info');
                
                // Nettoyer les variables globales
                delete window.DatabaseManager;
                delete window.SupabaseUtils;
                
                // Recharger la page
                setTimeout(() => {
                    location.reload();
                }, 1000);
            }
        }

        // Afficher le résumé
        function showSummary() {
            const total = Object.keys(diagnosticResults).length;
            const passed = Object.values(diagnosticResults).filter(r => r.success).length;
            const failed = total - passed;
            
            log(`📊 Résumé: ${passed}/${total} tests réussis, ${failed} échecs`, 'info');
        }

        // Fonctions utilitaires
        function log(message, level = 'info') {
            const logContainer = document.getElementById('diagnosticLog');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('diagnosticLog').innerHTML = '';
        }
    </script>
</body>
</html>
