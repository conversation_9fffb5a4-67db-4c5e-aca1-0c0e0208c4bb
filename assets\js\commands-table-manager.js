/**
 * Module de gestion de la table des commandes avec fonctionnalités avancées
 */

class CommandsTableManager {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.pageSize = 25;
        this.sortColumn = 'created_at';
        this.sortDirection = 'desc';
        this.filters = {};
        this.realtimeSubscription = null;
    }

    /**
     * Initialiser le gestionnaire de table
     */
    async initialize() {
        console.log('📦 Initialisation du gestionnaire de table des commandes...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            if (!this.currentUser) {
                throw new Error('Utilisateur non authentifié');
            }

            // Configurer les événements
            this.setupEventListeners();

            // Charger les données initiales
            await this.loadData();

            // Configurer les mises à jour en temps réel
            this.setupRealtimeSubscription();

            console.log('✅ Gestionnaire de table des commandes initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation table commandes:', error);
            throw error;
        }
    }

    /**
     * Configurer les écouteurs d'événements
     */
    setupEventListeners() {
        // Tri des colonnes
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const column = e.currentTarget.dataset.column;
                this.handleSort(column);
            });
        });

        // Taille de page
        document.getElementById('pageSize')?.addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.renderTable();
        });

        // Filtres
        document.getElementById('btnApplyFilters')?.addEventListener('click', () => {
            this.applyFilters();
        });

        document.getElementById('btnClearFilters')?.addEventListener('click', () => {
            this.clearFilters();
        });

        document.getElementById('btnToggleFilters')?.addEventListener('click', () => {
            this.toggleFilters();
        });

        // Export
        document.getElementById('btnExport')?.addEventListener('click', () => {
            this.exportData();
        });

        // Recherche globale en temps réel
        document.getElementById('searchGlobal')?.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.filters.global = e.target.value;
                this.applyFilters();
            }, 300);
        });
    }

    /**
     * Charger les données depuis Supabase
     */
    async loadData() {
        try {
            console.log('📥 Chargement des données de la table des commandes...');

            // Utiliser la vue optimisée commands_table_view
            let query = this.supabase
                .from('commands_table_view')
                .select('*');

            // Appliquer les filtres de permissions selon le rôle
            const userRole = this.currentUser?.user_metadata?.role || 'user';
            if (userRole !== 'admin') {
                // Les utilisateurs normaux ne voient que leurs propres commandes
                if (userRole === 'user') {
                    query = query.eq('demandeur_email', this.currentUser.email);
                }
                // Les autres rôles voient toutes les commandes mais avec des actions limitées
            }

            const { data, error } = await query.order('created_at', { ascending: false });

            if (error) {
                // Fallback vers la table principale si la vue n'existe pas encore
                console.warn('Vue commands_table_view non disponible, utilisation de la table principale');
                return await this.loadDataFallback();
            }

            this.data = data || [];
            this.filteredData = [...this.data];
            
            // Enrichir les données avec des informations calculées
            this.enrichData();

            // Rendre la table
            this.renderTable();

            console.log(`✅ ${this.data.length} commandes chargées`);

        } catch (error) {
            console.error('❌ Erreur chargement données commandes:', error);
            this.showNotification('Erreur lors du chargement des données', 'error');
        }
    }

    /**
     * Méthode de fallback pour charger les données depuis la table principale
     */
    async loadDataFallback() {
        let query = this.supabase
            .from('gestion_commandes')
            .select(`
                *,
                workflow:gestion_commande_workflow(*)
            `);

        const userRole = this.currentUser?.user_metadata?.role || 'user';
        if (userRole !== 'admin' && userRole === 'user') {
            query = query.eq('demandeur_email', this.currentUser.email);
        }

        const { data, error } = await query.order('created_at', { ascending: false });

        if (error) throw error;

        this.data = data || [];
        this.filteredData = [...this.data];
        this.enrichData();
        this.renderTable();
    }

    /**
     * Enrichir les données avec des informations calculées
     */
    enrichData() {
        this.data.forEach(cmd => {
            // Formater les dates
            cmd.date_commande_formatted = this.formatDate(cmd.date_commande);
            cmd.date_livraison_formatted = this.formatDate(cmd.date_livraison_prevue);
            
            // Calculer si en retard
            if (!cmd.en_retard) {
                cmd.en_retard = cmd.date_livraison_prevue && 
                    new Date(cmd.date_livraison_prevue) < new Date() &&
                    !['livre_complet', 'cloture', 'annule'].includes(cmd.statut);
            }
            
            // Formater le montant
            cmd.montant_formatted = this.formatAmount(cmd.montant_ttc, cmd.devise);
            
            // Description courte si pas déjà définie
            if (!cmd.description_courte) {
                cmd.description_courte = cmd.objet_commande?.length > 50 
                    ? cmd.objet_commande.substring(0, 47) + '...'
                    : cmd.objet_commande || 'Non spécifié';
            }
        });
    }

    /**
     * Formater une date
     */
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR');
    }

    /**
     * Formater un montant
     */
    formatAmount(amount, devise = 'TND') {
        if (!amount) return '0 ' + devise;
        return new Intl.NumberFormat('fr-TN', {
            style: 'decimal',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount) + ' ' + devise;
    }

    /**
     * Gérer le tri des colonnes
     */
    handleSort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.sortData();
        this.renderTable();
        this.updateSortIndicators();
    }

    /**
     * Trier les données
     */
    sortData() {
        this.filteredData.sort((a, b) => {
            let valueA = a[this.sortColumn];
            let valueB = b[this.sortColumn];

            // Gestion des valeurs nulles
            if (valueA == null) valueA = '';
            if (valueB == null) valueB = '';

            // Conversion pour les dates
            if (this.sortColumn.includes('date')) {
                valueA = new Date(valueA);
                valueB = new Date(valueB);
            }

            // Conversion pour les montants
            if (this.sortColumn === 'montant_ttc') {
                valueA = parseFloat(valueA) || 0;
                valueB = parseFloat(valueB) || 0;
            }

            let comparison = 0;
            if (valueA > valueB) comparison = 1;
            if (valueA < valueB) comparison = -1;

            return this.sortDirection === 'desc' ? -comparison : comparison;
        });
    }

    /**
     * Mettre à jour les indicateurs de tri
     */
    updateSortIndicators() {
        // Réinitialiser tous les indicateurs
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.className = 'sort-indicator';
        });

        // Mettre à jour l'indicateur actuel
        const currentHeader = document.querySelector(`[data-column="${this.sortColumn}"] .sort-indicator`);
        if (currentHeader) {
            currentHeader.className = `sort-indicator ${this.sortDirection}`;
        }
    }

    /**
     * Appliquer les filtres
     */
    applyFilters() {
        // Récupérer les valeurs des filtres
        this.filters = {
            global: document.getElementById('searchGlobal')?.value || '',
            statut: document.getElementById('filterStatut')?.value || '',
            type: document.getElementById('filterType')?.value || '',
            urgence: document.getElementById('filterUrgence')?.value || '',
            dateDebut: document.getElementById('filterDateDebut')?.value || '',
            dateFin: document.getElementById('filterDateFin')?.value || '',
            fournisseur: document.getElementById('filterFournisseur')?.value || '',
            demandeur: document.getElementById('filterDemandeur')?.value || ''
        };

        // Filtrer les données
        this.filteredData = this.data.filter(cmd => {
            // Filtre global
            if (this.filters.global) {
                const searchText = this.filters.global.toLowerCase();
                const searchableText = [
                    cmd.numero_commande,
                    cmd.fournisseur_nom,
                    cmd.demandeur_service,
                    cmd.objet_commande,
                    cmd.statut
                ].join(' ').toLowerCase();
                
                if (!searchableText.includes(searchText)) return false;
            }

            // Filtres spécifiques
            if (this.filters.statut && cmd.statut !== this.filters.statut) return false;
            if (this.filters.type && cmd.type_commande !== this.filters.type) return false;
            if (this.filters.urgence && cmd.urgence !== this.filters.urgence) return false;
            if (this.filters.fournisseur && !cmd.fournisseur_nom?.toLowerCase().includes(this.filters.fournisseur.toLowerCase())) return false;
            if (this.filters.demandeur && !cmd.demandeur_service?.toLowerCase().includes(this.filters.demandeur.toLowerCase())) return false;

            // Filtres de date
            if (this.filters.dateDebut) {
                const cmdDate = new Date(cmd.date_commande);
                const filterDate = new Date(this.filters.dateDebut);
                if (cmdDate < filterDate) return false;
            }
            if (this.filters.dateFin) {
                const cmdDate = new Date(cmd.date_commande);
                const filterDate = new Date(this.filters.dateFin);
                if (cmdDate > filterDate) return false;
            }

            return true;
        });

        // Réinitialiser la pagination
        this.currentPage = 1;
        
        // Re-trier et rendre
        this.sortData();
        this.renderTable();
    }

    /**
     * Effacer tous les filtres
     */
    clearFilters() {
        document.getElementById('searchGlobal').value = '';
        document.getElementById('filterStatut').value = '';
        document.getElementById('filterType').value = '';
        document.getElementById('filterUrgence').value = '';
        document.getElementById('filterDateDebut').value = '';
        document.getElementById('filterDateFin').value = '';
        document.getElementById('filterFournisseur').value = '';
        document.getElementById('filterDemandeur').value = '';

        this.filters = {};
        this.filteredData = [...this.data];
        this.currentPage = 1;
        this.renderTable();
    }

    /**
     * Basculer l'affichage des filtres
     */
    toggleFilters() {
        const filtersContent = document.getElementById('filtersContent');
        const isVisible = filtersContent.style.display !== 'none';
        filtersContent.style.display = isVisible ? 'none' : 'block';
    }

    /**
     * Rendre la table
     */
    renderTable() {
        const tbody = document.getElementById('commandsTableBody');
        if (!tbody) return;

        // Calculer la pagination
        const totalItems = this.filteredData.length;
        const totalPages = Math.ceil(totalItems / this.pageSize);
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, totalItems);
        const pageData = this.filteredData.slice(startIndex, endIndex);

        // Rendre les lignes
        tbody.innerHTML = pageData.map(cmd => this.renderTableRow(cmd)).join('');

        // Mettre à jour les informations
        this.updateTableInfo(totalItems, startIndex, endIndex);
        this.renderPagination(totalPages);
    }

    /**
     * Rendre une ligne de la table
     */
    renderTableRow(cmd) {
        const actions = this.generateActions(cmd);
        
        return `
            <tr data-id="${cmd.id}">
                <td><strong>${cmd.numero_commande}</strong></td>
                <td>
                    <span class="table-badge badge-${cmd.type_commande}">
                        ${this.getTypeIcon(cmd.type_commande)} ${this.getTypeLabel(cmd.type_commande)}
                    </span>
                </td>
                <td>${cmd.demandeur_service || 'Non spécifié'}</td>
                <td>${cmd.fournisseur_nom || 'Non spécifié'}</td>
                <td>
                    <span class="table-status status-${cmd.statut}">
                        ${this.getStatusLabel(cmd.statut)}
                    </span>
                    ${cmd.en_retard ? '<span style="color: #ef4444; margin-left: 5px;">⚠️</span>' : ''}
                </td>
                <td>${cmd.date_commande_formatted}</td>
                <td>${cmd.date_livraison_formatted || 'Non définie'}</td>
                <td><strong>${cmd.montant_formatted}</strong></td>
                <td class="actions-column">
                    <div class="table-actions">
                        ${actions}
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Obtenir l'icône du type
     */
    getTypeIcon(type) {
        const icons = {
            'fournitures': '📦',
            'equipements': '🔧',
            'services': '🛠️',
            'maintenance': '⚙️'
        };
        return icons[type] || '📋';
    }

    /**
     * Obtenir le libellé du type
     */
    getTypeLabel(type) {
        const labels = {
            'fournitures': 'Fournitures',
            'equipements': 'Équipements',
            'services': 'Services',
            'maintenance': 'Maintenance'
        };
        return labels[type] || type;
    }

    /**
     * Obtenir le libellé du statut
     */
    getStatusLabel(statut) {
        const labels = {
            'brouillon': 'Brouillon',
            'soumise': 'Soumise',
            'validee_chef': 'Validée Chef',
            'validee_dg': 'Validée DG',
            'approuvee_ms': 'Approuvée MS',
            'en_cours': 'En Cours',
            'expedie': 'Expédiée',
            'livre_complet': 'Livrée',
            'cloture': 'Clôturée',
            'annule': 'Annulée',
            'refuse': 'Refusée'
        };
        return labels[statut] || statut;
    }

    /**
     * Générer les actions pour une ligne
     */
    generateActions(cmd) {
        const userRole = this.currentUser?.user_metadata?.role || 'user';
        const actions = [];

        // Action Voir (toujours disponible)
        actions.push(`
            <button class="btn-table btn-view" onclick="commandsTableManager.viewCommand('${cmd.id}')" title="Voir détails">
                👁️
            </button>
        `);

        // Actions selon le rôle et le statut
        if (this.canEdit(cmd, userRole)) {
            actions.push(`
                <button class="btn-table btn-edit" onclick="commandsTableManager.editCommand('${cmd.id}')" title="Modifier">
                    ✏️
                </button>
            `);
        }

        if (this.canValidate(cmd, userRole)) {
            actions.push(`
                <button class="btn-table btn-validate" onclick="commandsTableManager.validateCommand('${cmd.id}')" title="Valider">
                    ✅
                </button>
            `);
        }

        if (this.canReject(cmd, userRole)) {
            actions.push(`
                <button class="btn-table btn-reject" onclick="commandsTableManager.rejectCommand('${cmd.id}')" title="Refuser">
                    ❌
                </button>
            `);
        }

        return actions.join('');
    }

    /**
     * Vérifier si l'utilisateur peut modifier une commande
     */
    canEdit(cmd, userRole) {
        // Seuls les admins autorisés peuvent modifier
        if (this.isAuthorizedAdmin()) return true;

        // Accès en lecture seule pour tous les autres utilisateurs
        return false;
    }

    /**
     * Vérifier si l'utilisateur est un admin autorisé
     */
    isAuthorizedAdmin() {
        if (!this.currentUser) return false;

        const authorizedAdmins = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        return authorizedAdmins.includes(this.currentUser.email);
    }

    /**
     * Vérifier si l'utilisateur peut valider une commande
     */
    canValidate(cmd, userRole) {
        // Seuls les admins autorisés peuvent valider
        return this.isAuthorizedAdmin();
    }

    /**
     * Vérifier si l'utilisateur peut refuser une commande
     */
    canReject(cmd, userRole) {
        // Seuls les admins autorisés peuvent refuser
        return this.isAuthorizedAdmin();
    }

    /**
     * Vérifier si l'utilisateur peut ajouter de nouvelles commandes
     */
    canAdd() {
        // Seuls les admins autorisés peuvent ajouter
        return this.isAuthorizedAdmin();
    }

    /**
     * Vérifier si l'utilisateur peut supprimer une commande
     */
    canDelete(cmd, userRole) {
        // Seuls les admins autorisés peuvent supprimer
        return this.isAuthorizedAdmin();
    }

    // Actions sur les commandes
    async viewCommand(commandId) {
        // Ouvrir le modal de détails ou rediriger
        window.open(`commands-management.html?id=${commandId}`, '_blank');
    }

    async editCommand(commandId) {
        // Logique d'édition
        console.log('Édition de la commande:', commandId);
        this.showNotification('Fonction d\'édition en cours de développement', 'info');
    }

    async validateCommand(commandId) {
        // Logique de validation
        console.log('Validation de la commande:', commandId);
        this.showNotification('Fonction de validation en cours de développement', 'info');
    }

    async rejectCommand(commandId) {
        // Logique de refus
        console.log('Refus de la commande:', commandId);
        this.showNotification('Fonction de refus en cours de développement', 'info');
    }

    /**
     * Mettre à jour les informations de la table
     */
    updateTableInfo(totalItems, startIndex, endIndex) {
        document.getElementById('tableResultsCount').textContent = `${totalItems} résultats`;
        document.getElementById('paginationInfo').textContent = 
            `Affichage de ${startIndex + 1} à ${endIndex} sur ${totalItems} résultats`;
    }

    /**
     * Rendre la pagination
     */
    renderPagination(totalPages) {
        const container = document.getElementById('paginationControls');
        if (!container) return;

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        const buttons = [];

        // Bouton précédent
        buttons.push(`
            <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="commandsTableManager.goToPage(${this.currentPage - 1})">
                ‹ Précédent
            </button>
        `);

        // Numéros de page
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            buttons.push(`<button class="pagination-btn" onclick="commandsTableManager.goToPage(1)">1</button>`);
            if (startPage > 2) buttons.push(`<span>...</span>`);
        }

        for (let i = startPage; i <= endPage; i++) {
            buttons.push(`
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="commandsTableManager.goToPage(${i})">
                    ${i}
                </button>
            `);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) buttons.push(`<span>...</span>`);
            buttons.push(`<button class="pagination-btn" onclick="commandsTableManager.goToPage(${totalPages})">${totalPages}</button>`);
        }

        // Bouton suivant
        buttons.push(`
            <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
                    onclick="commandsTableManager.goToPage(${this.currentPage + 1})">
                Suivant ›
            </button>
        `);

        container.innerHTML = buttons.join('');
    }

    /**
     * Aller à une page spécifique
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderTable();
        }
    }

    /**
     * Exporter les données
     */
    exportData() {
        try {
            const csvData = this.generateCSV();
            this.downloadCSV(csvData, `commandes_export_${new Date().toISOString().split('T')[0]}.csv`);
            this.showNotification('Export réussi', 'success');
        } catch (error) {
            console.error('Erreur export:', error);
            this.showNotification('Erreur lors de l\'export', 'error');
        }
    }

    /**
     * Générer les données CSV
     */
    generateCSV() {
        const headers = [
            'N° Commande', 'Type', 'Demandeur', 'Fournisseur', 'Statut', 
            'Date Commande', 'Livraison Prévue', 'Montant'
        ];

        const rows = this.filteredData.map(cmd => [
            cmd.numero_commande,
            this.getTypeLabel(cmd.type_commande),
            cmd.demandeur_service,
            cmd.fournisseur_nom,
            this.getStatusLabel(cmd.statut),
            cmd.date_commande_formatted,
            cmd.date_livraison_formatted,
            cmd.montant_formatted
        ]);

        return [headers, ...rows].map(row => 
            row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }

    /**
     * Télécharger le fichier CSV
     */
    downloadCSV(csvData, filename) {
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    /**
     * Configurer les mises à jour en temps réel
     */
    setupRealtimeSubscription() {
        try {
            this.realtimeSubscription = this.supabase
                .channel('commands_table')
                .on('postgres_changes', 
                    { event: '*', schema: 'public', table: 'gestion_commandes' },
                    (payload) => {
                        console.log('🔄 Mise à jour temps réel table commandes:', payload);
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .subscribe();

            console.log('✅ Abonnement temps réel configuré pour la table commandes');

        } catch (error) {
            console.error('❌ Erreur configuration temps réel table commandes:', error);
        }
    }

    /**
     * Afficher une notification
     */
    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Nettoyer les ressources
     */
    destroy() {
        if (this.realtimeSubscription) {
            this.supabase.removeChannel(this.realtimeSubscription);
        }
        
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
}

// Instance globale
window.commandsTableManager = new CommandsTableManager();
window.CommandsTableManager = CommandsTableManager;
