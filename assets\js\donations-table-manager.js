/**
 * Module de gestion de la table des dons avec fonctionnalités avancées
 */

class DonationsTableManager {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.data = [];
        this.filteredData = [];
        this.currentPage = 1;
        this.pageSize = 25;
        this.sortColumn = 'created_at';
        this.sortDirection = 'desc';
        this.filters = {};
        this.realtimeSubscription = null;
    }

    /**
     * Initialiser le gestionnaire de table
     */
    async initialize() {
        console.log('📊 Initialisation du gestionnaire de table des dons...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            if (!this.currentUser) {
                throw new Error('Utilisateur non authentifié');
            }

            // Configurer les événements
            this.setupEventListeners();

            // Charger les données initiales
            await this.loadData();

            // Configurer les mises à jour en temps réel
            this.setupRealtimeSubscription();

            console.log('✅ Gestionnaire de table initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation table:', error);
            throw error;
        }
    }

    /**
     * Configurer les écouteurs d'événements
     */
    setupEventListeners() {
        // Tri des colonnes
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const column = e.currentTarget.dataset.column;
                this.handleSort(column);
            });
        });

        // Taille de page
        document.getElementById('pageSize')?.addEventListener('change', (e) => {
            this.pageSize = parseInt(e.target.value);
            this.currentPage = 1;
            this.renderTable();
        });

        // Filtres
        document.getElementById('btnApplyFilters')?.addEventListener('click', () => {
            this.applyFilters();
        });

        document.getElementById('btnClearFilters')?.addEventListener('click', () => {
            this.clearFilters();
        });

        document.getElementById('btnToggleFilters')?.addEventListener('click', () => {
            this.toggleFilters();
        });

        // Export
        document.getElementById('btnExport')?.addEventListener('click', () => {
            this.exportData();
        });

        // Recherche globale en temps réel
        document.getElementById('searchGlobal')?.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.filters.global = e.target.value;
                this.applyFilters();
            }, 300);
        });
    }

    /**
     * Charger les données depuis Supabase
     */
    async loadData() {
        try {
            console.log('📥 Chargement des données de la table...');

            // Utiliser la vue optimisée donations_table_view
            let query = this.supabase
                .from('donations_table_view')
                .select('*');

            // Appliquer les filtres de permissions selon le rôle
            const userRole = this.currentUser?.user_metadata?.role || 'user';
            if (userRole !== 'admin') {
                // Les utilisateurs normaux ne voient que leurs propres dons
                if (userRole === 'user') {
                    query = query.eq('demandeur_email', this.currentUser.email);
                }
                // Les autres rôles voient tous les dons mais avec des actions limitées
            }

            const { data, error } = await query.order('created_at', { ascending: false });

            if (error) {
                // Fallback vers la table principale si la vue n'existe pas encore
                console.warn('Vue donations_table_view non disponible, utilisation de la table principale');
                return await this.loadDataFallback();
            }

            this.data = data || [];
            this.filteredData = [...this.data];

            // Enrichir les données avec des informations calculées
            this.enrichData();

            // Rendre la table
            this.renderTable();

            console.log(`✅ ${this.data.length} dons chargés`);

        } catch (error) {
            console.error('❌ Erreur chargement données:', error);
            this.showNotification('Erreur lors du chargement des données', 'error');
        }
    }

    /**
     * Méthode de fallback pour charger les données depuis la table principale
     */
    async loadDataFallback() {
        let query = this.supabase
            .from('gestion_donations')
            .select(`
                *,
                etapes:gestion_donation_etapes(*)
            `);

        const userRole = this.currentUser?.user_metadata?.role || 'user';
        if (userRole !== 'admin' && userRole === 'user') {
            query = query.eq('demandeur_email', this.currentUser.email);
        }

        const { data, error } = await query.order('created_at', { ascending: false });

        if (error) throw error;

        this.data = data || [];
        this.filteredData = [...this.data];
        this.enrichData();
        this.renderTable();
    }

    /**
     * Enrichir les données avec des informations calculées
     */
    enrichData() {
        this.data.forEach(don => {
            // Calculer l'étape actuelle
            don.etape_actuelle = this.calculateCurrentStep(don);
            
            // Générer la désignation à partir des items
            don.designation = this.generateDesignation(don);
            
            // Calculer le code du don
            don.code_don = don.numero_don || `DON-${don.id.substring(0, 8)}`;
            
            // Formater la date
            don.date_creation_formatted = this.formatDate(don.created_at);
        });
    }

    /**
     * Calculer l'étape actuelle d'un don
     */
    calculateCurrentStep(don) {
        if (!don.etapes) return 1;
        
        // Trouver l'étape en cours
        const currentStep = don.etapes.find(etape => etape.statut === 'en_cours');
        if (currentStep) return currentStep.numero;
        
        // Sinon, trouver la dernière étape terminée + 1
        const completedSteps = don.etapes.filter(etape => etape.statut === 'termine');
        if (completedSteps.length === 0) return 1;
        
        const lastCompleted = Math.max(...completedSteps.map(e => e.numero));
        return Math.min(lastCompleted + 1, 10);
    }

    /**
     * Générer la désignation à partir des items
     */
    generateDesignation(don) {
        if (!don.items || don.items.length === 0) {
            return don.raison_don || 'Non spécifié';
        }
        
        const items = don.items.slice(0, 3).map(item => item.nom_item).join(', ');
        const remaining = don.items.length - 3;
        
        return remaining > 0 ? `${items} (+${remaining} autres)` : items;
    }

    /**
     * Formater une date
     */
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString('fr-FR');
    }

    /**
     * Gérer le tri des colonnes
     */
    handleSort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.sortData();
        this.renderTable();
        this.updateSortIndicators();
    }

    /**
     * Trier les données
     */
    sortData() {
        this.filteredData.sort((a, b) => {
            let valueA = a[this.sortColumn];
            let valueB = b[this.sortColumn];

            // Gestion des valeurs nulles
            if (valueA == null) valueA = '';
            if (valueB == null) valueB = '';

            // Conversion pour les dates
            if (this.sortColumn === 'created_at') {
                valueA = new Date(valueA);
                valueB = new Date(valueB);
            }

            // Conversion pour les nombres
            if (this.sortColumn === 'etape_actuelle') {
                valueA = parseInt(valueA) || 0;
                valueB = parseInt(valueB) || 0;
            }

            let comparison = 0;
            if (valueA > valueB) comparison = 1;
            if (valueA < valueB) comparison = -1;

            return this.sortDirection === 'desc' ? -comparison : comparison;
        });
    }

    /**
     * Mettre à jour les indicateurs de tri
     */
    updateSortIndicators() {
        // Réinitialiser tous les indicateurs
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.className = 'sort-indicator';
        });

        // Mettre à jour l'indicateur actuel
        const currentHeader = document.querySelector(`[data-column="${this.sortColumn}"] .sort-indicator`);
        if (currentHeader) {
            currentHeader.className = `sort-indicator ${this.sortDirection}`;
        }
    }

    /**
     * Appliquer les filtres
     */
    applyFilters() {
        // Récupérer les valeurs des filtres
        this.filters = {
            global: document.getElementById('searchGlobal')?.value || '',
            statut: document.getElementById('filterStatut')?.value || '',
            type: document.getElementById('filterType')?.value || '',
            etape: document.getElementById('filterEtape')?.value || '',
            dateDebut: document.getElementById('filterDateDebut')?.value || '',
            dateFin: document.getElementById('filterDateFin')?.value || '',
            donateur: document.getElementById('filterDonateur')?.value || '',
            beneficiaire: document.getElementById('filterBeneficiaire')?.value || ''
        };

        // Filtrer les données
        this.filteredData = this.data.filter(don => {
            // Filtre global
            if (this.filters.global) {
                const searchText = this.filters.global.toLowerCase();
                const searchableText = [
                    don.numero_don,
                    don.donneur_nom,
                    don.demandeur_service,
                    don.designation,
                    don.statut
                ].join(' ').toLowerCase();
                
                if (!searchableText.includes(searchText)) return false;
            }

            // Filtres spécifiques
            if (this.filters.statut && don.statut !== this.filters.statut) return false;
            if (this.filters.type && don.type_don !== this.filters.type) return false;
            if (this.filters.etape && don.etape_actuelle != this.filters.etape) return false;
            if (this.filters.donateur && !don.donneur_nom?.toLowerCase().includes(this.filters.donateur.toLowerCase())) return false;
            if (this.filters.beneficiaire && !don.demandeur_service?.toLowerCase().includes(this.filters.beneficiaire.toLowerCase())) return false;

            // Filtres de date
            if (this.filters.dateDebut) {
                const donDate = new Date(don.created_at);
                const filterDate = new Date(this.filters.dateDebut);
                if (donDate < filterDate) return false;
            }
            if (this.filters.dateFin) {
                const donDate = new Date(don.created_at);
                const filterDate = new Date(this.filters.dateFin);
                if (donDate > filterDate) return false;
            }

            return true;
        });

        // Réinitialiser la pagination
        this.currentPage = 1;
        
        // Re-trier et rendre
        this.sortData();
        this.renderTable();
    }

    /**
     * Effacer tous les filtres
     */
    clearFilters() {
        document.getElementById('searchGlobal').value = '';
        document.getElementById('filterStatut').value = '';
        document.getElementById('filterType').value = '';
        document.getElementById('filterEtape').value = '';
        document.getElementById('filterDateDebut').value = '';
        document.getElementById('filterDateFin').value = '';
        document.getElementById('filterDonateur').value = '';
        document.getElementById('filterBeneficiaire').value = '';

        this.filters = {};
        this.filteredData = [...this.data];
        this.currentPage = 1;
        this.renderTable();
    }

    /**
     * Basculer l'affichage des filtres
     */
    toggleFilters() {
        const filtersContent = document.getElementById('filtersContent');
        const isVisible = filtersContent.style.display !== 'none';
        filtersContent.style.display = isVisible ? 'none' : 'block';
    }

    /**
     * Rendre la table
     */
    renderTable() {
        const tbody = document.getElementById('donationsTableBody');
        if (!tbody) return;

        // Calculer la pagination
        const totalItems = this.filteredData.length;
        const totalPages = Math.ceil(totalItems / this.pageSize);
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, totalItems);
        const pageData = this.filteredData.slice(startIndex, endIndex);

        // Rendre les lignes
        tbody.innerHTML = pageData.map(don => this.renderTableRow(don)).join('');

        // Mettre à jour les informations
        this.updateTableInfo(totalItems, startIndex, endIndex);
        this.renderPagination(totalPages);
    }

    /**
     * Rendre une ligne de la table
     */
    renderTableRow(don) {
        const actions = this.generateActions(don);
        
        return `
            <tr data-id="${don.id}">
                <td><strong>${don.numero_don || don.code_don}</strong></td>
                <td>
                    <span class="table-badge badge-${don.type_don}">
                        ${don.type_don === 'article' ? '📦 Article' : '🔧 Équipement'}
                    </span>
                </td>
                <td>${don.donneur_nom || 'Non spécifié'}</td>
                <td>${don.demandeur_service || 'Non spécifié'}</td>
                <td>${don.code_don}</td>
                <td class="designation-cell" title="${don.designation}">
                    ${don.designation}
                </td>
                <td>
                    <span class="table-status status-${don.statut}">
                        ${this.getStatusLabel(don.statut)}
                    </span>
                </td>
                <td>
                    <span class="table-step ${this.getStepClass(don)}">
                        ${don.etape_actuelle}
                    </span>
                </td>
                <td>${don.date_creation_formatted}</td>
                <td class="actions-column">
                    <div class="table-actions">
                        ${actions}
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Obtenir le libellé du statut
     */
    getStatusLabel(statut) {
        const labels = {
            'brouillon': 'Brouillon',
            'bp_cree': 'BP Créé',
            'chez_magasinier': 'Magasinier',
            'chez_transitaire': 'Transitaire',
            'chez_receptionniste': 'Réception',
            'bs_cree': 'BS Créé',
            'chez_rve': 'RVE',
            'recap_cree': 'Récap',
            'envoye_comptabilite': 'Comptabilité',
            'archive': 'Archivé'
        };
        return labels[statut] || statut;
    }

    /**
     * Obtenir la classe CSS pour l'étape
     */
    getStepClass(don) {
        // Logique pour déterminer si l'étape est en cours, terminée ou en retard
        if (don.etapes) {
            const currentStep = don.etapes.find(e => e.numero === don.etape_actuelle);
            if (currentStep) {
                if (currentStep.statut === 'en_cours') {
                    return currentStep.en_retard ? 'step-delayed' : 'step-current';
                }
            }
        }
        return '';
    }

    /**
     * Générer les actions pour une ligne
     */
    generateActions(don) {
        const userRole = this.currentUser?.user_metadata?.role || 'user';
        const actions = [];

        // Action Voir (toujours disponible)
        actions.push(`
            <button class="btn-table btn-view" onclick="donationsTableManager.viewDonation('${don.id}')" title="Voir détails">
                👁️
            </button>
        `);

        // Actions selon le rôle et le statut
        if (this.canEdit(don, userRole)) {
            actions.push(`
                <button class="btn-table btn-edit" onclick="donationsTableManager.editDonation('${don.id}')" title="Modifier">
                    ✏️
                </button>
            `);
        }

        if (this.canValidate(don, userRole)) {
            actions.push(`
                <button class="btn-table btn-validate" onclick="donationsTableManager.validateDonation('${don.id}')" title="Valider">
                    ✅
                </button>
            `);
        }

        if (this.canReject(don, userRole)) {
            actions.push(`
                <button class="btn-table btn-reject" onclick="donationsTableManager.rejectDonation('${don.id}')" title="Refuser">
                    ❌
                </button>
            `);
        }

        return actions.join('');
    }

    /**
     * Vérifier si l'utilisateur peut modifier un don
     */
    canEdit(don, userRole) {
        if (userRole === 'admin') return true;
        if (don.statut === 'brouillon' && don.demandeur_email === this.currentUser.email) return true;
        return false;
    }

    /**
     * Vérifier si l'utilisateur peut valider un don
     */
    canValidate(don, userRole) {
        const validationRoles = {
            'bp_cree': ['magasinier'],
            'chez_magasinier': ['magasinier'],
            'chez_transitaire': ['transitaire'],
            'chez_receptionniste': ['receptionniste'],
            'bs_cree': ['rve'],
            'chez_rve': ['rve']
        };
        
        return userRole === 'admin' || validationRoles[don.statut]?.includes(userRole);
    }

    /**
     * Vérifier si l'utilisateur peut refuser un don
     */
    canReject(don, userRole) {
        return userRole === 'admin' || ['dg', 'dt', 'ms'].includes(userRole);
    }

    // Actions sur les dons
    async viewDonation(donationId) {
        // Ouvrir le modal de détails ou rediriger
        if (typeof DonationsManager !== 'undefined' && DonationsManager.afficherDetailsDon) {
            DonationsManager.afficherDetailsDon(donationId);
        } else {
            window.open(`donations-management.html?id=${donationId}`, '_blank');
        }
    }

    async editDonation(donationId) {
        // Logique d'édition
        console.log('Édition du don:', donationId);
        this.showNotification('Fonction d\'édition en cours de développement', 'info');
    }

    async validateDonation(donationId) {
        // Logique de validation
        console.log('Validation du don:', donationId);
        this.showNotification('Fonction de validation en cours de développement', 'info');
    }

    async rejectDonation(donationId) {
        // Logique de refus
        console.log('Refus du don:', donationId);
        this.showNotification('Fonction de refus en cours de développement', 'info');
    }

    /**
     * Mettre à jour les informations de la table
     */
    updateTableInfo(totalItems, startIndex, endIndex) {
        document.getElementById('tableResultsCount').textContent = `${totalItems} résultats`;
        document.getElementById('paginationInfo').textContent = 
            `Affichage de ${startIndex + 1} à ${endIndex} sur ${totalItems} résultats`;
    }

    /**
     * Rendre la pagination
     */
    renderPagination(totalPages) {
        const container = document.getElementById('paginationControls');
        if (!container) return;

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        const buttons = [];

        // Bouton précédent
        buttons.push(`
            <button class="pagination-btn" ${this.currentPage === 1 ? 'disabled' : ''} 
                    onclick="donationsTableManager.goToPage(${this.currentPage - 1})">
                ‹ Précédent
            </button>
        `);

        // Numéros de page
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(totalPages, this.currentPage + 2);

        if (startPage > 1) {
            buttons.push(`<button class="pagination-btn" onclick="donationsTableManager.goToPage(1)">1</button>`);
            if (startPage > 2) buttons.push(`<span>...</span>`);
        }

        for (let i = startPage; i <= endPage; i++) {
            buttons.push(`
                <button class="pagination-btn ${i === this.currentPage ? 'active' : ''}" 
                        onclick="donationsTableManager.goToPage(${i})">
                    ${i}
                </button>
            `);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) buttons.push(`<span>...</span>`);
            buttons.push(`<button class="pagination-btn" onclick="donationsTableManager.goToPage(${totalPages})">${totalPages}</button>`);
        }

        // Bouton suivant
        buttons.push(`
            <button class="pagination-btn" ${this.currentPage === totalPages ? 'disabled' : ''} 
                    onclick="donationsTableManager.goToPage(${this.currentPage + 1})">
                Suivant ›
            </button>
        `);

        container.innerHTML = buttons.join('');
    }

    /**
     * Aller à une page spécifique
     */
    goToPage(page) {
        const totalPages = Math.ceil(this.filteredData.length / this.pageSize);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.renderTable();
        }
    }

    /**
     * Exporter les données
     */
    exportData() {
        try {
            const csvData = this.generateCSV();
            this.downloadCSV(csvData, `dons_export_${new Date().toISOString().split('T')[0]}.csv`);
            this.showNotification('Export réussi', 'success');
        } catch (error) {
            console.error('Erreur export:', error);
            this.showNotification('Erreur lors de l\'export', 'error');
        }
    }

    /**
     * Générer les données CSV
     */
    generateCSV() {
        const headers = [
            'N° Don', 'Type', 'Donateur', 'Bénéficiaire', 'Code', 
            'Désignation', 'Statut', 'Étape', 'Date Création'
        ];

        const rows = this.filteredData.map(don => [
            don.numero_don || don.code_don,
            don.type_don,
            don.donneur_nom,
            don.demandeur_service,
            don.code_don,
            don.designation,
            this.getStatusLabel(don.statut),
            don.etape_actuelle,
            don.date_creation_formatted
        ]);

        return [headers, ...rows].map(row => 
            row.map(cell => `"${(cell || '').toString().replace(/"/g, '""')}"`).join(',')
        ).join('\n');
    }

    /**
     * Télécharger le fichier CSV
     */
    downloadCSV(csvData, filename) {
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }

    /**
     * Configurer les mises à jour en temps réel
     */
    setupRealtimeSubscription() {
        try {
            this.realtimeSubscription = this.supabase
                .channel('donations_table')
                .on('postgres_changes', 
                    { event: '*', schema: 'public', table: 'gestion_donations' },
                    (payload) => {
                        console.log('🔄 Mise à jour temps réel table:', payload);
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .subscribe();

            console.log('✅ Abonnement temps réel configuré pour la table');

        } catch (error) {
            console.error('❌ Erreur configuration temps réel table:', error);
        }
    }

    /**
     * Afficher une notification
     */
    showNotification(message, type = 'info') {
        if (typeof showNotification === 'function') {
            showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Nettoyer les ressources
     */
    destroy() {
        if (this.realtimeSubscription) {
            this.supabase.removeChannel(this.realtimeSubscription);
        }
        
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
    }
}

// Instance globale
window.donationsTableManager = new DonationsTableManager();
window.DonationsTableManager = DonationsTableManager;
