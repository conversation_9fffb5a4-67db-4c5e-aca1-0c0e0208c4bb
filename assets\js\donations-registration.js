/**
 * Module de gestion de l'enregistrement des dons IPT
 * Système complet avec upload de documents et validation
 */

class DonationsRegistration {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.uploadedFiles = [];
        this.isDraft = false;
        this.formData = {};
    }

    async initialize() {
        console.log('🎁 Initialisation du module d\'enregistrement des dons...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            if (!this.currentUser) {
                throw new Error('Utilisateur non authentifié');
            }

            // Pré-remplir les informations utilisateur
            this.prefillUserInfo();

            // Configurer les événements
            this.setupEventListeners();

            // Configurer l'upload de fichiers
            this.setupFileUpload();

            console.log('✅ Module d\'enregistrement des dons initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation enregistrement dons:', error);
            throw error;
        }
    }

    prefillUserInfo() {
        // Pré-remplir avec les informations de l'utilisateur connecté
        const userMetadata = this.currentUser.user_metadata || {};
        
        document.getElementById('demandeur_email').value = this.currentUser.email || '';
        document.getElementById('demandeur_nom').value = userMetadata.nom || '';
        document.getElementById('demandeur_prenom').value = userMetadata.prenom || '';
        document.getElementById('demandeur_service').value = userMetadata.service || '';
        document.getElementById('demandeur_laboratoire').value = userMetadata.laboratoire || '';
        document.getElementById('demandeur_telephone').value = userMetadata.telephone || '';
    }

    setupEventListeners() {
        // Formulaire principal
        const form = document.getElementById('donationForm');
        form.addEventListener('submit', (e) => this.handleSubmit(e));

        // Validation en temps réel
        form.addEventListener('input', () => this.validateForm());
        form.addEventListener('change', () => this.validateForm());

        // Sauvegarde automatique en brouillon (toutes les 30 secondes)
        setInterval(() => {
            if (this.hasFormData()) {
                this.autoSaveDraft();
            }
        }, 30000);

        // Prévenir la perte de données
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = 'Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir quitter ?';
            }
        });
    }

    setupFileUpload() {
        const uploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');

        // Clic sur la zone d'upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Sélection de fichiers
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(e.target.files);
        });

        // Drag & Drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileSelection(e.dataTransfer.files);
        });
    }

    handleFileSelection(files) {
        Array.from(files).forEach(file => {
            if (this.validateFile(file)) {
                this.addFileToList(file);
            }
        });
    }

    validateFile(file) {
        // Vérifier la taille (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            this.showAlert(`Le fichier "${file.name}" est trop volumineux (max 10MB)`, 'error');
            return false;
        }

        // Vérifier le type
        const allowedTypes = [
            'application/pdf',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];

        if (!allowedTypes.includes(file.type)) {
            this.showAlert(`Type de fichier non autorisé pour "${file.name}"`, 'error');
            return false;
        }

        // Vérifier si le fichier n'est pas déjà ajouté
        if (this.uploadedFiles.some(f => f.name === file.name && f.size === file.size)) {
            this.showAlert(`Le fichier "${file.name}" est déjà ajouté`, 'error');
            return false;
        }

        return true;
    }

    addFileToList(file) {
        // Ajouter à la liste des fichiers
        this.uploadedFiles.push(file);

        // Mettre à jour l'affichage
        this.updateFileList();
    }

    updateFileList() {
        const fileList = document.getElementById('fileList');
        
        if (this.uploadedFiles.length === 0) {
            fileList.innerHTML = '';
            return;
        }

        fileList.innerHTML = this.uploadedFiles.map((file, index) => `
            <div class="file-item">
                <div class="file-info">
                    <div class="file-icon">${this.getFileIcon(file.type)}</div>
                    <div class="file-details">
                        <div class="file-name">${file.name}</div>
                        <div class="file-size">${this.formatFileSize(file.size)}</div>
                    </div>
                </div>
                <button type="button" class="file-remove" onclick="donationsRegistration.removeFile(${index})">
                    Supprimer
                </button>
            </div>
        `).join('');
    }

    removeFile(index) {
        this.uploadedFiles.splice(index, 1);
        this.updateFileList();
    }

    getFileIcon(mimeType) {
        if (mimeType.includes('pdf')) return '📄';
        if (mimeType.includes('image')) return '🖼️';
        if (mimeType.includes('word')) return '📝';
        return '📁';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    validateForm() {
        const form = document.getElementById('donationForm');
        const formData = new FormData(form);
        let isValid = true;
        const errors = [];

        // Validation du type de don
        if (!formData.get('type_don')) {
            errors.push('Le type de don est requis');
            isValid = false;
        }

        // Validation des champs obligatoires
        const requiredFields = [
            'demandeur_nom', 'demandeur_prenom', 'demandeur_email', 'demandeur_service',
            'motif_don', 'lieu_affectation', 'description_don', 'donateur_nom'
        ];

        requiredFields.forEach(field => {
            if (!formData.get(field) || formData.get(field).trim() === '') {
                errors.push(`Le champ ${this.getFieldLabel(field)} est requis`);
                isValid = false;
            }
        });

        // Validation de l'email
        const email = formData.get('demandeur_email');
        if (email && !this.isValidEmail(email)) {
            errors.push('L\'adresse email n\'est pas valide');
            isValid = false;
        }

        // Validation des équipements (spécifications techniques recommandées)
        if (formData.get('type_don') === 'equipement') {
            const specs = formData.get('specifications_techniques');
            if (!specs || specs.trim() === '') {
                // Avertissement mais pas d'erreur bloquante
                console.warn('Spécifications techniques recommandées pour les équipements');
            }
        }

        return { isValid, errors };
    }

    getFieldLabel(fieldName) {
        const labels = {
            'demandeur_nom': 'Nom',
            'demandeur_prenom': 'Prénom',
            'demandeur_email': 'Email',
            'demandeur_service': 'Service',
            'motif_don': 'Motif du don',
            'lieu_affectation': 'Lieu d\'affectation',
            'description_don': 'Description',
            'donateur_nom': 'Nom du donateur'
        };
        return labels[fieldName] || fieldName;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    async handleSubmit(e) {
        e.preventDefault();
        
        const { isValid, errors } = this.validateForm();
        
        if (!isValid) {
            this.showAlert('Veuillez corriger les erreurs suivantes :\n' + errors.join('\n'), 'error');
            return;
        }

        // Confirmer la soumission
        if (!confirm('Êtes-vous sûr de vouloir soumettre cette demande de don ? Elle sera envoyée pour validation.')) {
            return;
        }

        try {
            this.showLoading(true);
            
            // Collecter les données du formulaire
            const donationData = this.collectFormData();
            donationData.statut = 'soumis'; // Statut pour soumission
            
            // Créer le don dans la base de données
            const donationId = await this.createDonation(donationData);
            
            // Uploader les documents
            if (this.uploadedFiles.length > 0) {
                await this.uploadDocuments(donationId);
            }
            
            // Initialiser le workflow
            await this.initializeWorkflow(donationId, donationData.type_don);
            
            this.showAlert('Demande de don soumise avec succès ! Vous recevrez une notification par email.', 'success');
            
            // Rediriger vers la page de suivi après 3 secondes
            setTimeout(() => {
                window.location.href = 'donations-management.html';
            }, 3000);
            
        } catch (error) {
            console.error('Erreur soumission don:', error);
            this.showAlert('Erreur lors de la soumission. Veuillez réessayer.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    collectFormData() {
        const form = document.getElementById('donationForm');
        const formData = new FormData(form);
        
        const data = {
            type_don: formData.get('type_don'),
            demandeur_nom: formData.get('demandeur_nom'),
            demandeur_prenom: formData.get('demandeur_prenom'),
            demandeur_email: formData.get('demandeur_email'),
            demandeur_service: formData.get('demandeur_service'),
            demandeur_laboratoire: formData.get('demandeur_laboratoire'),
            demandeur_telephone: formData.get('demandeur_telephone'),
            motif_don: formData.get('motif_don'),
            lieu_affectation: formData.get('lieu_affectation'),
            description_don: formData.get('description_don'),
            specifications_techniques: formData.get('specifications_techniques'),
            donateur_nom: formData.get('donateur_nom'),
            donateur_contact: formData.get('donateur_contact'),
            donateur_adresse: formData.get('donateur_adresse'),
            quantite: parseInt(formData.get('quantite')) || 1,
            unite: formData.get('unite'),
            valeur_estimee: parseFloat(formData.get('valeur_estimee')) || null,
            devise: formData.get('devise'),
            priorite: formData.get('priorite'),
            date_livraison_prevue: formData.get('date_livraison_prevue') || null,
            created_by: this.currentUser.email
        };

        return data;
    }

    async createDonation(data) {
        // Générer le numéro de don
        const { data: numeroResult, error: numeroError } = await this.supabase
            .rpc('generate_donation_number');

        if (numeroError) throw numeroError;

        data.numero_don = numeroResult;

        // Insérer le don
        const { data: donation, error } = await this.supabase
            .from('gestion_donations_ipt')
            .insert([data])
            .select()
            .single();

        if (error) throw error;

        return donation.id;
    }

    async uploadDocuments(donationId) {
        for (const file of this.uploadedFiles) {
            try {
                // Upload vers Supabase Storage
                const fileName = `${donationId}/${Date.now()}_${file.name}`;
                const { data: uploadData, error: uploadError } = await this.supabase.storage
                    .from('donation-documents')
                    .upload(fileName, file);

                if (uploadError) throw uploadError;

                // Enregistrer les métadonnées du document
                const { error: docError } = await this.supabase
                    .from('gestion_donation_documents')
                    .insert([{
                        donation_id: donationId,
                        type_document: this.guessDocumentType(file.name),
                        nom_fichier: fileName,
                        nom_original: file.name,
                        taille_fichier: file.size,
                        type_mime: file.type,
                        url_stockage: uploadData.path,
                        uploaded_by: this.currentUser.email
                    }]);

                if (docError) throw docError;

            } catch (error) {
                console.error(`Erreur upload fichier ${file.name}:`, error);
                // Continuer avec les autres fichiers
            }
        }
    }

    guessDocumentType(fileName) {
        const name = fileName.toLowerCase();
        if (name.includes('lettre') || name.includes('don')) return 'lettre_don';
        if (name.includes('facture')) return 'facture';
        if (name.includes('livraison') || name.includes('bl')) return 'bon_livraison';
        if (name.includes('prelevement') || name.includes('bp')) return 'bon_prelevement';
        return 'autre';
    }

    async initializeWorkflow(donationId, typeDon) {
        const { error } = await this.supabase
            .rpc('initialize_donation_workflow', {
                p_donation_id: donationId,
                p_type_don: typeDon,
                p_created_by: this.currentUser.email
            });

        if (error) throw error;
    }

    async saveDraft() {
        try {
            this.showLoading(true);
            
            const donationData = this.collectFormData();
            donationData.statut = 'brouillon';
            
            if (this.isDraft && this.draftId) {
                // Mettre à jour le brouillon existant
                const { error } = await this.supabase
                    .from('gestion_donations_ipt')
                    .update(donationData)
                    .eq('id', this.draftId);

                if (error) throw error;
            } else {
                // Créer un nouveau brouillon
                const draftId = await this.createDonation(donationData);
                this.isDraft = true;
                this.draftId = draftId;
            }
            
            this.showAlert('Brouillon sauvegardé avec succès', 'success');
            
        } catch (error) {
            console.error('Erreur sauvegarde brouillon:', error);
            this.showAlert('Erreur lors de la sauvegarde du brouillon', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async autoSaveDraft() {
        if (!this.hasFormData()) return;
        
        try {
            await this.saveDraft();
            console.log('Brouillon sauvegardé automatiquement');
        } catch (error) {
            console.error('Erreur sauvegarde automatique:', error);
        }
    }

    hasFormData() {
        const form = document.getElementById('donationForm');
        const formData = new FormData(form);
        
        // Vérifier si au moins un champ important est rempli
        const importantFields = ['motif_don', 'description_don', 'donateur_nom'];
        return importantFields.some(field => {
            const value = formData.get(field);
            return value && value.trim() !== '';
        });
    }

    hasUnsavedChanges() {
        // Logique pour détecter les changements non sauvegardés
        return this.hasFormData() && !this.isDraft;
    }

    showLoading(show) {
        const submitBtn = document.querySelector('button[type="submit"]');
        if (show) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="loading"><div class="spinner"></div>Soumission en cours...</div>';
        } else {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '📤 Soumettre la demande';
        }
    }

    showAlert(message, type = 'info') {
        if (typeof showAlert === 'function') {
            showAlert(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Instance globale
window.donationsRegistration = new DonationsRegistration();
window.DonationsRegistration = DonationsRegistration;
