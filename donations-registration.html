<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enregistrement des Dons - Institut Pasteur <PERSON></title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            color: #2d3748;
            font-size: 28px;
            font-weight: 700;
        }

        .header p {
            margin: 10px 0 0 0;
            color: #718096;
            font-size: 16px;
        }

        .form-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 25px 30px;
        }

        .form-header h2 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
        }

        .form-content {
            padding: 30px;
        }

        .form-section {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #e2e8f0;
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            font-size: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-label.required::after {
            content: ' *';
            color: #e53e3e;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
            background: white;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-radio-group {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }

        .form-radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            padding: 10px 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            transition: all 0.2s;
        }

        .form-radio-item:hover {
            border-color: #4299e1;
            background: #f7fafc;
        }

        .form-radio-item.selected {
            border-color: #4299e1;
            background: #ebf8ff;
        }

        .form-radio-item input[type="radio"] {
            margin: 0;
        }

        .form-radio-item label {
            margin: 0;
            cursor: pointer;
            font-weight: 500;
        }

        .file-upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            transition: all 0.2s;
            cursor: pointer;
        }

        .file-upload-area:hover {
            border-color: #4299e1;
            background: #f7fafc;
        }

        .file-upload-area.dragover {
            border-color: #4299e1;
            background: #ebf8ff;
        }

        .file-upload-icon {
            font-size: 48px;
            color: #a0aec0;
            margin-bottom: 15px;
        }

        .file-upload-text {
            color: #4a5568;
            font-size: 16px;
            margin-bottom: 10px;
        }

        .file-upload-hint {
            color: #718096;
            font-size: 14px;
        }

        .file-list {
            margin-top: 20px;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: #f7fafc;
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-icon {
            font-size: 20px;
            color: #4299e1;
        }

        .file-details {
            display: flex;
            flex-direction: column;
        }

        .file-name {
            font-weight: 500;
            color: #2d3748;
        }

        .file-size {
            font-size: 12px;
            color: #718096;
        }

        .file-remove {
            background: #fed7d7;
            color: #c53030;
            border: none;
            border-radius: 6px;
            padding: 6px 12px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .file-remove:hover {
            background: #feb2b2;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 25px;
            border-top: 1px solid #e2e8f0;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }

        .btn-secondary {
            background: #edf2f7;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .alert-info {
            background: #ebf8ff;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .loading {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .spinner {
            width: 16px;
            height: 16px;
            border: 2px solid #e2e8f0;
            border-top: 2px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-radio-group {
                flex-direction: column;
                gap: 10px;
            }

            .form-actions {
                flex-direction: column;
            }
        }

        /* Styles spécifiques pour les équipements */
        .equipment-fields {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }

        .equipment-fields.show {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .progress-indicator {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 0 20px;
        }

        .progress-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            flex: 1;
            position: relative;
        }

        .progress-step::after {
            content: '';
            position: absolute;
            top: 15px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: #e2e8f0;
            z-index: -1;
        }

        .progress-step:last-child::after {
            display: none;
        }

        .progress-step.active::after {
            background: #4299e1;
        }

        .progress-circle {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            color: #718096;
            margin-bottom: 8px;
        }

        .progress-step.active .progress-circle {
            background: #4299e1;
            color: white;
        }

        .progress-step.completed .progress-circle {
            background: #48bb78;
            color: white;
        }

        .progress-label {
            font-size: 12px;
            color: #718096;
            text-align: center;
        }

        .progress-step.active .progress-label {
            color: #4299e1;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎁 Enregistrement des Dons</h1>
            <p>Institut Pasteur de Tunis - Système de Gestion des Dons</p>
        </div>

        <!-- Indicateur de progression -->
        <div class="progress-indicator">
            <div class="progress-step active">
                <div class="progress-circle">1</div>
                <div class="progress-label">Informations<br>Générales</div>
            </div>
            <div class="progress-step">
                <div class="progress-circle">2</div>
                <div class="progress-label">Documents<br>Justificatifs</div>
            </div>
            <div class="progress-step">
                <div class="progress-circle">3</div>
                <div class="progress-label">Validation<br>& Soumission</div>
            </div>
        </div>

        <!-- Formulaire d'enregistrement -->
        <div class="form-container">
            <div class="form-header">
                <h2>📝 Nouveau Dossier de Don</h2>
            </div>

            <div class="form-content">
                <!-- Messages d'alerte -->
                <div id="alertContainer"></div>

                <form id="donationForm">
                    <!-- Section 1: Type de don -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">🎯</span>
                            Type de Don
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label required">Sélectionnez le type de don</label>
                            <div class="form-radio-group">
                                <div class="form-radio-item" onclick="selectDonationType('article')">
                                    <input type="radio" id="type_article" name="type_don" value="article">
                                    <label for="type_article">📦 Articles / Consommables</label>
                                </div>
                                <div class="form-radio-item" onclick="selectDonationType('equipement')">
                                    <input type="radio" id="type_equipement" name="type_don" value="equipement">
                                    <label for="type_equipement">🔬 Équipements / Matériel</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section 2: Informations du demandeur -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">👤</span>
                            Informations du Demandeur
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label required" for="demandeur_nom">Nom</label>
                                <input type="text" id="demandeur_nom" name="demandeur_nom" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label required" for="demandeur_prenom">Prénom</label>
                                <input type="text" id="demandeur_prenom" name="demandeur_prenom" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label required" for="demandeur_email">Email</label>
                                <input type="email" id="demandeur_email" name="demandeur_email" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="demandeur_telephone">Téléphone</label>
                                <input type="tel" id="demandeur_telephone" name="demandeur_telephone" class="form-input">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label required" for="demandeur_service">Service</label>
                                <select id="demandeur_service" name="demandeur_service" class="form-select" required>
                                    <option value="">Sélectionnez un service</option>
                                    <option value="Laboratoire Microbiologie">Laboratoire Microbiologie</option>
                                    <option value="Laboratoire Virologie">Laboratoire Virologie</option>
                                    <option value="Laboratoire Immunologie">Laboratoire Immunologie</option>
                                    <option value="Laboratoire Parasitologie">Laboratoire Parasitologie</option>
                                    <option value="Laboratoire Biochimie">Laboratoire Biochimie</option>
                                    <option value="Direction Technique">Direction Technique</option>
                                    <option value="Administration">Administration</option>
                                    <option value="Autre">Autre</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="demandeur_laboratoire">Laboratoire / Unité</label>
                                <input type="text" id="demandeur_laboratoire" name="demandeur_laboratoire" class="form-input">
                            </div>
                        </div>
                    </div>

                    <!-- Section 3: Informations du don -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">🎁</span>
                            Informations du Don
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group full-width">
                                <label class="form-label required" for="motif_don">Motif du don</label>
                                <textarea id="motif_don" name="motif_don" class="form-textarea" 
                                         placeholder="Expliquez la raison et la justification de cette demande de don..." required></textarea>
                            </div>
                            
                            <div class="form-group full-width">
                                <label class="form-label required" for="lieu_affectation">Lieu d'affectation</label>
                                <input type="text" id="lieu_affectation" name="lieu_affectation" class="form-input" 
                                       placeholder="Ex: Laboratoire Microbiologie - Salle 101" required>
                            </div>
                            
                            <div class="form-group full-width">
                                <label class="form-label required" for="description_don">Description détaillée</label>
                                <textarea id="description_don" name="description_don" class="form-textarea" 
                                         placeholder="Décrivez précisément les articles ou équipements demandés..." required></textarea>
                            </div>
                            
                            <!-- Champs spécifiques aux équipements -->
                            <div class="form-group full-width equipment-fields" id="equipmentFields">
                                <label class="form-label" for="specifications_techniques">Spécifications techniques</label>
                                <textarea id="specifications_techniques" name="specifications_techniques" class="form-textarea" 
                                         placeholder="Spécifications techniques détaillées, modèle, caractéristiques..."></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="quantite">Quantité</label>
                                <input type="number" id="quantite" name="quantite" class="form-input" min="1" value="1">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="unite">Unité</label>
                                <select id="unite" name="unite" class="form-select">
                                    <option value="unité">Unité</option>
                                    <option value="lot">Lot</option>
                                    <option value="kit">Kit</option>
                                    <option value="boîte">Boîte</option>
                                    <option value="flacon">Flacon</option>
                                    <option value="autre">Autre</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="valeur_estimee">Valeur estimée</label>
                                <input type="number" id="valeur_estimee" name="valeur_estimee" class="form-input" 
                                       step="0.01" placeholder="0.00">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="devise">Devise</label>
                                <select id="devise" name="devise" class="form-select">
                                    <option value="TND">TND (Dinar Tunisien)</option>
                                    <option value="EUR">EUR (Euro)</option>
                                    <option value="USD">USD (Dollar US)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="priorite">Priorité</label>
                                <select id="priorite" name="priorite" class="form-select">
                                    <option value="normale">Normale</option>
                                    <option value="elevee">Élevée</option>
                                    <option value="urgente">Urgente</option>
                                    <option value="faible">Faible</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="date_livraison_prevue">Date de livraison souhaitée</label>
                                <input type="date" id="date_livraison_prevue" name="date_livraison_prevue" class="form-input">
                            </div>
                        </div>
                    </div>

                    <!-- Section 4: Informations du donateur -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">🏢</span>
                            Informations du Donateur / Fournisseur
                        </div>
                        
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label required" for="donateur_nom">Nom du donateur / fournisseur</label>
                                <input type="text" id="donateur_nom" name="donateur_nom" class="form-input" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label" for="donateur_contact">Contact</label>
                                <input type="text" id="donateur_contact" name="donateur_contact" class="form-input" 
                                       placeholder="Email ou téléphone">
                            </div>
                            
                            <div class="form-group full-width">
                                <label class="form-label" for="donateur_adresse">Adresse</label>
                                <textarea id="donateur_adresse" name="donateur_adresse" class="form-textarea" 
                                         placeholder="Adresse complète du donateur"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Section 5: Documents justificatifs -->
                    <div class="form-section">
                        <div class="section-title">
                            <span class="section-icon">📄</span>
                            Documents Justificatifs
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Téléversez les pièces justificatives</label>
                            <div class="file-upload-area" id="fileUploadArea">
                                <div class="file-upload-icon">📁</div>
                                <div class="file-upload-text">Cliquez pour sélectionner ou glissez-déposez vos fichiers</div>
                                <div class="file-upload-hint">
                                    Formats acceptés: PDF, JPG, PNG, DOC, DOCX (Max: 10MB par fichier)
                                </div>
                                <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="display: none;">
                            </div>
                            
                            <div class="file-list" id="fileList"></div>
                        </div>
                        
                        <div class="alert alert-info">
                            <span>ℹ️</span>
                            <div>
                                <strong>Documents requis :</strong><br>
                                • Lettre de don du fournisseur<br>
                                • Facture (si disponible)<br>
                                • Bon de livraison (si disponible)<br>
                                • Bon de prélèvement (si applicable)
                            </div>
                        </div>
                    </div>

                    <!-- Actions du formulaire -->
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="saveDraft()">
                            💾 Sauvegarder en brouillon
                        </button>
                        <button type="submit" class="btn btn-primary">
                            📤 Soumettre la demande
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/donations-registration.js"></script>
    
    <script>
        // Initialisation de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🎁 Initialisation de l\'enregistrement des dons...');
            
            try {
                // Initialiser Supabase
                if (typeof initializeSupabase === 'function') {
                    const { client, available } = await initializeSupabase();
                    
                    if (available) {
                        console.log('✅ Supabase connecté pour l\'enregistrement des dons');
                        
                        // Initialiser le gestionnaire de formulaire
                        if (typeof DonationsRegistration !== 'undefined') {
                            await donationsRegistration.initialize();
                            console.log('✅ Gestionnaire d\'enregistrement des dons initialisé');
                        }
                    } else {
                        console.warn('⚠️ Supabase non disponible - mode dégradé');
                        showAlert('Connexion à la base de données indisponible. Certaines fonctionnalités peuvent être limitées.', 'error');
                    }
                } else {
                    console.error('❌ Fonction initializeSupabase non trouvée');
                }
                
            } catch (error) {
                console.error('❌ Erreur initialisation enregistrement dons:', error);
                showAlert('Erreur lors de l\'initialisation. Veuillez rafraîchir la page.', 'error');
            }
        });

        // Fonction pour afficher les alertes
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            alert.innerHTML = `<span>${icon}</span><div>${message}</div>`;
            
            container.appendChild(alert);
            
            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        // Fonction pour sélectionner le type de don
        function selectDonationType(type) {
            // Mettre à jour les boutons radio
            document.querySelectorAll('.form-radio-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            const selectedItem = document.querySelector(`input[value="${type}"]`).closest('.form-radio-item');
            selectedItem.classList.add('selected');
            
            // Cocher le radio button
            document.getElementById(`type_${type}`).checked = true;
            
            // Afficher/masquer les champs spécifiques aux équipements
            const equipmentFields = document.getElementById('equipmentFields');
            if (type === 'equipement') {
                equipmentFields.classList.add('show');
            } else {
                equipmentFields.classList.remove('show');
            }
        }

        // Fonction pour sauvegarder en brouillon
        function saveDraft() {
            if (window.donationsRegistration) {
                donationsRegistration.saveDraft();
            } else {
                showAlert('Gestionnaire non initialisé', 'error');
            }
        }
    </script>
</body>
</html>
