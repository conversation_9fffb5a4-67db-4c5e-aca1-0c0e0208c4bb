// Système de synchronisation bidirectionnelle en temps réel
// Gère la synchronisation automatique entre localStorage et Supabase

class SupabaseSync {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.isActive = false;
        this.syncQueue = [];
        this.conflictQueue = [];
        this.lastSyncTimestamp = {};
        
        // Statistiques de synchronisation
        this.syncStats = {
            totalSynced: 0,
            conflicts: 0,
            errors: 0,
            lastSync: null,
            syncDuration: 0
        };
        
        // Listeners en temps réel
        this.realtimeChannels = [];
        
        // Callbacks
        this.callbacks = {
            onSync: [],
            onConflict: [],
            onError: [],
            onRealtimeUpdate: []
        };
    }

    // Démarrer la synchronisation
    async startSync() {
        if (this.isActive) {
            console.log('⚠️ Synchronisation déjà active');
            return;
        }

        this.isActive = true;
        console.log('🔄 Démarrage de la synchronisation Supabase...');
        
        try {
            // Synchronisation initiale
            await this.performFullSync();
            
            // Démarrer la synchronisation périodique
            this.startPeriodicSync();
            
            // Configurer les listeners en temps réel
            if (this.config.features?.realTimeSync) {
                await this.setupRealtimeListeners();
            }
            
            console.log('✅ Synchronisation Supabase active');
            
        } catch (error) {
            console.error('❌ Erreur démarrage synchronisation:', error);
            this.isActive = false;
            throw error;
        }
    }

    // Arrêter la synchronisation
    stopSync() {
        this.isActive = false;
        
        // Arrêter la synchronisation périodique
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        
        // Fermer les canaux en temps réel
        this.realtimeChannels.forEach(channel => {
            channel.unsubscribe();
        });
        this.realtimeChannels = [];
        
        console.log('🛑 Synchronisation Supabase arrêtée');
    }

    // Synchronisation complète
    async performFullSync() {
        const startTime = Date.now();
        console.log('🔄 Synchronisation complète en cours...');
        
        try {
            const tables = Object.keys(this.config.tables);
            const results = {};
            
            for (const table of tables) {
                try {
                    const result = await this.syncTable(table);
                    results[table] = result;
                    console.log(`✅ ${table}: ${result.synced} éléments synchronisés`);
                } catch (error) {
                    console.error(`❌ Erreur sync ${table}:`, error);
                    results[table] = { error: error.message, synced: 0 };
                    this.syncStats.errors++;
                }
            }
            
            this.syncStats.lastSync = new Date().toISOString();
            this.syncStats.syncDuration = Date.now() - startTime;
            
            console.log(`✅ Synchronisation terminée en ${this.syncStats.syncDuration}ms`);
            this.triggerCallbacks('onSync', results);
            
            return { success: true, results };
            
        } catch (error) {
            console.error('❌ Erreur synchronisation complète:', error);
            this.syncStats.errors++;
            this.triggerCallbacks('onError', error);
            throw error;
        }
    }

    // Synchroniser une table spécifique
    async syncTable(table) {
        const tableName = this.config.tables[table];
        if (!tableName) {
            throw new Error(`Table ${table} non configurée`);
        }

        // 1. Récupérer les données locales
        const localData = this.getLocalData(table);
        
        // 2. Récupérer les données Supabase
        const { data: remoteData, error } = await this.client
            .from(tableName)
            .select('*')
            .order('updated_at', { ascending: false });
        
        if (error) {
            throw error;
        }

        // 3. Détecter les conflits et synchroniser
        const syncResult = await this.resolveAndSync(table, localData, remoteData || []);
        
        return syncResult;
    }

    // Résoudre les conflits et synchroniser
    async resolveAndSync(table, localData, remoteData) {
        const tableName = this.config.tables[table];
        let syncedCount = 0;
        let conflictCount = 0;
        
        // Créer des maps pour faciliter la comparaison
        const localMap = new Map(localData.map(item => [item.id, item]));
        const remoteMap = new Map(remoteData.map(item => [item.id, item]));
        
        // 1. Éléments à envoyer vers Supabase (nouveaux ou modifiés localement)
        const toUpload = [];
        for (const [id, localItem] of localMap) {
            const remoteItem = remoteMap.get(id);
            
            if (!remoteItem) {
                // Nouvel élément local
                toUpload.push(localItem);
            } else {
                // Vérifier si modifié
                const localTime = new Date(localItem.updated_at || localItem.timestamp);
                const remoteTime = new Date(remoteItem.updated_at);
                
                if (localTime > remoteTime) {
                    toUpload.push(localItem);
                } else if (localTime < remoteTime) {
                    // Conflit potentiel - l'élément distant est plus récent
                    const conflict = {
                        id,
                        table,
                        local: localItem,
                        remote: remoteItem,
                        type: 'update_conflict'
                    };
                    
                    this.conflictQueue.push(conflict);
                    conflictCount++;
                    this.triggerCallbacks('onConflict', conflict);
                }
            }
        }
        
        // 2. Éléments à télécharger depuis Supabase (nouveaux distants)
        const toDownload = [];
        for (const [id, remoteItem] of remoteMap) {
            if (!localMap.has(id)) {
                toDownload.push(remoteItem);
            }
        }
        
        // 3. Uploader les modifications locales
        if (toUpload.length > 0) {
            try {
                const { error: uploadError } = await this.client
                    .from(tableName)
                    .upsert(toUpload, { onConflict: 'id' });
                
                if (uploadError) {
                    console.warn(`⚠️ Erreur upload ${table}:`, uploadError);
                } else {
                    syncedCount += toUpload.length;
                    console.log(`📤 ${toUpload.length} éléments uploadés pour ${table}`);
                }
            } catch (error) {
                console.error(`❌ Erreur upload ${table}:`, error);
            }
        }
        
        // 4. Télécharger les nouveaux éléments distants
        if (toDownload.length > 0) {
            const updatedLocal = [...localData, ...toDownload];
            this.saveLocalData(table, updatedLocal);
            syncedCount += toDownload.length;
            console.log(`📥 ${toDownload.length} éléments téléchargés pour ${table}`);
        }
        
        this.syncStats.totalSynced += syncedCount;
        this.syncStats.conflicts += conflictCount;
        
        return {
            synced: syncedCount,
            conflicts: conflictCount,
            uploaded: toUpload.length,
            downloaded: toDownload.length
        };
    }

    // Démarrer la synchronisation périodique
    startPeriodicSync() {
        const interval = this.config.sync?.interval || 30000;
        
        this.syncInterval = setInterval(async () => {
            if (this.isActive && this.syncQueue.length === 0) {
                try {
                    await this.performFullSync();
                } catch (error) {
                    console.warn('⚠️ Erreur sync périodique:', error);
                }
            }
        }, interval);
    }

    // Configurer les listeners en temps réel
    async setupRealtimeListeners() {
        console.log('🔄 Configuration des listeners temps réel...');
        
        const tables = Object.keys(this.config.tables);
        
        for (const table of tables) {
            const tableName = this.config.tables[table];
            
            try {
                const channel = this.client
                    .channel(`realtime_${table}`)
                    .on('postgres_changes', 
                        { 
                            event: '*', 
                            schema: 'public', 
                            table: tableName 
                        },
                        (payload) => this.handleRealtimeUpdate(table, payload)
                    )
                    .subscribe();
                
                this.realtimeChannels.push(channel);
                console.log(`✅ Listener temps réel configuré pour ${table}`);
                
            } catch (error) {
                console.error(`❌ Erreur listener ${table}:`, error);
            }
        }
    }

    // Gérer les mises à jour en temps réel
    handleRealtimeUpdate(table, payload) {
        console.log(`🔄 Mise à jour temps réel ${table}:`, payload);
        
        try {
            const { eventType, new: newRecord, old: oldRecord } = payload;
            
            // Mettre à jour les données locales
            const localData = this.getLocalData(table);
            let updatedData = [...localData];
            
            switch (eventType) {
                case 'INSERT':
                    // Vérifier si l'élément n'existe pas déjà localement
                    if (!updatedData.find(item => item.id === newRecord.id)) {
                        updatedData.push(newRecord);
                        console.log(`➕ Nouvel élément ajouté: ${table}/${newRecord.id}`);
                    }
                    break;
                    
                case 'UPDATE':
                    const updateIndex = updatedData.findIndex(item => item.id === newRecord.id);
                    if (updateIndex !== -1) {
                        updatedData[updateIndex] = newRecord;
                        console.log(`✏️ Élément mis à jour: ${table}/${newRecord.id}`);
                    } else {
                        updatedData.push(newRecord);
                    }
                    break;
                    
                case 'DELETE':
                    updatedData = updatedData.filter(item => item.id !== oldRecord.id);
                    console.log(`🗑️ Élément supprimé: ${table}/${oldRecord.id}`);
                    break;
            }
            
            // Sauvegarder les données mises à jour
            this.saveLocalData(table, updatedData);
            
            // Déclencher les callbacks
            this.triggerCallbacks('onRealtimeUpdate', {
                table,
                eventType,
                record: newRecord || oldRecord,
                localData: updatedData
            });
            
        } catch (error) {
            console.error('❌ Erreur traitement mise à jour temps réel:', error);
        }
    }

    // Ajouter un élément à la queue de synchronisation
    queueForSync(table, data, operation = 'upsert') {
        this.syncQueue.push({
            table,
            data,
            operation,
            timestamp: Date.now()
        });
        
        // Traiter la queue si pas trop chargée
        if (this.syncQueue.length < 10) {
            this.processSyncQueue();
        }
    }

    // Traiter la queue de synchronisation
    async processSyncQueue() {
        if (this.syncQueue.length === 0) return;
        
        const item = this.syncQueue.shift();
        const tableName = this.config.tables[item.table];
        
        if (!tableName) {
            console.warn(`⚠️ Table ${item.table} non configurée pour sync`);
            return;
        }
        
        try {
            let result;
            
            switch (item.operation) {
                case 'upsert':
                    result = await this.client
                        .from(tableName)
                        .upsert(item.data, { onConflict: 'id' });
                    break;
                    
                case 'delete':
                    result = await this.client
                        .from(tableName)
                        .delete()
                        .eq('id', item.data.id);
                    break;
                    
                default:
                    console.warn(`⚠️ Opération ${item.operation} non supportée`);
                    return;
            }
            
            if (result.error) {
                console.error(`❌ Erreur sync queue ${item.table}:`, result.error);
                this.syncStats.errors++;
            } else {
                console.log(`✅ Sync queue réussie: ${item.table}/${item.operation}`);
                this.syncStats.totalSynced++;
            }
            
        } catch (error) {
            console.error(`❌ Erreur traitement queue ${item.table}:`, error);
            this.syncStats.errors++;
        }
        
        // Continuer le traitement de la queue
        if (this.syncQueue.length > 0) {
            setTimeout(() => this.processSyncQueue(), 100);
        }
    }

    // Résoudre un conflit manuellement
    async resolveConflict(conflictId, resolution) {
        const conflict = this.conflictQueue.find(c => c.id === conflictId);
        if (!conflict) {
            throw new Error('Conflit non trouvé');
        }
        
        try {
            let dataToUse;
            
            switch (resolution) {
                case 'use_local':
                    dataToUse = conflict.local;
                    break;
                case 'use_remote':
                    dataToUse = conflict.remote;
                    break;
                case 'merge':
                    dataToUse = { ...conflict.remote, ...conflict.local };
                    break;
                default:
                    throw new Error('Résolution de conflit non valide');
            }
            
            // Appliquer la résolution
            const tableName = this.config.tables[conflict.table];
            const { error } = await this.client
                .from(tableName)
                .upsert(dataToUse, { onConflict: 'id' });
            
            if (error) throw error;
            
            // Mettre à jour les données locales
            const localData = this.getLocalData(conflict.table);
            const index = localData.findIndex(item => item.id === conflict.id);
            if (index !== -1) {
                localData[index] = dataToUse;
                this.saveLocalData(conflict.table, localData);
            }
            
            // Supprimer le conflit de la queue
            this.conflictQueue = this.conflictQueue.filter(c => c.id !== conflictId);
            
            console.log(`✅ Conflit résolu: ${conflict.table}/${conflict.id}`);
            
        } catch (error) {
            console.error('❌ Erreur résolution conflit:', error);
            throw error;
        }
    }

    // Récupérer les données locales
    getLocalData(table) {
        try {
            const key = `gestion_${table}`;
            return JSON.parse(localStorage.getItem(key) || '[]');
        } catch (error) {
            console.error(`❌ Erreur lecture locale ${table}:`, error);
            return [];
        }
    }

    // Sauvegarder les données locales
    saveLocalData(table, data) {
        try {
            const key = `gestion_${table}`;
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error(`❌ Erreur sauvegarde locale ${table}:`, error);
        }
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Déclencher les callbacks
    triggerCallbacks(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les statistiques
    getStats() {
        return { ...this.syncStats };
    }

    // Obtenir les conflits en attente
    getConflicts() {
        return [...this.conflictQueue];
    }

    // Forcer une synchronisation
    async forceSync() {
        console.log('🔄 Synchronisation forcée...');
        return await this.performFullSync();
    }
}

// Export de la classe
if (typeof window !== 'undefined') {
    window.SupabaseSync = SupabaseSync;
}
