<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fix DatabaseManager</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .log {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Fix DatabaseManager</h1>
        <p>Test rapide pour vérifier que le problème "DatabaseManager non disponible" est résolu.</p>
        
        <div>
            <button class="btn" onclick="testDatabaseManager()">🧪 Tester DatabaseManager</button>
            <button class="btn" onclick="testSaveMessage()">💾 Tester Sauvegarde Message</button>
            <button class="btn" onclick="testLoadData()">📥 Tester Chargement Données</button>
            <button class="btn" onclick="clearResults()">🗑️ Vider Résultats</button>
        </div>
        
        <div id="results"></div>
        <div class="log" id="log"></div>
    </div>

    <!-- Scripts dans le même ordre que l'application principale -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/app-config.js"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/database-manager-fix.js"></script>
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>
    <script src="assets/js/supabase-migration.js"></script>
    
    <script>
        // Fonction de logging
        function log(message, level = 'info') {
            const logContainer = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] [${level.toUpperCase()}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        // Fonction pour afficher les résultats
        function showResult(message, type = 'success') {
            const resultsContainer = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsContainer.appendChild(resultDiv);
        }

        // Test du DatabaseManager
        function testDatabaseManager() {
            log('🧪 Test DatabaseManager...', 'info');
            
            try {
                // Test 1: Vérifier l'existence
                if (typeof window.DatabaseManager === 'undefined') {
                    showResult('❌ window.DatabaseManager non défini', 'error');
                    log('❌ window.DatabaseManager non défini', 'error');
                } else {
                    showResult('✅ window.DatabaseManager existe', 'success');
                    log('✅ window.DatabaseManager existe', 'success');
                }

                // Test 2: Vérifier getDatabaseManager
                if (typeof window.getDatabaseManager === 'function') {
                    const dbManager = window.getDatabaseManager();
                    if (dbManager) {
                        showResult('✅ getDatabaseManager() fonctionne', 'success');
                        log('✅ getDatabaseManager() fonctionne', 'success');
                        
                        // Test 3: Vérifier les méthodes
                        const methods = ['save', 'load', 'update', 'delete', 'isAvailable'];
                        let allMethodsOk = true;
                        
                        methods.forEach(method => {
                            if (typeof dbManager[method] === 'function') {
                                log(`✅ Méthode ${method} disponible`, 'success');
                            } else {
                                log(`❌ Méthode ${method} manquante`, 'error');
                                allMethodsOk = false;
                            }
                        });
                        
                        if (allMethodsOk) {
                            showResult('✅ Toutes les méthodes DatabaseManager disponibles', 'success');
                        } else {
                            showResult('❌ Certaines méthodes DatabaseManager manquantes', 'error');
                        }
                        
                        // Test 4: Vérifier isAvailable
                        if (typeof dbManager.isAvailable === 'function') {
                            const available = dbManager.isAvailable();
                            showResult(`✅ DatabaseManager.isAvailable(): ${available}`, 'success');
                            log(`✅ DatabaseManager.isAvailable(): ${available}`, 'success');
                        }
                        
                    } else {
                        showResult('❌ getDatabaseManager() retourne null', 'error');
                        log('❌ getDatabaseManager() retourne null', 'error');
                    }
                } else {
                    showResult('❌ getDatabaseManager non disponible', 'error');
                    log('❌ getDatabaseManager non disponible', 'error');
                }

                // Test 5: Vérifier testDatabaseManager
                if (typeof window.testDatabaseManager === 'function') {
                    const testResult = window.testDatabaseManager();
                    showResult(`✅ testDatabaseManager(): ${testResult}`, testResult ? 'success' : 'error');
                    log(`✅ testDatabaseManager(): ${testResult}`, testResult ? 'success' : 'error');
                }

            } catch (error) {
                showResult(`❌ Erreur test DatabaseManager: ${error.message}`, 'error');
                log(`❌ Erreur test DatabaseManager: ${error.message}`, 'error');
            }
        }

        // Test de sauvegarde de message
        async function testSaveMessage() {
            log('💾 Test sauvegarde message...', 'info');
            
            try {
                const dbManager = window.getDatabaseManager ? window.getDatabaseManager() : window.DatabaseManager;
                
                if (!dbManager) {
                    showResult('❌ DatabaseManager non disponible pour test sauvegarde', 'error');
                    log('❌ DatabaseManager non disponible pour test sauvegarde', 'error');
                    return;
                }

                const testMessage = {
                    expediteur: 'test_user',
                    destinataire: 'admin',
                    contenu: 'Message de test - ' + new Date().toLocaleString(),
                    timestamp: new Date().toISOString()
                };

                log('💾 Tentative de sauvegarde...', 'info');
                const result = await dbManager.save('messages', testMessage);

                if (result) {
                    showResult('✅ Message sauvegardé avec succès', 'success');
                    log(`✅ Message sauvegardé: ${JSON.stringify(result)}`, 'success');
                } else {
                    showResult('❌ Échec sauvegarde message', 'error');
                    log('❌ Échec sauvegarde message', 'error');
                }

            } catch (error) {
                showResult(`❌ Erreur sauvegarde message: ${error.message}`, 'error');
                log(`❌ Erreur sauvegarde message: ${error.message}`, 'error');
            }
        }

        // Test de chargement de données
        async function testLoadData() {
            log('📥 Test chargement données...', 'info');
            
            try {
                const dbManager = window.getDatabaseManager ? window.getDatabaseManager() : window.DatabaseManager;
                
                if (!dbManager) {
                    showResult('❌ DatabaseManager non disponible pour test chargement', 'error');
                    log('❌ DatabaseManager non disponible pour test chargement', 'error');
                    return;
                }

                log('📥 Tentative de chargement messages...', 'info');
                const messages = await dbManager.load('messages');

                if (messages && Array.isArray(messages)) {
                    showResult(`✅ Messages chargés: ${messages.length} éléments`, 'success');
                    log(`✅ Messages chargés: ${messages.length} éléments`, 'success');
                    
                    if (messages.length > 0) {
                        log(`📄 Dernier message: ${JSON.stringify(messages[messages.length - 1])}`, 'info');
                    }
                } else {
                    showResult('⚠️ Aucun message trouvé ou erreur chargement', 'warning');
                    log('⚠️ Aucun message trouvé ou erreur chargement', 'warning');
                }

            } catch (error) {
                showResult(`❌ Erreur chargement données: ${error.message}`, 'error');
                log(`❌ Erreur chargement données: ${error.message}`, 'error');
            }
        }

        // Vider les résultats
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('log').innerHTML = '';
        }

        // Test automatique au chargement
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Page de test chargée', 'info');
            
            // Attendre un peu que tous les scripts se chargent
            setTimeout(() => {
                log('🔍 Lancement du test automatique...', 'info');
                testDatabaseManager();
            }, 1000);
        });
    </script>
</body>
</html>
