<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Contrôle d'Accès - Admin Commandes</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-user-card {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .test-user-card:hover {
            border-color: #3b82f6;
            background: #f8fafc;
        }
        
        .test-user-card.admin {
            border-color: #10b981;
            background: #f0fdf4;
        }
        
        .test-user-card.denied {
            border-color: #ef4444;
            background: #fef2f2;
        }
        
        .test-results {
            background: #f8fafc;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }
        
        .access-indicator {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .access-granted {
            background: #dcfce7;
            color: #166534;
        }
        
        .access-denied {
            background: #fee2e2;
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Test Contrôle d'Accès - Administration des Commandes</h1>
        
        <div class="test-section">
            <h2>👥 Utilisateurs de Test</h2>
            <p>Cliquez sur un utilisateur pour tester ses permissions :</p>
            
            <div class="test-user-card admin" onclick="testUser('<EMAIL>', 'admin')">
                <h3>🔧 Admin123 <span class="access-indicator access-granted">ACCÈS ADMIN</span></h3>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Rôle:</strong> Administrateur Système</p>
                <p><strong>Permissions attendues:</strong> Lecture, Écriture, Suppression, Export</p>
            </div>
            
            <div class="test-user-card admin" onclick="testUser('<EMAIL>', 'admin')">
                <h3>🔧 Namara <span class="access-indicator access-granted">ACCÈS ADMIN</span></h3>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Rôle:</strong> Administrateur Autorisé</p>
                <p><strong>Permissions attendues:</strong> Lecture, Écriture, Suppression, Export</p>
            </div>
            
            <div class="test-user-card admin" onclick="testUser('<EMAIL>', 'admin')">
                <h3>🔧 Rym <span class="access-indicator access-granted">ACCÈS ADMIN</span></h3>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Rôle:</strong> Administrateur Autorisé</p>
                <p><strong>Permissions attendues:</strong> Lecture, Écriture, Suppression, Export</p>
            </div>
            
            <div class="test-user-card denied" onclick="testUser('<EMAIL>', 'user')">
                <h3>👤 Utilisateur Normal <span class="access-indicator access-denied">ACCÈS REFUSÉ</span></h3>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Rôle:</strong> Utilisateur Standard</p>
                <p><strong>Permissions attendues:</strong> Aucune (accès refusé)</p>
            </div>
            
            <div class="test-user-card denied" onclick="testUser('<EMAIL>', 'chef_service')">
                <h3>👔 Chef de Service <span class="access-indicator access-denied">ACCÈS REFUSÉ</span></h3>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Rôle:</strong> Chef de Service</p>
                <p><strong>Permissions attendues:</strong> Aucune (accès refusé)</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📊 Résultats des Tests</h2>
            <div id="testResults" class="test-results">
                <p>Sélectionnez un utilisateur ci-dessus pour tester ses permissions.</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔧 Interface Admin (Test en Direct)</h2>
            <div id="commandsAdminContainer">
                <!-- L'interface admin sera chargée ici -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/commands-table-manager.js"></script>
    <script src="assets/js/commands-admin-interface.js"></script>

    <script>
        let currentTestUser = null;
        let mockSupabase = null;

        // Fonction pour tester un utilisateur
        function testUser(email, role) {
            currentTestUser = { email, role };
            
            // Créer un mock Supabase pour les tests
            mockSupabase = {
                auth: {
                    getUser: () => Promise.resolve({ 
                        data: { 
                            user: {
                                email: email,
                                user_metadata: { role_ipt: role }
                            }
                        }
                    })
                },
                from: (table) => ({
                    select: () => ({
                        eq: () => Promise.resolve({ data: [], error: null })
                    }),
                    insert: () => Promise.resolve({ data: [], error: null }),
                    update: () => ({
                        eq: () => Promise.resolve({ data: [], error: null })
                    }),
                    delete: () => ({
                        eq: () => Promise.resolve({ data: [], error: null })
                    })
                })
            };

            // Remplacer le client Supabase global
            window.supabaseClient = mockSupabase;

            // Tester les permissions
            testPermissions(email, role);
            
            // Charger l'interface admin
            loadAdminInterface();
        }

        // Fonction pour tester les permissions
        function testPermissions(email, role) {
            const authorizedAdmins = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
            const isAuthorized = authorizedAdmins.includes(email);
            
            const results = document.getElementById('testResults');
            results.innerHTML = `
                <h3>Test pour: ${email}</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                    <div class="permission-test">
                        <strong>🔍 Lecture:</strong> 
                        <span class="access-indicator ${isAuthorized ? 'access-granted' : 'access-denied'}">
                            ${isAuthorized ? '✅ AUTORISÉ' : '❌ REFUSÉ'}
                        </span>
                    </div>
                    <div class="permission-test">
                        <strong>✏️ Écriture:</strong> 
                        <span class="access-indicator ${isAuthorized ? 'access-granted' : 'access-denied'}">
                            ${isAuthorized ? '✅ AUTORISÉ' : '❌ REFUSÉ'}
                        </span>
                    </div>
                    <div class="permission-test">
                        <strong>🗑️ Suppression:</strong> 
                        <span class="access-indicator ${isAuthorized ? 'access-granted' : 'access-denied'}">
                            ${isAuthorized ? '✅ AUTORISÉ' : '❌ REFUSÉ'}
                        </span>
                    </div>
                    <div class="permission-test">
                        <strong>📊 Export:</strong> 
                        <span class="access-indicator ${isAuthorized ? 'access-granted' : 'access-denied'}">
                            ${isAuthorized ? '✅ AUTORISÉ' : '❌ REFUSÉ'}
                        </span>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 10px; background: ${isAuthorized ? '#f0fdf4' : '#fef2f2'}; border-radius: 6px;">
                    <strong>Résultat:</strong> ${isAuthorized ? 
                        '✅ Cet utilisateur a accès à l\'interface d\'administration.' : 
                        '❌ Cet utilisateur n\'a PAS accès à l\'interface d\'administration.'}
                </div>
            `;
        }

        // Fonction pour charger l'interface admin
        function loadAdminInterface() {
            const container = document.getElementById('commandsAdminContainer');
            
            try {
                if (typeof CommandsAdminInterface !== 'undefined') {
                    const adminInterface = new CommandsAdminInterface();
                    adminInterface.initialize();
                } else {
                    container.innerHTML = '<p>❌ Classe CommandsAdminInterface non trouvée</p>';
                }
            } catch (error) {
                container.innerHTML = `<p>❌ Erreur: ${error.message}</p>`;
            }
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Page de test du contrôle d\'accès chargée');
        });
    </script>
</body>
</html>
