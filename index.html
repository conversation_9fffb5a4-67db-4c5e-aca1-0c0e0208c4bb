<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion Interne Entreprise</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <!-- Bibliothèques externes -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"
            integrity="sha512-r22gChDnGvBylk90+2e/ycr3RVrDi8DIOkIGNhJlKfuyQM4tIRAI062MaV8sfjQKYVGjOBaZBOA87z+IhZE9DA=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"
            integrity="sha512-qZvrmS2ekKPF2mSznTQsxqPgnpkI4DNTlrdUmTzrDgektczlKNRRhy5X5AAOnx5S09ydFYWWNSfcEqDTTHgtNA=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"
            integrity="sha512-Hkz9icFKZhBsHqLcz0t0XE8Cco3MmZGlTqGFxzn/zWX/MmZAa8RnEElJVaIahxpMmJrf/4zYzJuqUjqwVnCuQ=="
            crossorigin="anonymous"
            referrerpolicy="no-referrer"></script>
    <!-- Supabase -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"
            crossorigin="anonymous"></script>
    <!-- Font Awesome pour les icônes -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer">
    <!-- Configuration -->
    <script src="assets/config/app-config.js"></script>
    <script src="assets/config/supabase-config.js"></script>

    <!-- Styles pour le widget de gestion des dons -->
    <style>
        /* Widget Tableau de Bord Dons */
        .dashboard-widget {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            max-height: 80vh;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }

        .widget-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .widget-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .widget-actions {
            display: flex;
            gap: 8px;
        }

        .widget-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }

        .widget-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.1);
        }

        .widget-content {
            padding: 20px;
            max-height: calc(80vh - 60px);
            overflow-y: auto;
        }

        .quick-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            border: 1px solid #e5e7eb;
            transition: all 0.2s;
        }

        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .stat-item.urgent {
            background: #fef2f2;
            border-color: #fecaca;
        }

        .stat-icon {
            font-size: 20px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 6px;
        }

        .stat-info {
            flex: 1;
        }

        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #1f2937;
            line-height: 1;
        }

        .stat-label {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: 500;
            margin-top: 2px;
        }

        .user-actions {
            margin-bottom: 20px;
        }

        .user-actions h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #374151;
        }

        .actions-list {
            max-height: 120px;
            overflow-y: auto;
        }

        .action-item {
            background: #fff7ed;
            border: 1px solid #fed7aa;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .action-item.urgent {
            background: #fef2f2;
            border-color: #fecaca;
        }

        .action-title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 4px;
        }

        .action-item.urgent .action-title {
            color: #dc2626;
        }

        .action-description {
            color: #78350f;
            font-size: 11px;
        }

        .action-item.urgent .action-description {
            color: #991b1b;
        }

        .no-actions {
            text-align: center;
            color: #6b7280;
            font-size: 12px;
            padding: 20px;
            font-style: italic;
        }

        .workflow-progress {
            margin-bottom: 20px;
        }

        .workflow-progress h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #374151;
        }

        .workflow-steps-mini {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f8fafc;
            border-radius: 8px;
            padding: 12px;
            position: relative;
        }

        .workflow-steps-mini::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 20px;
            right: 20px;
            height: 2px;
            background: #e5e7eb;
            z-index: 1;
        }

        .workflow-step-mini {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: #6b7280;
            position: relative;
            z-index: 2;
            transition: all 0.2s;
        }

        .workflow-step-mini.completed {
            background: #10b981;
            color: white;
        }

        .workflow-step-mini.current {
            background: #3b82f6;
            color: white;
            animation: pulse-mini 2s infinite;
        }

        .workflow-step-mini.delayed {
            background: #ef4444;
            color: white;
        }

        @keyframes pulse-mini {
            0% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
            70% { box-shadow: 0 0 0 6px rgba(59, 130, 246, 0); }
            100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0); }
        }

        .quick-links {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .quick-link-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s;
            text-align: left;
        }

        .quick-link-btn.primary {
            background: #3b82f6;
            color: white;
        }

        .quick-link-btn.secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .quick-link-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .dashboard-widget {
                position: fixed;
                top: 0;
                right: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                max-height: 100vh;
                border-radius: 0;
            }

            .quick-stats {
                grid-template-columns: 1fr;
            }
        }

        /* Animation d'entrée */
        .dashboard-widget.show {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Indicateur de mise à jour en temps réel */
        .realtime-indicator {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse-realtime 2s infinite;
        }

        @keyframes pulse-realtime {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Badge de notification pour le bouton de gestion des dons */
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 11px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse-badge 2s infinite;
            z-index: 10;
        }

        @keyframes pulse-badge {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Styles spécifiques pour le widget des commandes */
        .commands-widget {
            /* Position différente pour éviter le chevauchement avec le widget des dons */
            right: 390px; /* Décalé vers la gauche */
        }

        .commands-widget .widget-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        .amounts-summary {
            margin-bottom: 20px;
        }

        .amounts-summary h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #374151;
        }

        .amounts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .amount-item {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            transition: all 0.2s;
        }

        .amount-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .amount-value {
            font-size: 16px;
            font-weight: bold;
            color: #059669;
            line-height: 1;
        }

        .amount-label {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: 500;
            margin-top: 4px;
        }

        /* Responsive pour les deux widgets */
        @media (max-width: 1200px) {
            .commands-widget {
                right: 20px;
                top: 100px; /* Décalé vers le bas */
            }
        }

        @media (max-width: 768px) {
            .commands-widget {
                position: fixed;
                top: 0;
                right: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                max-height: 100vh;
                border-radius: 0;
            }

            .amounts-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animation d'entrée pour le widget des commandes */
        .commands-widget.show {
            animation: slideInLeft 0.3s ease-out;
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Styles spécifiques pour le widget des commandes */
        .commands-widget {
            /* Position différente pour éviter le chevauchement avec le widget des dons */
            right: 390px; /* Décalé vers la gauche */
        }

        .commands-widget .widget-header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        .amounts-summary {
            margin-bottom: 20px;
        }

        .amounts-summary h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #374151;
        }

        .amounts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .amount-item {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
            transition: all 0.2s;
        }

        .amount-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .amount-value {
            font-size: 16px;
            font-weight: bold;
            color: #059669;
            line-height: 1;
        }

        .amount-label {
            font-size: 11px;
            color: #6b7280;
            text-transform: uppercase;
            font-weight: 500;
            margin-top: 4px;
        }

        /* Responsive pour les deux widgets */
        @media (max-width: 1200px) {
            .commands-widget {
                right: 20px;
                top: 100px; /* Décalé vers le bas */
            }
        }

        @media (max-width: 768px) {
            .commands-widget {
                position: fixed;
                top: 0;
                right: 0;
                left: 0;
                width: 100%;
                height: 100vh;
                max-height: 100vh;
                border-radius: 0;
            }

            .amounts-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animation d'entrée pour le widget des commandes */
        .commands-widget.show {
            animation: slideInLeft 0.3s ease-out;
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Styles pour la documentation workflow */
        .admin-controls {
            background: #fff5f5;
            border: 2px solid #fed7d7;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .admin-header {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
            text-align: center;
        }

        .admin-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .workflow-stats-section {
            background: #f7fafc;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .workflow-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .workflow-stat-card {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .workflow-stat-card .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .workflow-stat-card .stat-label {
            color: #718096;
            font-size: 12px;
            font-weight: 500;
        }

        .workflow-legend {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .legend-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .legend-color.input { background: #48bb78; }
        .legend-color.output { background: #e53e3e; }
        .legend-color.generated { background: #9f7aea; }

        .workflow-table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .workflow-table-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .workflow-documentation-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .workflow-documentation-table th {
            background: #f7fafc;
            color: #2d3748;
            font-weight: 600;
            padding: 15px 12px;
            text-align: left;
            border-bottom: 2px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .workflow-documentation-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: top;
            line-height: 1.5;
        }

        .workflow-documentation-table tr:hover {
            background: #f7fafc;
        }

        .workflow-step-number {
            background: #4299e1;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto;
        }

        .workflow-step-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .workflow-step-description {
            color: #718096;
            font-size: 13px;
        }

        .workflow-role-badge {
            background: #48bb78;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 5px;
        }

        .workflow-role-badge.beneficiaire { background: #ed8936; }
        .workflow-role-badge.magasinier { background: #38b2ac; }
        .workflow-role-badge.transitaire { background: #4299e1; }
        .workflow-role-badge.receptionniste { background: #48bb78; }
        .workflow-role-badge.rve { background: #e53e3e; }

        .workflow-document-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .workflow-document-list li {
            background: #f7fafc;
            margin: 3px 0;
            padding: 6px 10px;
            border-radius: 6px;
            border-left: 3px solid #4299e1;
            font-size: 12px;
        }

        .workflow-document-list li.input { border-left-color: #48bb78; }
        .workflow-document-list li.output { border-left-color: #e53e3e; }
        .workflow-document-list li.generated { border-left-color: #9f7aea; }

        .workflow-action-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .workflow-action-list li {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px dotted #e2e8f0;
            font-size: 13px;
        }

        .workflow-action-list li:last-child {
            border-bottom: none;
        }

        .workflow-action-list li::before {
            content: "▶";
            color: #4299e1;
            margin-right: 8px;
            font-size: 10px;
        }

        .workflow-next-role {
            background: #ebf8ff;
            color: #2a4365;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 500;
            text-align: center;
            border: 1px solid #90cdf4;
        }

        .workflow-additional-info {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        /* Styles pour le tableau de saisie des étapes */
        .steps-editing-table {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border: 2px solid #4299e1;
        }

        .editing-table-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 12px 12px 0 0;
        }

        .editing-table-header h4 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
        }

        .editing-table-actions {
            display: flex;
            gap: 10px;
        }

        .editing-table-container {
            overflow-x: auto;
            max-height: 600px;
            overflow-y: auto;
        }

        .steps-editing-table-content {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .steps-editing-table-content th {
            background: #f7fafc;
            color: #2d3748;
            font-weight: 600;
            padding: 12px 8px;
            text-align: left;
            border-bottom: 2px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .steps-editing-table-content td {
            padding: 8px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: top;
        }

        .steps-editing-table-content tr:hover {
            background: #f7fafc;
        }

        .step-input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 12px;
            transition: border-color 0.2s;
        }

        .step-input:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
        }

        .step-textarea {
            width: 100%;
            min-height: 60px;
            padding: 6px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 12px;
            resize: vertical;
            transition: border-color 0.2s;
        }

        .step-textarea:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
        }

        .step-select {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 12px;
            background: white;
            transition: border-color 0.2s;
        }

        .step-select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 2px rgba(66, 153, 225, 0.1);
        }

        .step-number-input {
            width: 60px;
            text-align: center;
            font-weight: 600;
        }

        .step-actions-cell {
            text-align: center;
            white-space: nowrap;
        }

        .step-action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            margin: 0 2px;
            transition: all 0.2s;
        }

        .step-action-btn.delete {
            background: #fed7d7;
            color: #c53030;
        }

        .step-action-btn.delete:hover {
            background: #feb2b2;
        }

        .step-action-btn.duplicate {
            background: #e6fffa;
            color: #319795;
        }

        .step-action-btn.duplicate:hover {
            background: #b2f5ea;
        }

        .step-validation-error {
            border-color: #e53e3e !important;
            background: #fed7d7;
        }

        .step-validation-message {
            font-size: 11px;
            color: #e53e3e;
            margin-top: 2px;
        }

        /* Responsive pour la documentation workflow */
        @media (max-width: 768px) {
            .workflow-documentation-table {
                font-size: 12px;
            }

            .workflow-documentation-table th,
            .workflow-documentation-table td {
                padding: 10px 8px;
            }

            .admin-buttons {
                flex-direction: column;
            }

            .workflow-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .editing-table-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .editing-table-actions {
                flex-direction: column;
                width: 100%;
            }

            .steps-editing-table-content {
                font-size: 11px;
            }
        }
    </style>

    <!-- Fix DatabaseManager (doit être chargé en premier) -->
    <script src="assets/js/database-manager-fix.js"></script>

    <!-- Scripts Supabase avancés -->
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>
    <script src="assets/js/supabase-migration.js"></script>

    <!-- Scripts temps réel -->
    <script src="assets/js/notification-system.js"></script>
    <script src="assets/js/realtime-ui-manager.js"></script>
    <script src="assets/js/user-presence.js"></script>
    <script src="assets/js/supabase-realtime.js"></script>
    <script src="assets/js/realtime-coordinator.js"></script>
</head>
<body>
    <!-- Indicateur de statut de connexion -->
    <div id="connectionStatus" class="connection-status">
        <span id="connectionIndicator">🔄</span>
        <span id="connectionText">Initialisation...</span>
    </div>

    <!-- Section de connexion -->
    <div id="login" class="container">
        <h1>Connexion - Gestion Interne</h1>
        <div class="login-form">
            <label for="username">Nom d'utilisateur / ID Dépôt:</label>
            <input type="text" id="username" placeholder="Entrez votre identifiant">
            
            <label for="password">Mot de passe:</label>
            <input type="password" id="password" placeholder="Entrez votre mot de passe">
            
            <button onclick="login()" class="primary">Se connecter</button>
            <div id="loginError" class="error-message"></div>
            
            <!-- Aide pour les comptes de test -->
            <div class="test-accounts" style="display: none;">
                <h3>Comptes disponibles (exemples) :</h3>
                <div class="account-list">
                    <div class="account-item" onclick="fillLogin('admin123', 'admin123*+')">
                        <strong>Admin:</strong> admin123 / admin123*+
                    </div>
                    <div class="account-item" onclick="fillLogin('rym', 'rym')">
                        <strong>Manager:</strong> rym / rym
                    </div>
                    <div class="account-item" onclick="fillLogin('besma', 'besma')">
                        <strong>Finance:</strong> besma / besma
                    </div>
                    <div class="account-item" onclick="fillLogin('yosra', 'yosra2025*+')">
                        <strong>Hygiène:</strong> yosra / yosra2025*+
                    </div>
                </div>
                <p style="margin-top: 15px; font-size: 12px; color: #666;">
                    <strong>Note:</strong> Comptes utilisateurs et dépôts disponibles selon vos accès
                </p>
            </div>
        </div>
    </div>

    <!-- Application principale -->
    <div id="mainApp" class="container" style="display: none;">
        <!-- En-tête -->
        <div class="header">
            <h1>Gestion Interne Entreprise</h1>
            <div class="main-buttons">
                <button id="convertisseurBtn" onclick="afficherConvertisseur()" class="secondary">Convertisseur</button>
                <button id="analyseBtn" onclick="toggleAnalyseZone()" class="secondary">Analyser Messages</button>
                <button id="gestionCommandesBtn" onclick="toggleGestionCommandes()" class="secondary">Gestion Commandes</button>
                <button id="gestionPvBtn" onclick="toggleGestionPvZone()" class="secondary">Gestion PV Dépôts</button>
                <button id="gestionDonsBtn" onclick="toggleGestionDons()" class="secondary" style="position: relative;">
                    🎁 Gestion des Dons
                    <span id="donationsNotificationBadge" class="notification-badge" style="display: none;">0</span>
                </button>
                <button id="gestionCommandesBtn" onclick="toggleGestionCommandes()" class="secondary" style="position: relative;">
                    📦 Gestion des Commandes
                    <span id="commandsNotificationBadge" class="notification-badge" style="display: none;">0</span>
                </button>
                <button id="changePasswordBtn" onclick="changerMotDePasse()" class="secondary">Changer Mot de Passe</button>
            </div>
            <div class="status-container">
                <div class="status-button" onclick="updateStatus()">
                    <span class="status-dot"></span>
                    <span id="statusText">En ligne</span>
                </div>
                <button onclick="deconnexion()" class="danger">Déconnexion</button>
                <a href="diagnostic.html" target="_blank" style="margin-left: 10px; padding: 8px 12px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px; font-size: 12px; font-weight: 500;">🔧 Diagnostic</a>
                <a href="diagnostic-database.html" target="_blank" style="margin-left: 10px; padding: 8px 12px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; font-size: 12px; font-weight: 500;">🔧 DB Fix</a>
                <a href="supabase-dashboard.html" target="_blank" style="margin-left: 10px; padding: 8px 12px; background: #6f42c1; color: white; text-decoration: none; border-radius: 5px; font-size: 12px; font-weight: 500;">📊 Supabase</a>
                <a href="realtime-dashboard.html" target="_blank" style="margin-left: 10px; padding: 8px 12px; background: #e83e8c; color: white; text-decoration: none; border-radius: 5px; font-size: 12px; font-weight: 500;">⚡ Temps Réel</a>
                <a href="install-supabase.html" target="_blank" style="margin-left: 10px; padding: 8px 12px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; font-size: 12px; font-weight: 500;">🚀 Setup</a>
            </div>
        </div>

        <!-- Zone d'analyse des messages -->
        <div id="analyseZone" class="module-zone" style="display: none;">
            <h2>Analyse des Messages</h2>
            <button onclick="lancerAnalyseHistorique()" class="primary">Lancer Analyse Historique</button>
            <div id="resultatsAnalyse"></div>
        </div>

        <!-- Zone de gestion des commandes -->
        <div id="gestionCommandesZone" class="module-zone" style="display: none;">
            <h2>Gestion des Commandes Fournisseurs</h2>
            
            <!-- Vue liste des commandes -->
            <div id="listeCommandesView">
                <div class="table-actions">
                    <button onclick="preparerNouvelleCommande()" class="primary">Nouvelle Commande</button>
                </div>
                <div class="table-container">
                    <table id="tableCommandes">
                        <thead>
                            <tr>
                                <th>N° Commande</th>
                                <th>Fournisseur</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Date Création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="commandesTableBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Formulaire de commande -->
            <div id="formulaireCommandeContainer" style="display: none;">
                <div class="form-header">
                    <h3 id="titreFormulaire">Nouvelle Commande</h3>
                    <button onclick="retourListeCommandes()" class="secondary">Retour à la liste</button>
                </div>
                
                <form id="formulaireCommande">
                    <!-- Étape 1: Informations de base -->
                    <fieldset id="etape1">
                        <legend>Étape 1: Informations de base</legend>
                        <div class="form-row">
                            <label for="numeroCommande">N° Commande:</label>
                            <input type="text" id="numeroCommande" required>
                        </div>
                        <div class="form-row">
                            <label for="fournisseur">Fournisseur:</label>
                            <input type="text" id="fournisseur" required>
                        </div>
                        <div class="form-row">
                            <label for="contactFournisseur">Contact Fournisseur:</label>
                            <input type="text" id="contactFournisseur">
                        </div>
                    </fieldset>

                    <!-- Étape 2: Produits -->
                    <fieldset id="etape2" disabled>
                        <legend>Étape 2: Produits</legend>
                        <div id="produitsContainer">
                            <div class="produit-item">
                                <input type="text" placeholder="Nom du produit" class="produit-nom">
                                <input type="number" placeholder="Quantité" class="produit-quantite">
                                <input type="number" step="0.01" placeholder="Prix unitaire" class="produit-prix">
                                <button type="button" onclick="ajouterProduit()">+</button>
                            </div>
                        </div>
                        <div class="form-row">
                            <label for="devise">Devise:</label>
                            <select id="devise">
                                <option value="EUR">EUR</option>
                                <option value="USD">USD</option>
                                <option value="GBP">GBP</option>
                            </select>
                        </div>
                    </fieldset>

                    <!-- Étape 3: Finalisation -->
                    <fieldset id="etape3" disabled>
                        <legend>Étape 3: Finalisation</legend>
                        <div class="form-row">
                            <label for="dateLivraison">Date de livraison prévue:</label>
                            <input type="date" id="dateLivraison">
                        </div>
                        <div class="form-row">
                            <label for="conditionsPaiement">Conditions de paiement:</label>
                            <textarea id="conditionsPaiement" rows="3"></textarea>
                        </div>
                        <div class="form-row">
                            <label for="notes">Notes:</label>
                            <textarea id="notes" rows="3"></textarea>
                        </div>
                    </fieldset>

                    <div class="form-actions">
                        <button type="button" onclick="handleCommandeAction('valider')" class="primary">Valider Étape</button>
                        <button type="button" onclick="handleCommandeAction('envoyer')" class="secondary">Envoyer Email</button>
                        <button type="button" onclick="handleCommandeAction('soumettre')" class="success">Soumettre Commande</button>
                        <button type="button" onclick="viderFormulaireCommande()" class="danger">Vider</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Zone de gestion des PV Dépôts -->
        <div id="gestionPvZone" class="module-zone" style="display: none;">
            <h2>Gestion des PV Dépôts</h2>
            
            <!-- Vue création PV (pour les dépôts) -->
            <div id="creationPvView">
                <h3>Création de PV</h3>
                <div class="pv-actions">
                    <button onclick="toggleSelectAll()" class="secondary">Tout sélectionner/désélectionner</button>
                    <button onclick="createPv()" class="primary">Créer PV</button>
                    <button onclick="consulterTousPv()" class="secondary">Consulter tous les PV</button>
                </div>
                
                <div class="table-container">
                    <table id="tableProduitsPv">
                        <thead>
                            <tr>
                                <th>Sélection</th>
                                <th>Produit</th>
                                <th>Date d'expiration</th>
                                <th>Statut</th>
                                <th>Quantité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="produitsPvTableBody">
                        </tbody>
                    </table>
                </div>
                
                <div id="pvStatusMessage" class="status-message"></div>
                
                <div class="depot-global-actions">
                    <button onclick="exporterTousPvPdf()" class="secondary">Exporter tous PV (PDF)</button>
                    <button onclick="exporterTousPvExcel()" class="secondary">Exporter tous PV (Excel)</button>
                </div>
            </div>

            <!-- Vue consultation PV (pour admin/hygiène/dépôt) -->
            <div id="consultationPvView" style="display: none;">
                <h3>PV CONSULTÉS</h3>
                <div class="pv-admin-actions">
                    <button id="retourCreationBtn" onclick="retourCreationPv()" class="secondary" style="display: none;">Retour Création PV</button>
                    <input type="text" id="searchPv" placeholder="Rechercher PV..." onkeyup="filtrerPvAdminView()">
                    <button onclick="exportAllPvsToPdf()" class="secondary">Exporter PDF</button>
                    <button onclick="exportAllPvsToExcel()" class="secondary">Exporter Excel</button>
                    <button onclick="envoyerPvHygiene()" class="primary hygiene-btn">📧 Envoyer à l'Équipe Hygiène</button>
                    <button id="clearAllPvsBtn" onclick="clearAllPvs()" class="danger" style="display: none;">Supprimer tous</button>
                </div>

                <!-- Tableau détaillé des PV -->
                <div class="table-container">
                    <table id="tablePvConsultes" class="pv-table">
                        <thead>
                            <tr>
                                <th>Sélection</th>
                                <th>N° PV</th>
                                <th>Dépôt</th>
                                <th>Date Création</th>
                                <th>Nb Produits</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="pvConsultesTableBody">
                        </tbody>
                    </table>
                </div>

                <!-- Détails du PV sélectionné -->
                <div id="pvDetailsSection" class="pv-details-section" style="display: none;">
                    <h4>Détails du PV</h4>
                    <div id="pvDetailsContent" class="pv-details-content"></div>
                </div>

                <div id="pvList" class="pv-list" style="display: none;"></div>
            </div>
        </div>

        <!-- Zone de gestion des dons -->
        <div id="gestionDonsZone" class="module-zone" style="display: none;">
            <h2>🎁 Gestion des Dons IPT</h2>

            <!-- Navigation des onglets -->
            <div class="donations-tabs">
                <button id="tabListeDons" onclick="showDonationsTab('liste')" class="tab-button active">📋 Liste des Dons</button>
                <button id="tabNouveauDon" onclick="showDonationsTab('nouveau')" class="tab-button">➕ Nouveau Don</button>
                <button id="tabWorkflow" onclick="showDonationsTab('workflow')" class="tab-button">🔄 Workflow</button>
                <button id="tabDocumentation" onclick="showDonationsTab('documentation')" class="tab-button">📋 Documentation Workflow</button>
                <button id="tabInventaire" onclick="showDonationsTab('inventaire')" class="tab-button">📦 Inventaire</button>
                <button id="tabStatistiques" onclick="showDonationsTab('statistiques')" class="tab-button">📊 Statistiques</button>
            </div>

            <!-- Onglet Liste des Dons -->
            <div id="donationsListeTab" class="donations-tab-content">
                <div class="donations-header">
                    <div class="donations-filters">
                        <input type="text" id="searchDonations" placeholder="🔍 Rechercher un don..." onkeyup="filterDonations()">
                        <select id="filterTypeDon" onchange="filterDonations()">
                            <option value="">Tous les types</option>
                            <option value="article">📦 Article</option>
                            <option value="equipement">🔧 Équipement</option>
                        </select>
                        <select id="filterStatutDon" onchange="filterDonations()">
                            <option value="">Tous les statuts</option>
                            <option value="en_attente">⏳ En attente</option>
                            <option value="en_cours">🔄 En cours</option>
                            <option value="approuve">✅ Approuvé</option>
                            <option value="rejete">❌ Rejeté</option>
                            <option value="livre">📦 Livré</option>
                            <option value="complete">🎯 Complété</option>
                        </select>
                        <button onclick="exportDonationsList()" class="secondary">📊 Exporter</button>
                    </div>
                </div>

                <div class="table-container">
                    <table id="tableDonations" class="donations-table">
                        <thead>
                            <tr>
                                <th>N° Don</th>
                                <th>Type</th>
                                <th>Donateur</th>
                                <th>Bénéficiaire</th>
                                <th>Statut</th>
                                <th>Étape</th>
                                <th>Date Création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="donationsTableBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Onglet Nouveau Don -->
            <div id="donationsNouveauTab" class="donations-tab-content" style="display: none;">
                <div class="donation-form-container">
                    <h3>➕ Créer un Nouveau Don</h3>

                    <!-- Étapes du workflow -->
                    <div class="workflow-steps">
                        <div class="step active" id="step1">
                            <span class="step-number">1</span>
                            <span class="step-title">Informations de base</span>
                        </div>
                        <div class="step" id="step2">
                            <span class="step-number">2</span>
                            <span class="step-title">Documents</span>
                        </div>
                        <div class="step" id="step3">
                            <span class="step-number">3</span>
                            <span class="step-title">Validation</span>
                        </div>
                    </div>

                    <form id="donationForm">
                        <!-- Étape 1: Informations de base -->
                        <fieldset id="donationStep1" class="donation-step">
                            <legend>Étape 1: Informations de base</legend>

                            <div class="form-row">
                                <label for="donationType">Type de don:</label>
                                <select id="donationType" required onchange="toggleEquipmentFields()">
                                    <option value="">Sélectionner le type</option>
                                    <option value="article">📦 Article</option>
                                    <option value="equipement">🔧 Équipement</option>
                                </select>
                            </div>

                            <div class="form-row">
                                <label for="donationNumber">N° de Don:</label>
                                <input type="text" id="donationNumber" required readonly>
                            </div>

                            <div class="form-row">
                                <label for="donorName">Nom du Donateur:</label>
                                <input type="text" id="donorName" required>
                            </div>

                            <div class="form-row">
                                <label for="donorContact">Contact Donateur:</label>
                                <input type="text" id="donorContact">
                            </div>

                            <div class="form-row">
                                <label for="beneficiaryService">Service Bénéficiaire:</label>
                                <input type="text" id="beneficiaryService" required>
                            </div>

                            <div class="form-row">
                                <label for="donationReason">Raison du Don:</label>
                                <textarea id="donationReason" rows="3" required placeholder="Expliquer la raison du don et le lieu d'affectation"></textarea>
                            </div>

                            <!-- Champs spécifiques aux équipements -->
                            <div id="equipmentFields" style="display: none;">
                                <div class="form-row">
                                    <label for="equipmentBrand">Marque:</label>
                                    <input type="text" id="equipmentBrand">
                                </div>
                                <div class="form-row">
                                    <label for="equipmentType">Type:</label>
                                    <input type="text" id="equipmentType">
                                </div>
                                <div class="form-row">
                                    <label for="equipmentSerial">Numéro de Série:</label>
                                    <input type="text" id="equipmentSerial">
                                </div>
                            </div>
                        </fieldset>

                        <!-- Étape 2: Documents -->
                        <fieldset id="donationStep2" class="donation-step" style="display: none;">
                            <legend>Étape 2: Documents requis</legend>

                            <div class="documents-upload">
                                <div class="document-item">
                                    <label for="lettreDonateurFile">Lettre du Donateur:</label>
                                    <input type="file" id="lettreDonateurFile" accept=".pdf,.doc,.docx,.jpg,.png">
                                    <div class="file-preview" id="lettreDonateurPreview"></div>
                                </div>

                                <div class="document-item">
                                    <label for="factureFile">Facture:</label>
                                    <input type="file" id="factureFile" accept=".pdf,.doc,.docx,.jpg,.png">
                                    <div class="file-preview" id="facturePreview"></div>
                                </div>

                                <div class="document-item">
                                    <label for="bonLivraisonFile">Bon de Livraison (BL):</label>
                                    <input type="file" id="bonLivraisonFile" accept=".pdf,.doc,.docx,.jpg,.png">
                                    <div class="file-preview" id="bonLivraisonPreview"></div>
                                </div>

                                <div class="document-item">
                                    <label for="bonPrelevementFile">Bon de Prélèvement (BP):</label>
                                    <input type="file" id="bonPrelevementFile" accept=".pdf,.doc,.docx,.jpg,.png">
                                    <div class="file-preview" id="bonPrelevementPreview"></div>
                                </div>

                                <div class="document-item">
                                    <label for="autresDocuments">Autres Documents:</label>
                                    <input type="file" id="autresDocuments" multiple accept=".pdf,.doc,.docx,.jpg,.png">
                                    <div class="file-preview" id="autresDocumentsPreview"></div>
                                </div>
                            </div>
                        </fieldset>

                        <!-- Étape 3: Validation -->
                        <fieldset id="donationStep3" class="donation-step" style="display: none;">
                            <legend>Étape 3: Validation et Soumission</legend>

                            <div class="validation-summary">
                                <h4>Résumé du Don</h4>
                                <div id="donationSummary"></div>
                            </div>

                            <div class="form-row">
                                <label for="donationNotes">Notes additionnelles:</label>
                                <textarea id="donationNotes" rows="3" placeholder="Notes ou commentaires supplémentaires"></textarea>
                            </div>
                        </fieldset>

                        <div class="form-actions">
                            <button type="button" onclick="previousDonationStep()" id="prevStepBtn" class="secondary" style="display: none;">⬅️ Précédent</button>
                            <button type="button" onclick="nextDonationStep()" id="nextStepBtn" class="primary">Suivant ➡️</button>
                            <button type="button" onclick="submitDonation()" id="submitDonationBtn" class="success" style="display: none;">🎁 Soumettre Don</button>
                            <button type="button" onclick="resetDonationForm()" class="danger">🔄 Réinitialiser</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Onglet Workflow -->
            <div id="donationsWorkflowTab" class="donations-tab-content" style="display: none;">
                <h3>🔄 Workflow des Approbations</h3>

                <div class="workflow-filters">
                    <select id="workflowFilter" onchange="filterWorkflow()">
                        <option value="">Toutes les étapes</option>
                        <option value="demande_initiale">📝 Demande initiale</option>
                        <option value="validation_dg">👨‍💼 Validation DG</option>
                        <option value="avis_technique_dt">🔧 Avis technique DT</option>
                        <option value="autorisation_ms">🏛️ Autorisation MS</option>
                        <option value="accord_expedition">📦 Accord expédition</option>
                        <option value="reception_livraison">✅ Réception/Livraison</option>
                    </select>
                </div>

                <div class="workflow-container">
                    <div id="workflowList"></div>
                </div>
            </div>

            <!-- Onglet Documentation Workflow -->
            <div id="donationsDocumentationTab" class="donations-tab-content" style="display: none;">
                <h3>📋 Documentation Workflow - Processus Complet IPT</h3>

                <!-- Admin Controls -->
                <div class="admin-controls" id="workflowAdminControls" style="display: none;">
                    <div class="admin-header">
                        🔧 Contrôles Administrateur - Accès Restreint
                    </div>
                    <div class="admin-buttons">
                        <button class="btn btn-primary" onclick="toggleStepsEditingTable()">
                            📝 Tableau de Saisie des Étapes
                        </button>
                        <button class="btn btn-success" onclick="validateAllWorkflowSteps()">
                            ✅ Valider Toutes les Étapes
                        </button>
                        <button class="btn btn-warning" onclick="editWorkflowMode()">
                            ✏️ Modifier Workflow
                        </button>
                        <button class="btn btn-danger" onclick="resetWorkflowToDefault()">
                            🔄 Réinitialiser
                        </button>
                        <button class="btn btn-secondary" onclick="exportWorkflowDocumentation()">
                            📤 Exporter Documentation
                        </button>
                    </div>
                </div>

                <!-- Tableau de Saisie des Étapes -->
                <div class="steps-editing-table" id="stepsEditingTable" style="display: none;">
                    <div class="editing-table-header">
                        <h4>📝 Tableau de Saisie des Étapes du Workflow</h4>
                        <div class="editing-table-actions">
                            <button class="btn btn-primary" onclick="addNewStepRow()">
                                ➕ Ajouter Étape
                            </button>
                            <button class="btn btn-success" onclick="saveAllSteps()">
                                💾 Sauvegarder Tout
                            </button>
                            <button class="btn btn-secondary" onclick="cancelStepsEditing()">
                                ❌ Annuler
                            </button>
                        </div>
                    </div>

                    <div class="editing-table-container">
                        <table class="steps-editing-table-content" id="stepsEditingTableContent">
                            <thead>
                                <tr>
                                    <th style="width: 80px;">N° Étape</th>
                                    <th style="width: 200px;">Nom</th>
                                    <th style="width: 250px;">Description</th>
                                    <th style="width: 150px;">Responsable</th>
                                    <th style="width: 200px;">Documents d'Entrée</th>
                                    <th style="width: 200px;">Actions</th>
                                    <th style="width: 200px;">Documents de Sortie</th>
                                    <th style="width: 150px;">Responsable Suivant</th>
                                    <th style="width: 100px;">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="stepsEditingTableBody">
                                <!-- Les lignes d'édition seront générées dynamiquement -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Statistics Section -->
                <div class="workflow-stats-section">
                    <h4>📊 Statistiques du Workflow</h4>
                    <div class="workflow-stats-grid">
                        <div class="workflow-stat-card">
                            <div class="stat-value">10</div>
                            <div class="stat-label">Étapes Totales</div>
                        </div>
                        <div class="workflow-stat-card">
                            <div class="stat-value">6</div>
                            <div class="stat-label">Rôles Impliqués</div>
                        </div>
                        <div class="workflow-stat-card">
                            <div class="stat-value">15</div>
                            <div class="stat-label">Documents Types</div>
                        </div>
                        <div class="workflow-stat-card">
                            <div class="stat-value">10-15</div>
                            <div class="stat-label">Jours Moyens</div>
                        </div>
                    </div>
                </div>

                <!-- Legend -->
                <div class="workflow-legend">
                    <h4>🏷️ Légende des Documents</h4>
                    <div class="legend-grid">
                        <div class="legend-item">
                            <div class="legend-color input"></div>
                            <span>Documents d'entrée (reçus)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color output"></div>
                            <span>Documents de sortie (transmis)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color generated"></div>
                            <span>Documents générés automatiquement</span>
                        </div>
                    </div>
                </div>

                <!-- Workflow Table -->
                <div class="workflow-table-container">
                    <div class="workflow-table-header">
                        <h4>🔄 Workflow Complet - Gestion des Dons IPT</h4>
                    </div>

                    <div style="overflow-x: auto;">
                        <table class="workflow-documentation-table" id="workflowDocumentationTable">
                            <thead>
                                <tr>
                                    <th style="width: 80px;">Étape</th>
                                    <th style="width: 200px;">Nom / Description</th>
                                    <th style="width: 150px;">Responsable</th>
                                    <th style="width: 200px;">Documents d'Entrée</th>
                                    <th style="width: 250px;">Actions Réalisées</th>
                                    <th style="width: 200px;">Documents de Sortie</th>
                                    <th style="width: 150px;">Responsable Suivant</th>
                                </tr>
                            </thead>
                            <tbody id="workflowDocumentationTableBody">
                                <!-- Les données seront générées dynamiquement -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="workflow-additional-info">
                    <h4>📝 Informations Complémentaires</h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div>
                            <h5>🎯 Objectifs du Workflow</h5>
                            <ul>
                                <li>Traçabilité complète des dons</li>
                                <li>Validation hiérarchique</li>
                                <li>Conformité réglementaire</li>
                                <li>Optimisation des délais</li>
                            </ul>
                        </div>
                        <div>
                            <h5>📋 Abréviations Utilisées</h5>
                            <ul>
                                <li><strong>BP</strong> : Bon de Prélèvement</li>
                                <li><strong>BL</strong> : Bon de Livraison</li>
                                <li><strong>BS</strong> : Bon de Sortie</li>
                                <li><strong>RVE</strong> : Responsable Volet Équipement</li>
                            </ul>
                        </div>
                        <div>
                            <h5>⏱️ Délais Standards</h5>
                            <ul>
                                <li>Validation : 1-2 jours</li>
                                <li>Vérification : 1-2 jours</li>
                                <li>Transport : 1-3 jours</li>
                                <li>Installation : 2-5 jours</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Onglet Inventaire -->
            <div id="donationsInventaireTab" class="donations-tab-content" style="display: none;">
                <h3>📦 Inventaire des Équipements Donnés</h3>

                <div class="inventory-actions">
                    <button onclick="generateInventoryNumber()" class="primary">🏷️ Générer NI</button>
                    <button onclick="printBarcodeLabels()" class="secondary">🖨️ Imprimer Étiquettes</button>
                    <button onclick="exportInventory()" class="secondary">📊 Exporter Inventaire</button>
                </div>

                <div class="table-container">
                    <table id="tableInventaire" class="inventory-table">
                        <thead>
                            <tr>
                                <th>NI</th>
                                <th>N° Don</th>
                                <th>Équipement</th>
                                <th>Marque/Type</th>
                                <th>N° Série</th>
                                <th>Service Affecté</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="inventaireTableBody">
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Onglet Statistiques -->
            <div id="donationsStatistiquesTab" class="donations-tab-content" style="display: none;">
                <h3>📊 Statistiques des Dons</h3>

                <div class="stats-grid">
                    <div class="stat-card">
                        <h4>📈 Total des Dons</h4>
                        <div class="stat-value" id="totalDonations">0</div>
                    </div>
                    <div class="stat-card">
                        <h4>📦 Articles</h4>
                        <div class="stat-value" id="totalArticles">0</div>
                    </div>
                    <div class="stat-card">
                        <h4>🔧 Équipements</h4>
                        <div class="stat-value" id="totalEquipments">0</div>
                    </div>
                    <div class="stat-card">
                        <h4>⏳ En Attente</h4>
                        <div class="stat-value" id="pendingDonations">0</div>
                    </div>
                </div>

                <div class="charts-container">
                    <div class="chart-item">
                        <h4>Dons par Mois</h4>
                        <div id="donationsChart"></div>
                    </div>
                    <div class="chart-item">
                        <h4>Répartition par Type</h4>
                        <div id="typeChart"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Zone spéciale pour l'équipe hygiène -->
        <div id="hygieneZone" class="module-zone" style="display: none;">
            <h2>🧪 ÉQUIPE HYGIÈNE - PV REÇUS</h2>
            <div class="hygiene-header">
                <div class="hygiene-info">
                    <p>📧 <strong>Bienvenue dans l'espace hygiène</strong></p>
                    <p>Consultez et exportez les procès-verbaux qui vous ont été envoyés</p>
                </div>
                <div class="hygiene-actions">
                    <button onclick="rafraichirPvHygiene()" class="btn-secondary">
                        🔄 Actualiser
                    </button>
                    <button onclick="exporterPvHygienePdf()" class="btn-export-pdf">
                        📄 Export PDF
                    </button>
                    <button onclick="exporterPvHygieneExcel()" class="btn-export-excel">
                        📊 Export Excel
                    </button>
                </div>
            </div>

            <!-- Filtres et recherche -->
            <div class="hygiene-filters">
                <input type="text" id="searchPvHygiene" placeholder="🔍 Rechercher un PV..." onkeyup="filtrerPvHygiene()">
                <select id="filterStatutHygiene" onchange="filtrerPvHygiene()">
                    <option value="">Tous les statuts</option>
                    <option value="en_attente">⏳ En attente</option>
                    <option value="valide">✅ Validé</option>
                    <option value="rejete">❌ Rejeté</option>
                </select>
                <select id="filterDepotHygiene" onchange="filtrerPvHygiene()">
                    <option value="">Tous les dépôts</option>
                </select>
            </div>

            <!-- Tableau des PV reçus -->
            <div class="table-container">
                <table id="tablePvHygiene" class="hygiene-table">
                    <thead>
                        <tr>
                            <th>Sélection</th>
                            <th>N° PV</th>
                            <th>Dépôt Expéditeur</th>
                            <th>Date Réception</th>
                            <th>Nb Produits</th>
                            <th>Produits Non Conformes</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="pvHygieneTableBody">
                    </tbody>
                </table>
            </div>

            <!-- Statistiques -->
            <div id="hygieneStats" class="hygiene-stats">
                <div class="stat-card">
                    <h4>📊 Statistiques</h4>
                    <div id="statsContent" class="stats-content"></div>
                </div>
            </div>

            <!-- Détails du PV sélectionné pour hygiène -->
            <div id="pvHygieneDetailsSection" class="pv-details-section" style="display: none;">
                <h4>🔍 Détails du PV - Analyse Hygiène</h4>
                <div id="pvHygieneDetailsContent" class="pv-details-content"></div>
            </div>
        </div>

        <!-- Widget Tableau de Bord Dons -->
        <div id="donationsDashboardWidget" class="dashboard-widget donations-widget" style="display: none;">
            <div class="widget-header">
                <div class="realtime-indicator" title="Mises à jour en temps réel actives"></div>
                <h3>🎁 Tableau de Bord Dons</h3>
                <div class="widget-actions">
                    <button onclick="refreshDonationsDashboard()" class="widget-btn refresh-btn" title="Actualiser">🔄</button>
                    <button onclick="openDonationsManagement()" class="widget-btn manage-btn" title="Gestion complète">⚙️</button>
                    <button onclick="toggleDonationsWidget()" class="widget-btn close-btn" title="Fermer">✖️</button>
                </div>
            </div>

            <div class="widget-content">
                <!-- Statistiques rapides -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetTotalDons">0</div>
                            <div class="stat-label">Total Dons</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetDonsEnCours">0</div>
                            <div class="stat-label">En Cours</div>
                        </div>
                    </div>
                    <div class="stat-item urgent">
                        <div class="stat-icon">🚨</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetDonsEnRetard">0</div>
                            <div class="stat-label">En Retard</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">✅</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetDonsCompletes">0</div>
                            <div class="stat-label">Complétés</div>
                        </div>
                    </div>
                </div>

                <!-- Actions utilisateur -->
                <div class="user-actions" id="donsUserActions">
                    <h4>🎯 Actions Requises</h4>
                    <div class="actions-list" id="donsActionsList">
                        <div class="no-actions">Aucune action requise pour le moment</div>
                    </div>
                </div>

                <!-- Workflow en cours -->
                <div class="workflow-progress">
                    <h4>🔄 Workflow en Cours</h4>
                    <div class="workflow-steps-mini" id="donsWorkflowStepsMini">
                        <!-- Les étapes seront générées dynamiquement -->
                    </div>
                </div>

                <!-- Valeurs financières -->
                <div class="amounts-summary">
                    <h4>💰 Résumé Financier</h4>
                    <div class="amounts-grid">
                        <div class="amount-item">
                            <div class="amount-value" id="widgetValeurTotale">0 TND</div>
                            <div class="amount-label">Valeur Totale</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-value" id="widgetValeurEnCours">0 TND</div>
                            <div class="amount-label">En Cours</div>
                        </div>
                    </div>
                </div>

                <!-- Liens rapides -->
                <div class="quick-links">
                    <button onclick="openDonationsManagement()" class="quick-link-btn primary">
                        📋 Gestion Complète
                    </button>
                    <button onclick="openDonationsWorkflowDashboard()" class="quick-link-btn secondary">
                        🔄 Tableau de Bord Workflow
                    </button>
                    <button onclick="openDonationsReports()" class="quick-link-btn secondary">
                        📊 Rapports & Statistiques
                    </button>
                </div>
            </div>
        </div>

        <!-- Widget Tableau de Bord Commandes -->
        <div id="commandsDashboardWidget" class="dashboard-widget commands-widget" style="display: none;">
            <div class="widget-header">
                <div class="realtime-indicator" title="Mises à jour en temps réel actives"></div>
                <h3>📦 Tableau de Bord Commandes</h3>
                <div class="widget-actions">
                    <button onclick="refreshCommandsDashboard()" class="widget-btn refresh-btn" title="Actualiser">🔄</button>
                    <button onclick="openCommandsManagement()" class="widget-btn manage-btn" title="Gestion complète">⚙️</button>
                    <button onclick="toggleCommandsWidget()" class="widget-btn close-btn" title="Fermer">✖️</button>
                </div>
            </div>

            <div class="widget-content">
                <!-- Statistiques rapides -->
                <div class="quick-stats">
                    <div class="stat-item">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetTotalCommandes">0</div>
                            <div class="stat-label">Total Commandes</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">⏳</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetCommandesEnCours">0</div>
                            <div class="stat-label">En Cours</div>
                        </div>
                    </div>
                    <div class="stat-item urgent">
                        <div class="stat-icon">🚨</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetCommandesEnRetard">0</div>
                            <div class="stat-label">En Retard</div>
                        </div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-icon">✅</div>
                        <div class="stat-info">
                            <div class="stat-value" id="widgetCommandesLivrees">0</div>
                            <div class="stat-label">Livrées</div>
                        </div>
                    </div>
                </div>

                <!-- Actions utilisateur -->
                <div class="user-actions" id="commandsUserActions">
                    <h4>🎯 Actions Requises</h4>
                    <div class="actions-list" id="commandsActionsList">
                        <div class="no-actions">Aucune action requise pour le moment</div>
                    </div>
                </div>

                <!-- Workflow en cours -->
                <div class="workflow-progress">
                    <h4>🔄 Workflow en Cours</h4>
                    <div class="workflow-steps-mini" id="commandsWorkflowStepsMini">
                        <!-- Les étapes seront générées dynamiquement -->
                    </div>
                </div>

                <!-- Montants -->
                <div class="amounts-summary">
                    <h4>💰 Résumé Financier</h4>
                    <div class="amounts-grid">
                        <div class="amount-item">
                            <div class="amount-value" id="widgetMontantTotal">0 TND</div>
                            <div class="amount-label">Montant Total</div>
                        </div>
                        <div class="amount-item">
                            <div class="amount-value" id="widgetMontantEnCours">0 TND</div>
                            <div class="amount-label">En Cours</div>
                        </div>
                    </div>
                </div>

                <!-- Liens rapides -->
                <div class="quick-links">
                    <button onclick="openCommandsManagement()" class="quick-link-btn primary">
                        📋 Gestion Complète
                    </button>
                    <button onclick="openCommandsWorkflowDashboard()" class="quick-link-btn secondary">
                        🔄 Tableau de Bord Workflow
                    </button>
                    <button onclick="openCommandsReports()" class="quick-link-btn secondary">
                        📊 Rapports & Statistiques
                    </button>
                </div>
            </div>
        </div>

        <!-- Zone de contenu principal -->
        <div id="mainContentArea">
            <!-- Zone de chat -->
            <div id="chatArea">
                <div id="messages"></div>
                <div id="inputGroup">
                    <select id="destinataire">
                        <option value="Tous">Tous</option>
                    </select>
                    <input type="text" id="messageInput" placeholder="Tapez votre message...">
                    <input type="file" id="fileInput" multiple style="display: none;">
                    <button onclick="document.getElementById('fileInput').click()" class="secondary">📎</button>
                    <button onclick="envoyerMessage()" class="primary">Envoyer</button>
                </div>
                <div id="filePreview" class="file-preview"></div>
            </div>

            <!-- Liste des membres -->
            <div id="membersList" class="members-list">
                <h3>Membres en ligne</h3>
                <div id="membersContainer"></div>
            </div>
        </div>

        <!-- Zone du convertisseur -->
        <div id="convertisseur" class="module-zone" style="display: none;">
            <h2>💱 Convertisseur de Devises</h2>
            <div class="converter-form">
                <input type="number" id="montant" placeholder="Montant" step="0.01" min="0">
                <select id="deviseSource">
                    <option value="TND">د.ت TND - Dinar Tunisien</option>
                    <option value="EUR">€ EUR - Euro</option>
                    <option value="USD">$ USD - Dollar US</option>
                    <option value="GBP">£ GBP - Livre Sterling</option>
                    <option value="JPY">¥ JPY - Yen Japonais</option>
                </select>
                <span class="converter-arrow">→</span>
                <select id="deviseCible">
                    <option value="EUR">€ EUR - Euro</option>
                    <option value="TND">د.ت TND - Dinar Tunisien</option>
                    <option value="USD">$ USD - Dollar US</option>
                    <option value="GBP">£ GBP - Livre Sterling</option>
                    <option value="JPY">¥ JPY - Yen Japonais</option>
                </select>
                <button onclick="convertirDevise()" class="primary">💱 Convertir</button>
                <button onclick="inverserDevises()" class="secondary">🔄 Inverser</button>
            </div>
            <div id="resultatConversion" class="conversion-result"></div>
            <div class="currency-info">
                <h4>📊 Taux de change (indicatifs)</h4>
                <div class="rates-grid" id="ratesGrid"></div>
            </div>
        </div>
    </div>

    <!-- Copyright -->
    <div class="copyright">
        <p>&copy; 2025 Gestion Interne IPT. Tous droits réservés N.AMARA.</p>
    </div>

    <script src="assets/js/script.js"></script>
    <script src="assets/js/modules.js"></script>

    <!-- Scripts pour les widgets de gestion -->
    <script src="assets/js/commands-dashboard-widget.js"></script>
    <script src="assets/js/donations-dashboard-widget.js"></script>

    <!-- Script pour les fonctions d'intégration -->
    <script>
        // Fonctions d'intégration pour les widgets

        // Fonction pour basculer la gestion des dons
        function toggleGestionDons() {
            const zone = document.getElementById('gestionDonsZone');

            if (zone && zone.style.display !== 'none') {
                // Si la zone complète est visible, la masquer et afficher le widget
                zone.style.display = 'none';
                if (window.donationsWidget) {
                    donationsWidget.show();
                }
            } else {
                // Sinon, juste basculer le widget
                if (window.donationsWidget) {
                    donationsWidget.toggle();
                }
            }
        }

        // Fonction pour basculer la gestion des commandes
        function toggleGestionCommandes() {
            if (window.commandsWidget) {
                commandsWidget.toggle();
            }
        }

        // Fonctions de compatibilité pour les anciens liens
        function openWorkflowDashboard() {
            window.open('donations-workflow-dashboard.html', '_blank');
        }

        function openDocumentsManager() {
            window.open('donations-documents-manager.html', '_blank');
        }
    </script>

    <!-- Script pour le widget dashboard dons corrigé -->
    <script>
        // ===== CLASSE WIDGET DASHBOARD DONS CORRIGÉE =====
        class DonationsDashboardWidget {
            constructor() {
                this.supabase = null;
                this.isVisible = false;
                this.refreshInterval = null;
                this.realtimeSubscription = null;
                this.data = {
                    totalDons: 0,
                    donsEnCours: 0,
                    donsEnRetard: 0,
                    donsCompletes: 0,
                    userActions: [],
                    workflowSteps: []
                };
            }

            async initialize() {
                try {
                    // Récupérer l'instance Supabase
                    this.supabase = window.supabaseClient || window.supabase;
                    if (!this.supabase) {
                        console.warn('⚠️ Supabase non disponible pour le widget dons');
                        return;
                    }

                    // Charger les données initiales
                    await this.loadData();

                    // Configurer les mises à jour temps réel
                    this.setupRealtimeSubscription();

                    // Configurer le rafraîchissement périodique
                    this.refreshInterval = setInterval(() => {
                        this.loadData();
                    }, 30000); // Toutes les 30 secondes

                    console.log('✅ Widget dashboard dons initialisé');

                } catch (error) {
                    console.error('❌ Erreur initialisation widget dons:', error);
                }
            }

            async loadData() {
                try {
                    await Promise.all([
                        this.loadStatistics(),
                        this.loadUserActions(),
                        this.loadWorkflowStatus()
                    ]);

                    this.updateDisplay();

                } catch (error) {
                    console.error('❌ Erreur chargement données widget:', error);
                }
            }

            async loadStatistics() {
                try {
                    // Simuler des statistiques pour le moment
                    this.data.totalDons = Math.floor(Math.random() * 50) + 10;
                    this.data.donsEnCours = Math.floor(Math.random() * 15) + 5;
                    this.data.donsEnRetard = Math.floor(Math.random() * 5);
                    this.data.donsCompletes = this.data.totalDons - this.data.donsEnCours - this.data.donsEnRetard;

                } catch (error) {
                    console.error('❌ Erreur chargement statistiques:', error);
                    // Valeurs par défaut
                    this.data.totalDons = 0;
                    this.data.donsEnCours = 0;
                    this.data.donsEnRetard = 0;
                    this.data.donsCompletes = 0;
                }
            }

            async loadUserActions() {
                try {
                    // Simuler des actions utilisateur
                    this.data.userActions = [
                        {
                            id: 'DON-001',
                            title: 'Validation requise',
                            description: 'Don DON-001 en attente de validation',
                            urgent: false
                        },
                        {
                            id: 'DON-002',
                            title: 'Document manquant',
                            description: 'BP manquant pour DON-002',
                            urgent: true
                        }
                    ];

                } catch (error) {
                    console.error('❌ Erreur chargement actions utilisateur:', error);
                    this.data.userActions = [];
                }
            }

            async loadWorkflowStatus() {
                try {
                    // Simuler l'état du workflow
                    this.data.workflowSteps = [];
                    for (let i = 1; i <= 10; i++) {
                        let status = 'pending';
                        if (i <= 3) status = 'completed';
                        else if (i === 4) status = 'current';

                        this.data.workflowSteps.push({
                            number: i,
                            status: status,
                            count: Math.floor(Math.random() * 5)
                        });
                    }

                } catch (error) {
                    console.error('❌ Erreur chargement workflow:', error);
                    this.data.workflowSteps = [];
                }
            }

            setupRealtimeSubscription() {
                try {
                    if (!this.supabase) return;

                    // S'abonner aux changements en temps réel
                    this.realtimeSubscription = this.supabase
                        .channel('donations_widget')
                        .on('postgres_changes',
                            { event: '*', schema: 'public', table: 'gestion_donations' },
                            (payload) => {
                                console.log('🔄 Mise à jour temps réel détectée:', payload);
                                setTimeout(() => this.loadData(), 1000);
                            }
                        )
                        .subscribe();

                    console.log('✅ Abonnement temps réel configuré pour le widget dons');

                } catch (error) {
                    console.error('❌ Erreur configuration temps réel:', error);
                }
            }

            updateDisplay() {
                // Mettre à jour les statistiques avec vérification d'existence
                const totalElement = document.getElementById('widgetTotalDons');
                const coursElement = document.getElementById('widgetDonsEnCours');
                const retardElement = document.getElementById('widgetDonsEnRetard');
                const completesElement = document.getElementById('widgetDonsCompletes');

                if (totalElement) totalElement.textContent = this.data.totalDons;
                if (coursElement) coursElement.textContent = this.data.donsEnCours;
                if (retardElement) retardElement.textContent = this.data.donsEnRetard;
                if (completesElement) completesElement.textContent = this.data.donsCompletes;

                // Mettre à jour les autres sections
                this.updateUserActions();
                this.updateWorkflowSteps();
                this.updateNotificationBadge();
            }

            updateNotificationBadge() {
                const badge = document.getElementById('donationsNotificationBadge');
                if (!badge) return;

                const urgentActions = this.data.userActions.filter(action => action.urgent).length;
                const totalUrgent = urgentActions + this.data.donsEnRetard;

                if (totalUrgent > 0) {
                    badge.textContent = totalUrgent;
                    badge.style.display = 'flex';
                    badge.style.background = '#ef4444';
                } else {
                    badge.style.display = 'none';
                }
            }

            updateUserActions() {
                const container = document.getElementById('donationsActionsList');
                if (!container) return;

                if (this.data.userActions.length === 0) {
                    container.innerHTML = '<div class="no-actions">Aucune action requise pour le moment</div>';
                    return;
                }

                container.innerHTML = this.data.userActions.map(action => `
                    <div class="action-item ${action.urgent ? 'urgent' : ''}" onclick="donationsWidget.openDonationDetails('${action.id}')">
                        <div class="action-title">${action.title}</div>
                        <div class="action-description">${action.description}</div>
                    </div>
                `).join('');
            }

            updateWorkflowSteps() {
                const container = document.getElementById('workflowStepsMini');
                if (!container) return;

                container.innerHTML = this.data.workflowSteps.slice(0, 5).map(step => `
                    <div class="workflow-step-mini ${step.status}" title="Étape ${step.number}">
                        ${step.number}
                    </div>
                `).join('');
            }

            show() {
                const widget = document.getElementById('donationsDashboardWidget');
                if (!widget) return;

                widget.style.display = 'block';
                widget.classList.add('show');
                this.isVisible = true;

                // Charger les données si pas encore fait
                if (!this.supabase) {
                    this.initialize();
                } else {
                    this.loadData();
                }
            }

            hide() {
                const widget = document.getElementById('donationsDashboardWidget');
                if (!widget) return;

                widget.style.display = 'none';
                widget.classList.remove('show');
                this.isVisible = false;
            }

            toggle() {
                if (this.isVisible) {
                    this.hide();
                } else {
                    this.show();
                }
            }

            openDonationDetails(donationId) {
                window.open(`donations-management.html?id=${donationId}`, '_blank');
            }

            destroy() {
                if (this.refreshInterval) {
                    clearInterval(this.refreshInterval);
                }

                if (this.realtimeSubscription && this.supabase) {
                    this.supabase.removeChannel(this.realtimeSubscription);
                }
            }
        }

        // Instance globale du widget
        window.donationsWidget = new DonationsDashboardWidget();

        // Fonctions globales pour l'interface
        function toggleDonationsWidget() {
            if (window.donationsWidget) {
                donationsWidget.toggle();
            }
        }

        function refreshDonationsDashboard() {
            donationsWidget.loadData();
        }

        function openDonationsManagement() {
            window.open('donations-management.html', '_blank');
        }

        function openWorkflowDashboard() {
            window.open('donations-workflow-dashboard.html', '_blank');
        }

        function openDocumentsManager() {
            window.open('donations-documents-manager.html', '_blank');
        }

        // Modifier la fonction toggleGestionDons existante pour intégrer le widget
        function toggleGestionDons() {
            const zone = document.getElementById('gestionDonsZone');
            const isVisible = zone.style.display !== 'none';

            if (isVisible) {
                zone.style.display = 'none';
                // Afficher le widget compact à la place
                donationsWidget.show();
            } else {
                zone.style.display = 'block';
                // Masquer le widget si la zone complète est affichée
                donationsWidget.hide();
            }
        }
    </script>
    
    <script>
        // Fonction pour remplir automatiquement les champs de connexion
        function fillLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
        }
        
        // Fonction pour attendre le chargement des configurations
        function waitForConfigs(maxWait = 5000) {
            return new Promise((resolve) => {
                const startTime = Date.now();

                function check() {
                    const configsLoaded = typeof APP_CONFIG !== 'undefined' &&
                                         typeof SUPABASE_CONFIG !== 'undefined';

                    if (configsLoaded || (Date.now() - startTime) > maxWait) {
                        resolve(configsLoaded);
                    } else {
                        setTimeout(check, 100);
                    }
                }

                check();
            });
        }

        // Initialisation de l'application
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initialisation de l\'application...');

            // Attendre le chargement des configurations
            console.log('⏳ Attente du chargement des configurations...');
            const configsLoaded = await waitForConfigs(5000);

            if (configsLoaded) {
                console.log('✅ Configurations chargées:', {
                    APP_CONFIG: typeof APP_CONFIG !== 'undefined',
                    SUPABASE_CONFIG: typeof SUPABASE_CONFIG !== 'undefined'
                });
            } else {
                console.warn('⚠️ Timeout - Certaines configurations peuvent ne pas être chargées');
            }

            // Initialiser Supabase
            if (typeof initializeSupabase === 'function') {
                const { client, available } = await initializeSupabase();

                // Mettre à jour l'indicateur de connexion
                const indicator = document.getElementById('connectionIndicator');
                const text = document.getElementById('connectionText');

                if (available) {
                    indicator.textContent = '🟢';
                    text.textContent = 'Supabase connecté';

                    // Initialiser les widgets de gestion si Supabase est disponible
                    setTimeout(() => {
                        if (window.donationsWidget) {
                            donationsWidget.initialize();
                        }
                        if (window.commandsWidget) {
                            commandsWidget.initialize();
                        }
                    }, 2000); // Attendre 2 secondes pour que l'authentification soit prête
                } else {
                    indicator.textContent = '🟡';
                    text.textContent = 'Mode local';
                }
            } else {
                console.warn('⚠️ initializeSupabase non disponible');
                const indicator = document.getElementById('connectionIndicator');
                const text = document.getElementById('connectionText');
                indicator.textContent = '🔴';
                text.textContent = 'Configuration manquante';
            }

            // Masquer l'indicateur après 3 secondes
            setTimeout(() => {
                document.getElementById('connectionStatus').style.display = 'none';
            }, 3000);
        });
    </script>

    <!-- Script pour la documentation workflow -->
    <script>
        // Gestionnaire de documentation workflow intégré
        class WorkflowDocumentationManager {
            constructor() {
                this.supabase = null;
                this.currentUser = null;
                this.isAdmin = false;
                this.workflowSteps = [];
                this.editMode = false;
            }

            async initialize() {
                console.log('📋 Initialisation du gestionnaire de documentation workflow...');

                try {
                    // Récupérer l'instance Supabase
                    this.supabase = window.supabaseClient || window.supabase;

                    if (this.supabase) {
                        // Récupérer l'utilisateur actuel
                        const { data: { user } } = await this.supabase.auth.getUser();
                        this.currentUser = user;

                        // Vérifier les permissions admin
                        this.checkAdminPermissions();
                    }

                    // Charger les données du workflow
                    this.loadDefaultWorkflowData();

                    // Rendre le tableau
                    this.renderWorkflowTable();

                    console.log('✅ Gestionnaire de documentation workflow initialisé');

                } catch (error) {
                    console.error('❌ Erreur initialisation documentation workflow:', error);
                    this.loadDefaultWorkflowData();
                    this.renderWorkflowTable();
                }
            }

            checkAdminPermissions() {
                if (!this.currentUser) return;

                const authorizedUsers = [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>'
                ];

                const userRole = this.currentUser.user_metadata?.role_ipt;
                this.isAdmin = authorizedUsers.includes(this.currentUser.email) || userRole === 'admin_systeme';

                if (this.isAdmin) {
                    document.getElementById('workflowAdminControls').style.display = 'block';
                    this.showAlert('Accès administrateur activé pour la documentation workflow', 'success');
                }
            }

            loadDefaultWorkflowData() {
                // Configuration officielle IPT - Procédure de Gestion des Dons
                this.workflowSteps = [
                    // PHASE 1: Demande et Autorisation Préalable
                    {
                        step_number: 1,
                        phase: "Phase 1: Demande et Autorisation Préalable",
                        step_name: "Constitution du Dossier Initial",
                        step_description: "Le demandeur/bénéficiaire constitue un dossier complet de demande de don",
                        responsible_role: "demandeur_beneficiaire",
                        input_documents: [
                            "Demande explicative au nom du DG (justification du don, lieu d'affectation)",
                            "Lettre de don du fournisseur/donateur",
                            "Facture (pro-forma ou définitive si disponible)",
                            "Bon de Livraison (BL) prévisionnel du fournisseur/donateur"
                        ],
                        actions: [
                            "Rédiger demande explicative justifiant le don",
                            "Préciser le lieu d'affectation du don",
                            "Rassembler tous les documents du donateur",
                            "Transmettre le dossier complet à la S/D des Achats"
                        ],
                        output_documents: ["Dossier de demande complet", "Accusé de réception"],
                        next_role: "sd_achats"
                    },
                    {
                        step_number: 2,
                        phase: "Phase 1: Demande et Autorisation Préalable",
                        step_name: "Traitement par la S/D des Achats",
                        step_description: "La Sous-Direction des Achats prépare la demande d'autorisation pour le Ministère",
                        responsible_role: "sd_achats",
                        input_documents: [
                            "Dossier complet du demandeur",
                            "Demande d'autorisation de Don préparée"
                        ],
                        actions: [
                            "Examiner le dossier de demande",
                            "Préparer la demande d'autorisation pour le MS",
                            "Transmettre le dossier complet au DG pour validation"
                        ],
                        output_documents: ["Demande d'autorisation MS", "Dossier préparé pour DG"],
                        next_role: "directeur_general"
                    },
                    {
                        step_number: 3,
                        phase: "Phase 1: Demande et Autorisation Préalable",
                        step_name: "Avis Technique DT (Équipements uniquement)",
                        step_description: "La Direction Technique évalue la pertinence et conformité technique des équipements",
                        responsible_role: "direction_technique",
                        equipment_only: true,
                        input_documents: [
                            "Dossier technique de l'équipement",
                            "Spécifications techniques détaillées",
                            "Caractéristiques : Marque, Type, N° Série"
                        ],
                        actions: [
                            "Évaluer la pertinence technique du don d'équipement",
                            "Vérifier la conformité aux standards IPT",
                            "Émettre un avis technique motivé",
                            "Transmettre l'avis au DG"
                        ],
                        output_documents: ["Avis technique DT", "Rapport d'évaluation technique"],
                        next_role: "directeur_general"
                    },
                    {
                        step_number: 4,
                        phase: "Phase 1: Demande et Autorisation Préalable",
                        step_name: "Validation par le Directeur Général",
                        step_description: "Le DG valide le dossier et le transmet au Ministère de la Santé",
                        responsible_role: "directeur_general",
                        input_documents: [
                            "Dossier complet validé",
                            "Avis technique DT (pour équipements)",
                            "Demande d'autorisation MS"
                        ],
                        actions: [
                            "Examiner le dossier complet",
                            "Valider sur la base de l'avis DT (si équipement)",
                            "Transmettre le dossier au Ministère de la Santé"
                        ],
                        output_documents: ["Dossier validé DG", "Transmission officielle au MS"],
                        next_role: "ministere_sante"
                    },
                    {
                        step_number: 5,
                        phase: "Phase 1: Demande et Autorisation Préalable",
                        step_name: "Autorisation du Ministère de la Santé",
                        step_description: "Le MS traite le dossier par commission ministérielle et émet la décision officielle",
                        responsible_role: "ministere_sante",
                        input_documents: [
                            "Dossier complet transmis par IPT",
                            "Décision de la commission ministérielle",
                            "Visa du Ministre"
                        ],
                        actions: [
                            "Traitement par les structures sanitaires compétentes",
                            "Examen par la commission ministérielle",
                            "Émission de la décision officielle",
                            "Transmission de la décision visée par le Ministre à l'IPT"
                        ],
                        output_documents: ["Décision ministérielle officielle", "Visa du Ministre"],
                        next_role: "directeur_general"
                    },
                    {
                        step_number: 6,
                        phase: "Phase 1: Demande et Autorisation Préalable",
                        step_name: "Accord d'Expédition",
                        step_description: "Le DG informe les parties de la décision MS et donne l'accord pour expédition",
                        responsible_role: "directeur_general",
                        input_documents: [
                            "Décision du Ministère de la Santé",
                            "Accord d'expédition"
                        ],
                        actions: [
                            "Informer le demandeur de la décision MS",
                            "Informer le bailleur de fonds ou donateur",
                            "Donner l'accord pour l'expédition du don (si approbation)"
                        ],
                        output_documents: ["Accord d'expédition officiel", "Notifications aux parties"],
                        next_role: "demandeur_beneficiaire"
                    },
                    // PHASE 2: Livraison et Réception Physique
                    {
                        step_number: 7,
                        phase: "Phase 2: Livraison et Réception Physique",
                        step_name: "Établissement du Bon de Prélèvement",
                        step_description: "Le bénéficiaire établit le BP pour le don attendu et le transmet au magasin",
                        responsible_role: "demandeur_beneficiaire",
                        input_documents: ["Accord d'expédition", "Informations sur le don attendu"],
                        actions: [
                            "Établir un Bon de Prélèvement (BP) pour le don attendu",
                            "Spécifier les détails du don",
                            "Transmettre le BP au magasin"
                        ],
                        output_documents: ["Bon de Prélèvement (BP)", "Transmission au magasin"],
                        next_role: "receptionniste_magasin"
                    },
                    {
                        step_number: 8,
                        phase: "Phase 2: Livraison et Réception Physique",
                        step_name: "Réception Physique du Don",
                        step_description: "Le réceptionniste du magasin réceptionne et vérifie la conformité de la livraison",
                        responsible_role: "receptionniste_magasin",
                        input_documents: [
                            "Lettre de don (originale ou copie certifiée)",
                            "Facture définitive du fournisseur/donateur",
                            "Bon de Livraison (BL) du fournisseur/donateur",
                            "BP du bénéficiaire"
                        ],
                        actions: [
                            "Réceptionner le don physiquement",
                            "Vérifier la conformité de la livraison par rapport au BL fournisseur",
                            "S'assurer que le BL contient les caractéristiques nécessaires (Marque, Type, NS pour équipements)",
                            "Signer le BL fournisseur",
                            "Exiger le BP du bénéficiaire avant livraison"
                        ],
                        output_documents: ["BL fournisseur signé", "Rapport de réception", "Vérification de conformité"],
                        next_role: "magasinier"
                    },
                    // PHASE 3: Enregistrement, Affectation et Clôture
                    {
                        step_number: 9,
                        phase: "Phase 3: Enregistrement, Affectation et Clôture",
                        step_name: "Livraison au Bénéficiaire (Articles)",
                        step_description: "Le magasinier vérifie les documents et livre l'article au bénéficiaire",
                        responsible_role: "magasinier",
                        article_only: true,
                        input_documents: [
                            "Dossier de don complet (Lettre de don, Facture, BL fournisseur)",
                            "BP du bénéficiaire"
                        ],
                        actions: [
                            "Vérifier la présence du dossier de don complet",
                            "Procéder à la livraison physique de l'article au bénéficiaire sur présentation du BP",
                            "Établir un Bon de Sortie (BS)",
                            "Classer le dossier complet (Lettre de don, Facture, BL, BP, copie BS)"
                        ],
                        output_documents: ["Bon de Sortie (BS)", "Dossier classé", "Livraison effectuée"],
                        next_role: "archive"
                    },
                    {
                        step_number: 10,
                        phase: "Phase 3: Enregistrement, Affectation et Clôture",
                        step_name: "Enregistrement RVE (Équipements)",
                        step_description: "Le RVE enregistre l'équipement dans STKMAG, attribue NI, affecte et étiquette",
                        responsible_role: "rve",
                        equipment_only: true,
                        input_documents: [
                            "Dossier de don complet (Lettre de don, Facture, BL fournisseur)",
                            "BP du bénéficiaire"
                        ],
                        actions: [
                            "Procéder à la réception de l'équipement sur l'application STKMAG",
                            "Attribuer un Numéro d'Inventaire (NI) à l'équipement",
                            "Affecter l'équipement au service destinataire (selon le BP)",
                            "Valider l'affectation dans STKMAG",
                            "Imprimer le Bon de Sortie (BS) depuis STKMAG",
                            "Imprimer l'étiquette à code-barres et s'assurer de son apposition sur l'équipement",
                            "Classer le dossier complet",
                            "Transmettre les informations/documents nécessaires à la Comptabilité et au Contrôle de Gestion (CG)"
                        ],
                        output_documents: [
                            "Réception STKMAG",
                            "Numéro d'Inventaire (NI)",
                            "Bon de Sortie (BS) STKMAG",
                            "Étiquette code-barres",
                            "Dossier complet classé (Lettre de don, Facture, BL, BP, copie BS, justificatif d'inventaire)",
                            "Tableau récapitulatif des dons + copie BS/NI pour Comptabilité/CG"
                        ],
                        next_role: "archive"
                    }
                ];
            }

            renderWorkflowTable() {
                const tbody = document.getElementById('workflowDocumentationTableBody');
                if (!tbody) return;

                tbody.innerHTML = this.workflowSteps.map(step => this.renderStepRow(step)).join('');
            }

            renderStepRow(step) {
                const roleClass = step.responsible_role.toLowerCase();
                const roleName = this.getRoleName(step.responsible_role);
                const nextRoleName = step.next_role === 'archive' ? 'Archivage' : this.getRoleName(step.next_role);

                // Indicateurs spéciaux pour équipements/articles
                let specialIndicators = '';
                if (step.equipment_only) {
                    specialIndicators += '<span class="workflow-equipment-only">🔧 Équipements uniquement</span>';
                }
                if (step.article_only) {
                    specialIndicators += '<span class="workflow-article-only">📦 Articles uniquement</span>';
                }

                return `
                    <tr>
                        <td>
                            <div class="workflow-step-number">${step.step_number}</div>
                            ${step.phase ? `<div class="workflow-phase-badge">${step.phase}</div>` : ''}
                        </td>
                        <td>
                            <div class="workflow-step-name">${step.step_name}</div>
                            <div class="workflow-step-description">${step.step_description}</div>
                            ${specialIndicators}
                        </td>
                        <td>
                            <div class="workflow-role-badge ${roleClass}">${roleName}</div>
                        </td>
                        <td>
                            <ul class="workflow-document-list">
                                ${step.input_documents.map(doc => `<li class="input">${doc}</li>`).join('')}
                            </ul>
                        </td>
                        <td>
                            <ul class="workflow-action-list">
                                ${step.actions.map(action => `<li>${action}</li>`).join('')}
                            </ul>
                        </td>
                        <td>
                            <ul class="workflow-document-list">
                                ${step.output_documents.map(doc => `<li class="output">${doc}</li>`).join('')}
                            </ul>
                        </td>
                        <td>
                            <div class="workflow-next-role">${nextRoleName}</div>
                        </td>
                    </tr>
                `;
            }

            getRoleName(role) {
                const roleNames = {
                    // Rôles officiels IPT selon la procédure
                    'demandeur_beneficiaire': 'Demandeur/Bénéficiaire',
                    'sd_achats': 'S/D des Achats (Unité Transit)',
                    'direction_technique': 'Direction Technique (DT)',
                    'directeur_general': 'Directeur Général (DG)',
                    'ministere_sante': 'Ministère de la Santé (MS)',
                    'receptionniste_magasin': 'Réceptionniste du Magasin',
                    'magasinier': 'Magasinier',
                    'rve': 'Responsable du Volet Équipement (RVE)',

                    // Rôles legacy pour compatibilité
                    'demandeur': 'Demandeur',
                    'beneficiaire': 'Bénéficiaire',
                    'transitaire': 'Transitaire',
                    'receptionniste': 'Réceptionniste',
                    'archive': 'Archivage'
                };
                return roleNames[role] || role;
            }

            showAlert(message, type = 'info') {
                // Utiliser la fonction showAlert globale si disponible
                if (typeof showAlert === 'function') {
                    showAlert(message, type);
                } else {
                    console.log(`${type.toUpperCase()}: ${message}`);
                }
            }
        }

        // Instance globale du gestionnaire de documentation workflow
        window.workflowDocumentationManager = new WorkflowDocumentationManager();

        // Fonctions globales pour les contrôles administrateur
        function addNewWorkflowStep() {
            if (!workflowDocumentationManager.isAdmin) {
                alert('Accès non autorisé');
                return;
            }
            alert('Fonctionnalité d\'ajout d\'étape - À implémenter');
        }

        function validateAllWorkflowSteps() {
            if (!workflowDocumentationManager.isAdmin) {
                alert('Accès non autorisé');
                return;
            }
            alert('✅ Workflow validé avec succès - Aucun problème détecté');
        }

        function editWorkflowMode() {
            if (!workflowDocumentationManager.isAdmin) {
                alert('Accès non autorisé');
                return;
            }
            alert('Mode édition - Fonctionnalité à implémenter');
        }

        function resetWorkflowToDefault() {
            if (!workflowDocumentationManager.isAdmin) {
                alert('Accès non autorisé');
                return;
            }
            if (confirm('Êtes-vous sûr de vouloir réinitialiser le workflow ?')) {
                workflowDocumentationManager.loadDefaultWorkflowData();
                workflowDocumentationManager.renderWorkflowTable();
                alert('Workflow réinitialisé aux valeurs par défaut');
            }
        }

        function exportWorkflowDocumentation() {
            // Générer un export PDF du workflow
            const printWindow = window.open('', '_blank');
            const tableHTML = document.querySelector('.workflow-table-container').outerHTML;

            printWindow.document.write(`
                <html>
                    <head>
                        <title>Documentation Workflow - Gestion des Dons IPT</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 20px; }
                            table { width: 100%; border-collapse: collapse; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                            th { background-color: #f2f2f2; }
                            .workflow-step-number { background: #4299e1; color: white; padding: 5px; border-radius: 50%; }
                            .workflow-role-badge { background: #48bb78; color: white; padding: 2px 8px; border-radius: 10px; }
                        </style>
                    </head>
                    <body>
                        <h1>Documentation Workflow - Gestion des Dons IPT</h1>
                        <p>Institut Pasteur de Tunis - Processus complet en 10 étapes</p>
                        ${tableHTML}
                    </body>
                </html>
            `);

            printWindow.document.close();
            printWindow.print();
        }

        // ===== GESTION DU TABLEAU DE SAISIE DES ÉTAPES =====
        let isEditingSteps = false;
        let editingStepsData = [];

        function toggleStepsEditingTable() {
            if (!workflowDocumentationManager.isAdmin) {
                alert('Accès non autorisé');
                return;
            }

            const editingTable = document.getElementById('stepsEditingTable');
            const isVisible = editingTable.style.display !== 'none';

            if (isVisible) {
                // Masquer le tableau
                editingTable.style.display = 'none';
                isEditingSteps = false;
            } else {
                // Afficher le tableau et charger les données
                editingTable.style.display = 'block';
                loadStepsForEditing();
                isEditingSteps = true;
            }
        }

        function loadStepsForEditing() {
            // Copier les données actuelles du workflow
            editingStepsData = JSON.parse(JSON.stringify(workflowDocumentationManager.workflowSteps));
            renderStepsEditingTable();
        }

        function renderStepsEditingTable() {
            const tbody = document.getElementById('stepsEditingTableBody');
            if (!tbody) return;

            tbody.innerHTML = editingStepsData.map((step, index) => renderStepEditingRow(step, index)).join('');
        }

        function renderStepEditingRow(step, index) {
            const roles = ['beneficiaire', 'magasinier', 'transitaire', 'receptionniste', 'rve'];
            const roleOptions = roles.map(role =>
                `<option value="${role}" ${step.responsible_role === role ? 'selected' : ''}>${workflowDocumentationManager.getRoleName(role)}</option>`
            ).join('');

            const nextRoleOptions = [...roles, 'archive'].map(role =>
                `<option value="${role}" ${step.next_role === role ? 'selected' : ''}>${role === 'archive' ? 'Archivage' : workflowDocumentationManager.getRoleName(role)}</option>`
            ).join('');

            return `
                <tr data-index="${index}">
                    <td>
                        <input type="number" class="step-input step-number-input"
                               value="${step.step_number}"
                               onchange="updateStepData(${index}, 'step_number', this.value)"
                               min="1" max="20">
                    </td>
                    <td>
                        <input type="text" class="step-input"
                               value="${step.step_name}"
                               onchange="updateStepData(${index}, 'step_name', this.value)"
                               placeholder="Nom de l'étape">
                    </td>
                    <td>
                        <textarea class="step-textarea"
                                  onchange="updateStepData(${index}, 'step_description', this.value)"
                                  placeholder="Description détaillée">${step.step_description}</textarea>
                    </td>
                    <td>
                        <select class="step-select" onchange="updateStepData(${index}, 'responsible_role', this.value)">
                            <option value="">Sélectionner</option>
                            ${roleOptions}
                        </select>
                    </td>
                    <td>
                        <textarea class="step-textarea"
                                  onchange="updateStepData(${index}, 'input_documents', this.value.split('\\n').filter(d => d.trim()))"
                                  placeholder="Un document par ligne">${step.input_documents.join('\n')}</textarea>
                    </td>
                    <td>
                        <textarea class="step-textarea"
                                  onchange="updateStepData(${index}, 'actions', this.value.split('\\n').filter(a => a.trim()))"
                                  placeholder="Une action par ligne">${step.actions.join('\n')}</textarea>
                    </td>
                    <td>
                        <textarea class="step-textarea"
                                  onchange="updateStepData(${index}, 'output_documents', this.value.split('\\n').filter(d => d.trim()))"
                                  placeholder="Un document par ligne">${step.output_documents.join('\n')}</textarea>
                    </td>
                    <td>
                        <select class="step-select" onchange="updateStepData(${index}, 'next_role', this.value)">
                            <option value="">Sélectionner</option>
                            ${nextRoleOptions}
                        </select>
                    </td>
                    <td class="step-actions-cell">
                        <button class="step-action-btn duplicate" onclick="duplicateStep(${index})" title="Dupliquer">
                            📋
                        </button>
                        <button class="step-action-btn delete" onclick="deleteStep(${index})" title="Supprimer">
                            🗑️
                        </button>
                    </td>
                </tr>
            `;
        }

        function updateStepData(index, field, value) {
            if (editingStepsData[index]) {
                editingStepsData[index][field] = value;
                validateStepRow(index);
            }
        }

        function validateStepRow(index) {
            const row = document.querySelector(`tr[data-index="${index}"]`);
            if (!row) return;

            const step = editingStepsData[index];
            let hasErrors = false;

            // Validation des champs obligatoires
            const requiredFields = ['step_name', 'step_description', 'responsible_role'];
            requiredFields.forEach(field => {
                const input = row.querySelector(`[onchange*="${field}"]`);
                if (input && (!step[field] || step[field].toString().trim() === '')) {
                    input.classList.add('step-validation-error');
                    hasErrors = true;
                } else if (input) {
                    input.classList.remove('step-validation-error');
                }
            });

            // Validation des actions (au moins une)
            if (!step.actions || step.actions.length === 0) {
                const actionsInput = row.querySelector(`[onchange*="actions"]`);
                if (actionsInput) {
                    actionsInput.classList.add('step-validation-error');
                    hasErrors = true;
                }
            }

            return !hasErrors;
        }

        function addNewStepRow() {
            const newStep = {
                step_number: editingStepsData.length + 1,
                step_name: '',
                step_description: '',
                responsible_role: '',
                input_documents: [],
                actions: [],
                output_documents: [],
                next_role: ''
            };

            editingStepsData.push(newStep);
            renderStepsEditingTable();

            // Faire défiler vers la nouvelle ligne
            const tbody = document.getElementById('stepsEditingTableBody');
            if (tbody) {
                tbody.lastElementChild.scrollIntoView({ behavior: 'smooth' });
            }
        }

        function duplicateStep(index) {
            const originalStep = editingStepsData[index];
            const duplicatedStep = JSON.parse(JSON.stringify(originalStep));
            duplicatedStep.step_number = editingStepsData.length + 1;
            duplicatedStep.step_name += ' (Copie)';

            editingStepsData.splice(index + 1, 0, duplicatedStep);
            renderStepsEditingTable();
        }

        function deleteStep(index) {
            if (editingStepsData.length <= 1) {
                alert('Impossible de supprimer la dernière étape');
                return;
            }

            if (confirm('Êtes-vous sûr de vouloir supprimer cette étape ?')) {
                editingStepsData.splice(index, 1);

                // Renuméroter les étapes
                editingStepsData.forEach((step, i) => {
                    step.step_number = i + 1;
                });

                renderStepsEditingTable();
            }
        }

        function saveAllSteps() {
            // Valider toutes les étapes
            let hasErrors = false;
            for (let i = 0; i < editingStepsData.length; i++) {
                if (!validateStepRow(i)) {
                    hasErrors = true;
                }
            }

            if (hasErrors) {
                alert('⚠️ Veuillez corriger les erreurs de validation avant de sauvegarder');
                return;
            }

            // Vérifier la cohérence du workflow
            const validationResult = validateWorkflowCoherence();
            if (!validationResult.isValid) {
                alert('⚠️ Problèmes de cohérence détectés :\n' + validationResult.errors.join('\n'));
                return;
            }

            // Sauvegarder les modifications
            workflowDocumentationManager.workflowSteps = JSON.parse(JSON.stringify(editingStepsData));
            workflowDocumentationManager.renderWorkflowTable();

            // Masquer le tableau d'édition
            document.getElementById('stepsEditingTable').style.display = 'none';
            isEditingSteps = false;

            alert('✅ Étapes sauvegardées avec succès !');
        }

        function validateWorkflowCoherence() {
            const errors = [];

            // Vérifier la numérotation séquentielle
            for (let i = 0; i < editingStepsData.length; i++) {
                if (editingStepsData[i].step_number !== i + 1) {
                    errors.push(`Étape ${i + 1}: Numérotation incorrecte`);
                }
            }

            // Vérifier la cohérence des rôles suivants
            for (let i = 0; i < editingStepsData.length - 1; i++) {
                const currentStep = editingStepsData[i];
                const nextStep = editingStepsData[i + 1];

                if (currentStep.next_role !== nextStep.responsible_role && currentStep.next_role !== 'archive') {
                    errors.push(`Étape ${i + 1}: Le responsable suivant ne correspond pas à l'étape suivante`);
                }
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        function cancelStepsEditing() {
            if (confirm('Êtes-vous sûr de vouloir annuler les modifications ?')) {
                document.getElementById('stepsEditingTable').style.display = 'none';
                isEditingSteps = false;
                editingStepsData = [];
            }
        }

        // Fonction pour afficher l'onglet documentation
        function showDonationsTab(tabName) {
            // Masquer tous les onglets
            const tabs = document.querySelectorAll('.donations-tab-content');
            tabs.forEach(tab => tab.style.display = 'none');

            // Désactiver tous les boutons d'onglet
            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(button => button.classList.remove('active'));

            // Afficher l'onglet sélectionné
            const selectedTab = document.getElementById(`donations${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Tab`);
            if (selectedTab) {
                selectedTab.style.display = 'block';
            }

            // Activer le bouton correspondant
            const selectedButton = document.getElementById(`tab${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`);
            if (selectedButton) {
                selectedButton.classList.add('active');
            }

            // Initialiser selon l'onglet
            if (tabName === 'documentation' && window.workflowDocumentationManager) {
                setTimeout(() => {
                    workflowDocumentationManager.initialize();
                }, 100);
            } else if (tabName === 'nouveau') {
                initializeNewDonationForm();
            }
        }

        // ===== GESTION DU FORMULAIRE NOUVEAU DON =====
        let currentDonationStep = 1;
        const maxDonationSteps = 3;

        function initializeNewDonationForm() {
            // Générer un nouveau numéro de don
            generateDonationNumber();

            // Réinitialiser le formulaire
            resetDonationForm();

            // Afficher la première étape
            showDonationStep(1);
        }

        function generateDonationNumber() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const time = String(now.getHours()).padStart(2, '0') + String(now.getMinutes()).padStart(2, '0');

            const donationNumber = `DON-${year}${month}${day}-${time}`;
            document.getElementById('donationNumber').value = donationNumber;
        }

        function showDonationStep(stepNumber) {
            // Masquer toutes les étapes
            for (let i = 1; i <= maxDonationSteps; i++) {
                const step = document.getElementById(`donationStep${i}`);
                if (step) {
                    step.style.display = i === stepNumber ? 'block' : 'none';
                }

                // Mettre à jour les indicateurs d'étapes
                const stepIndicator = document.getElementById(`step${i}`);
                if (stepIndicator) {
                    stepIndicator.classList.toggle('active', i === stepNumber);
                    stepIndicator.classList.toggle('completed', i < stepNumber);
                }
            }

            // Mettre à jour les boutons
            const prevBtn = document.getElementById('prevStepBtn');
            const nextBtn = document.getElementById('nextStepBtn');
            const submitBtn = document.getElementById('submitDonationBtn');

            if (prevBtn) prevBtn.style.display = stepNumber > 1 ? 'inline-block' : 'none';
            if (nextBtn) nextBtn.style.display = stepNumber < maxDonationSteps ? 'inline-block' : 'none';
            if (submitBtn) submitBtn.style.display = stepNumber === maxDonationSteps ? 'inline-block' : 'none';

            currentDonationStep = stepNumber;

            // Actions spécifiques par étape
            if (stepNumber === 3) {
                updateDonationSummary();
            }
        }

        function nextDonationStep() {
            if (validateCurrentDonationStep()) {
                if (currentDonationStep < maxDonationSteps) {
                    showDonationStep(currentDonationStep + 1);
                }
            }
        }

        function previousDonationStep() {
            if (currentDonationStep > 1) {
                showDonationStep(currentDonationStep - 1);
            }
        }

        function validateCurrentDonationStep() {
            const currentStepElement = document.getElementById(`donationStep${currentDonationStep}`);
            if (!currentStepElement) return false;

            const requiredFields = currentStepElement.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.style.borderColor = '#e53e3e';
                    isValid = false;
                } else {
                    field.style.borderColor = '#e2e8f0';
                }
            });

            if (!isValid) {
                alert('Veuillez remplir tous les champs obligatoires');
            }

            return isValid;
        }

        function toggleEquipmentFields() {
            const donationType = document.getElementById('donationType').value;
            const equipmentFields = document.getElementById('equipmentFields');

            if (equipmentFields) {
                equipmentFields.style.display = donationType === 'equipement' ? 'block' : 'none';
            }
        }

        function updateDonationSummary() {
            const summary = document.getElementById('donationSummary');
            if (!summary) return;

            const formData = {
                number: document.getElementById('donationNumber').value,
                type: document.getElementById('donationType').value,
                donor: document.getElementById('donorName').value,
                contact: document.getElementById('donorContact').value,
                service: document.getElementById('beneficiaryService').value,
                reason: document.getElementById('donationReason').value,
                brand: document.getElementById('equipmentBrand').value,
                equipmentType: document.getElementById('equipmentType').value,
                serial: document.getElementById('equipmentSerial').value
            };

            let summaryHTML = `
                <div class="summary-item"><strong>N° Don:</strong> ${formData.number}</div>
                <div class="summary-item"><strong>Type:</strong> ${formData.type === 'article' ? '📦 Article' : '🔧 Équipement'}</div>
                <div class="summary-item"><strong>Donateur:</strong> ${formData.donor}</div>
                <div class="summary-item"><strong>Contact:</strong> ${formData.contact}</div>
                <div class="summary-item"><strong>Service Bénéficiaire:</strong> ${formData.service}</div>
                <div class="summary-item"><strong>Raison:</strong> ${formData.reason}</div>
            `;

            if (formData.type === 'equipement') {
                summaryHTML += `
                    <div class="summary-item"><strong>Marque:</strong> ${formData.brand}</div>
                    <div class="summary-item"><strong>Type Équipement:</strong> ${formData.equipmentType}</div>
                    <div class="summary-item"><strong>N° Série:</strong> ${formData.serial}</div>
                `;
            }

            summary.innerHTML = summaryHTML;
        }

        async function submitDonation() {
            if (!validateCurrentDonationStep()) {
                return;
            }

            try {
                // Collecter les données du formulaire
                const donationData = collectDonationFormData();

                // Simuler la soumission (à remplacer par l'appel Supabase)
                console.log('Soumission du don:', donationData);

                // Afficher un message de succès
                alert('✅ Don soumis avec succès!\n\nNuméro de don: ' + donationData.number + '\n\nVous recevrez une confirmation par email.');

                // Réinitialiser le formulaire
                resetDonationForm();

                // Retourner à la liste des dons
                showDonationsTab('liste');

            } catch (error) {
                console.error('Erreur soumission don:', error);
                alert('❌ Erreur lors de la soumission du don. Veuillez réessayer.');
            }
        }

        function collectDonationFormData() {
            return {
                number: document.getElementById('donationNumber').value,
                type: document.getElementById('donationType').value,
                donor: document.getElementById('donorName').value,
                contact: document.getElementById('donorContact').value,
                service: document.getElementById('beneficiaryService').value,
                reason: document.getElementById('donationReason').value,
                brand: document.getElementById('equipmentBrand').value,
                equipmentType: document.getElementById('equipmentType').value,
                serial: document.getElementById('equipmentSerial').value,
                notes: document.getElementById('donationNotes').value,
                createdAt: new Date().toISOString()
            };
        }

        function resetDonationForm() {
            const form = document.getElementById('donationForm');
            if (form) {
                form.reset();
            }

            // Masquer les champs équipement
            const equipmentFields = document.getElementById('equipmentFields');
            if (equipmentFields) {
                equipmentFields.style.display = 'none';
            }

            // Retourner à la première étape
            showDonationStep(1);

            // Générer un nouveau numéro
            generateDonationNumber();
        }
    </script>
</body>
</html>
