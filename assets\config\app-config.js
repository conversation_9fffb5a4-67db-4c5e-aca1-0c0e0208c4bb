// Configuration de l'application
const APP_CONFIG = {
    // Informations générales
    app: {
        name: 'Gestion Interne Entreprise',
        version: '1.0.0',
        language: 'fr',
        timezone: 'Europe/Paris',
        description: 'Application de gestion interne avec messagerie, commandes et PV'
    },
    
    // Configuration des rôles et permissions
    roles: {
        admin: {
            name: 'Administrateur',
            permissions: ['all'],
            color: '#dc3545',
            description: 'Accès complet à toutes les fonctionnalités'
        },
        manager: {
            name: 'Manager',
            permissions: ['commands', 'analysis', 'chat', 'converter'],
            color: '#28a745',
            description: 'Gestion des commandes et analyse'
        },
        finance: {
            name: 'Finance',
            permissions: ['commands', 'chat', 'converter'],
            color: '#ffc107',
            description: 'Gestion des commandes fournisseurs'
        },
        user: {
            name: 'Utilisateur',
            permissions: ['chat', 'converter'],
            color: '#007bff',
            description: 'Messagerie et convertisseur'
        },
        depot: {
            name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            permissions: ['pv', 'chat', 'converter'],
            color: '#6f42c1',
            description: 'Gestion des PV de dépôts'
        },
        hygiene: {
            name: 'Hygiène',
            permissions: ['pv_view', 'chat', 'converter'],
            color: '#fd7e14',
            description: 'Consultation des PV'
        }
    },
    
    // Utilisateurs réels de l'entreprise
    // !!! ATTENTION : NON SÉCURISÉ - POUR DÉMO UNIQUEMENT !!!
    demoUsers: {
        // Admins (Peut tout faire)
        "admin123": { password: "admin123*+", couleur: "#4a90e2", role: "admin", actif: true },

        // Managers (Gère commandes, chatte librement)
        "rym": { password: "rym", couleur: "#8e44ad", role: "manager", actif: true },
        "riadh": { password: "riadh", couleur: "#f39c12", role: "manager", actif: true },

        // Utilisateurs Finance (Consulte commandes, voit messages ciblés 'Finance', PAS de chat, PAS de PV)
        "besma": { password: "besma", couleur: "#27ae60", role: "finance", actif: true },
        "moufida": { password: "moufida", couleur: "#16a085", role: "finance", actif: true },
        "ichark": { password: "ichark", couleur: "#1abc9c", role: "finance", actif: true },

        // Utilisateurs Standard (Chatte librement, consulte commandes)
        "rsoltani": { password: "rsoltani", couleur: "#9b59b6", role: "user", actif: true },
        "namara": { password: "namara", couleur: "#e2b84a", role: "user", actif: true },
        "bjlassi": { password: "bjlassi", couleur: "#2ecc71", role: "user", actif: true },
        "syousfi": { password: "syousfi", couleur: "#d35400", role: "user", actif: true },
        "mbaissa": { password: "mbaissa", couleur: "#7f8c8d", role: "user", actif: true },
        "sghouili": { password: "sghouili", couleur: "#e24a4a", role: "user", actif: true },
        "ghaith": { password: "ghaith", couleur: "#4ae24a", role: "user", actif: true },
        "issam": { password: "issam", couleur: "#3498db", role: "user", actif: true },
        "bbeji": { password: "beji", couleur: "#2ecc71", role: "user", actif: true },
        "ichikaoui": { password: "ichikaoui", couleur: "#e74c3c", role: "user", actif: true },
        "sdouagi": { password: "sdouagi", couleur: "#d35400", role: "user", actif: true },
        "mhelal": { password: "mhelal", couleur: "#1abc9c", role: "user", actif: true },
        "skoudhai": { password: "skoudhai", couleur: "#c0392b", role: "user", actif: true },
        "asediri": { password: "sediri", couleur: "#8e44ad", role: "user", actif: true },
        "ssalama": { password: "ssalama", couleur: "#2980b9", role: "user", actif: true },
        "zboubaker": { password: "zboubaker", couleur: "#7f8c8d", role: "user", actif: true },
        "invite": { password: "invite", couleur: "#34495e", role: "user", actif: true },

        // Responsables Hygiène (Consulte PV, reçoit notifs PV)
        "yosra": { password: "yosra2025*+", couleur: "#ff69b4", role: "hygiene", actif: true },
        "moncef": { password: "moncef2025*+", couleur: "#4682b4", role: "hygiene", actif: true },

        // Dépôts (Crée PV, envoie notifs PV, consulte/exporte tous les PV)
        "10": { password: "10012025*+", role: "depot", couleur: "#3498db", actif: true },
        "43": { password: "43022025*+", role: "depot", couleur: "#e67e22", actif: true },
        "44": { password: "44032025*+", role: "depot", couleur: "#2ecc71", actif: true },
        "45": { password: "45042025*+", role: "depot", couleur: "#f1c40f", actif: true },
        "46": { password: "46052025*+", role: "depot", couleur: "#9b59b6", actif: true },
        "47": { password: "47062025*+", role: "depot", couleur: "#e74c3c", actif: true },
        "48": { password: "48072025*+", role: "depot", couleur: "#1abc9c", actif: true },
        "49": { password: "49082025*+", role: "depot", couleur: "#34495e", actif: true },
        "50": { password: "50092025*+", role: "depot", couleur: "#d35400", actif: true },
        "51": { password: "51102025*+", role: "depot", couleur: "#c0392b", actif: true },
        "52": { password: "52122025*+", role: "depot", couleur: "#8e44ad", actif: true },
        "53": { password: "53132025*+", role: "depot", couleur: "#2980b9", actif: true },
        "54": { password: "54142025*+", role: "depot", couleur: "#16a085", actif: true },
        "55": { password: "55152025*+", role: "depot", couleur: "#27ae60", actif: true },
        "56": { password: "56162025*+", role: "depot", couleur: "#f39c12", actif: true },
        "57": { password: "57172025*+", role: "depot", couleur: "#4a90e2", actif: true },
        "58": { password: "58182025*+", role: "depot", couleur: "#7f8c8d", actif: true },
        "59": { password: "59192025*+", role: "depot", couleur: "#e2b84a", actif: true },
        "60": { password: "60202025*+", role: "depot", couleur: "#9b59b6", actif: true },
        "61": { password: "61212025*+", role: "depot", couleur: "#e74c3c", actif: true },
        "62": { password: "62222025*+", role: "depot", couleur: "#1abc9c", actif: true },
        "63": { password: "63232025*+", role: "depot", couleur: "#34495e", actif: true },
        "64": { password: "64242025*+", role: "depot", couleur: "#d35400", actif: true },
        "65": { password: "65252025*+", role: "depot", couleur: "#c0392b", actif: true },
        "66": { password: "66262025*+", role: "depot", couleur: "#8e44ad", actif: true },
        "67": { password: "67272025*+", role: "depot", couleur: "#2980b9", actif: true },
        "68": { password: "68282025*+", role: "depot", couleur: "#16a085", actif: true },
        "69": { password: "69292025*+", role: "depot", couleur: "#27ae60", actif: true },
        "70": { password: "70302025*+", role: "depot", couleur: "#f39c12", actif: true },
        "71": { password: "71312025*+", role: "depot", couleur: "#4a90e2", actif: true },
        "72": { password: "72322025*+", role: "depot", couleur: "#7f8c8d", actif: true },
        "73": { password: "73332025*+", role: "depot", couleur: "#e2b84a", actif: true },
        "74": { password: "74342025*+", role: "depot", couleur: "#9b59b6", actif: true },
        "75": { password: "75352025*+", role: "depot", couleur: "#e74c3c", actif: true },
        "76": { password: "76362025*+", role: "depot", couleur: "#1abc9c", actif: true },
        "77": { password: "77372025*+", role: "depot", couleur: "#34495e", actif: true },
        "78": { password: "78382025*+", role: "depot", couleur: "#d35400", actif: true },
        "79": { password: "79392025*+", role: "depot", couleur: "#c0392b", actif: true },
        "80": { password: "80402025*+", role: "depot", couleur: "#8e44ad", actif: true },
        "81": { password: "81412025*+", role: "depot", couleur: "#2980b9", actif: true },
        "82": { password: "82422025*+", role: "depot", couleur: "#16a085", actif: true },
        "83": { password: "83432025*+", role: "depot", couleur: "#27ae60", actif: true },
        "84": { password: "84442025*+", role: "depot", couleur: "#f39c12", actif: true },
        "85": { password: "85452025*+", role: "depot", couleur: "#4a90e2", actif: true },
        "86": { password: "86462025*+", role: "depot", couleur: "#7f8c8d", actif: true },
        "87": { password: "87472025*+", role: "depot", couleur: "#e2b84a", actif: true },
        "88": { password: "88482025*+", role: "depot", couleur: "#9b59b6", actif: true },
        "89": { password: "89492025*+", role: "depot", couleur: "#e74c3c", actif: true },
        "90": { password: "90502025*+", role: "depot", couleur: "#1abc9c", actif: true },
        "91": { password: "91512025*+", role: "depot", couleur: "#34495e", actif: true },
        "92": { password: "92522025*+", role: "depot", couleur: "#d35400", actif: true },
        "93": { password: "93532025*+", role: "depot", couleur: "#c0392b", actif: true },
        "94": { password: "94542025*+", role: "depot", couleur: "#8e44ad", actif: true }
    },
    
    // Produits par défaut pour les PV (Liste complète IPT)
    defaultProducts: [
        { id: 10012002, name: "ACIDE HIPPURIQUE P.A", expiryDate: null },
        { id: 10012402, name: "ACIDE ISONICOTINISUE P.A", expiryDate: null },
        { id: 10012703, name: "GLASS BEADS ACID WASHED", expiryDate: null },
        { id: 10013001, name: "ACIDE MOLYBDIQUE PUR", expiryDate: null },
        { id: 10013002, name: "ACIDE MOLYBDIQUE P.A", expiryDate: null },
        { id: 10013202, name: "ACIDE N-BUTYRIQUE P.A", expiryDate: null },
        { id: 10013301, name: "ACIDE NAPTHOQUINONE N1-2", expiryDate: null },
        { id: 10013502, name: "ACIDE NITRIQUE PA", expiryDate: null },
        { id: 10013602, name: "ACIDE OCTANOIQUE P.A 250ML", expiryDate: null },
        { id: 10013906, name: "ACIDE ORTHOPHOSPHORIQUE 85%", expiryDate: null },
        { id: 10014001, name: "ACIDE OSMIQUE PUR", expiryDate: null },
        { id: 10014102, name: "ACIDE OXALIQUE P.A", expiryDate: null },
        { id: 10014302, name: "ACIDE PERIODIQUE", expiryDate: null },
        { id: 10014602, name: "PHOSPHOTUNGSTIC ACID", expiryDate: null },
        { id: 10014901, name: "ACIDE PYRROLIDINE-1", expiryDate: null },
        { id: 10015402, name: "ACIDE SUCCINIQUE P.A", expiryDate: null },
        { id: 10015501, name: "ACIDE SULFANILIQUE PUR", expiryDate: null },
        { id: 10015601, name: "ACIDE SULFOSALICYLIQUE PUR", expiryDate: null },
        { id: 10015702, name: "ACIDE SULFURIQUE PA", expiryDate: null },
        { id: 10015801, name: "ACIDE SULFURIQUE 96%", expiryDate: null },
        { id: 10015909, name: "ACIDE SULFURIQUE TECHNIQUE", expiryDate: null },
        { id: 10016001, name: "ACIDE TANNIQUE PUR", expiryDate: null },
        { id: 10016101, name: "ACIDE TARTRIQUE (L) PUR", expiryDate: null },
        { id: 10016201, name: "ACIDE THIOGLYCOLIQUE PUR", expiryDate: null },
        { id: 10016401, name: "ACIDE TRIFLUROACETIQUE TFA", expiryDate: null },
        { id: 10016414, name: "ACIDE TRIFLUOACETIQUE", expiryDate: null },
        { id: 10016415, name: "GUARANTED TFA PEPTIDE WITH ACETATE", expiryDate: null },
        { id: 10016416, name: "GUARANTED TFA PEPTIDE WITH FITC", expiryDate: null },
        { id: 10016417, name: "GUARANTED TFA PEPTIDE 3", expiryDate: null },
        { id: 10016601, name: "ACIDE TUNGSTIQUE PUR", expiryDate: null },
        { id: 10016801, name: "ACIDE URIQUE PUR", expiryDate: null },
        { id: 10017013, name: "ACIDE FORMIQUE HPLC", expiryDate: null },
        { id: 10017101, name: "ACIDE PYCRIQUE EN SOLUTION PUR", expiryDate: null },
        { id: 10017502, name: "ACIDE SULFAMIQUE P.A", expiryDate: null },
        { id: 10017802, name: "CHLORPTATINIC ACID HYDRATE", expiryDate: null },
        { id: 10018801, name: "ACIDE SULFURIQUE 96%", expiryDate: null },
        { id: 10019613, name: "ACIDE DIAMINOPIMEMIQUE", expiryDate: null },
        { id: 10019614, name: "HYDROCHLORIC ACID", expiryDate: null },
        { id: 10019615, name: "4-FLUOROCINNAMIC ACID", expiryDate: null },
        { id: 10019999, name: "FRAIS DE TRANSPORT", expiryDate: null },
        { id: 10020000, name: "URACIL", expiryDate: null },
        { id: 10020001, name: "URACIL -13C, 15N2", expiryDate: null },
        { id: 10020002, name: "DIHYDRO URACIL", expiryDate: null },
        { id: 10020003, name: "5,6-DIHYDRO URACIL-13C, 15N2", expiryDate: null },
        { id: 10020004, name: "5'-DEOXY-5'-(METHYLTHIO)ADENOSINE (MTA)", expiryDate: null },
        { id: 10020005, name: "L-GLUTAMINE", expiryDate: null },
        { id: 10020006, name: "N-N DIMETHYLHYDRAZINE", expiryDate: null },
        { id: 10020097, name: "3,4-DIHYDROXYBENZALDEHYDE", expiryDate: null },
        { id: 10020098, name: "1-PYRENOACETIC ACID (PCA)", expiryDate: null },
        { id: 10020099, name: "TRANS BLOT TRANSFERT ÉLECTROPHORÈSES", expiryDate: null },
        { id: 10020100, name: "FECTO PRO", expiryDate: null },
        { id: 10020101, name: "ACIDE ASPARTIQUES (D-L)PUR", expiryDate: null },
        { id: 10020102, name: "N-HYDROXY -L-ARGININE ACETATE SALT", expiryDate: null },
        { id: 10020103, name: "CASAMINO ACIDS", expiryDate: null },
        { id: 10020201, name: "ACIDE L GLUTALIQUE PUR", expiryDate: null },
        { id: 10020602, name: "ASPARAGINE (L) P.A", expiryDate: null },
        { id: 10020605, name: "ASPARAGINE PHARMACOPEE", expiryDate: null },
        { id: 10020701, name: "ALANINE BETA PUR", expiryDate: null },
        { id: 10020830, name: "HOMOCYSTEINE", expiryDate: null },
        { id: 10021301, name: "GLYCINE PUR", expiryDate: null },
        { id: 10021302, name: "GLYCINE P;A", expiryDate: null },
        { id: 10021417, name: "FMOC-PHE-OH", expiryDate: null },
        { id: 10021422, name: "FMOC-LEU-OH", expiryDate: null },
        { id: 10021502, name: "CYSTEINE (L) HYDROCHLORURE ANHYDRE P.A FL 100GR", expiryDate: null },
        { id: 10021513, name: "CYSTEINE (L) HYDROCHLORURE 1H2O FL 100GR", expiryDate: null },
        { id: 10021602, name: "GLUTAMINE(L) P.A", expiryDate: null },
        { id: 10021618, name: "GLUTAMINE(L) G8540(SIGMA)", expiryDate: null },
        { id: 10021802, name: "TYROSINE(L) P.A", expiryDate: null },
        { id: 10021901, name: "ALANINE (L)PUR", expiryDate: null },
        { id: 10021902, name: "ALANINE(L)P.A", expiryDate: null },
        { id: 10022102, name: "L-ARGININE", expiryDate: null },
        { id: 10022113, name: "L-GLUTATHIONE RDUCED", expiryDate: null },
        { id: 10022115, name: "L-HISTIDINE", expiryDate: null },
        { id: 10022116, name: "POLYAMINE SUPPLEMENT (1000X)", expiryDate: null },
        { id: 10022301, name: "METHIONINE (L) PUR", expiryDate: null },
        { id: 10022402, name: "ORITHINE (L)MONOCHL ORHYDRATE P.A", expiryDate: null },
        { id: 10022601, name: "TRYPTOPHANE (L) PUR", expiryDate: null },
        { id: 10022702, name: "VALINE (L) P.A", expiryDate: null },
        { id: 10023313, name: "N-ACETYL-LCYSTEINE", expiryDate: null },
        { id: 10023402, name: "PHENYLALANINE (D-L)P.A", expiryDate: null },
        { id: 10023501, name: "PROLINE PUR", expiryDate: null },
        { id: 10024101, name: "URACIL PUR", expiryDate: null },
        { id: 10024301, name: "PYRAZINE CARBOXAMIDE PUR", expiryDate: null },
        { id: 10024402, name: "CYSTEINE PA", expiryDate: null },
        { id: 10024912, name: "GLYCINE", expiryDate: null },
        { id: 10024913, name: "GLYCINE", expiryDate: null },
        { id: 10025214, name: "L-LEUCINE", expiryDate: null },
        { id: 10025215, name: "L-GLUTAMIC ACID", expiryDate: null },
        { id: 10025216, name: "CEF POOL", expiryDate: null },
        { id: 10025217, name: "CEFT MHC-II POOL", expiryDate: null },
        { id: 10025801, name: "LIPOFECTAMINE 2000", expiryDate: null },
        { id: 10025802, name: "L-GLUTATHIONE OXIDIZED", expiryDate: null },
        { id: 10025803, name: "L-GLUTATHIONE REDUCED", expiryDate: null },
        { id: 10025804, name: "BETAINE", expiryDate: null },
        { id: 10030000, name: "HV AMANDE DOUCE", expiryDate: null },
        { id: 10030001, name: "HV AMANDE AMER BIO", expiryDate: null },
        { id: 10030002, name: "HV ARGAN BIO", expiryDate: null },
        { id: 10030003, name: "HV AVOCAT", expiryDate: null },
        { id: 10030004, name: "HV JOJOBA", expiryDate: null },
        { id: 10030005, name: "HV LENTISQUE BIO", expiryDate: null },
        { id: 10030006, name: "LIPIDES", expiryDate: null },
        { id: 10030007, name: "HV LIN", expiryDate: null },
        { id: 10030008, name: "18:1 LYSO PA P/N", expiryDate: null },
        { id: 10030009, name: "16:0 CYCLIC", expiryDate: null },
        { id: 10030010, name: "12:1 LYSO NBD PC", expiryDate: null },
        { id: 10030011, name: "HUILE LUBRIFIANTE 150CC", expiryDate: null },
        { id: 10030012, name: "LAEMMLI SAMPLE BUFFER", expiryDate: null },
        { id: 10030013, name: "PEROXYDE D¿HYDROGÈNE 3%", expiryDate: null },
        { id: 10030014, name: "GLYCÉROL 98%", expiryDate: null },
        { id: 10030015, name: "PEROXYDE D'HYDROGENE 30%", expiryDate: null },
        { id: 10030016, name: "HV MACADEMIA", expiryDate: null },
        { id: 10030017, name: "HV NIGELLE", expiryDate: null },
        { id: 10030018, name: "HV NOISETTE", expiryDate: null },
        { id: 10030019, name: "HV NOYAUX D'ABRICOT", expiryDate: null },
        { id: 10030020, name: "HV NOIX DE COCO", expiryDate: null },
        { id: 10030021, name: "HV PÉPINS DE FIGUE DE BARBARIE", expiryDate: null },
        { id: 10030022, name: "HV PÉPINS DE GRENADE", expiryDate: null },
        { id: 10030023, name: "HV PÉPINS DE FRAMBOISE", expiryDate: null },
        { id: 10030024, name: "HV RICIN", expiryDate: null },
        { id: 10030025, name: "HV SESAME", expiryDate: null },
        { id: 10030026, name: "BEURRE DE KARITE", expiryDate: null },
        { id: 10030027, name: "HV MOUTARDE", expiryDate: null },
        { id: 10030028, name: "HV FENUGREC", expiryDate: null },
        { id: 10030029, name: "HV ANIS VERT", expiryDate: null },
        { id: 10030030, name: "GLYCEROL FOR MOLECULAR BIOLOGY = 99 %", expiryDate: null },
        { id: 10030031, name: "10X BOLT SAMPLE REDUCING AGENT, 10ML", expiryDate: null },
        { id: 10030302, name: "CIRE-BLANCHE", expiryDate: null },
        { id: 10030401, name: "GLYCERINE PUR", expiryDate: null },
        { id: 10030405, name: "GLYCEROL PHARMACOPEE", expiryDate: null },
        { id: 10030413, name: "KIT GLYCEROL", expiryDate: null },
        { id: 10030414, name: "CLYCEROL 99+%", expiryDate: null },
        { id: 10030501, name: "HUILE A IMMERSION PUR", expiryDate: null },
        { id: 10030502, name: "HUILE A IMMERSION PA", expiryDate: null },
        { id: 10030515, name: "HUILE A IMMERSION", expiryDate: null },
        { id: 10031001, name: "HUILE DE PARAFFINE PUR", expiryDate: null },
        { id: 10031201, name: "HUILE DE RICIN PUR", expiryDate: null },
        { id: 10031901, name: "HUIL MINERAL(HUILE BLANCHE LEGERE)", expiryDate: null },
        { id: 10032001, name: "1-PALMITOYL-SN-GLYCERO", expiryDate: null },
        { id: 10032401, name: "PARRAFFINE EN PASTILLE", expiryDate: null },
        { id: 10032513, name: "SAPONIN FROM QUILLAJA BARK", expiryDate: null },
        { id: 10032802, name: "GLYCEROL TRIPALMITATE PA FL 100GR", expiryDate: null },
        { id: 10033201, name: "LECTIN FROM PHASEOLUS VULGARIS", expiryDate: null },
        { id: 10033202, name: "FATTY ACID SUPPLEMENT", expiryDate: null },
        { id: 10040001, name: "GLYCERYL STEARATE SE", expiryDate: null },
        { id: 10040002, name: "GLYCERYL OLEATE CITRATE", expiryDate: null },
        { id: 10040003, name: "DECON BIDON 5L", expiryDate: null },
        { id: 10040004, name: "PARIETAL CELLS (PCA)", expiryDate: null },
        { id: 10040005, name: "BLUE CUPS", expiryDate: null },
        { id: 10040006, name: "GLYCERYL STEARATE CITRATE", expiryDate: null },
        { id: 10040007, name: "CETEARYL OLIVATE", expiryDate: null },
        { id: 10040008, name: "OLIVATE", expiryDate: null },
        { id: 10040009, name: "SORBITAN OLIVATE", expiryDate: null },
        { id: 10040010, name: "TENSIOACTIF SCI (SODIUM COCOYL ISETHIONATE)", expiryDate: null },
        { id: 10040011, name: "TENSIOACTIF SLSA (SODIUM LAURYL SULFOACETATE)", expiryDate: null },
        { id: 10040012, name: "RBS 50C", expiryDate: null },
        { id: 10040014, name: "POLOXAMER 188 SOLUTION", expiryDate: null },
        { id: 10040015, name: "TWEEN 60", expiryDate: null },
        { id: 10040016, name: "DÉSINFECTANT DE SURFACE", expiryDate: null },
        { id: 10040018, name: "SURFANIOS", expiryDate: null },
        { id: 10040019, name: "ANIOSPRAY (C329)", expiryDate: null },
        { id: 10040020, name: "LUXOL FAST BLUES STAIN (MYELIN STAIN ) (100 TESTS", expiryDate: null },
        { id: 10040021, name: "LIH (SÉRUM INDICES SOLUTION)", expiryDate: null }
    ],
    
    // Configuration des devises
    currencies: {
        default: 'TND',
        supported: ['TND', 'EUR', 'USD', 'GBP', 'JPY'],
        names: {
            'TND': 'Dinar Tunisien',
            'EUR': 'Euro',
            'USD': 'Dollar US',
            'GBP': 'Livre Sterling',
            'JPY': 'Yen Japonais'
        },
        symbols: {
            'TND': 'د.ت',
            'EUR': '€',
            'USD': '$',
            'GBP': '£',
            'JPY': '¥'
        },
        exchangeRates: {
            'TND': { 'EUR': 0.31, 'USD': 0.32, 'GBP': 0.25, 'JPY': 48.50 },
            'EUR': { 'TND': 3.23, 'USD': 1.08, 'GBP': 0.86, 'JPY': 161.50 },
            'USD': { 'TND': 3.12, 'EUR': 0.93, 'GBP': 0.79, 'JPY': 149.50 },
            'GBP': { 'TND': 4.01, 'EUR': 1.16, 'USD': 1.27, 'JPY': 189.20 },
            'JPY': { 'TND': 0.021, 'EUR': 0.0062, 'USD': 0.0067, 'GBP': 0.0053 }
        }
    },
    
    // Configuration des statuts
    statuses: {
        commands: {
            brouillon: 'Brouillon',
            soumise: 'Soumise',
            approuvee: 'Approuvée',
            rejetee: 'Rejetée',
            terminee: 'Terminée'
        },
        pv: {
            en_attente: 'En attente',
            approuve: 'Approuvé',
            rejete: 'Rejeté'
        },
        products: {
            conforme: 'Conforme',
            'non-conforme': 'Non conforme',
            'a-verifier': 'À vérifier'
        }
    },
    
    // Configuration des limites
    limits: {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxFilesPerMessage: 5,
        maxMessageLength: 1000,
        maxProductsPerPv: 50,
        maxProductsPerCommand: 100
    },
    
    // Configuration des formats de date
    dateFormats: {
        display: 'dd/MM/yyyy HH:mm',
        export: 'yyyy-MM-dd',
        api: 'ISO'
    },
    
    // Configuration des exports
    export: {
        pdf: {
            format: 'A4',
            orientation: 'portrait',
            margins: { top: 20, right: 20, bottom: 20, left: 20 }
        },
        excel: {
            sheetName: 'Données',
            autoWidth: true
        }
    },
    
    // Configuration des notifications
    notifications: {
        duration: 5000, // 5 secondes
        position: 'top-right',
        types: {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        }
    },
    
    // Configuration du stockage
    storage: {
        keys: {
            messages: 'gestion_messages',
            commands: 'gestion_commandes',
            pv: 'gestion_pv',
            user: 'gestion_user',
            settings: 'gestion_settings'
        },
        useSupabase: true,
        fallbackToLocal: true
    },
    
    // Configuration de l'interface
    ui: {
        theme: 'light',
        animations: true,
        compactMode: false,
        showTooltips: true,
        autoSave: true,
        autoSaveInterval: 30000 // 30 secondes
    },
    
    // Configuration de sécurité
    security: {
        sessionTimeout: 8 * 60 * 60 * 1000, // 8 heures
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 minutes
        passwordMinLength: 6,
        requirePasswordChange: false
    },
    
    // Configuration des fonctionnalités
    features: {
        chat: {
            enabled: true,
            allowFileUpload: true,
            allowPrivateMessages: true,
            showTypingIndicator: false,
            maxHistoryDays: 365
        },
        commands: {
            enabled: true,
            requireApproval: false,
            allowDrafts: true,
            autoCalculateTotal: true,
            emailNotifications: true
        },
        pv: {
            enabled: true,
            allowBulkActions: true,
            autoNotifyHygiene: true,
            requireQuantities: true,
            allowNotes: true
        },
        analysis: {
            enabled: true,
            realTimeStats: false,
            historicalData: true,
            recommendations: true
        },
        converter: {
            enabled: true,
            realTimeRates: false,
            supportedCurrencies: ['EUR', 'USD', 'GBP', 'JPY'],
            updateInterval: 3600000 // 1 heure
        }
    },
    
    // Configuration de développement
    development: {
        debug: false,
        mockData: true,
        logLevel: 'info',
        enableConsoleLogging: true
    },
    
    // Messages système
    messages: {
        errors: {
            loginFailed: 'Identifiants incorrects',
            accessDenied: 'Accès non autorisé',
            networkError: 'Erreur de connexion réseau',
            fileTooBig: 'Fichier trop volumineux',
            invalidFormat: 'Format de fichier non supporté'
        },
        success: {
            loginSuccess: 'Connexion réussie',
            dataSaved: 'Données sauvegardées avec succès',
            emailSent: 'Email envoyé avec succès',
            exportCompleted: 'Export terminé avec succès'
        },
        info: {
            loading: 'Chargement en cours...',
            processing: 'Traitement en cours...',
            noData: 'Aucune donnée disponible',
            offlineMode: 'Mode hors ligne activé'
        }
    }
};

// Fonctions utilitaires de configuration
const ConfigUtils = {
    // Obtenir la configuration d'un rôle
    getRoleConfig(role) {
        return APP_CONFIG.roles[role] || null;
    },
    
    // Vérifier si un utilisateur a une permission
    hasPermission(userRole, permission) {
        const roleConfig = this.getRoleConfig(userRole);
        if (!roleConfig) return false;
        
        return roleConfig.permissions.includes('all') || 
               roleConfig.permissions.includes(permission);
    },
    
    // Obtenir la couleur d'un rôle
    getRoleColor(role) {
        const roleConfig = this.getRoleConfig(role);
        return roleConfig ? roleConfig.color : '#007bff';
    },
    
    // Obtenir les taux de change
    getExchangeRate(from, to) {
        if (from === to) return 1;
        return APP_CONFIG.currencies.exchangeRates[from]?.[to] || null;
    },
    
    // Formater une date selon la configuration
    formatDate(date, format = 'display') {
        const d = new Date(date);
        const formatStr = APP_CONFIG.dateFormats[format];
        
        // Format simple pour la démo
        if (format === 'display') {
            return d.toLocaleDateString('fr-FR', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
        
        return d.toISOString();
    },
    
    // Obtenir un message système
    getMessage(category, key) {
        return APP_CONFIG.messages[category]?.[key] || 'Message non trouvé';
    }
};

// Export des configurations et fonctions
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { APP_CONFIG, ConfigUtils };
} else if (typeof window !== 'undefined') {
    window.APP_CONFIG = APP_CONFIG;
    window.ConfigUtils = ConfigUtils;
}
