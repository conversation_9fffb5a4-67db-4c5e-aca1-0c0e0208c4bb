-- Script de validation de l'installation du système de gestion des dons IPT
-- À exécuter dans l'éditeur SQL de Supabase après l'installation

-- =====================================================
-- VALIDATION DES TABLES
-- =====================================================

DO $$
DECLARE
    table_count INTEGER;
    missing_tables TEXT[] := ARRAY[]::TEXT[];
    table_name TEXT;
BEGIN
    RAISE NOTICE '🔍 Validation des tables du système de dons...';
    
    -- Vérifier chaque table
    FOREACH table_name IN ARRAY ARRAY['gestion_donations', 'gestion_donation_items', 'gestion_donation_documents', 'gestion_donation_workflow', 'gestion_donation_approvals']
    LOOP
        SELECT COUNT(*) INTO table_count
        FROM information_schema.tables 
        WHERE table_name = table_name AND table_schema = 'public';
        
        IF table_count = 0 THEN
            missing_tables := array_append(missing_tables, table_name);
        ELSE
            RAISE NOTICE '✅ Table % existe', table_name;
        END IF;
    END LOOP;
    
    IF array_length(missing_tables, 1) > 0 THEN
        RAISE EXCEPTION '❌ Tables manquantes: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE '✅ Toutes les tables sont présentes';
    END IF;
END $$;

-- =====================================================
-- VALIDATION DES FONCTIONS
-- =====================================================

DO $$
DECLARE
    function_count INTEGER;
    missing_functions TEXT[] := ARRAY[]::TEXT[];
    function_name TEXT;
BEGIN
    RAISE NOTICE '🔍 Validation des fonctions...';
    
    -- Vérifier chaque fonction
    FOREACH function_name IN ARRAY ARRAY[
        'generate_donation_number',
        'generate_inventory_number', 
        'log_donation_workflow',
        'create_donation_notification',
        'submit_donation_to_dg',
        'validate_donation_by_dg',
        'provide_technical_advice',
        'authorize_donation_by_ms',
        'mark_donation_received',
        'assign_inventory_number',
        'generate_bon_sortie',
        'get_donations_statistics'
    ]
    LOOP
        SELECT COUNT(*) INTO function_count
        FROM information_schema.routines 
        WHERE routine_name = function_name AND routine_schema = 'public';
        
        IF function_count = 0 THEN
            missing_functions := array_append(missing_functions, function_name);
        ELSE
            RAISE NOTICE '✅ Fonction % existe', function_name;
        END IF;
    END LOOP;
    
    IF array_length(missing_functions, 1) > 0 THEN
        RAISE WARNING '⚠️ Fonctions manquantes: %', array_to_string(missing_functions, ', ');
    ELSE
        RAISE NOTICE '✅ Toutes les fonctions sont présentes';
    END IF;
END $$;

-- =====================================================
-- VALIDATION DES VUES
-- =====================================================

DO $$
DECLARE
    view_count INTEGER;
    missing_views TEXT[] := ARRAY[]::TEXT[];
    view_name TEXT;
BEGIN
    RAISE NOTICE '🔍 Validation des vues...';
    
    -- Vérifier chaque vue
    FOREACH view_name IN ARRAY ARRAY['donations_complete', 'donations_stats']
    LOOP
        SELECT COUNT(*) INTO view_count
        FROM information_schema.views 
        WHERE table_name = view_name AND table_schema = 'public';
        
        IF view_count = 0 THEN
            missing_views := array_append(missing_views, view_name);
        ELSE
            RAISE NOTICE '✅ Vue % existe', view_name;
        END IF;
    END LOOP;
    
    IF array_length(missing_views, 1) > 0 THEN
        RAISE WARNING '⚠️ Vues manquantes: %', array_to_string(missing_views, ', ');
    ELSE
        RAISE NOTICE '✅ Toutes les vues sont présentes';
    END IF;
END $$;

-- =====================================================
-- VALIDATION DES INDEX
-- =====================================================

DO $$
DECLARE
    index_count INTEGER;
    missing_indexes TEXT[] := ARRAY[]::TEXT[];
    index_name TEXT;
BEGIN
    RAISE NOTICE '🔍 Validation des index...';
    
    -- Vérifier les index principaux
    FOREACH index_name IN ARRAY ARRAY[
        'idx_donations_statut',
        'idx_donations_type', 
        'idx_donations_demandeur',
        'idx_donations_date',
        'idx_donation_items_donation',
        'idx_donation_documents_donation',
        'idx_donation_workflow_donation',
        'idx_donation_approvals_donation'
    ]
    LOOP
        SELECT COUNT(*) INTO index_count
        FROM pg_indexes 
        WHERE indexname = index_name AND schemaname = 'public';
        
        IF index_count = 0 THEN
            missing_indexes := array_append(missing_indexes, index_name);
        ELSE
            RAISE NOTICE '✅ Index % existe', index_name;
        END IF;
    END LOOP;
    
    IF array_length(missing_indexes, 1) > 0 THEN
        RAISE WARNING '⚠️ Index manquants: %', array_to_string(missing_indexes, ', ');
    ELSE
        RAISE NOTICE '✅ Tous les index sont présents';
    END IF;
END $$;

-- =====================================================
-- VALIDATION DES TRIGGERS
-- =====================================================

DO $$
DECLARE
    trigger_count INTEGER;
    missing_triggers TEXT[] := ARRAY[]::TEXT[];
    trigger_name TEXT;
BEGIN
    RAISE NOTICE '🔍 Validation des triggers...';
    
    -- Vérifier les triggers updated_at
    FOREACH trigger_name IN ARRAY ARRAY[
        'update_gestion_donations_updated_at',
        'update_gestion_donation_items_updated_at',
        'update_gestion_donation_documents_updated_at',
        'update_gestion_donation_approvals_updated_at'
    ]
    LOOP
        SELECT COUNT(*) INTO trigger_count
        FROM information_schema.triggers 
        WHERE trigger_name = trigger_name;
        
        IF trigger_count = 0 THEN
            missing_triggers := array_append(missing_triggers, trigger_name);
        ELSE
            RAISE NOTICE '✅ Trigger % existe', trigger_name;
        END IF;
    END LOOP;
    
    IF array_length(missing_triggers, 1) > 0 THEN
        RAISE WARNING '⚠️ Triggers manquants: %', array_to_string(missing_triggers, ', ');
    ELSE
        RAISE NOTICE '✅ Tous les triggers sont présents';
    END IF;
END $$;

-- =====================================================
-- TEST DES FONCTIONS PRINCIPALES
-- =====================================================

DO $$
DECLARE
    test_numero_don TEXT;
    test_numero_ni TEXT;
BEGIN
    RAISE NOTICE '🧪 Test des fonctions principales...';
    
    -- Test génération numéro de don
    BEGIN
        SELECT generate_donation_number() INTO test_numero_don;
        RAISE NOTICE '✅ generate_donation_number(): %', test_numero_don;
    EXCEPTION WHEN OTHERS THEN
        RAISE WARNING '❌ Erreur generate_donation_number(): %', SQLERRM;
    END;
    
    -- Test génération numéro inventaire
    BEGIN
        SELECT generate_inventory_number() INTO test_numero_ni;
        RAISE NOTICE '✅ generate_inventory_number(): %', test_numero_ni;
    EXCEPTION WHEN OTHERS THEN
        RAISE WARNING '❌ Erreur generate_inventory_number(): %', SQLERRM;
    END;
    
    RAISE NOTICE '✅ Tests des fonctions terminés';
END $$;

-- =====================================================
-- VALIDATION DES CONTRAINTES
-- =====================================================

DO $$
DECLARE
    constraint_count INTEGER;
BEGIN
    RAISE NOTICE '🔍 Validation des contraintes...';
    
    -- Vérifier les contraintes CHECK sur type_don
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.check_constraints 
    WHERE constraint_name LIKE '%type_don%';
    
    IF constraint_count > 0 THEN
        RAISE NOTICE '✅ Contraintes type_don présentes';
    ELSE
        RAISE WARNING '⚠️ Contraintes type_don manquantes';
    END IF;
    
    -- Vérifier les contraintes CHECK sur statut
    SELECT COUNT(*) INTO constraint_count
    FROM information_schema.check_constraints 
    WHERE constraint_name LIKE '%statut%';
    
    IF constraint_count > 0 THEN
        RAISE NOTICE '✅ Contraintes statut présentes';
    ELSE
        RAISE WARNING '⚠️ Contraintes statut manquantes';
    END IF;
    
    RAISE NOTICE '✅ Validation des contraintes terminée';
END $$;

-- =====================================================
-- VALIDATION DES POLITIQUES RLS
-- =====================================================

DO $$
DECLARE
    rls_enabled BOOLEAN;
    policy_count INTEGER;
    table_name TEXT;
BEGIN
    RAISE NOTICE '🔍 Validation des politiques RLS...';
    
    -- Vérifier que RLS est activé sur les tables principales
    FOREACH table_name IN ARRAY ARRAY['gestion_donations', 'gestion_donation_items', 'gestion_donation_documents', 'gestion_donation_workflow', 'gestion_donation_approvals']
    LOOP
        SELECT relrowsecurity INTO rls_enabled
        FROM pg_class 
        WHERE relname = table_name;
        
        IF rls_enabled THEN
            RAISE NOTICE '✅ RLS activé sur %', table_name;
            
            -- Compter les politiques pour cette table
            SELECT COUNT(*) INTO policy_count
            FROM pg_policies 
            WHERE tablename = table_name;
            
            RAISE NOTICE '  └─ % politiques configurées', policy_count;
        ELSE
            RAISE WARNING '⚠️ RLS non activé sur %', table_name;
        END IF;
    END LOOP;
    
    RAISE NOTICE '✅ Validation RLS terminée';
END $$;

-- =====================================================
-- RÉSUMÉ DE L'INSTALLATION
-- =====================================================

DO $$
DECLARE
    total_tables INTEGER;
    total_functions INTEGER;
    total_views INTEGER;
    total_indexes INTEGER;
    total_triggers INTEGER;
    total_policies INTEGER;
BEGIN
    RAISE NOTICE '📊 RÉSUMÉ DE L''INSTALLATION';
    RAISE NOTICE '================================';
    
    -- Compter les éléments installés
    SELECT COUNT(*) INTO total_tables
    FROM information_schema.tables 
    WHERE table_name LIKE 'gestion_donation%' AND table_schema = 'public';
    
    SELECT COUNT(*) INTO total_functions
    FROM information_schema.routines 
    WHERE routine_name IN (
        'generate_donation_number', 'generate_inventory_number', 'log_donation_workflow',
        'submit_donation_to_dg', 'validate_donation_by_dg', 'provide_technical_advice',
        'authorize_donation_by_ms', 'mark_donation_received', 'assign_inventory_number'
    ) AND routine_schema = 'public';
    
    SELECT COUNT(*) INTO total_views
    FROM information_schema.views 
    WHERE table_name IN ('donations_complete', 'donations_stats') AND table_schema = 'public';
    
    SELECT COUNT(*) INTO total_indexes
    FROM pg_indexes 
    WHERE indexname LIKE 'idx_donation%' AND schemaname = 'public';
    
    SELECT COUNT(*) INTO total_triggers
    FROM information_schema.triggers 
    WHERE trigger_name LIKE '%donation%';
    
    SELECT COUNT(*) INTO total_policies
    FROM pg_policies 
    WHERE tablename LIKE 'gestion_donation%';
    
    RAISE NOTICE '📋 Tables: %', total_tables;
    RAISE NOTICE '⚙️ Fonctions: %', total_functions;
    RAISE NOTICE '👁️ Vues: %', total_views;
    RAISE NOTICE '🔍 Index: %', total_indexes;
    RAISE NOTICE '🔄 Triggers: %', total_triggers;
    RAISE NOTICE '🔒 Politiques RLS: %', total_policies;
    RAISE NOTICE '================================';
    
    IF total_tables >= 5 AND total_functions >= 8 AND total_views >= 2 THEN
        RAISE NOTICE '🎉 INSTALLATION RÉUSSIE!';
        RAISE NOTICE 'Le système de gestion des dons est prêt à être utilisé.';
    ELSE
        RAISE WARNING '⚠️ INSTALLATION INCOMPLÈTE';
        RAISE NOTICE 'Veuillez vérifier les erreurs ci-dessus et réexécuter les scripts manquants.';
    END IF;
END $$;
