// Gestionnaire de Dons IPT - Module complet
// Système de gestion des dons avec workflow en 3 étapes

class DonationsManager {
    constructor() {
        this.donations = [];
        this.currentDonation = null;
        this.currentStep = 1;
        this.maxSteps = 3;
        
        // Configuration des rôles et permissions
        this.rolePermissions = {
            'admin': ['create', 'read', 'update', 'delete', 'approve', 'manage_workflow'],
            'manager': ['create', 'read', 'update', 'approve', 'manage_workflow'],
            'dg': ['read', 'approve', 'validate_execution'],
            'procurement': ['create', 'read', 'update', 'implement_procedures'],
            'warehouse': ['read', 'receive', 'verify_documents'],
            'transit': ['read', 'verify_compliance'],
            'rve': ['read', 'manage_equipment', 'assign_ni', 'generate_labels'],
            'user': ['create', 'read']
        };

        // États du workflow
        this.workflowStates = {
            'demande_initiale': { name: 'Demande initiale', icon: '📝', next: 'validation_dg' },
            'validation_dg': { name: 'Validation DG', icon: '👨‍💼', next: 'avis_technique_dt' },
            'avis_technique_dt': { name: 'Avis technique DT', icon: '🔧', next: 'autorisation_ms' },
            'autorisation_ms': { name: 'Autorisation MS', icon: '🏛️', next: 'accord_expedition' },
            'accord_expedition': { name: 'Accord expédition', icon: '📦', next: 'reception_livraison' },
            'reception_livraison': { name: 'Réception/Livraison', icon: '✅', next: 'complete' },
            'complete': { name: 'Complété', icon: '🎯', next: null },
            'rejete': { name: 'Rejeté', icon: '❌', next: null }
        };

        this.init();
    }

    async init() {
        console.log('🎁 Initialisation du gestionnaire de dons...');
        
        try {
            await this.loadDonations();
            this.setupEventListeners();
            this.generateDonationNumber();
            console.log('✅ Gestionnaire de dons initialisé');
        } catch (error) {
            console.error('❌ Erreur initialisation gestionnaire de dons:', error);
        }
    }

    // Vérifier les permissions utilisateur
    hasPermission(action) {
        const userRole = utilisateurCourant?.role || 'user';
        const permissions = this.rolePermissions[userRole] || [];
        return permissions.includes(action);
    }

    // Charger les dons depuis la base de données
    async loadDonations() {
        try {
            const dbManager = getDatabaseManager();
            if (dbManager) {
                const result = await dbManager.load('donations');
                this.donations = result || [];
                console.log(`📦 ${this.donations.length} dons chargés`);
            } else {
                // Fallback localStorage
                const stored = localStorage.getItem('gestion_donations');
                this.donations = stored ? JSON.parse(stored) : [];
            }
            
            this.updateDonationsDisplay();
            this.updateStatistics();
        } catch (error) {
            console.error('❌ Erreur chargement dons:', error);
            this.donations = [];
        }
    }

    // Sauvegarder un don
    async saveDonation(donation) {
        try {
            const dbManager = getDatabaseManager();
            if (dbManager) {
                const result = await dbManager.save('donations', donation);
                if (result) {
                    console.log('✅ Don sauvegardé avec succès');
                    return result;
                }
            } else {
                // Fallback localStorage
                const stored = JSON.parse(localStorage.getItem('gestion_donations') || '[]');
                stored.push(donation);
                localStorage.setItem('gestion_donations', JSON.stringify(stored));
            }
            
            return donation;
        } catch (error) {
            console.error('❌ Erreur sauvegarde don:', error);
            throw error;
        }
    }

    // Générer un numéro de don unique
    generateDonationNumber() {
        const year = new Date().getFullYear();
        const month = String(new Date().getMonth() + 1).padStart(2, '0');
        const day = String(new Date().getDate()).padStart(2, '0');
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        const donationNumber = `DON-${year}${month}${day}-${random}`;
        
        const numberInput = document.getElementById('donationNumber');
        if (numberInput) {
            numberInput.value = donationNumber;
        }
        
        return donationNumber;
    }

    // Configuration des écouteurs d'événements
    setupEventListeners() {
        // Écouteurs pour les fichiers
        const fileInputs = [
            'lettreDonateurFile',
            'factureFile', 
            'bonLivraisonFile',
            'bonPrelevementFile',
            'autresDocuments'
        ];

        fileInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('change', (e) => this.handleFileUpload(e, inputId));
            }
        });

        // Écouteur pour le type de don
        const typeSelect = document.getElementById('donationType');
        if (typeSelect) {
            typeSelect.addEventListener('change', () => this.toggleEquipmentFields());
        }
    }

    // Gérer l'upload de fichiers
    async handleFileUpload(event, inputId) {
        const files = Array.from(event.target.files);
        const previewId = inputId.replace('File', 'Preview');
        const previewContainer = document.getElementById(previewId);
        
        if (!previewContainer) return;

        previewContainer.innerHTML = '';

        for (const file of files) {
            // Vérifier la taille du fichier
            const maxSize = SUPABASE_CONFIG.storage.maxFileSize.donation_documents;
            if (file.size > maxSize) {
                alert(`Le fichier ${file.name} est trop volumineux (max ${maxSize / 1024 / 1024}MB)`);
                continue;
            }

            // Créer l'aperçu du fichier
            const filePreview = document.createElement('div');
            filePreview.className = 'file-preview-item';
            filePreview.innerHTML = `
                <div class="file-info">
                    <span class="file-icon">${this.getFileIcon(file.type)}</span>
                    <span class="file-name">${file.name}</span>
                    <span class="file-size">(${(file.size / 1024).toFixed(1)} KB)</span>
                </div>
                <button type="button" onclick="this.parentElement.remove()" class="remove-file">❌</button>
            `;
            previewContainer.appendChild(filePreview);

            // Sauvegarder le fichier si Supabase est disponible
            try {
                await this.uploadFileToStorage(file, inputId);
            } catch (error) {
                console.warn('⚠️ Erreur upload fichier:', error);
            }
        }
    }

    // Obtenir l'icône selon le type de fichier
    getFileIcon(mimeType) {
        if (mimeType.includes('pdf')) return '📄';
        if (mimeType.includes('word')) return '📝';
        if (mimeType.includes('image')) return '🖼️';
        return '📎';
    }

    // Upload fichier vers Supabase Storage
    async uploadFileToStorage(file, category) {
        try {
            const supabaseClient = SupabaseUtils.getClient();
            if (!supabaseClient) {
                throw new Error('Supabase non disponible');
            }

            const fileName = `${Date.now()}_${file.name}`;
            const filePath = `donations/${category}/${fileName}`;

            const { data, error } = await supabaseClient.storage
                .from('donation-documents')
                .upload(filePath, file);

            if (error) throw error;

            console.log(`✅ Fichier uploadé: ${fileName}`);
            return data;
        } catch (error) {
            console.error('❌ Erreur upload fichier:', error);
            throw error;
        }
    }

    // Basculer l'affichage des champs équipement
    toggleEquipmentFields() {
        const donationType = document.getElementById('donationType').value;
        const equipmentFields = document.getElementById('equipmentFields');
        
        if (equipmentFields) {
            equipmentFields.style.display = donationType === 'equipement' ? 'block' : 'none';
        }
    }

    // Navigation entre les étapes
    nextDonationStep() {
        if (this.currentStep < this.maxSteps) {
            if (this.validateCurrentStep()) {
                this.currentStep++;
                this.updateStepDisplay();
            }
        }
    }

    previousDonationStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    }

    // Valider l'étape actuelle
    validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                return this.validateStep1();
            case 2:
                return this.validateStep2();
            case 3:
                return this.validateStep3();
            default:
                return true;
        }
    }

    validateStep1() {
        const required = ['donationType', 'donorName', 'beneficiaryService', 'donationReason'];
        
        for (const fieldId of required) {
            const field = document.getElementById(fieldId);
            if (!field || !field.value.trim()) {
                alert(`Le champ ${fieldId} est requis`);
                field?.focus();
                return false;
            }
        }

        // Validation spécifique pour les équipements
        const donationType = document.getElementById('donationType').value;
        if (donationType === 'equipement') {
            const equipmentRequired = ['equipmentBrand', 'equipmentType'];
            for (const fieldId of equipmentRequired) {
                const field = document.getElementById(fieldId);
                if (!field || !field.value.trim()) {
                    alert(`Le champ ${fieldId} est requis pour les équipements`);
                    field?.focus();
                    return false;
                }
            }
        }

        return true;
    }

    validateStep2() {
        // Vérifier qu'au moins les documents obligatoires sont présents
        const requiredDocs = ['lettreDonateurFile', 'factureFile', 'bonLivraisonFile'];
        
        for (const docId of requiredDocs) {
            const input = document.getElementById(docId);
            if (!input || !input.files.length) {
                alert(`Le document ${docId.replace('File', '')} est requis`);
                return false;
            }
        }

        return true;
    }

    validateStep3() {
        // Générer le résumé pour validation
        this.generateDonationSummary();
        return true;
    }

    // Mettre à jour l'affichage des étapes
    updateStepDisplay() {
        // Cacher toutes les étapes
        for (let i = 1; i <= this.maxSteps; i++) {
            const step = document.getElementById(`donationStep${i}`);
            if (step) {
                step.style.display = i === this.currentStep ? 'block' : 'none';
            }

            // Mettre à jour les indicateurs d'étapes
            const stepIndicator = document.getElementById(`step${i}`);
            if (stepIndicator) {
                stepIndicator.className = i <= this.currentStep ? 'step active' : 'step';
            }
        }

        // Mettre à jour les boutons
        const prevBtn = document.getElementById('prevStepBtn');
        const nextBtn = document.getElementById('nextStepBtn');
        const submitBtn = document.getElementById('submitDonationBtn');

        if (prevBtn) prevBtn.style.display = this.currentStep > 1 ? 'inline-block' : 'none';
        if (nextBtn) nextBtn.style.display = this.currentStep < this.maxSteps ? 'inline-block' : 'none';
        if (submitBtn) submitBtn.style.display = this.currentStep === this.maxSteps ? 'inline-block' : 'none';
    }

    // Générer le résumé du don
    generateDonationSummary() {
        const summaryContainer = document.getElementById('donationSummary');
        if (!summaryContainer) return;

        const formData = this.collectFormData();
        
        summaryContainer.innerHTML = `
            <div class="summary-section">
                <h5>📋 Informations générales</h5>
                <p><strong>Type:</strong> ${formData.type === 'article' ? '📦 Article' : '🔧 Équipement'}</p>
                <p><strong>N° Don:</strong> ${formData.donationNumber}</p>
                <p><strong>Donateur:</strong> ${formData.donorName}</p>
                <p><strong>Bénéficiaire:</strong> ${formData.beneficiaryService}</p>
                <p><strong>Raison:</strong> ${formData.donationReason}</p>
            </div>
            
            ${formData.type === 'equipement' ? `
                <div class="summary-section">
                    <h5>🔧 Détails équipement</h5>
                    <p><strong>Marque:</strong> ${formData.equipmentBrand || 'N/A'}</p>
                    <p><strong>Type:</strong> ${formData.equipmentType || 'N/A'}</p>
                    <p><strong>N° Série:</strong> ${formData.equipmentSerial || 'N/A'}</p>
                </div>
            ` : ''}
            
            <div class="summary-section">
                <h5>📎 Documents attachés</h5>
                <ul>
                    ${formData.documents.map(doc => `<li>${doc}</li>`).join('')}
                </ul>
            </div>
        `;
    }

    // Collecter les données du formulaire
    collectFormData() {
        const data = {
            type: document.getElementById('donationType')?.value || '',
            donationNumber: document.getElementById('donationNumber')?.value || '',
            donorName: document.getElementById('donorName')?.value || '',
            donorContact: document.getElementById('donorContact')?.value || '',
            beneficiaryService: document.getElementById('beneficiaryService')?.value || '',
            donationReason: document.getElementById('donationReason')?.value || '',
            equipmentBrand: document.getElementById('equipmentBrand')?.value || '',
            equipmentType: document.getElementById('equipmentType')?.value || '',
            equipmentSerial: document.getElementById('equipmentSerial')?.value || '',
            notes: document.getElementById('donationNotes')?.value || '',
            documents: []
        };

        // Collecter les documents
        const fileInputs = [
            { id: 'lettreDonateurFile', name: 'Lettre du donateur' },
            { id: 'factureFile', name: 'Facture' },
            { id: 'bonLivraisonFile', name: 'Bon de livraison' },
            { id: 'bonPrelevementFile', name: 'Bon de prélèvement' },
            { id: 'autresDocuments', name: 'Autres documents' }
        ];

        fileInputs.forEach(input => {
            const element = document.getElementById(input.id);
            if (element && element.files.length > 0) {
                data.documents.push(`${input.name} (${element.files.length} fichier(s))`);
            }
        });

        return data;
    }
}

// Instance globale du gestionnaire de dons
let donationsManager = null;

// Fonctions globales pour l'interface
function toggleGestionDons() {
    if (!utilisateurCourant) {
        alert('Veuillez vous connecter');
        return;
    }

    hideAllModuleZones();
    document.getElementById('gestionDonsZone').style.display = 'block';
    
    // Initialiser le gestionnaire si nécessaire
    if (!donationsManager) {
        donationsManager = new DonationsManager();
    }
    
    // Afficher l'onglet par défaut
    showDonationsTab('liste');
}

function showDonationsTab(tabName) {
    // Cacher tous les onglets
    const tabs = document.querySelectorAll('.donations-tab-content');
    tabs.forEach(tab => tab.style.display = 'none');
    
    // Désactiver tous les boutons d'onglets
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    // Afficher l'onglet sélectionné
    const selectedTab = document.getElementById(`donations${tabName.charAt(0).toUpperCase() + tabName.slice(1)}Tab`);
    if (selectedTab) {
        selectedTab.style.display = 'block';
    }
    
    // Activer le bouton d'onglet
    const selectedButton = document.getElementById(`tab${tabName.charAt(0).toUpperCase() + tabName.slice(1)}`);
    if (selectedButton) {
        selectedButton.classList.add('active');
    }
    
    // Actions spécifiques selon l'onglet
    switch (tabName) {
        case 'liste':
            if (donationsManager) donationsManager.updateDonationsDisplay();
            break;
        case 'nouveau':
            if (donationsManager) donationsManager.generateDonationNumber();
            break;
        case 'workflow':
            if (donationsManager) donationsManager.updateWorkflowDisplay();
            break;
        case 'inventaire':
            if (donationsManager) donationsManager.updateInventoryDisplay();
            break;
        case 'statistiques':
            if (donationsManager) donationsManager.updateStatistics();
            break;
    }
}

// Fonctions pour la navigation des étapes
function nextDonationStep() {
    if (donationsManager) {
        donationsManager.nextDonationStep();
    }
}

function previousDonationStep() {
    if (donationsManager) {
        donationsManager.previousDonationStep();
    }
}

function toggleEquipmentFields() {
    if (donationsManager) {
        donationsManager.toggleEquipmentFields();
    }
}

function resetDonationForm() {
    if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
        document.getElementById('donationForm').reset();
        if (donationsManager) {
            donationsManager.currentStep = 1;
            donationsManager.updateStepDisplay();
            donationsManager.generateDonationNumber();
        }
    }
}

// Export du module
if (typeof window !== 'undefined') {
    window.DonationsManager = DonationsManager;
    window.donationsManager = donationsManager;
}
