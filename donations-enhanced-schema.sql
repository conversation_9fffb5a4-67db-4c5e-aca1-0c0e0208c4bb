-- Schéma amélioré pour le système de gestion des dons IPT
-- Intègre le logigramme complet avec toutes les étapes

-- =====================================================
-- MISE À JOUR DE LA TABLE PRINCIPALE DES DONS
-- =====================================================

-- Ajouter les nouveaux statuts selon le logigramme
ALTER TABLE gestion_donations DROP CONSTRAINT IF EXISTS gestion_donations_statut_check;
ALTER TABLE gestion_donations ADD CONSTRAINT gestion_donations_statut_check 
CHECK (statut IN (
    'brouillon',           -- Étape 1: Création par donneur/bénéficiaire
    'bp_cree',             -- Étape 2: BP créé par bénéficiaire
    'chez_magasinier',     -- Étape 3: <PERSON><PERSON>r chez magasinier
    'chez_transitaire',    -- Étape 4: <PERSON><PERSON>r chez transitaire
    'chez_receptionniste', -- Étape 5: Dossier chez réceptionniste
    'bs_cree',             -- Étape 6: BS créé par magasinier
    'chez_rve',            -- Étape 7: Copie dossier chez RVE
    'recap_cree',          -- Étape 8: Tableau récap créé par RVE
    'envoye_comptabilite', -- Étape 9: Envoyé à comptabilité
    'archive',             -- Étape 10: Archivé par RVE
    'refuse_dg',           -- Refus par DG
    'refuse_dt',           -- Refus par DT (équipements)
    'refuse_ms'            -- Refus par MS
));

-- Ajouter les champs pour le suivi détaillé
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS numero_bp TEXT;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS numero_bs TEXT;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS date_bp DATE;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS date_bs DATE;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS date_envoi_comptabilite TIMESTAMP WITH TIME ZONE;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS date_archivage TIMESTAMP WITH TIME ZONE;

-- Ajouter les responsables pour chaque étape
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS magasinier_responsable TEXT;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS transitaire_responsable TEXT;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS receptionniste_responsable TEXT;
ALTER TABLE gestion_donations ADD COLUMN IF NOT EXISTS rve_responsable TEXT;

-- =====================================================
-- TABLE DES ÉTAPES DU WORKFLOW DÉTAILLÉ
-- =====================================================

CREATE TABLE IF NOT EXISTS gestion_donation_etapes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    donation_id UUID NOT NULL REFERENCES gestion_donations(id) ON DELETE CASCADE,
    
    -- Numéro d'étape selon le logigramme
    numero_etape INTEGER NOT NULL CHECK (numero_etape BETWEEN 1 AND 10),
    
    -- Informations de l'étape
    nom_etape TEXT NOT NULL,
    responsable_role TEXT NOT NULL,
    responsable_nom TEXT,
    
    -- Statut de l'étape
    statut_etape TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut_etape IN (
        'en_attente', 'en_cours', 'termine', 'bloque', 'ignore'
    )),
    
    -- Documents requis et fournis
    documents_requis JSONB DEFAULT '[]'::jsonb,
    documents_fournis JSONB DEFAULT '[]'::jsonb,
    
    -- Dates importantes
    date_debut TIMESTAMP WITH TIME ZONE,
    date_fin TIMESTAMP WITH TIME ZONE,
    duree_prevue_heures INTEGER DEFAULT 24,
    
    -- Commentaires et observations
    commentaire TEXT,
    observations TEXT,
    
    -- Métadonnées
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour performance
CREATE INDEX IF NOT EXISTS idx_donation_etapes_donation ON gestion_donation_etapes(donation_id);
CREATE INDEX IF NOT EXISTS idx_donation_etapes_numero ON gestion_donation_etapes(numero_etape);
CREATE INDEX IF NOT EXISTS idx_donation_etapes_statut ON gestion_donation_etapes(statut_etape);

-- =====================================================
-- TABLE DES BONS DE PRÉLÈVEMENT (BP)
-- =====================================================

CREATE TABLE IF NOT EXISTS gestion_donation_bp (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    donation_id UUID NOT NULL REFERENCES gestion_donations(id) ON DELETE CASCADE,
    
    -- Informations du BP
    numero_bp TEXT UNIQUE NOT NULL,
    date_creation DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Bénéficiaire
    beneficiaire_nom TEXT NOT NULL,
    beneficiaire_service TEXT NOT NULL,
    beneficiaire_email TEXT NOT NULL,
    
    -- Détails du prélèvement
    motif_prelevement TEXT NOT NULL,
    lieu_prelevement TEXT NOT NULL,
    date_prelevement_prevue DATE,
    
    -- Items demandés
    items_demandes JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Validation
    valide_par TEXT,
    date_validation TIMESTAMP WITH TIME ZONE,
    
    -- Statut
    statut TEXT NOT NULL DEFAULT 'cree' CHECK (statut IN (
        'cree', 'valide', 'en_cours', 'termine', 'annule'
    )),
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TABLE DES BONS DE SORTIE (BS)
-- =====================================================

CREATE TABLE IF NOT EXISTS gestion_donation_bs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    donation_id UUID NOT NULL REFERENCES gestion_donations(id) ON DELETE CASCADE,
    bp_id UUID REFERENCES gestion_donation_bp(id),
    
    -- Informations du BS
    numero_bs TEXT UNIQUE NOT NULL,
    date_creation DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Magasinier responsable
    magasinier_nom TEXT NOT NULL,
    magasinier_signature TEXT,
    
    -- Détails de la sortie
    items_sortis JSONB NOT NULL DEFAULT '[]'::jsonb,
    quantite_totale INTEGER DEFAULT 0,
    
    -- Destination
    destination_service TEXT NOT NULL,
    destination_responsable TEXT NOT NULL,
    
    -- Validation et réception
    recu_par TEXT,
    date_reception TIMESTAMP WITH TIME ZONE,
    signature_reception TEXT,
    
    -- Statut
    statut TEXT NOT NULL DEFAULT 'cree' CHECK (statut IN (
        'cree', 'valide', 'sorti', 'recu', 'archive'
    )),
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TABLE DES TABLEAUX RÉCAPITULATIFS
-- =====================================================

CREATE TABLE IF NOT EXISTS gestion_donation_recap (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Période du récapitulatif
    periode_debut DATE NOT NULL,
    periode_fin DATE NOT NULL,
    
    -- Informations du récap
    numero_recap TEXT UNIQUE NOT NULL,
    titre TEXT NOT NULL,
    
    -- Dons inclus
    donations_inclus JSONB NOT NULL DEFAULT '[]'::jsonb,
    nombre_dons INTEGER DEFAULT 0,
    valeur_totale DECIMAL(12,2) DEFAULT 0,
    
    -- Statistiques
    stats_par_type JSONB DEFAULT '{}'::jsonb,
    stats_par_service JSONB DEFAULT '{}'::jsonb,
    stats_par_donneur JSONB DEFAULT '{}'::jsonb,
    
    -- RVE responsable
    rve_nom TEXT NOT NULL,
    rve_signature TEXT,
    
    -- Envoi comptabilité
    envoye_comptabilite BOOLEAN DEFAULT FALSE,
    date_envoi_comptabilite TIMESTAMP WITH TIME ZONE,
    recu_par_comptabilite TEXT,
    
    -- Statut
    statut TEXT NOT NULL DEFAULT 'brouillon' CHECK (statut IN (
        'brouillon', 'finalise', 'envoye', 'recu', 'archive'
    )),
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- TRIGGERS POUR UPDATED_AT
-- =====================================================

CREATE TRIGGER update_gestion_donation_etapes_updated_at 
    BEFORE UPDATE ON gestion_donation_etapes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gestion_donation_bp_updated_at 
    BEFORE UPDATE ON gestion_donation_bp 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gestion_donation_bs_updated_at 
    BEFORE UPDATE ON gestion_donation_bs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gestion_donation_recap_updated_at 
    BEFORE UPDATE ON gestion_donation_recap 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VUES AMÉLIORÉES
-- =====================================================

-- Vue complète avec toutes les étapes
CREATE OR REPLACE VIEW donations_workflow_complete AS
SELECT 
    d.*,
    -- Étapes du workflow
    (SELECT json_agg(
        json_build_object(
            'numero', e.numero_etape,
            'nom', e.nom_etape,
            'responsable', e.responsable_nom,
            'statut', e.statut_etape,
            'date_debut', e.date_debut,
            'date_fin', e.date_fin,
            'duree_heures', EXTRACT(EPOCH FROM (e.date_fin - e.date_debut))/3600
        ) ORDER BY e.numero_etape
    ) FROM gestion_donation_etapes e WHERE e.donation_id = d.id) as etapes,
    
    -- BP associé
    bp.numero_bp,
    bp.statut as statut_bp,
    
    -- BS associé
    bs.numero_bs,
    bs.statut as statut_bs,
    
    -- Progression
    (SELECT COUNT(*) FROM gestion_donation_etapes e 
     WHERE e.donation_id = d.id AND e.statut_etape = 'termine') as etapes_terminees,
    (SELECT COUNT(*) FROM gestion_donation_etapes e 
     WHERE e.donation_id = d.id) as etapes_totales,
    
    -- Temps total de traitement
    CASE 
        WHEN d.date_archivage IS NOT NULL 
        THEN EXTRACT(DAYS FROM (d.date_archivage - d.created_at))
        ELSE EXTRACT(DAYS FROM (NOW() - d.created_at))
    END as duree_traitement_jours

FROM gestion_donations d
LEFT JOIN gestion_donation_bp bp ON d.id = bp.donation_id
LEFT JOIN gestion_donation_bs bs ON d.id = bs.donation_id;

-- Vue des dons en retard
CREATE OR REPLACE VIEW donations_en_retard AS
SELECT 
    d.*,
    e.numero_etape,
    e.nom_etape,
    e.responsable_nom,
    e.date_debut,
    EXTRACT(HOURS FROM (NOW() - e.date_debut)) as heures_en_retard,
    e.duree_prevue_heures
FROM gestion_donations d
JOIN gestion_donation_etapes e ON d.id = e.donation_id
WHERE e.statut_etape = 'en_cours'
AND e.date_debut + INTERVAL '1 hour' * e.duree_prevue_heures < NOW();

-- =====================================================
-- COMMENTAIRES
-- =====================================================

COMMENT ON TABLE gestion_donation_etapes IS 'Suivi détaillé des 10 étapes du workflow selon le logigramme IPT';
COMMENT ON TABLE gestion_donation_bp IS 'Bons de prélèvement créés par les bénéficiaires';
COMMENT ON TABLE gestion_donation_bs IS 'Bons de sortie créés par les magasiniers';
COMMENT ON TABLE gestion_donation_recap IS 'Tableaux récapitulatifs créés par le RVE';

-- =====================================================
-- MESSAGE DE CONFIRMATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Schéma amélioré installé avec succès!';
    RAISE NOTICE '📋 Nouvelles tables: gestion_donation_etapes, gestion_donation_bp, gestion_donation_bs, gestion_donation_recap';
    RAISE NOTICE '🔄 Workflow détaillé avec 10 étapes selon le logigramme IPT';
    RAISE NOTICE '📊 Vues améliorées: donations_workflow_complete, donations_en_retard';
END $$;
