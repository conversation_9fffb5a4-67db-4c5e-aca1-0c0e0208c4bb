// Système de monitoring et gestion d'erreurs Supabase
// Surveillance en temps réel de la connexion et des performances

class SupabaseMonitor {
    constructor(config) {
        this.config = config;
        this.client = null;
        this.isMonitoring = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = config.sync?.maxReconnectAttempts || 10;
        this.reconnectDelay = config.sync?.reconnectDelay || 5000;
        
        // Métriques de monitoring
        this.metrics = {
            connectionStatus: 'disconnected',
            lastHealthCheck: null,
            errorCount: 0,
            successCount: 0,
            averageResponseTime: 0,
            totalRequests: 0,
            lastError: null,
            uptime: 0,
            startTime: Date.now()
        };
        
        // Historique des erreurs
        this.errorHistory = [];
        this.maxErrorHistory = 100;
        
        // Callbacks pour les événements
        this.callbacks = {
            onConnect: [],
            onDisconnect: [],
            onError: [],
            onReconnect: []
        };
    }

    // Démarrer le monitoring
    async startMonitoring(supabaseClient) {
        if (this.isMonitoring) {
            console.log('⚠️ Monitoring déjà actif');
            return;
        }

        this.client = supabaseClient;
        this.isMonitoring = true;
        this.metrics.startTime = Date.now();
        
        console.log('📊 Démarrage du monitoring Supabase...');
        
        // Démarrer les vérifications périodiques
        this.startHealthChecks();
        this.startMetricsCollection();
        
        // Vérification initiale
        await this.performHealthCheck();
        
        console.log('✅ Monitoring Supabase actif');
    }

    // Arrêter le monitoring
    stopMonitoring() {
        this.isMonitoring = false;
        
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
        }
        
        if (this.metricsInterval) {
            clearInterval(this.metricsInterval);
        }
        
        console.log('🛑 Monitoring Supabase arrêté');
    }

    // Démarrer les vérifications de santé
    startHealthChecks() {
        const interval = this.config.monitoring?.healthCheckInterval || 30000;
        
        this.healthCheckInterval = setInterval(async () => {
            if (this.isMonitoring) {
                await this.performHealthCheck();
            }
        }, interval);
    }

    // Démarrer la collecte de métriques
    startMetricsCollection() {
        const interval = this.config.monitoring?.metricsInterval || 60000;
        
        this.metricsInterval = setInterval(() => {
            if (this.isMonitoring) {
                this.updateMetrics();
                this.logMetrics();
            }
        }, interval);
    }

    // Effectuer une vérification de santé
    async performHealthCheck() {
        const startTime = Date.now();
        
        try {
            // Test simple de connectivité
            const { data, error } = await this.client
                .from('gestion_users')
                .select('count')
                .limit(1);
            
            const responseTime = Date.now() - startTime;
            
            if (error && error.code !== 'PGRST116') {
                throw error;
            }
            
            // Connexion réussie
            if (this.metrics.connectionStatus !== 'connected') {
                this.onConnectionRestored();
            }
            
            this.metrics.connectionStatus = 'connected';
            this.metrics.lastHealthCheck = new Date().toISOString();
            this.metrics.successCount++;
            this.metrics.totalRequests++;
            this.updateAverageResponseTime(responseTime);
            this.reconnectAttempts = 0;
            
        } catch (error) {
            this.handleHealthCheckError(error);
        }
    }

    // Gérer les erreurs de vérification de santé
    handleHealthCheckError(error) {
        this.metrics.connectionStatus = 'disconnected';
        this.metrics.errorCount++;
        this.metrics.totalRequests++;
        this.metrics.lastError = {
            message: error.message,
            timestamp: new Date().toISOString(),
            type: 'health_check'
        };
        
        this.addToErrorHistory(error, 'health_check');
        
        // Déclencher la reconnexion automatique si activée
        if (this.config.sync?.autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnection();
        }
        
        // Déclencher les callbacks d'erreur
        this.triggerCallbacks('onError', error);
        
        console.error('❌ Erreur health check Supabase:', error);
    }

    // Tentative de reconnexion
    async attemptReconnection() {
        this.reconnectAttempts++;
        
        console.log(`🔄 Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts}...`);
        
        setTimeout(async () => {
            try {
                await this.performHealthCheck();
                
                if (this.metrics.connectionStatus === 'connected') {
                    console.log('✅ Reconnexion réussie');
                    this.triggerCallbacks('onReconnect');
                }
            } catch (error) {
                console.warn(`⚠️ Échec de reconnexion ${this.reconnectAttempts}:`, error);
                
                if (this.reconnectAttempts >= this.maxReconnectAttempts) {
                    console.error('❌ Nombre maximum de tentatives de reconnexion atteint');
                    this.triggerCallbacks('onDisconnect');
                }
            }
        }, this.reconnectDelay);
    }

    // Connexion restaurée
    onConnectionRestored() {
        console.log('🔗 Connexion Supabase restaurée');
        this.triggerCallbacks('onConnect');
    }

    // Mettre à jour le temps de réponse moyen
    updateAverageResponseTime(responseTime) {
        if (this.metrics.averageResponseTime === 0) {
            this.metrics.averageResponseTime = responseTime;
        } else {
            this.metrics.averageResponseTime = 
                (this.metrics.averageResponseTime + responseTime) / 2;
        }
    }

    // Mettre à jour les métriques
    updateMetrics() {
        this.metrics.uptime = Date.now() - this.metrics.startTime;
        
        // Calculer le taux de succès
        if (this.metrics.totalRequests > 0) {
            this.metrics.successRate = 
                (this.metrics.successCount / this.metrics.totalRequests) * 100;
        }
    }

    // Logger les métriques
    logMetrics() {
        if (this.config.monitoring?.logLevel === 'debug') {
            console.log('📊 Métriques Supabase:', {
                status: this.metrics.connectionStatus,
                uptime: Math.round(this.metrics.uptime / 1000) + 's',
                successRate: Math.round(this.metrics.successRate || 0) + '%',
                avgResponseTime: Math.round(this.metrics.averageResponseTime) + 'ms',
                errors: this.metrics.errorCount,
                reconnectAttempts: this.reconnectAttempts
            });
        }
    }

    // Ajouter une erreur à l'historique
    addToErrorHistory(error, type = 'unknown') {
        const errorEntry = {
            message: error.message,
            type: type,
            timestamp: new Date().toISOString(),
            stack: error.stack
        };
        
        this.errorHistory.unshift(errorEntry);
        
        // Limiter la taille de l'historique
        if (this.errorHistory.length > this.maxErrorHistory) {
            this.errorHistory = this.errorHistory.slice(0, this.maxErrorHistory);
        }
        
        // Vérifier le seuil d'alerte
        const alertThreshold = this.config.monitoring?.alertThreshold || 5;
        const recentErrors = this.errorHistory.filter(e => 
            Date.now() - new Date(e.timestamp).getTime() < 300000 // 5 minutes
        );
        
        if (recentErrors.length >= alertThreshold) {
            this.triggerAlert(recentErrors);
        }
    }

    // Déclencher une alerte
    triggerAlert(recentErrors) {
        const alert = {
            type: 'high_error_rate',
            message: `${recentErrors.length} erreurs détectées en 5 minutes`,
            timestamp: new Date().toISOString(),
            errors: recentErrors
        };
        
        console.warn('🚨 ALERTE Supabase:', alert);
        
        // Déclencher les callbacks d'erreur avec l'alerte
        this.triggerCallbacks('onError', alert);
    }

    // Ajouter un callback pour les événements
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Supprimer un callback
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    // Déclencher les callbacks
    triggerCallbacks(event, data = null) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les métriques actuelles
    getMetrics() {
        return { ...this.metrics };
    }

    // Obtenir l'historique des erreurs
    getErrorHistory() {
        return [...this.errorHistory];
    }

    // Obtenir un rapport de santé complet
    getHealthReport() {
        return {
            status: this.metrics.connectionStatus,
            uptime: this.metrics.uptime,
            metrics: this.getMetrics(),
            recentErrors: this.errorHistory.slice(0, 10),
            reconnectAttempts: this.reconnectAttempts,
            isMonitoring: this.isMonitoring,
            timestamp: new Date().toISOString()
        };
    }

    // Réinitialiser les métriques
    resetMetrics() {
        this.metrics = {
            connectionStatus: this.metrics.connectionStatus,
            lastHealthCheck: null,
            errorCount: 0,
            successCount: 0,
            averageResponseTime: 0,
            totalRequests: 0,
            lastError: null,
            uptime: 0,
            startTime: Date.now()
        };
        
        this.errorHistory = [];
        this.reconnectAttempts = 0;
        
        console.log('🔄 Métriques Supabase réinitialisées');
    }

    // Forcer une vérification de santé
    async forceHealthCheck() {
        console.log('🔍 Vérification de santé forcée...');
        await this.performHealthCheck();
        return this.getHealthReport();
    }
}

// Export de la classe
if (typeof window !== 'undefined') {
    window.SupabaseMonitor = SupabaseMonitor;
}
