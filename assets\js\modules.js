// Modules supplémentaires pour la gestion des commandes et PV

// ===== GESTION DES COMMANDES =====

function toggleGestionCommandes() {
    if (!canManageCommands()) {
        alert('Accès non autorisé');
        return;
    }
    
    hideAllModuleZones();
    document.getElementById('gestionCommandesZone').style.display = 'block';
    afficherListeCommandes();
}

function afficherListeCommandes() {
    document.getElementById('listeCommandesView').style.display = 'block';
    document.getElementById('formulaireCommandeContainer').style.display = 'none';
    
    const tbody = document.getElementById('commandesTableBody');
    tbody.innerHTML = '';
    
    commandes.forEach(commande => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${commande.numeroCommande}</td>
            <td>${commande.fournisseur || 'N/A'}</td>
            <td>${commande.montantTotal ? commande.montantTotal.toFixed(2) + ' ' + commande.devise : 'N/A'}</td>
            <td><span class="produit-status ${commande.statut}">${commande.statut}</span></td>
            <td>${formatDate(commande.createdAt)}</td>
            <td>
                <button onclick="consulterCommande('${commande.id}')" class="action-button secondary">Consulter</button>
                <button onclick="chargerEtAfficherCommande('${commande.id}')" class="action-button primary">Modifier</button>
                ${isAdmin() ? `<button onclick="supprimerCommande('${commande.id}')" class="action-button danger">Supprimer</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

function preparerNouvelleCommande() {
    commandeEnCours = null;
    viderFormulaireCommande();
    document.getElementById('titreFormulaire').textContent = 'Nouvelle Commande';
    document.getElementById('listeCommandesView').style.display = 'none';
    document.getElementById('formulaireCommandeContainer').style.display = 'block';
    updateWorkflowUI();
}

function retourListeCommandes() {
    document.getElementById('listeCommandesView').style.display = 'block';
    document.getElementById('formulaireCommandeContainer').style.display = 'none';
    commandeEnCours = null;
}

function handleCommandeAction(action) {
    switch (action) {
        case 'valider':
            validerEtapeCommande();
            break;
        case 'envoyer':
            envoyerEmailCommande();
            break;
        case 'soumettre':
            soumettreCommande();
            break;
    }
}

function validerEtapeCommande() {
    const etapeActuelle = commandeEnCours ? commandeEnCours.etapeActuelle : 1;
    
    if (etapeActuelle === 1) {
        const numeroCommande = document.getElementById('numeroCommande').value.trim();
        const fournisseur = document.getElementById('fournisseur').value.trim();
        
        if (!numeroCommande || !fournisseur) {
            alert('Veuillez remplir tous les champs obligatoires de l\'étape 1');
            return;
        }
        
        document.getElementById('etape2').disabled = false;
        if (commandeEnCours) commandeEnCours.etapeActuelle = 2;
        
    } else if (etapeActuelle === 2) {
        const produits = collecterProduits();
        if (produits.length === 0) {
            alert('Veuillez ajouter au moins un produit');
            return;
        }
        
        document.getElementById('etape3').disabled = false;
        if (commandeEnCours) commandeEnCours.etapeActuelle = 3;
    }
    
    sauvegarderEtatCommande();
    updateWorkflowUI();
}

function collecterProduits() {
    const produits = [];
    const produitsContainer = document.getElementById('produitsContainer');
    const produitItems = produitsContainer.querySelectorAll('.produit-item');
    
    produitItems.forEach(item => {
        const nom = item.querySelector('.produit-nom').value.trim();
        const quantite = parseInt(item.querySelector('.produit-quantite').value);
        const prix = parseFloat(item.querySelector('.produit-prix').value);
        
        if (nom && quantite > 0 && prix >= 0) {
            produits.push({ nom, quantite, prix });
        }
    });
    
    return produits;
}

function ajouterProduit() {
    const container = document.getElementById('produitsContainer');
    const newItem = document.createElement('div');
    newItem.className = 'produit-item';
    newItem.innerHTML = `
        <input type="text" placeholder="Nom du produit" class="produit-nom">
        <input type="number" placeholder="Quantité" class="produit-quantite">
        <input type="number" step="0.01" placeholder="Prix unitaire" class="produit-prix">
        <button type="button" onclick="supprimerProduit(this)" class="danger">-</button>
    `;
    container.appendChild(newItem);
}

function supprimerProduit(button) {
    button.parentElement.remove();
}

function envoyerEmailCommande() {
    const numeroCommande = document.getElementById('numeroCommande').value;
    const fournisseur = document.getElementById('fournisseur').value;
    
    if (!numeroCommande || !fournisseur) {
        alert('Veuillez remplir au moins le numéro de commande et le fournisseur');
        return;
    }
    
    alert(`Email envoyé au fournisseur ${fournisseur} pour la commande ${numeroCommande}`);
    envoyerMessageAutomatique(`Email envoyé pour la commande ${numeroCommande} par ${utilisateurCourant.username}`);
}

async function soumettreCommande() {
    const numeroCommande = document.getElementById('numeroCommande').value.trim();
    const fournisseur = document.getElementById('fournisseur').value.trim();
    const contactFournisseur = document.getElementById('contactFournisseur').value.trim();
    const produits = collecterProduits();
    const devise = document.getElementById('devise').value;
    const dateLivraison = document.getElementById('dateLivraison').value;
    const conditionsPaiement = document.getElementById('conditionsPaiement').value.trim();
    const notes = document.getElementById('notes').value.trim();
    
    if (!numeroCommande || !fournisseur || produits.length === 0) {
        alert('Veuillez remplir tous les champs obligatoires');
        return;
    }
    
    const montantTotal = produits.reduce((total, p) => total + (p.quantite * p.prix), 0);
    
    const commande = {
        id: commandeEnCours ? commandeEnCours.id : generateId(),
        numeroCommande,
        fournisseur,
        contactFournisseur,
        produits,
        montantTotal,
        devise,
        statut: 'soumise',
        etapeActuelle: 3,
        createdBy: utilisateurCourant.username,
        createdAt: commandeEnCours ? commandeEnCours.createdAt : new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        dateLivraisonPrevue: dateLivraison,
        conditionsPaiement,
        notes
    };
    
    if (commandeEnCours) {
        const index = commandes.findIndex(c => c.id === commandeEnCours.id);
        if (index !== -1) {
            commandes[index] = commande;
        }
    } else {
        commandes.push(commande);
    }
    
    try {
        await DatabaseManager.save('commandes', commande);
        console.log('Commande sauvegardée avec succès');
    } catch (error) {
        console.error('Erreur lors de la sauvegarde de la commande:', error);
    }
    
    alert('Commande soumise avec succès !');
    envoyerMessageAutomatique(`Commande ${numeroCommande} soumise par ${utilisateurCourant.username}`);
    
    retourListeCommandes();
    afficherListeCommandes();
}

function viderFormulaireCommande() {
    document.getElementById('formulaireCommande').reset();
    document.getElementById('produitsContainer').innerHTML = `
        <div class="produit-item">
            <input type="text" placeholder="Nom du produit" class="produit-nom">
            <input type="number" placeholder="Quantité" class="produit-quantite">
            <input type="number" step="0.01" placeholder="Prix unitaire" class="produit-prix">
            <button type="button" onclick="ajouterProduit()">+</button>
        </div>
    `;
    
    document.getElementById('etape1').disabled = false;
    document.getElementById('etape2').disabled = true;
    document.getElementById('etape3').disabled = true;
}

function updateWorkflowUI() {
    const etapeActuelle = commandeEnCours ? commandeEnCours.etapeActuelle : 1;
    
    document.getElementById('etape1').disabled = false;
    document.getElementById('etape2').disabled = etapeActuelle < 2;
    document.getElementById('etape3').disabled = etapeActuelle < 3;
    
    const fieldsets = document.querySelectorAll('#formulaireCommandeContainer fieldset');
    fieldsets.forEach((fieldset, index) => {
        if (index + 1 <= etapeActuelle) {
            fieldset.style.opacity = '1';
            fieldset.style.background = 'var(--light-color)';
        } else {
            fieldset.style.opacity = '0.6';
            fieldset.style.background = '#f5f5f5';
        }
    });
}

function sauvegarderEtatCommande() {
    if (!commandeEnCours) {
        commandeEnCours = {
            id: generateId(),
            etapeActuelle: 1,
            createdAt: new Date().toISOString(),
            createdBy: utilisateurCourant.username
        };
    }
    
    commandeEnCours.numeroCommande = document.getElementById('numeroCommande').value;
    commandeEnCours.fournisseur = document.getElementById('fournisseur').value;
    commandeEnCours.contactFournisseur = document.getElementById('contactFournisseur').value;
    commandeEnCours.produits = collecterProduits();
    commandeEnCours.devise = document.getElementById('devise').value;
    commandeEnCours.dateLivraisonPrevue = document.getElementById('dateLivraison').value;
    commandeEnCours.conditionsPaiement = document.getElementById('conditionsPaiement').value;
    commandeEnCours.notes = document.getElementById('notes').value;
    commandeEnCours.updatedAt = new Date().toISOString();
}

function consulterCommande(commandeId) {
    const commande = commandes.find(c => c.id === commandeId);
    if (!commande) {
        alert('Commande non trouvée');
        return;
    }
    
    const produitsText = commande.produits ? 
        commande.produits.map(p => `${p.nom} (Qté: ${p.quantite}, Prix: ${p.prix})`).join('\n') : 
        'Aucun produit';
    
    alert(`Détails de la commande ${commande.numeroCommande}:
    
Fournisseur: ${commande.fournisseur || 'N/A'}
Contact: ${commande.contactFournisseur || 'N/A'}
Montant total: ${commande.montantTotal ? commande.montantTotal.toFixed(2) + ' ' + commande.devise : 'N/A'}
Statut: ${commande.statut}
Date de livraison: ${commande.dateLivraisonPrevue || 'N/A'}
Conditions de paiement: ${commande.conditionsPaiement || 'N/A'}
Notes: ${commande.notes || 'N/A'}

Produits:
${produitsText}`);
}

function chargerEtAfficherCommande(commandeId) {
    const commande = commandes.find(c => c.id === commandeId);
    if (!commande) {
        alert('Commande non trouvée');
        return;
    }
    
    commandeEnCours = commande;
    chargerDonneesCommande(commande);
    document.getElementById('titreFormulaire').textContent = `Modifier Commande ${commande.numeroCommande}`;
    document.getElementById('listeCommandesView').style.display = 'none';
    document.getElementById('formulaireCommandeContainer').style.display = 'block';
    updateWorkflowUI();
}

function chargerDonneesCommande(commande) {
    document.getElementById('numeroCommande').value = commande.numeroCommande || '';
    document.getElementById('fournisseur').value = commande.fournisseur || '';
    document.getElementById('contactFournisseur').value = commande.contactFournisseur || '';
    document.getElementById('devise').value = commande.devise || 'EUR';
    document.getElementById('dateLivraison').value = commande.dateLivraisonPrevue || '';
    document.getElementById('conditionsPaiement').value = commande.conditionsPaiement || '';
    document.getElementById('notes').value = commande.notes || '';
    
    const container = document.getElementById('produitsContainer');
    container.innerHTML = '';
    
    if (commande.produits && commande.produits.length > 0) {
        commande.produits.forEach(produit => {
            const item = document.createElement('div');
            item.className = 'produit-item';
            item.innerHTML = `
                <input type="text" placeholder="Nom du produit" class="produit-nom" value="${produit.nom}">
                <input type="number" placeholder="Quantité" class="produit-quantite" value="${produit.quantite}">
                <input type="number" step="0.01" placeholder="Prix unitaire" class="produit-prix" value="${produit.prix}">
                <button type="button" onclick="supprimerProduit(this)" class="danger">-</button>
            `;
            container.appendChild(item);
        });
    } else {
        ajouterProduit();
    }
}

async function supprimerCommande(commandeId) {
    if (!isAdmin()) {
        alert('Accès non autorisé');
        return;
    }
    
    if (confirm('Êtes-vous sûr de vouloir supprimer cette commande ?')) {
        commandes = commandes.filter(c => c.id !== commandeId);
        
        try {
            await DatabaseManager.delete('commandes', commandeId);
            console.log('Commande supprimée avec succès');
        } catch (error) {
            console.error('Erreur lors de la suppression de la commande:', error);
        }
        
        afficherListeCommandes();
        envoyerMessageAutomatique(`Commande supprimée par ${utilisateurCourant.username}`);
    }
}

// ===== GESTION DES PV DÉPÔTS =====

function toggleGestionPvZone() {
    console.log('🔄 Basculement vers la zone de gestion PV...');

    if (!canManagePV()) {
        alert('Accès non autorisé');
        return;
    }

    hideAllModuleZones();

    const gestionPvZone = document.getElementById('gestionPvZone');
    if (!gestionPvZone) {
        console.error('❌ Element gestionPvZone non trouvé');
        return;
    }

    gestionPvZone.style.display = 'block';
    console.log('✅ Zone gestion PV affichée');

    if (isDepot()) {
        console.log('🏪 Mode dépôt - Affichage création PV');
        document.getElementById('creationPvView').style.display = 'block';
        document.getElementById('consultationPvView').style.display = 'none';
        loadProductsForDepot();
    } else if (isHygiene()) {
        console.log('🧪 Mode hygiène - Redirection vers zone hygiène');
        // Pour l'équipe hygiène, afficher leur zone spécialisée
        setTimeout(() => {
            afficherZoneHygiene();
        }, 100);
    } else {
        console.log('👨‍💼 Mode admin/manager - Affichage consultation PV');
        document.getElementById('creationPvView').style.display = 'none';
        document.getElementById('consultationPvView').style.display = 'block';
        document.getElementById('retourCreationBtn').style.display = 'none';

        // Attendre que les éléments soient visibles avant d'afficher les données
        setTimeout(() => {
            displayAdminPVs();
        }, 100);
    }
}

function loadProductsForDepot() {
    console.log('🏪 Chargement des produits pour le dépôt...');

    const tbody = document.getElementById('produitsPvTableBody');
    if (!tbody) {
        console.error('❌ Element produitsPvTableBody non trouvé');
        return;
    }

    tbody.innerHTML = '';
    console.log(`📦 Produits disponibles: ${products.length}`);

    // Grouper les produits par catégorie
    const productsByCategory = {};
    products.forEach(product => {
        const category = product.categorie || 'Autres';
        if (!productsByCategory[category]) {
            productsByCategory[category] = [];
        }
        productsByCategory[category].push(product);
    });

    const categories = Object.keys(productsByCategory);
    console.log(`📂 Catégories trouvées: ${categories.length} (${categories.join(', ')})`);

    // Afficher les produits groupés par catégorie
    Object.keys(productsByCategory).sort().forEach(category => {
        // Ajouter une ligne de titre de catégorie
        const categoryRow = document.createElement('tr');
        categoryRow.className = 'category-header';
        categoryRow.innerHTML = `
            <td colspan="6" class="category-title">
                <strong>📂 ${category}</strong>
                <button onclick="toggleCategorySelection('${category}')" class="action-button secondary" style="float: right;">
                    Sélectionner tout
                </button>
            </td>
        `;
        tbody.appendChild(categoryRow);

        // Ajouter les produits de cette catégorie
        productsByCategory[category].forEach(product => {
            const row = document.createElement('tr');
            row.className = `product-row category-${category.replace(/\s+/g, '-').toLowerCase()}`;

            // Déterminer le statut selon la date d'expiration
            let expirationStatus = getExpirationStatus(product.expiryDate || product.dateExpiration);

            // Si pas de date d'expiration, utiliser "Non définie"
            if (!product.expiryDate && !product.dateExpiration) {
                expirationStatus = {
                    class: 'no-date',
                    icon: '⚪',
                    text: 'Non définie',
                    daysText: 'Date non renseignée'
                };
            }

            row.innerHTML = `
                <td class="product-id-cell">
                    <div class="product-id-display">
                        <strong>ID: ${product.id}</strong>
                    </div>
                    <input type="checkbox" id="product_${product.id}" onchange="updateProductStatus(${product.id})" data-category="${category}">
                </td>
                <td>
                    <div class="product-name">${product.name || product.nom}</div>
                    <small class="product-category">${category}</small>
                </td>
                <td class="expiration-cell ${expirationStatus.class}">
                    <div class="expiration-info">
                        <span class="expiration-icon">${expirationStatus.icon}</span>
                        <input type="date"
                               id="expiry_${product.id}"
                               class="expiry-date-input"
                               value="${product.expiryDate || product.dateExpiration || ''}"
                               onchange="updateExpiryDate(${product.id})"
                               title="Modifier la date d'expiration">
                    </div>
                    <small class="expiration-status">${expirationStatus.text}</small>
                    <small class="days-info">${expirationStatus.daysText}</small>
                    <div class="expiry-actions">
                        <button type="button" class="btn-mini btn-success" onclick="markAsValid(${product.id})" title="Marquer comme valable">
                            ✅ Valable
                        </button>
                        <button type="button" class="btn-mini btn-danger" onclick="markAsExpired(${product.id})" title="Marquer comme périmé">
                            ❌ Périmé
                        </button>
                    </div>
                </td>
                <td class="status-cell">
                    <div class="status-display" id="status_display_${product.id}">
                        ${getProductStatusDisplay(product)}
                    </div>
                    <select id="status_${product.id}" class="status-select" onchange="updateProductStatusManual(${product.id})">
                        <option value="conforme">Conforme</option>
                        <option value="non-conforme">Non conforme</option>
                        <option value="verifie">Vérifié</option>
                    </select>
                </td>
                <td class="quantity-cell">
                    <div class="quantity-controls">
                        <button type="button" class="qty-btn qty-minus" onclick="adjustQuantity(${product.id}, -1)" title="Diminuer">-</button>
                        <input type="number"
                               id="quantity_${product.id}"
                               min="0"
                               max="9999"
                               value="0"
                               class="quantity-input"
                               placeholder="Qté"
                               onchange="updateQuantity(${product.id})"
                               oninput="updateQuantity(${product.id})">
                        <button type="button" class="qty-btn qty-plus" onclick="adjustQuantity(${product.id}, 1)" title="Augmenter">+</button>
                    </div>
                    <small class="quantity-unit">unités</small>
                </td>
                <td>
                    <button onclick="toggleProductInput(${product.id})" class="action-button secondary">
                        <span id="toggle_text_${product.id}">Activer</span>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    });
}

function toggleCategorySelection(category) {
    const checkboxes = document.querySelectorAll(`input[data-category="${category}"]`);
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
        const productId = parseInt(checkbox.id.replace('product_', ''));
        updateProductStatus(productId);
    });
}

function formatDateFr(dateString) {
    if (!dateString) return 'Non définie';
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

// Fonction pour déterminer le statut d'un produit selon sa date d'expiration
function getProductStatusDisplay(product) {
    const dateString = product.expiryDate || product.dateExpiration;

    if (!dateString) {
        return '<span class="status-badge status-unknown">⚪ À vérifier</span>';
    }

    const expirationDate = new Date(dateString);
    const today = new Date();
    const daysUntilExpiration = Math.ceil((expirationDate - today) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiration <= 0) {
        // Produit périmé
        return '<span class="status-badge status-expired">❌ Non conforme</span>';
    } else {
        // Produit valable
        return '<span class="status-badge status-valid">✅ Conforme</span>';
    }
}

// Fonction pour déterminer le statut d'expiration d'un produit
function getExpirationStatus(dateString) {
    if (!dateString) {
        return {
            class: 'no-date',
            icon: '⚪',
            text: 'Non définie',
            daysText: 'Date non renseignée'
        };
    }

    const expirationDate = new Date(dateString);
    const today = new Date();
    const daysUntilExpiration = Math.ceil((expirationDate - today) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiration < 0) {
        const daysExpired = Math.abs(daysUntilExpiration);
        return {
            class: 'expired',
            icon: '❌',
            text: 'PÉRIMÉ',
            daysText: `Expiré depuis ${daysExpired} jour${daysExpired > 1 ? 's' : ''}`
        };
    } else if (daysUntilExpiration === 0) {
        return {
            class: 'expiring-today',
            icon: '❌',
            text: 'PÉRIMÉ',
            daysText: 'Expire aujourd\'hui - À retirer'
        };
    } else if (daysUntilExpiration <= 3) {
        return {
            class: 'expiring-critical',
            icon: '⚠️',
            text: 'CRITIQUE',
            daysText: `Expire dans ${daysUntilExpiration} jour${daysUntilExpiration > 1 ? 's' : ''}`
        };
    } else if (daysUntilExpiration <= 7) {
        return {
            class: 'expiring-soon',
            icon: '⚠️',
            text: 'PROCHE',
            daysText: `Expire dans ${daysUntilExpiration} jours`
        };
    } else if (daysUntilExpiration <= 30) {
        return {
            class: 'expiring-month',
            icon: '⚠️',
            text: 'ATTENTION',
            daysText: `Expire dans ${daysUntilExpiration} jours`
        };
    } else {
        return {
            class: 'valid',
            icon: '✅',
            text: 'VALABLE',
            daysText: `Expire dans ${daysUntilExpiration} jours`
        };
    }
}

function updateProductStatus(productId) {
    const checkbox = document.getElementById(`product_${productId}`);
    const statusSelect = document.getElementById(`status_${productId}`);
    const quantityInput = document.getElementById(`quantity_${productId}`);
    const toggleText = document.getElementById(`toggle_text_${productId}`);

    if (checkbox.checked) {
        statusSelect.disabled = false;
        quantityInput.disabled = false;
        quantityInput.focus();
        if (toggleText) toggleText.textContent = 'Désactiver';

        // Mettre une valeur par défaut si vide
        if (!quantityInput.value) {
            quantityInput.value = '1';
        }
    } else {
        statusSelect.disabled = true;
        quantityInput.disabled = true;
        statusSelect.value = 'conforme';
        quantityInput.value = '';
        if (toggleText) toggleText.textContent = 'Activer';
    }
}

function toggleProductInput(productId) {
    const checkbox = document.getElementById(`product_${productId}`);
    checkbox.checked = !checkbox.checked;
    updateProductStatus(productId);
}

// Fonction pour mettre à jour la date d'expiration d'un produit
function updateExpiryDate(productId) {
    const dateInput = document.getElementById(`expiry_${productId}`);
    const newDate = dateInput.value;

    // Trouver le produit dans la liste et mettre à jour sa date
    const products = APP_CONFIG.defaultProducts;
    const productIndex = products.findIndex(p => p.id === productId);

    if (productIndex !== -1) {
        products[productIndex].expiryDate = newDate;
        products[productIndex].dateExpiration = newDate;

        console.log(`📅 Date d'expiration mise à jour pour le produit ${productId}: ${newDate}`);

        // Rafraîchir l'affichage du statut d'expiration
        refreshProductExpiryStatus(productId);

        // Mettre à jour le statut du produit selon la nouvelle date
        updateProductStatusByDate(productId, products[productIndex]);

        // Notification
        if (typeof showNotification === 'function') {
            showNotification(`Date d'expiration mise à jour pour le produit ${productId}`, 'success');
        }
    }
}

// Fonction pour mettre à jour le statut manuel d'un produit
function updateProductStatusManual(productId) {
    const statusSelect = document.getElementById(`status_${productId}`);
    const statusDisplay = document.getElementById(`status_display_${productId}`);
    const selectedValue = statusSelect.value;

    let statusHTML = '';
    switch(selectedValue) {
        case 'conforme':
            statusHTML = '<span class="status-badge status-valid">✅ Conforme</span>';
            break;
        case 'non-conforme':
            statusHTML = '<span class="status-badge status-expired">❌ Non conforme</span>';
            break;
        case 'verifie':
            statusHTML = '<span class="status-badge status-verified">🔍 Vérifié</span>';
            break;
        default:
            statusHTML = '<span class="status-badge status-unknown">⚪ À vérifier</span>';
    }

    statusDisplay.innerHTML = statusHTML;
    console.log(`📋 Statut manuel mis à jour pour le produit ${productId}: ${selectedValue}`);
}

// Fonction pour mettre à jour le statut automatiquement selon la date
function updateProductStatusByDate(productId, product) {
    const statusDisplay = document.getElementById(`status_display_${productId}`);
    const statusSelect = document.getElementById(`status_${productId}`);

    if (!statusDisplay || !statusSelect) return;

    const newStatusHTML = getProductStatusDisplay(product);
    statusDisplay.innerHTML = newStatusHTML;

    // Mettre à jour la valeur du select selon la date
    const dateString = product.expiryDate || product.dateExpiration;
    if (dateString) {
        const expirationDate = new Date(dateString);
        const today = new Date();
        const daysUntilExpiration = Math.ceil((expirationDate - today) / (1000 * 60 * 60 * 24));

        if (daysUntilExpiration <= 0) {
            statusSelect.value = 'non-conforme';
        } else {
            statusSelect.value = 'conforme';
        }
    } else {
        statusSelect.value = 'conforme'; // Par défaut
    }
}

// Fonction pour marquer un produit comme valable (ajouter 1 an à la date actuelle)
function markAsValid(productId) {
    const dateInput = document.getElementById(`expiry_${productId}`);
    const futureDate = new Date();
    futureDate.setFullYear(futureDate.getFullYear() + 1);
    const formattedDate = futureDate.toISOString().split('T')[0];

    dateInput.value = formattedDate;
    updateExpiryDate(productId);

    console.log(`✅ Produit ${productId} marqué comme valable jusqu'au ${formattedDate}`);
}

// Fonction pour marquer un produit comme périmé (date d'hier)
function markAsExpired(productId) {
    const dateInput = document.getElementById(`expiry_${productId}`);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const formattedDate = yesterday.toISOString().split('T')[0];

    dateInput.value = formattedDate;
    updateExpiryDate(productId);

    console.log(`❌ Produit ${productId} marqué comme périmé depuis le ${formattedDate}`);
}

// Fonction pour rafraîchir le statut d'expiration d'un produit
function refreshProductExpiryStatus(productId) {
    const dateInput = document.getElementById(`expiry_${productId}`);
    const expirationCell = dateInput.closest('.expiration-cell');
    const statusElement = expirationCell.querySelector('.expiration-status');
    const daysInfoElement = expirationCell.querySelector('.days-info');
    const iconElement = expirationCell.querySelector('.expiration-icon');

    if (!dateInput.value) {
        // Pas de date définie
        expirationCell.className = 'expiration-cell no-date';
        iconElement.textContent = '⚪';
        statusElement.textContent = 'NON DÉFINIE';
        daysInfoElement.textContent = 'Date non renseignée';
        return;
    }

    const expirationStatus = getExpirationStatus(dateInput.value);

    // Mettre à jour les classes CSS
    expirationCell.className = `expiration-cell ${expirationStatus.class}`;

    // Mettre à jour le contenu
    iconElement.textContent = expirationStatus.icon;
    statusElement.textContent = expirationStatus.text;
    daysInfoElement.textContent = expirationStatus.daysText;

    console.log(`🔄 Statut mis à jour pour produit ${productId}: ${expirationStatus.text} (${expirationStatus.daysText})`);
}

// Fonction pour ajuster la quantité avec les boutons +/-
function adjustQuantity(productId, change) {
    const quantityInput = document.getElementById(`quantity_${productId}`);
    const currentValue = parseInt(quantityInput.value) || 0;
    const newValue = Math.max(0, Math.min(9999, currentValue + change));

    quantityInput.value = newValue;
    updateQuantity(productId);

    // Auto-activer le produit si quantité > 0
    if (newValue > 0) {
        const checkbox = document.getElementById(`product_${productId}`);
        if (!checkbox.checked) {
            checkbox.checked = true;
            updateProductStatus(productId);
        }
    }
}

// Fonction pour mettre à jour la quantité
function updateQuantity(productId) {
    const quantityInput = document.getElementById(`quantity_${productId}`);
    const checkbox = document.getElementById(`product_${productId}`);
    const value = parseInt(quantityInput.value) || 0;

    // Valider la valeur
    if (value < 0) {
        quantityInput.value = 0;
        return;
    }
    if (value > 9999) {
        quantityInput.value = 9999;
        return;
    }

    // Auto-activer/désactiver selon la quantité
    if (value > 0 && !checkbox.checked) {
        checkbox.checked = true;
        updateProductStatus(productId);
    } else if (value === 0 && checkbox.checked) {
        checkbox.checked = false;
        updateProductStatus(productId);
    }

    // Mettre à jour l'affichage visuel
    updateQuantityDisplay(productId, value);

    console.log(`📦 Quantité mise à jour pour le produit ${productId}: ${value}`);
}

// Fonction pour mettre à jour l'affichage visuel de la quantité
function updateQuantityDisplay(productId, quantity) {
    const quantityCell = document.getElementById(`quantity_${productId}`).closest('.quantity-cell');
    const unitElement = quantityCell.querySelector('.quantity-unit');

    // Mettre à jour le texte de l'unité
    if (quantity === 0) {
        unitElement.textContent = 'unités';
        quantityCell.classList.remove('has-quantity');
    } else if (quantity === 1) {
        unitElement.textContent = 'unité';
        quantityCell.classList.add('has-quantity');
    } else {
        unitElement.textContent = 'unités';
        quantityCell.classList.add('has-quantity');
    }
}

function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('#produitsPvTableBody input[type="checkbox"]');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);

    checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
        const productId = checkbox.id.replace('product_', '');
        updateProductStatus(parseInt(productId));
    });
}

async function createPv() {
    const selectedProducts = [];
    const checkboxes = document.querySelectorAll('#produitsPvTableBody input[type="checkbox"]:checked');

    if (checkboxes.length === 0) {
        alert('Veuillez sélectionner au moins un produit');
        return;
    }

    checkboxes.forEach(checkbox => {
        const productId = parseInt(checkbox.id.replace('product_', ''));
        const product = products.find(p => p.id === productId);
        const status = document.getElementById(`status_${productId}`).value;
        const quantity = parseInt(document.getElementById(`quantity_${productId}`).value) || 0;

        if (product && quantity > 0) {
            selectedProducts.push({
                id: productId,
                nom: product.nom,
                dateExpiration: product.dateExpiration,
                statut: status,
                quantite: quantity
            });
        }
    });

    if (selectedProducts.length === 0) {
        alert('Veuillez renseigner les quantités pour les produits sélectionnés');
        return;
    }

    const pv = {
        id: generateId(),
        numeroPv: `PV-${utilisateurCourant.username}-${Date.now()}`,
        depot: utilisateurCourant.username,
        produits: selectedProducts,
        statut: 'en_attente',
        createdAt: new Date().toISOString(),
        notes: ''
    };

    pvs.push(pv);

    try {
        await DatabaseManager.save('pv_depots', pv);
        console.log('PV sauvegardé avec succès');
    } catch (error) {
        console.error('Erreur lors de la sauvegarde du PV:', error);
    }

    // Réinitialiser le formulaire
    const checkboxes2 = document.querySelectorAll('#produitsPvTableBody input[type="checkbox"]');
    checkboxes2.forEach(checkbox => {
        checkbox.checked = false;
        const productId = checkbox.id.replace('product_', '');
        updateProductStatus(parseInt(productId));
    });

    // Afficher message de succès
    const statusMessage = document.getElementById('pvStatusMessage');
    statusMessage.innerHTML = '<div class="status-message success">PV créé avec succès !</div>';
    setTimeout(() => {
        statusMessage.innerHTML = '';
    }, 3000);

    // Envoyer notification
    sendPvNotification(pv);

    alert(`PV ${pv.numeroPv} créé avec succès !`);
}

function sendPvNotification(pv) {
    const message = `Nouveau PV créé: ${pv.numeroPv} par ${pv.depot} - ${pv.produits.length} produit(s)`;
    envoyerMessageAutomatique(message);
}

function consulterTousPv() {
    document.getElementById('creationPvView').style.display = 'none';
    document.getElementById('consultationPvView').style.display = 'block';
    document.getElementById('retourCreationBtn').style.display = 'inline-block';
    displayAdminPVs();
}

function retourCreationPv() {
    document.getElementById('creationPvView').style.display = 'block';
    document.getElementById('consultationPvView').style.display = 'none';
    loadProductsForDepot();
}

function filtrerPvAdminView() {
    const searchTerm = document.getElementById('searchPv').value.toLowerCase();
    displayAdminPVs(searchTerm);
}

function displayAdminPVs(searchTerm = '') {
    console.log('🔍 Affichage des PV admin/dépôt...');

    // Vérifier si on est dans la vue consultation
    const consultationView = document.getElementById('consultationPvView');
    if (!consultationView || consultationView.style.display === 'none') {
        console.log('⚠️ Vue consultation PV non visible');
        return;
    }

    // Afficher le tableau détaillé des PV
    displayPvTable(searchTerm);

    // Masquer l'ancienne liste et afficher le tableau
    const pvList = document.getElementById('pvList');
    const tableContainer = document.querySelector('#consultationPvView .table-container');

    if (pvList) {
        pvList.style.display = 'none';
        console.log('📋 Ancienne liste PV masquée');
    }
    if (tableContainer) {
        tableContainer.style.display = 'block';
        console.log('📊 Tableau PV affiché');
    } else {
        console.error('❌ Container du tableau PV non trouvé');
    }
}

function displayPvTable(searchTerm = '') {
    console.log('📊 Génération du tableau PV...');

    const tbody = document.getElementById('pvConsultesTableBody');
    if (!tbody) {
        console.error('❌ Element pvConsultesTableBody non trouvé');
        return;
    }

    tbody.innerHTML = '';
    console.log(`📋 PV disponibles: ${pvs.length}`);

    let pvsFiltres = [...pvs];

    if (isDepot()) {
        pvsFiltres = pvsFiltres.filter(pv => pv.depot === utilisateurCourant.username);
        console.log(`🏪 Filtrage dépôt (${utilisateurCourant.username}): ${pvsFiltres.length} PV`);
    }

    if (searchTerm) {
        pvsFiltres = pvsFiltres.filter(pv =>
            pv.numeroPv.toLowerCase().includes(searchTerm) ||
            pv.depot.toLowerCase().includes(searchTerm) ||
            pv.statut.toLowerCase().includes(searchTerm)
        );
        console.log(`🔍 Filtrage recherche (${searchTerm}): ${pvsFiltres.length} PV`);
    }

    pvsFiltres.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    if (pvsFiltres.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="no-data">Aucun PV trouvé.</td></tr>';
        console.log('📭 Aucun PV à afficher');
        return;
    }

    console.log(`✅ Affichage de ${pvsFiltres.length} PV dans le tableau`);

    pvsFiltres.forEach(pv => {
        const row = document.createElement('tr');
        row.className = 'pv-row';

        const statutClass = pv.statut === 'en_attente' ? 'status-pending' :
                           pv.statut === 'valide' ? 'status-valid' : 'status-expired';

        const statutText = pv.statut === 'en_attente' ? '⏳ En attente' :
                          pv.statut === 'valide' ? '✅ Validé' : '❌ Rejeté';

        row.innerHTML = `
            <td>
                <input type="checkbox" id="pv_select_${pv.id}" class="pv-checkbox" onchange="togglePvSelection('${pv.id}')">
            </td>
            <td class="pv-numero-cell">
                <strong>${pv.numeroPv}</strong>
            </td>
            <td class="pv-depot-cell">
                <span class="depot-badge">${pv.depot}</span>
            </td>
            <td class="pv-date-cell">
                ${formatDate(pv.createdAt)}
            </td>
            <td class="pv-count-cell">
                <span class="count-badge">${pv.produits.length}</span>
            </td>
            <td class="pv-status-cell">
                <span class="status-badge ${statutClass}">${statutText}</span>
            </td>
            <td class="pv-actions-cell">
                <button onclick="afficherDetailsPV('${pv.id}')" class="btn-mini btn-info" title="Voir détails">
                    👁️ Détails
                </button>
                <button onclick="consulterPV('${pv.id}')" class="btn-mini btn-secondary" title="Consulter">
                    📋 Consulter
                </button>
                ${isAdmin() ? `<button onclick="supprimerPV('${pv.id}')" class="btn-mini btn-danger" title="Supprimer">🗑️</button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

function togglePvDetails(pvId) {
    const details = document.getElementById(`details_${pvId}`);
    if (details.classList.contains('show')) {
        details.classList.remove('show');
    } else {
        details.classList.add('show');
    }
}

function consulterPV(pvId) {
    const pv = pvs.find(p => p.id === pvId);
    if (!pv) {
        alert('PV non trouvé');
        return;
    }

    const produitsText = pv.produits.map(p =>
        `${p.nom} - Quantité: ${p.quantite} - Statut: ${p.statut} - Expiration: ${p.dateExpiration}`
    ).join('\n');

    alert(`Détails du PV ${pv.numeroPv}:

Dépôt: ${pv.depot}
Date de création: ${formatDate(pv.createdAt)}
Statut: ${pv.statut}
Nombre de produits: ${pv.produits.length}

Produits:
${produitsText}

Notes: ${pv.notes || 'Aucune note'}`);
}

async function supprimerPV(pvId) {
    if (!isAdmin()) {
        alert('Accès non autorisé');
        return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer ce PV ?')) {
        pvs = pvs.filter(p => p.id !== pvId);

        try {
            await DatabaseManager.delete('pv_depots', pvId);
            console.log('PV supprimé avec succès');
        } catch (error) {
            console.error('Erreur lors de la suppression du PV:', error);
        }

        displayAdminPVs();
        envoyerMessageAutomatique(`PV supprimé par ${utilisateurCourant.username}`);
    }
}

// Fonctions d'export (simplifiées pour la démo)
function exporterTousPvPdf() {
    if (typeof jsPDF === 'undefined') {
        alert('Bibliothèque PDF non disponible');
        return;
    }

    alert('Export PDF en cours... (fonctionnalité de démo)');
    console.log('Export PDF des PV pour:', utilisateurCourant.username);
}

function exporterTousPvExcel() {
    if (typeof XLSX === 'undefined') {
        alert('Bibliothèque Excel non disponible');
        return;
    }

    alert('Export Excel en cours... (fonctionnalité de démo)');
    console.log('Export Excel des PV pour:', utilisateurCourant.username);
}

function exportAllPvsToPdf() {
    exporterTousPvPdf();
}

function exportAllPvsToExcel() {
    exporterTousPvExcel();
}

async function clearAllPvs() {
    if (!isAdmin()) {
        alert('Accès non autorisé');
        return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer TOUS les PV ? Cette action est irréversible.')) {
        pvs = [];

        try {
            // En production, supprimer tous les PV de Supabase
            console.log('Suppression de tous les PV (simulée)');
        } catch (error) {
            console.error('Erreur lors de la suppression des PV:', error);
        }

        displayAdminPVs();
        envoyerMessageAutomatique(`Tous les PV ont été supprimés par ${utilisateurCourant.username}`);
        alert('Tous les PV ont été supprimés');
    }
}

// Fonction pour afficher les détails d'un PV dans la section dédiée
function afficherDetailsPV(pvId) {
    const pv = pvs.find(p => p.id === pvId);
    if (!pv) {
        alert('PV non trouvé');
        return;
    }

    const detailsSection = document.getElementById('pvDetailsSection');
    const detailsContent = document.getElementById('pvDetailsContent');

    if (!detailsSection || !detailsContent) return;

    // Construire le contenu détaillé
    const produitsHTML = pv.produits.map(produit => `
        <tr>
            <td>${produit.id || 'N/A'}</td>
            <td>${produit.nom}</td>
            <td>${produit.quantite}</td>
            <td>
                <span class="status-badge ${produit.statut === 'conforme' ? 'status-valid' : 'status-expired'}">
                    ${produit.statut === 'conforme' ? '✅ Conforme' : '❌ Non conforme'}
                </span>
            </td>
            <td>${produit.dateExpiration || 'Non définie'}</td>
        </tr>
    `).join('');

    detailsContent.innerHTML = `
        <div class="pv-details-header">
            <div class="pv-info-grid">
                <div class="pv-info-item">
                    <strong>N° PV:</strong> ${pv.numeroPv}
                </div>
                <div class="pv-info-item">
                    <strong>Dépôt:</strong> ${pv.depot}
                </div>
                <div class="pv-info-item">
                    <strong>Date de création:</strong> ${formatDate(pv.createdAt)}
                </div>
                <div class="pv-info-item">
                    <strong>Statut:</strong>
                    <span class="status-badge ${pv.statut === 'en_attente' ? 'status-pending' : pv.statut === 'valide' ? 'status-valid' : 'status-expired'}">
                        ${pv.statut === 'en_attente' ? '⏳ En attente' : pv.statut === 'valide' ? '✅ Validé' : '❌ Rejeté'}
                    </span>
                </div>
            </div>
        </div>

        <div class="pv-products-details">
            <h5>Produits (${pv.produits.length}):</h5>
            <table class="products-detail-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nom du produit</th>
                        <th>Quantité</th>
                        <th>Statut</th>
                        <th>Date d'expiration</th>
                    </tr>
                </thead>
                <tbody>
                    ${produitsHTML}
                </tbody>
            </table>
        </div>

        ${pv.notes ? `
            <div class="pv-notes">
                <h5>Notes:</h5>
                <p>${pv.notes}</p>
            </div>
        ` : ''}

        <div class="pv-actions-detail">
            <button onclick="envoyerPvSpecifique('${pv.id}')" class="btn-primary">
                📧 Envoyer ce PV à l'Équipe Hygiène
            </button>
            <button onclick="masquerDetailsPV()" class="btn-secondary">
                ✖️ Fermer
            </button>
        </div>
    `;

    detailsSection.style.display = 'block';
    detailsSection.scrollIntoView({ behavior: 'smooth' });
}

// Fonction pour masquer les détails du PV
function masquerDetailsPV() {
    const detailsSection = document.getElementById('pvDetailsSection');
    if (detailsSection) {
        detailsSection.style.display = 'none';
    }
}

// Fonction pour gérer la sélection des PV
function togglePvSelection(pvId) {
    const checkbox = document.getElementById(`pv_select_${pvId}`);
    console.log(`PV ${pvId} ${checkbox.checked ? 'sélectionné' : 'désélectionné'}`);
}

// Fonction pour envoyer les PV sélectionnés à l'équipe hygiène
function envoyerPvHygiene() {
    const checkboxes = document.querySelectorAll('.pv-checkbox:checked');

    if (checkboxes.length === 0) {
        alert('Veuillez sélectionner au moins un PV à envoyer à l\'équipe hygiène.');
        return;
    }

    const pvsSelectionnes = [];
    checkboxes.forEach(checkbox => {
        const pvId = checkbox.id.replace('pv_select_', '');
        const pv = pvs.find(p => p.id === pvId);
        if (pv) {
            pvsSelectionnes.push(pv);
        }
    });

    if (pvsSelectionnes.length === 0) {
        alert('Aucun PV valide sélectionné.');
        return;
    }

    // Construire le message pour l'équipe hygiène
    const message = construireMessageHygiene(pvsSelectionnes);

    // Envoyer le message
    envoyerMessageAutomatique(message);

    // Notification de succès
    alert(`${pvsSelectionnes.length} PV(s) envoyé(s) à l'équipe hygiène (Yosra et Moncef).`);

    // Décocher les cases
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

// Fonction pour envoyer un PV spécifique
function envoyerPvSpecifique(pvId) {
    const pv = pvs.find(p => p.id === pvId);
    if (!pv) {
        alert('PV non trouvé');
        return;
    }

    const message = construireMessageHygiene([pv]);
    envoyerMessageAutomatique(message);

    alert(`PV ${pv.numeroPv} envoyé à l'équipe hygiène (Yosra et Moncef).`);
    masquerDetailsPV();
}

// Fonction pour construire le message pour l'équipe hygiène
function construireMessageHygiene(pvs) {
    const timestamp = new Date().toLocaleString('fr-FR');

    let message = `🧪 RAPPORT HYGIÈNE - ${timestamp}\n`;
    message += `📧 À l'attention de : Yosra (Responsable Hygiène) et Moncef (Chef de Service)\n\n`;
    message += `📋 PROCÈS-VERBAUX À EXAMINER (${pvs.length})\n`;
    message += `${'='.repeat(50)}\n\n`;

    pvs.forEach((pv, index) => {
        message += `${index + 1}. PV: ${pv.numeroPv}\n`;
        message += `   📍 Dépôt: ${pv.depot}\n`;
        message += `   📅 Date: ${formatDate(pv.createdAt)}\n`;
        message += `   📦 Produits: ${pv.produits.length}\n`;
        message += `   🔍 Statut: ${pv.statut}\n`;

        // Détail des produits non conformes
        const produitsNonConformes = pv.produits.filter(p => p.statut === 'non-conforme');
        if (produitsNonConformes.length > 0) {
            message += `   ⚠️ Produits non conformes: ${produitsNonConformes.length}\n`;
            produitsNonConformes.forEach(produit => {
                message += `      - ${produit.nom} (Qté: ${produit.quantite})\n`;
            });
        }
        message += `\n`;
    });

    message += `📞 Action requise: Vérification et validation des PV\n`;
    message += `🏥 Équipe: Service Hygiène IPT\n`;
    message += `\n--- Message automatique du système de gestion ---`;

    return message;
}

// ========================================
// FONCTIONS POUR L'ÉQUIPE HYGIÈNE
// ========================================

// Fonction pour afficher la zone hygiène
function afficherZoneHygiene() {
    console.log('🧪 Affichage de la zone hygiène...');

    // Masquer toutes les autres zones
    document.querySelectorAll('.module-zone').forEach(zone => {
        zone.style.display = 'none';
    });

    // Afficher la zone hygiène
    const hygieneZone = document.getElementById('hygieneZone');
    if (hygieneZone) {
        hygieneZone.style.display = 'block';
        console.log('✅ Zone hygiène affichée');

        // Charger les données
        chargerPvHygiene();
        initialiserFiltresHygiene();
        calculerStatistiquesHygiene();
    } else {
        console.error('❌ Element hygieneZone non trouvé dans le DOM');

        // Essayer de créer la zone hygiène si elle n'existe pas
        creerZoneHygieneManuelle();
    }
}

// Fonction pour créer la zone hygiène manuellement
function creerZoneHygieneManuelle() {
    console.log('🔧 Création manuelle de la zone hygiène...');

    // Créer la zone hygiène dans la consultation PV
    const consultationView = document.getElementById('consultationPvView');
    if (consultationView) {
        consultationView.style.display = 'block';
        console.log('📋 Affichage de la vue consultation PV pour l\'équipe hygiène');

        // Modifier le titre pour l'équipe hygiène
        const titre = consultationView.querySelector('h3');
        if (titre) {
            titre.textContent = '🧪 ÉQUIPE HYGIÈNE - PV REÇUS';
            titre.style.color = '#4caf50';
        }

        // Remplacer le tableau existant par le tableau hygiène
        const tableContainer = consultationView.querySelector('.table-container');
        if (tableContainer) {
            tableContainer.innerHTML = `
                <table id="tablePvHygiene" class="hygiene-table">
                    <thead>
                        <tr>
                            <th>Sélection</th>
                            <th>N° PV</th>
                            <th>Dépôt Expéditeur</th>
                            <th>Date Réception</th>
                            <th>Nb Produits</th>
                            <th>Produits Non Conformes</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="pvHygieneTableBody">
                    </tbody>
                </table>
            `;
        }

        // Ajouter les boutons d'export hygiène
        const actionsDiv = consultationView.querySelector('.pv-admin-actions');
        if (actionsDiv) {
            // Remplacer les boutons existants par les boutons hygiène
            actionsDiv.innerHTML = `
                <button onclick="rafraichirPvHygiene()" class="btn-secondary">
                    🔄 Actualiser
                </button>
                <button onclick="exporterPvHygienePdf()" class="btn-export-pdf">
                    📄 Export PDF Détaillé
                </button>
                <button onclick="exporterPvHygieneExcel()" class="btn-export-excel">
                    📊 Export Excel Détaillé
                </button>
                <input type="text" id="searchPvHygiene" placeholder="🔍 Rechercher un PV..." onkeyup="filtrerPvHygiene()" style="margin-left: 20px;">
            `;
        }

        // Charger les PV pour l'équipe hygiène
        chargerPvHygiene();
    }
}

// Fonction pour charger les PV reçus par l'équipe hygiène
function chargerPvHygiene() {
    console.log('📋 Chargement des PV pour l\'équipe hygiène...');

    const tbody = document.getElementById('pvHygieneTableBody');
    if (!tbody) {
        console.error('❌ Element pvHygieneTableBody non trouvé');
        // Utiliser le tableau de consultation à la place
        displayAdminPVs();
        return;
    }

    tbody.innerHTML = '';
    console.log(`📊 PV totaux disponibles: ${pvs.length}`);

    // Filtrer les PV qui ont été envoyés à l'équipe hygiène
    // (En production, cela viendrait d'une table dédiée dans Supabase)
    const pvsRecus = pvs.filter(pv => {
        // Simuler les PV reçus - en réalité, il faudrait une table de notifications/envois
        return pv.statut === 'en_attente' || pv.statut === 'valide' || pv.statut === 'rejete';
    });

    console.log(`🧪 PV pour équipe hygiène: ${pvsRecus.length}`);

    if (pvsRecus.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="no-data">Aucun PV reçu pour le moment.</td></tr>';
        console.log('📭 Aucun PV à afficher pour l\'équipe hygiène');
        return;
    }

    console.log(`✅ Génération de ${pvsRecus.length} lignes dans le tableau hygiène`);

    pvsRecus.forEach(pv => {
        const row = document.createElement('tr');
        row.className = 'hygiene-pv-row';

        // Calculer les produits non conformes
        const produitsNonConformes = pv.produits.filter(p => p.statut === 'non-conforme').length;

        const statutClass = pv.statut === 'en_attente' ? 'status-pending' :
                           pv.statut === 'valide' ? 'status-valid' : 'status-expired';

        const statutText = pv.statut === 'en_attente' ? '⏳ En attente' :
                          pv.statut === 'valide' ? '✅ Validé' : '❌ Rejeté';

        row.innerHTML = `
            <td>
                <input type="checkbox" id="hygiene_pv_${pv.id}" class="hygiene-pv-checkbox" onchange="toggleHygienePvSelection('${pv.id}')">
            </td>
            <td class="pv-numero-cell">
                <strong>${pv.numeroPv}</strong>
            </td>
            <td class="pv-depot-cell">
                <span class="depot-badge">${pv.depot}</span>
            </td>
            <td class="pv-date-cell">
                ${formatDate(pv.createdAt)}
            </td>
            <td class="pv-count-cell">
                <span class="count-badge">${pv.produits.length}</span>
            </td>
            <td class="pv-nonconforme-cell">
                <span class="nonconforme-badge ${produitsNonConformes > 0 ? 'has-issues' : 'no-issues'}">
                    ${produitsNonConformes}
                </span>
            </td>
            <td class="pv-status-cell">
                <span class="status-badge ${statutClass}">${statutText}</span>
            </td>
            <td class="pv-actions-cell">
                <button onclick="afficherDetailsHygienePV('${pv.id}')" class="btn-mini btn-info" title="Analyser">
                    🔍 Analyser
                </button>
                <button onclick="validerPV('${pv.id}')" class="btn-mini btn-success" title="Valider">
                    ✅ Valider
                </button>
                <button onclick="rejeterPV('${pv.id}')" class="btn-mini btn-danger" title="Rejeter">
                    ❌ Rejeter
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Fonction pour initialiser les filtres de la zone hygiène
function initialiserFiltresHygiene() {
    const filterDepot = document.getElementById('filterDepotHygiene');
    if (!filterDepot) return;

    // Remplir le filtre des dépôts
    const depots = [...new Set(pvs.map(pv => pv.depot))];
    filterDepot.innerHTML = '<option value="">Tous les dépôts</option>';

    depots.forEach(depot => {
        const option = document.createElement('option');
        option.value = depot;
        option.textContent = depot;
        filterDepot.appendChild(option);
    });
}

// Fonction pour filtrer les PV dans la zone hygiène
function filtrerPvHygiene() {
    const searchTerm = document.getElementById('searchPvHygiene').value.toLowerCase();
    const filterStatut = document.getElementById('filterStatutHygiene').value;
    const filterDepot = document.getElementById('filterDepotHygiene').value;

    const rows = document.querySelectorAll('.hygiene-pv-row');

    rows.forEach(row => {
        const numero = row.querySelector('.pv-numero-cell').textContent.toLowerCase();
        const depot = row.querySelector('.depot-badge').textContent;
        const statutElement = row.querySelector('.status-badge');
        const statut = statutElement.className.includes('status-pending') ? 'en_attente' :
                      statutElement.className.includes('status-valid') ? 'valide' : 'rejete';

        const matchSearch = numero.includes(searchTerm) || depot.toLowerCase().includes(searchTerm);
        const matchStatut = !filterStatut || statut === filterStatut;
        const matchDepot = !filterDepot || depot === filterDepot;

        row.style.display = matchSearch && matchStatut && matchDepot ? '' : 'none';
    });
}

// Fonction pour calculer les statistiques hygiène
function calculerStatistiquesHygiene() {
    const statsContent = document.getElementById('statsContent');
    if (!statsContent) return;

    const pvsRecus = pvs.length;
    const pvsEnAttente = pvs.filter(pv => pv.statut === 'en_attente').length;
    const pvsValides = pvs.filter(pv => pv.statut === 'valide').length;
    const pvsRejetes = pvs.filter(pv => pv.statut === 'rejete').length;

    const totalProduits = pvs.reduce((total, pv) => total + pv.produits.length, 0);
    const produitsNonConformes = pvs.reduce((total, pv) =>
        total + pv.produits.filter(p => p.statut === 'non-conforme').length, 0);

    const tauxConformite = totalProduits > 0 ?
        ((totalProduits - produitsNonConformes) / totalProduits * 100).toFixed(1) : 0;

    statsContent.innerHTML = `
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">${pvsRecus}</div>
                <div class="stat-label">PV Reçus</div>
            </div>
            <div class="stat-item pending">
                <div class="stat-number">${pvsEnAttente}</div>
                <div class="stat-label">En Attente</div>
            </div>
            <div class="stat-item valid">
                <div class="stat-number">${pvsValides}</div>
                <div class="stat-label">Validés</div>
            </div>
            <div class="stat-item rejected">
                <div class="stat-number">${pvsRejetes}</div>
                <div class="stat-label">Rejetés</div>
            </div>
            <div class="stat-item conformity">
                <div class="stat-number">${tauxConformite}%</div>
                <div class="stat-label">Taux de Conformité</div>
            </div>
            <div class="stat-item issues">
                <div class="stat-number">${produitsNonConformes}</div>
                <div class="stat-label">Produits Non Conformes</div>
            </div>
        </div>
    `;
}

// Fonction pour afficher les détails d'un PV dans la zone hygiène
function afficherDetailsHygienePV(pvId) {
    const pv = pvs.find(p => p.id === pvId);
    if (!pv) {
        alert('PV non trouvé');
        return;
    }

    const detailsSection = document.getElementById('pvHygieneDetailsSection');
    const detailsContent = document.getElementById('pvHygieneDetailsContent');

    if (!detailsSection || !detailsContent) return;

    // Calculer les statistiques du PV
    const totalProduits = pv.produits.length;
    const produitsConformes = pv.produits.filter(p => p.statut === 'conforme').length;
    const produitsNonConformes = pv.produits.filter(p => p.statut === 'non-conforme').length;
    const tauxConformite = totalProduits > 0 ? (produitsConformes / totalProduits * 100).toFixed(1) : 0;

    // Construire le tableau des produits avec analyse hygiène
    const produitsHTML = pv.produits.map(produit => {
        const expirationStatus = getExpirationStatus(produit.dateExpiration);
        const risqueHygiene = determinerRisqueHygiene(produit);

        return `
            <tr class="product-analysis-row">
                <td>${produit.id || 'N/A'}</td>
                <td>
                    <div class="product-name-detail">${produit.nom}</div>
                    <small class="product-category">${produit.category || 'Non classé'}</small>
                </td>
                <td>${produit.quantite}</td>
                <td>
                    <span class="status-badge ${produit.statut === 'conforme' ? 'status-valid' : 'status-expired'}">
                        ${produit.statut === 'conforme' ? '✅ Conforme' : '❌ Non conforme'}
                    </span>
                </td>
                <td>
                    <div class="expiration-analysis">
                        <span class="expiration-icon">${expirationStatus.icon}</span>
                        <span class="expiration-date">${formatDateFr(produit.dateExpiration)}</span>
                        <small class="expiration-status">${expirationStatus.text}</small>
                    </div>
                </td>
                <td>
                    <span class="risk-badge ${risqueHygiene.class}">${risqueHygiene.text}</span>
                </td>
                <td class="hygiene-actions">
                    <button onclick="marquerProduitConforme('${pv.id}', '${produit.id}')" class="btn-micro btn-success" title="Marquer conforme">✅</button>
                    <button onclick="marquerProduitNonConforme('${pv.id}', '${produit.id}')" class="btn-micro btn-danger" title="Marquer non conforme">❌</button>
                </td>
            </tr>
        `;
    }).join('');

    detailsContent.innerHTML = `
        <div class="hygiene-analysis-header">
            <div class="pv-info-grid">
                <div class="pv-info-item">
                    <strong>N° PV:</strong> ${pv.numeroPv}
                </div>
                <div class="pv-info-item">
                    <strong>Dépôt expéditeur:</strong> ${pv.depot}
                </div>
                <div class="pv-info-item">
                    <strong>Date de réception:</strong> ${formatDate(pv.createdAt)}
                </div>
                <div class="pv-info-item">
                    <strong>Statut actuel:</strong>
                    <span class="status-badge ${pv.statut === 'en_attente' ? 'status-pending' : pv.statut === 'valide' ? 'status-valid' : 'status-expired'}">
                        ${pv.statut === 'en_attente' ? '⏳ En attente' : pv.statut === 'valide' ? '✅ Validé' : '❌ Rejeté'}
                    </span>
                </div>
            </div>

            <div class="hygiene-stats-summary">
                <div class="summary-item">
                    <div class="summary-number">${totalProduits}</div>
                    <div class="summary-label">Total Produits</div>
                </div>
                <div class="summary-item conformes">
                    <div class="summary-number">${produitsConformes}</div>
                    <div class="summary-label">Conformes</div>
                </div>
                <div class="summary-item non-conformes">
                    <div class="summary-number">${produitsNonConformes}</div>
                    <div class="summary-label">Non Conformes</div>
                </div>
                <div class="summary-item taux">
                    <div class="summary-number">${tauxConformite}%</div>
                    <div class="summary-label">Taux Conformité</div>
                </div>
            </div>
        </div>

        <div class="hygiene-products-analysis">
            <h5>🔬 Analyse Détaillée des Produits:</h5>
            <table class="hygiene-analysis-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Produit</th>
                        <th>Quantité</th>
                        <th>Statut</th>
                        <th>Expiration</th>
                        <th>Risque Hygiène</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    ${produitsHTML}
                </tbody>
            </table>
        </div>

        ${pv.notes ? `
            <div class="pv-notes">
                <h5>📝 Notes du dépôt:</h5>
                <p>${pv.notes}</p>
            </div>
        ` : ''}

        <div class="hygiene-decision-section">
            <h5>🎯 Décision Hygiène:</h5>
            <div class="decision-actions">
                <button onclick="validerPVComplet('${pv.id}')" class="btn-decision btn-validate">
                    ✅ Valider le PV
                </button>
                <button onclick="rejeterPVComplet('${pv.id}')" class="btn-decision btn-reject">
                    ❌ Rejeter le PV
                </button>
                <button onclick="demanderComplements('${pv.id}')" class="btn-decision btn-info">
                    📋 Demander Compléments
                </button>
            </div>

            <div class="decision-notes">
                <label for="decisionNotes_${pv.id}">Notes de décision (optionnel):</label>
                <textarea id="decisionNotes_${pv.id}" placeholder="Ajoutez vos observations, recommandations ou demandes de complément..."></textarea>
            </div>
        </div>

        <div class="hygiene-actions-detail">
            <button onclick="exporterAnalysePdf('${pv.id}')" class="btn-export">
                📄 Exporter Analyse PDF
            </button>
            <button onclick="masquerDetailsHygienePV()" class="btn-secondary">
                ✖️ Fermer l'Analyse
            </button>
        </div>
    `;

    detailsSection.style.display = 'block';
    detailsSection.scrollIntoView({ behavior: 'smooth' });
}

// Fonction pour déterminer le risque hygiène d'un produit
function determinerRisqueHygiene(produit) {
    const dateExpiration = produit.dateExpiration;
    if (!dateExpiration) {
        return { class: 'risk-unknown', text: '⚪ Indéterminé' };
    }

    const expirationDate = new Date(dateExpiration);
    const today = new Date();
    const daysUntilExpiration = Math.ceil((expirationDate - today) / (1000 * 60 * 60 * 24));

    if (daysUntilExpiration <= 0) {
        return { class: 'risk-critical', text: '🔴 Critique' };
    } else if (daysUntilExpiration <= 7) {
        return { class: 'risk-high', text: '🟠 Élevé' };
    } else if (daysUntilExpiration <= 30) {
        return { class: 'risk-medium', text: '🟡 Moyen' };
    } else {
        return { class: 'risk-low', text: '🟢 Faible' };
    }
}

// Fonction pour masquer les détails hygiène
function masquerDetailsHygienePV() {
    const detailsSection = document.getElementById('pvHygieneDetailsSection');
    if (detailsSection) {
        detailsSection.style.display = 'none';
    }
}

// Fonction pour gérer la sélection des PV hygiène
function toggleHygienePvSelection(pvId) {
    const checkbox = document.getElementById(`hygiene_pv_${pvId}`);
    console.log(`PV Hygiène ${pvId} ${checkbox.checked ? 'sélectionné' : 'désélectionné'}`);
}

// Fonction pour rafraîchir les PV hygiène
function rafraichirPvHygiene() {
    chargerPvHygiene();
    calculerStatistiquesHygiene();
    console.log('🔄 PV hygiène actualisés');
}

// Fonction pour valider un PV
function validerPV(pvId) {
    const pv = pvs.find(p => p.id === pvId);
    if (!pv) return;

    if (confirm(`Êtes-vous sûr de vouloir valider le PV ${pv.numeroPv} ?`)) {
        pv.statut = 'valide';
        pv.validePar = utilisateurCourant.username;
        pv.dateValidation = new Date().toISOString();

        chargerPvHygiene();
        calculerStatistiquesHygiene();

        // Envoyer notification
        envoyerMessageAutomatique(`✅ PV ${pv.numeroPv} validé par ${utilisateurCourant.username} (Équipe Hygiène)`);

        alert(`PV ${pv.numeroPv} validé avec succès.`);
    }
}

// Fonction pour rejeter un PV
function rejeterPV(pvId) {
    const pv = pvs.find(p => p.id === pvId);
    if (!pv) return;

    const raison = prompt(`Raison du rejet du PV ${pv.numeroPv}:`);
    if (raison !== null) {
        pv.statut = 'rejete';
        pv.rejetePar = utilisateurCourant.username;
        pv.dateRejet = new Date().toISOString();
        pv.raisonRejet = raison;

        chargerPvHygiene();
        calculerStatistiquesHygiene();

        // Envoyer notification
        envoyerMessageAutomatique(`❌ PV ${pv.numeroPv} rejeté par ${utilisateurCourant.username} (Équipe Hygiène). Raison: ${raison}`);

        alert(`PV ${pv.numeroPv} rejeté.`);
    }
}

// Fonction pour exporter les PV hygiène en PDF
function exporterPvHygienePdf() {
    const checkboxes = document.querySelectorAll('.hygiene-pv-checkbox:checked');
    let pvsAExporter = [];

    if (checkboxes.length === 0) {
        // Si aucun PV sélectionné, exporter tous les PV visibles
        const rows = document.querySelectorAll('.hygiene-pv-row:not([style*="display: none"])');
        rows.forEach(row => {
            const pvId = row.querySelector('.hygiene-pv-checkbox').id.replace('hygiene_pv_', '');
            const pv = pvs.find(p => p.id === pvId);
            if (pv) pvsAExporter.push(pv);
        });
    } else {
        // Exporter les PV sélectionnés
        checkboxes.forEach(checkbox => {
            const pvId = checkbox.id.replace('hygiene_pv_', '');
            const pv = pvs.find(p => p.id === pvId);
            if (pv) pvsAExporter.push(pv);
        });
    }

    if (pvsAExporter.length === 0) {
        alert('Aucun PV à exporter.');
        return;
    }

    // Créer le contenu PDF
    const timestamp = new Date().toLocaleString('fr-FR');
    const utilisateur = utilisateurCourant.username;

    let contenuPdf = `
        RAPPORT D'ANALYSE HYGIÈNE
        ========================

        Date d'export: ${timestamp}
        Analysé par: ${utilisateur} (Équipe Hygiène IPT)
        Nombre de PV: ${pvsAExporter.length}

        `;

    pvsAExporter.forEach((pv, index) => {
        const totalProduits = pv.produits.length;
        const produitsConformes = pv.produits.filter(p => p.statut === 'conforme').length;
        const produitsNonConformes = pv.produits.filter(p => p.statut === 'non-conforme').length;
        const tauxConformite = totalProduits > 0 ? (produitsConformes / totalProduits * 100).toFixed(1) : 0;

        contenuPdf += `
        ${index + 1}. PV: ${pv.numeroPv}
        --------------------------------
        Dépôt: ${pv.depot}
        Date de réception: ${formatDate(pv.createdAt)}
        Statut: ${pv.statut}

        ANALYSE:
        - Total produits: ${totalProduits}
        - Produits conformes: ${produitsConformes}
        - Produits non conformes: ${produitsNonConformes}
        - Taux de conformité: ${tauxConformite}%

        DÉTAIL DES PRODUITS:
        `;

        pv.produits.forEach((produit, index) => {
            const risque = determinerRisqueHygiene(produit);
            const nomProduit = produit.nom || produit.name || 'Produit non identifié';
            const idProduit = produit.id || `TEMP_${index + 1}`;
            const categorieProduit = produit.categorie || produit.category || 'Non classé';
            const quantiteProduit = produit.quantite || produit.quantity || 0;
            const statutProduit = produit.statut || 'Non défini';
            const dateExpiration = produit.dateExpiration || produit.expiryDate || 'Non définie';

            contenuPdf += `
        ${index + 1}. ${nomProduit}
           ├─ ID Produit: ${idProduit}
           ├─ Catégorie: ${categorieProduit}
           ├─ Quantité: ${quantiteProduit} unité(s)
           ├─ Statut de conformité: ${statutProduit === 'conforme' ? '✅ CONFORME' : '❌ NON CONFORME'}
           ├─ Date d'expiration: ${formatDateFr(dateExpiration)}
           ├─ Risque hygiène: ${risque.text}
           └─ Évaluation: ${statutProduit === 'conforme' ? 'Produit validé pour utilisation' : 'Produit à retirer ou vérifier'}
           `;
        });

        if (pv.notes) {
            contenuPdf += `
        NOTES DU DÉPÔT: ${pv.notes}`;
        }

        contenuPdf += `\n\n`;
    });

    contenuPdf += `
        RÉSUMÉ GLOBAL:
        ==============
        Total PV analysés: ${pvsAExporter.length}
        PV en attente: ${pvsAExporter.filter(pv => pv.statut === 'en_attente').length}
        PV validés: ${pvsAExporter.filter(pv => pv.statut === 'valide').length}
        PV rejetés: ${pvsAExporter.filter(pv => pv.statut === 'rejete').length}

        --- Rapport généré automatiquement par le système de gestion IPT ---
    `;

    // Créer et télécharger le fichier
    const blob = new Blob([contenuPdf], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Rapport_Hygiene_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log(`📄 Export PDF hygiène: ${pvsAExporter.length} PV(s) exporté(s)`);
    alert(`Rapport d'analyse hygiène exporté (${pvsAExporter.length} PV(s)).`);
}

// Fonction pour exporter les PV hygiène en Excel
function exporterPvHygieneExcel() {
    const checkboxes = document.querySelectorAll('.hygiene-pv-checkbox:checked');
    let pvsAExporter = [];

    if (checkboxes.length === 0) {
        // Si aucun PV sélectionné, exporter tous les PV visibles
        const rows = document.querySelectorAll('.hygiene-pv-row:not([style*="display: none"])');
        rows.forEach(row => {
            const pvId = row.querySelector('.hygiene-pv-checkbox').id.replace('hygiene_pv_', '');
            const pv = pvs.find(p => p.id === pvId);
            if (pv) pvsAExporter.push(pv);
        });
    } else {
        // Exporter les PV sélectionnés
        checkboxes.forEach(checkbox => {
            const pvId = checkbox.id.replace('hygiene_pv_', '');
            const pv = pvs.find(p => p.id === pvId);
            if (pv) pvsAExporter.push(pv);
        });
    }

    if (pvsAExporter.length === 0) {
        alert('Aucun PV à exporter.');
        return;
    }

    // Créer le contenu CSV (compatible Excel) avec en-têtes détaillés
    let csvContent = 'N° PV,Dépôt Expéditeur,Date Réception,Statut PV,Total Produits,Produits Conformes,Produits Non Conformes,Taux Conformité (%),ID Produit,Nom Produit Complet,Catégorie Produit,Quantité,Unité,Statut Conformité,Date Expiration,Jours Avant Expiration,Risque Hygiène,Évaluation Hygiène,Notes PV,Analysé Par\n';

    pvsAExporter.forEach(pv => {
        const totalProduits = pv.produits.length;
        const produitsConformes = pv.produits.filter(p => p.statut === 'conforme').length;
        const produitsNonConformes = pv.produits.filter(p => p.statut === 'non-conforme').length;
        const tauxConformite = totalProduits > 0 ? (produitsConformes / totalProduits * 100).toFixed(1) : 0;

        if (pv.produits.length === 0) {
            // PV sans produits
            csvContent += `"${pv.numeroPv}","${pv.depot}","${formatDate(pv.createdAt)}","${pv.statut}","${totalProduits}","${produitsConformes}","${produitsNonConformes}","${tauxConformite}","","Aucun produit","","","","","","","","Aucune évaluation","${pv.notes || ''}","${utilisateurCourant.username}"\n`;
        } else {
            // PV avec produits - détails complets
            pv.produits.forEach((produit, index) => {
                const risque = determinerRisqueHygiene(produit);
                const nomProduit = produit.nom || produit.name || 'Produit non identifié';
                const idProduit = produit.id || `TEMP_${index + 1}`;
                const categorieProduit = produit.categorie || produit.category || 'Non classé';
                const quantiteProduit = produit.quantite || produit.quantity || 0;
                const statutProduit = produit.statut || 'Non défini';
                const dateExpiration = produit.dateExpiration || produit.expiryDate || '';

                // Calculer les jours avant expiration
                let joursAvantExpiration = 'N/A';
                if (dateExpiration) {
                    const expirationDate = new Date(dateExpiration);
                    const today = new Date();
                    const daysUntilExpiration = Math.ceil((expirationDate - today) / (1000 * 60 * 60 * 24));
                    joursAvantExpiration = daysUntilExpiration <= 0 ? `Expiré depuis ${Math.abs(daysUntilExpiration)} jour(s)` : `${daysUntilExpiration} jour(s)`;
                }

                // Évaluation hygiène détaillée
                const evaluationHygiene = statutProduit === 'conforme' ?
                    'Produit validé - Utilisation autorisée' :
                    'Produit non conforme - Retrait recommandé';

                const pvInfo = index === 0 ?
                    `"${pv.numeroPv}","${pv.depot}","${formatDate(pv.createdAt)}","${pv.statut}","${totalProduits}","${produitsConformes}","${produitsNonConformes}","${tauxConformite}"` :
                    '"","","","","","","",""';

                csvContent += `${pvInfo},"${idProduit}","${nomProduit}","${categorieProduit}","${quantiteProduit}","unité(s)","${statutProduit === 'conforme' ? 'CONFORME' : 'NON CONFORME'}","${formatDateFr(dateExpiration)}","${joursAvantExpiration}","${risque.text}","${evaluationHygiene}","${index === 0 ? (pv.notes || '') : ''}","${utilisateurCourant.username}"\n`;
            });
        }
    });

    // Créer et télécharger le fichier
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `Analyse_Hygiene_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log(`📊 Export Excel hygiène: ${pvsAExporter.length} PV(s) exporté(s)`);
    alert(`Données d'analyse hygiène exportées en Excel (${pvsAExporter.length} PV(s)).`);
}

// ========================================
// FONCTIONS DE DÉBOGAGE ET TEST
// ========================================

// Fonction pour tester l'affichage des tableaux
function testerAffichageTableaux() {
    console.log('🧪 Test de l\'affichage des tableaux...');

    // Vérifier les éléments HTML essentiels
    const elements = [
        'gestionPvZone',
        'creationPvView',
        'consultationPvView',
        'produitsPvTableBody',
        'pvConsultesTableBody',
        'tablePvConsultes',
        'hygieneZone',
        'pvHygieneTableBody'
    ];

    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ ${id}: trouvé`);
        } else {
            console.error(`❌ ${id}: NON TROUVÉ`);
        }
    });

    // Vérifier les données
    console.log(`📊 Données disponibles:`);
    console.log(`   - Produits: ${products ? products.length : 'NON DÉFINI'}`);
    console.log(`   - PV: ${pvs ? pvs.length : 'NON DÉFINI'}`);
    console.log(`   - Utilisateur: ${utilisateurCourant ? utilisateurCourant.username : 'NON DÉFINI'}`);
    console.log(`   - Rôle: ${utilisateurCourant ? utilisateurCourant.role : 'NON DÉFINI'}`);

    // Vérifier les fonctions de rôle
    console.log(`🔐 Vérification des rôles:`);
    console.log(`   - isDepot(): ${typeof isDepot === 'function' ? isDepot() : 'FONCTION NON DÉFINIE'}`);
    console.log(`   - isHygiene(): ${typeof isHygiene === 'function' ? isHygiene() : 'FONCTION NON DÉFINIE'}`);
    console.log(`   - isAdmin(): ${typeof isAdmin === 'function' ? isAdmin() : 'FONCTION NON DÉFINIE'}`);
    console.log(`   - canManagePV(): ${typeof canManagePV === 'function' ? canManagePV() : 'FONCTION NON DÉFINIE'}`);
}

// Fonction pour forcer l'affichage des tableaux
function forcerAffichageTableaux() {
    console.log('🔧 Forçage de l\'affichage des tableaux...');

    if (isDepot()) {
        console.log('🏪 Mode dépôt détecté');
        const creationView = document.getElementById('creationPvView');
        if (creationView) {
            creationView.style.display = 'block';
            loadProductsForDepot();
        }
    } else if (isHygiene()) {
        console.log('🧪 Mode hygiène détecté');
        afficherZoneHygiene();
    } else {
        console.log('👨‍💼 Mode admin/manager détecté');
        const consultationView = document.getElementById('consultationPvView');
        if (consultationView) {
            consultationView.style.display = 'block';
            displayAdminPVs();
        }
    }
}

// Ajouter les fonctions de test au window pour les rendre accessibles depuis la console
if (typeof window !== 'undefined') {
    window.testerAffichageTableaux = testerAffichageTableaux;
    window.forcerAffichageTableaux = forcerAffichageTableaux;
    window.afficherZoneHygiene = afficherZoneHygiene;
    window.displayAdminPVs = displayAdminPVs;
    window.loadProductsForDepot = loadProductsForDepot;
}
