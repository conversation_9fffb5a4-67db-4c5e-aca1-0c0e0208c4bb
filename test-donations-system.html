<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Système de Gestion des Dons - IPT</title>
    
    <!-- CSS existant -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration et modules existants -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    <script src="assets/js/supabase-database.js"></script>
    
    <style>
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .test-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .test-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .test-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .sql-code {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f3f4f6;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #10b981;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>🧪 Test Système de Gestion des Dons</h1>
                <div class="header-actions">
                    <button id="btnRunAllTests" class="test-button">▶️ Lancer tous les tests</button>
                    <button id="btnClearResults" class="test-button">🗑️ Effacer résultats</button>
                </div>
            </div>
        </header>

        <div class="test-container">
            <!-- Progression globale -->
            <div class="test-section">
                <h2>📊 Progression des Tests</h2>
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                </div>
                <div id="progressText">0/0 tests complétés</div>
            </div>

            <!-- Test 1: Connexion Supabase -->
            <div class="test-section">
                <h3>🔌 Test 1: Connexion Supabase</h3>
                <p>Vérification de la connexion à la base de données Supabase</p>
                <button onclick="testSupabaseConnection()" class="test-button">Tester</button>
                <div id="test1Results"></div>
            </div>

            <!-- Test 2: Vérification des tables -->
            <div class="test-section">
                <h3>📋 Test 2: Vérification des Tables</h3>
                <p>Vérification de l'existence des tables de gestion des dons</p>
                <button onclick="testTablesExistence()" class="test-button">Tester</button>
                <div id="test2Results"></div>
            </div>

            <!-- Test 3: Test des fonctions SQL -->
            <div class="test-section">
                <h3>⚙️ Test 3: Fonctions SQL</h3>
                <p>Test des fonctions de génération de numéros et de workflow</p>
                <button onclick="testSQLFunctions()" class="test-button">Tester</button>
                <div id="test3Results"></div>
            </div>

            <!-- Test 4: Test des vues -->
            <div class="test-section">
                <h3>👁️ Test 4: Vues de Données</h3>
                <p>Vérification des vues donations_complete et donations_stats</p>
                <button onclick="testViews()" class="test-button">Tester</button>
                <div id="test4Results"></div>
            </div>

            <!-- Test 5: Test CRUD de base -->
            <div class="test-section">
                <h3>💾 Test 5: Opérations CRUD</h3>
                <p>Test de création, lecture, mise à jour et suppression d'un don</p>
                <button onclick="testCRUDOperations()" class="test-button">Tester</button>
                <div id="test5Results"></div>
            </div>

            <!-- Test 6: Test du workflow -->
            <div class="test-section">
                <h3>🔄 Test 6: Workflow des Dons</h3>
                <p>Test du workflow complet d'un don (création → validation → approbation)</p>
                <button onclick="testWorkflow()" class="test-button">Tester</button>
                <div id="test6Results"></div>
            </div>

            <!-- Test 7: Test des politiques RLS -->
            <div class="test-section">
                <h3>🔒 Test 7: Politiques de Sécurité (RLS)</h3>
                <p>Vérification des politiques Row Level Security</p>
                <button onclick="testRLSPolicies()" class="test-button">Tester</button>
                <div id="test7Results"></div>
            </div>

            <!-- Résultats globaux -->
            <div class="test-section">
                <h3>📈 Résultats Globaux</h3>
                <div id="globalResults">
                    <p>Aucun test exécuté pour le moment.</p>
                </div>
            </div>

            <!-- Instructions d'installation -->
            <div class="test-section">
                <h3>📝 Instructions d'Installation</h3>
                <p>Pour installer le système de gestion des dons, exécutez les scripts SQL suivants dans l'ordre :</p>
                <ol>
                    <li><strong>donations-complete-setup.sql</strong> - Création des tables et structures de base</li>
                    <li><strong>donations-functions.sql</strong> - Fonctions de workflow</li>
                    <li><strong>donations-functions-part2.sql</strong> - Fonctions complémentaires</li>
                    <li><strong>donations-rls-policies.sql</strong> - Politiques de sécurité</li>
                    <li><strong>donations-test-data.sql</strong> - Données de test (optionnel)</li>
                </ol>
                
                <div class="sql-code" id="installationSQL">
-- Ordre d'exécution des scripts SQL dans Supabase:

-- 1. Tables et structures de base
-- Copier et exécuter le contenu de donations-complete-setup.sql

-- 2. Fonctions de workflow
-- Copier et exécuter le contenu de donations-functions.sql

-- 3. Fonctions complémentaires  
-- Copier et exécuter le contenu de donations-functions-part2.sql

-- 4. Politiques de sécurité
-- Copier et exécuter le contenu de donations-rls-policies.sql

-- 5. Données de test (optionnel)
-- Copier et exécuter le contenu de donations-test-data.sql

-- Vérification de l'installation
SELECT 'Installation terminée' as status;
                </div>
            </div>
        </div>
    </div>

    <script>
        let supabaseClient;
        let testResults = {
            total: 7,
            completed: 0,
            passed: 0,
            failed: 0
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🧪 Initialisation des tests...');
            
            try {
                // Récupérer le client Supabase
                supabaseClient = window.supabaseClient || window.supabase;
                
                if (!supabaseClient) {
                    showTestResult('test1Results', 'Client Supabase non disponible', 'error');
                    return;
                }

                console.log('✅ Tests prêts à être exécutés');
                
            } catch (error) {
                console.error('❌ Erreur initialisation tests:', error);
            }
        });

        // Fonction utilitaire pour afficher les résultats
        function showTestResult(containerId, message, type = 'info', details = null) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            
            let content = message;
            if (details) {
                content += `<br><small>${details}</small>`;
            }
            
            resultDiv.innerHTML = content;
            container.appendChild(resultDiv);
            
            // Scroll vers le résultat
            resultDiv.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }

        // Mettre à jour la progression
        function updateProgress() {
            const percentage = (testResults.completed / testResults.total) * 100;
            document.getElementById('progressFill').style.width = `${percentage}%`;
            document.getElementById('progressText').textContent = 
                `${testResults.completed}/${testResults.total} tests complétés (${testResults.passed} réussis, ${testResults.failed} échoués)`;
            
            // Mettre à jour les résultats globaux
            const globalContainer = document.getElementById('globalResults');
            globalContainer.innerHTML = `
                <div class="test-result test-info">
                    <strong>Progression:</strong> ${testResults.completed}/${testResults.total} tests<br>
                    <strong>Réussis:</strong> ${testResults.passed}<br>
                    <strong>Échoués:</strong> ${testResults.failed}<br>
                    <strong>Taux de réussite:</strong> ${testResults.completed > 0 ? Math.round((testResults.passed / testResults.completed) * 100) : 0}%
                </div>
            `;
        }

        // Test 1: Connexion Supabase
        async function testSupabaseConnection() {
            const containerId = 'test1Results';
            document.getElementById(containerId).innerHTML = '';
            
            try {
                showTestResult(containerId, '🔄 Test de connexion en cours...', 'info');
                
                if (!supabaseClient) {
                    throw new Error('Client Supabase non initialisé');
                }

                // Test simple de connexion
                const { data, error } = await supabaseClient
                    .from('gestion_users')
                    .select('count')
                    .limit(1);

                if (error && !error.message.includes('relation') && !error.message.includes('permission')) {
                    throw error;
                }

                showTestResult(containerId, '✅ Connexion Supabase réussie', 'success');
                testResults.passed++;
                
            } catch (error) {
                showTestResult(containerId, '❌ Erreur de connexion Supabase', 'error', error.message);
                testResults.failed++;
            }
            
            testResults.completed++;
            updateProgress();
        }

        // Lancer tous les tests
        async function runAllTests() {
            // Réinitialiser les résultats
            testResults = { total: 7, completed: 0, passed: 0, failed: 0 };
            
            // Effacer les résultats précédents
            for (let i = 1; i <= 7; i++) {
                document.getElementById(`test${i}Results`).innerHTML = '';
            }
            
            // Exécuter les tests en séquence
            await testSupabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testTablesExistence();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testSQLFunctions();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testViews();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCRUDOperations();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWorkflow();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRLSPolicies();
        }

        // Event listeners
        document.getElementById('btnRunAllTests')?.addEventListener('click', runAllTests);
        document.getElementById('btnClearResults')?.addEventListener('click', () => {
            for (let i = 1; i <= 7; i++) {
                document.getElementById(`test${i}Results`).innerHTML = '';
            }
            testResults = { total: 7, completed: 0, passed: 0, failed: 0 };
            updateProgress();
        });
    </script>
    
    <!-- Script des tests individuels -->
    <script src="assets/js/donations-tests.js"></script>
</body>
</html>
