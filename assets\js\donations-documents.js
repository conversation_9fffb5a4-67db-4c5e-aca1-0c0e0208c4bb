/**
 * Module de gestion des documents et signatures numériques
 * Système complet pour les dons IPT
 */

class DonationsDocuments {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.donationId = null;
        this.donation = null;
        this.documents = [];
        this.signatureCanvas = null;
        this.signatureContext = null;
        this.isDrawing = false;
        this.documentTypes = [
            {
                id: 'lettre_don',
                name: 'Lettre de Don',
                icon: '📄',
                description: 'Lettre officielle du donateur confirmant le don',
                required: true
            },
            {
                id: 'facture',
                name: 'Facture',
                icon: '🧾',
                description: 'Facture ou devis des articles/équipements',
                required: false
            },
            {
                id: 'bon_livraison',
                name: '<PERSON> Liv<PERSON>',
                icon: '📦',
                description: 'Bon de livraison signé par le transporteur',
                required: true
            },
            {
                id: 'bon_prelevement',
                name: 'Bon de Prélèvement',
                icon: '📋',
                description: 'Bon de prélèvement pour les équipements',
                required: false
            }
        ];
    }

    async initialize(donationId) {
        console.log('📄 Initialisation du gestionnaire de documents...');
        
        try {
            this.donationId = donationId;

            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            if (!this.currentUser) {
                throw new Error('Utilisateur non authentifié');
            }

            // Charger les informations du don
            await this.loadDonationInfo();

            // Charger les documents existants
            await this.loadDocuments();

            // Initialiser l'interface
            this.initializeInterface();

            // Configurer la signature numérique
            this.initializeSignature();

            console.log('✅ Gestionnaire de documents initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation documents:', error);
            throw error;
        }
    }

    async loadDonationInfo() {
        try {
            const { data: donation, error } = await this.supabase
                .from('donations_ipt_complete')
                .select('*')
                .eq('id', this.donationId)
                .single();

            if (error) throw error;

            this.donation = donation;
            this.updateDonationDisplay();

        } catch (error) {
            console.error('Erreur chargement info don:', error);
            throw new Error('Don non trouvé');
        }
    }

    updateDonationDisplay() {
        document.getElementById('donationNumber').textContent = this.donation.numero_don;
        document.getElementById('donationType').textContent = this.donation.type_don;
        document.getElementById('donationRequester').textContent = 
            `${this.donation.demandeur_nom} ${this.donation.demandeur_prenom}`;
        document.getElementById('donationStatus').textContent = this.formatStatus(this.donation.statut);
    }

    formatStatus(status) {
        const statusMap = {
            'brouillon': '📝 Brouillon',
            'soumis': '📤 Soumis',
            'valide_appro': '✅ Validé Appro',
            'avis_technique': '🔧 Avis Technique',
            'valide_dg': '✅ Validé DG',
            'valide_sd_achats': '✅ Validé Achats',
            'soumis_ms': '🏛️ Soumis MS',
            'approuve_ms': '✅ Approuvé MS',
            'receptionne': '📦 Réceptionné',
            'inventorie': '📋 Inventorié',
            'affecte': '🎯 Affecté',
            'archive': '📁 Archivé'
        };
        return statusMap[status] || status;
    }

    async loadDocuments() {
        try {
            const { data: documents, error } = await this.supabase
                .from('gestion_donation_documents')
                .select('*')
                .eq('donation_id', this.donationId)
                .order('uploaded_at', { ascending: false });

            if (error) throw error;

            this.documents = documents || [];
            this.updateDocumentsDisplay();
            this.updateProgress();

        } catch (error) {
            console.error('Erreur chargement documents:', error);
            this.documents = [];
            this.updateDocumentsDisplay();
        }
    }

    initializeInterface() {
        // Générer les cartes de types de documents
        this.renderDocumentTypes();

        // Afficher les documents uploadés
        this.updateDocumentsDisplay();

        // Générer les étapes de validation
        this.renderValidationSteps();

        // Afficher la section signature si nécessaire
        this.updateSignatureSection();
    }

    renderDocumentTypes() {
        const container = document.getElementById('documentTypes');
        
        container.innerHTML = this.documentTypes.map(type => {
            const uploadedDoc = this.documents.find(doc => doc.type_document === type.id);
            const isUploaded = !!uploadedDoc;
            const cardClass = type.required ? (isUploaded ? 'uploaded' : 'required') : (isUploaded ? 'uploaded' : '');
            const statusClass = type.required ? (isUploaded ? 'status-uploaded' : 'status-required') : 'status-optional';
            const statusText = type.required ? (isUploaded ? 'Uploadé' : 'Requis') : 'Optionnel';

            return `
                <div class="document-type-card ${cardClass}">
                    <div class="document-type-header">
                        <span class="document-type-icon">${type.icon}</span>
                        <span class="document-type-title">${type.name}</span>
                        <span class="document-type-status ${statusClass}">${statusText}</span>
                    </div>
                    <div class="document-type-description">${type.description}</div>
                    
                    ${isUploaded ? this.renderUploadedDocument(uploadedDoc) : this.renderUploadArea(type.id)}
                </div>
            `;
        }).join('');
    }

    renderUploadArea(typeId) {
        return `
            <div class="upload-area" onclick="triggerFileUpload('${typeId}')" 
                 ondrop="handleFileDrop(event, '${typeId}')" 
                 ondragover="handleDragOver(event)"
                 ondragleave="handleDragLeave(event)">
                <div class="upload-icon">📁</div>
                <div class="upload-text">Cliquer pour sélectionner</div>
                <div class="upload-hint">ou glisser-déposer le fichier</div>
                <input type="file" id="fileInput_${typeId}" style="display: none;" 
                       accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                       onchange="handleFileSelect(event, '${typeId}')">
            </div>
        `;
    }

    renderUploadedDocument(document) {
        return `
            <div class="document-list">
                <div class="document-item">
                    <div class="document-info">
                        <div class="document-icon">${this.getFileIcon(document.type_mime)}</div>
                        <div class="document-details">
                            <div class="document-name">${document.nom_original}</div>
                            <div class="document-meta">
                                ${this.formatFileSize(document.taille_fichier)} • 
                                ${this.formatDate(document.uploaded_at)}
                            </div>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button class="btn btn-primary" onclick="openDocumentModal('${document.id}')">
                            👁️ Voir
                        </button>
                        <button class="btn btn-success" onclick="downloadDocument('${document.id}')">
                            📥 Télécharger
                        </button>
                        ${this.canDeleteDocument(document) ? `
                            <button class="btn btn-danger" onclick="deleteDocument('${document.id}')">
                                🗑️ Supprimer
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    canDeleteDocument(document) {
        // Seul l'uploader ou un admin peut supprimer
        return document.uploaded_by === this.currentUser.email || 
               this.currentUser.user_metadata?.role_ipt === 'admin_systeme';
    }

    getFileIcon(mimeType) {
        if (mimeType.includes('pdf')) return '📄';
        if (mimeType.includes('image')) return '🖼️';
        if (mimeType.includes('word')) return '📝';
        return '📁';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    updateProgress() {
        const requiredTypes = this.documentTypes.filter(type => type.required);
        const uploadedRequired = requiredTypes.filter(type => 
            this.documents.some(doc => doc.type_document === type.id)
        );
        
        const progress = (uploadedRequired.length / requiredTypes.length) * 100;
        
        document.getElementById('progressFill').style.width = `${progress}%`;
        document.getElementById('progressText').textContent = 
            `${uploadedRequired.length} document(s) sur ${requiredTypes.length} requis`;
    }

    updateDocumentsDisplay() {
        const container = document.getElementById('uploadedDocuments');
        
        if (this.documents.length === 0) {
            container.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #718096;">
                    <div style="font-size: 48px; margin-bottom: 15px;">📄</div>
                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">Aucun document uploadé</div>
                    <div>Utilisez les sections ci-dessus pour uploader vos documents</div>
                </div>
            `;
            return;
        }

        container.innerHTML = this.documents.map(doc => `
            <div class="document-item">
                <div class="document-info">
                    <div class="document-icon">${this.getFileIcon(doc.type_mime)}</div>
                    <div class="document-details">
                        <div class="document-name">${doc.nom_original}</div>
                        <div class="document-meta">
                            ${this.getDocumentTypeName(doc.type_document)} • 
                            ${this.formatFileSize(doc.taille_fichier)} • 
                            ${this.formatDate(doc.uploaded_at)}
                        </div>
                    </div>
                </div>
                <div class="document-actions">
                    <button class="btn btn-primary" onclick="openDocumentModal('${doc.id}')">
                        👁️ Voir
                    </button>
                    <button class="btn btn-success" onclick="downloadDocument('${doc.id}')">
                        📥 Télécharger
                    </button>
                    ${this.canDeleteDocument(doc) ? `
                        <button class="btn btn-danger" onclick="deleteDocument('${doc.id}')">
                            🗑️ Supprimer
                        </button>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    getDocumentTypeName(typeId) {
        const type = this.documentTypes.find(t => t.id === typeId);
        return type ? type.name : typeId;
    }

    renderValidationSteps() {
        const container = document.getElementById('validationSteps');
        const currentStep = this.donation.etape_actuelle;
        const totalSteps = this.donation.type_don === 'equipement' ? 10 : 9;
        
        let stepsHtml = '';
        for (let i = 1; i <= totalSteps; i++) {
            let stepClass = 'validation-step';
            if (i < currentStep) stepClass += ' completed';
            else if (i === currentStep) stepClass += ' current';
            
            stepsHtml += `
                <div class="${stepClass}">
                    <div class="validation-circle">${i}</div>
                    <div class="validation-label">${this.getStepLabel(i, this.donation.type_don)}</div>
                </div>
            `;
        }
        
        container.innerHTML = stepsHtml;
    }

    getStepLabel(step, type) {
        const labels = {
            1: 'Appro',
            2: type === 'equipement' ? 'Tech' : 'DG',
            3: type === 'equipement' ? 'DG' : 'Achats',
            4: type === 'equipement' ? 'Achats' : 'MS',
            5: type === 'equipement' ? 'MS' : 'Décision',
            6: type === 'equipement' ? 'Décision' : 'Réception',
            7: type === 'equipement' ? 'Réception' : 'Inventaire',
            8: type === 'equipement' ? 'Inventaire' : 'Affectation',
            9: type === 'equipement' ? 'Affectation' : 'Archive',
            10: 'Archive'
        };
        return labels[step] || step.toString();
    }

    updateSignatureSection() {
        const section = document.getElementById('signatureSection');
        const userRole = this.currentUser.user_metadata?.role_ipt;
        
        // Afficher la section signature pour les réceptionnistes
        if (userRole === 'receptionniste' && this.donation.statut === 'approuve_ms') {
            section.style.display = 'block';
        } else {
            section.style.display = 'none';
        }
    }

    initializeSignature() {
        this.signatureCanvas = document.getElementById('signatureCanvas');
        if (!this.signatureCanvas) return;

        this.signatureContext = this.signatureCanvas.getContext('2d');
        
        // Configuration du canvas
        this.signatureContext.strokeStyle = '#2d3748';
        this.signatureContext.lineWidth = 2;
        this.signatureContext.lineCap = 'round';
        this.signatureContext.lineJoin = 'round';

        // Événements souris
        this.signatureCanvas.addEventListener('mousedown', (e) => this.startDrawing(e));
        this.signatureCanvas.addEventListener('mousemove', (e) => this.draw(e));
        this.signatureCanvas.addEventListener('mouseup', () => this.stopDrawing());
        this.signatureCanvas.addEventListener('mouseout', () => this.stopDrawing());

        // Événements tactiles
        this.signatureCanvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            this.startDrawing(e.touches[0]);
        });
        this.signatureCanvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            this.draw(e.touches[0]);
        });
        this.signatureCanvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.stopDrawing();
        });
    }

    startDrawing(e) {
        this.isDrawing = true;
        const rect = this.signatureCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.signatureContext.beginPath();
        this.signatureContext.moveTo(x, y);
    }

    draw(e) {
        if (!this.isDrawing) return;
        
        const rect = this.signatureCanvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        this.signatureContext.lineTo(x, y);
        this.signatureContext.stroke();
    }

    stopDrawing() {
        this.isDrawing = false;
    }

    clearSignature() {
        this.signatureContext.clearRect(0, 0, this.signatureCanvas.width, this.signatureCanvas.height);
    }

    async saveSignature() {
        try {
            // Vérifier qu'il y a une signature
            const imageData = this.signatureContext.getImageData(0, 0, this.signatureCanvas.width, this.signatureCanvas.height);
            const hasSignature = imageData.data.some(channel => channel !== 0);
            
            if (!hasSignature) {
                this.showAlert('Veuillez signer avant de sauvegarder', 'warning');
                return;
            }

            // Convertir en blob
            const canvas = this.signatureCanvas;
            const blob = await new Promise(resolve => canvas.toBlob(resolve, 'image/png'));
            
            // Upload vers Supabase Storage
            const fileName = `${this.donationId}/signature_${Date.now()}.png`;
            const { data: uploadData, error: uploadError } = await this.supabase.storage
                .from('donation-documents')
                .upload(fileName, blob);

            if (uploadError) throw uploadError;

            // Enregistrer les métadonnées
            const { error: docError } = await this.supabase
                .from('gestion_donation_documents')
                .insert([{
                    donation_id: this.donationId,
                    type_document: 'bon_livraison',
                    nom_fichier: fileName,
                    nom_original: 'Signature_BL.png',
                    taille_fichier: blob.size,
                    type_mime: 'image/png',
                    url_stockage: uploadData.path,
                    signe_numeriquement: true,
                    signature_par: this.currentUser.email,
                    signature_date: new Date().toISOString(),
                    uploaded_by: this.currentUser.email
                }]);

            if (docError) throw docError;

            this.showAlert('Signature sauvegardée avec succès', 'success');
            
            // Recharger les documents
            await this.loadDocuments();
            this.renderDocumentTypes();

        } catch (error) {
            console.error('Erreur sauvegarde signature:', error);
            this.showAlert('Erreur lors de la sauvegarde de la signature', 'error');
        }
    }

    async uploadFile(file, typeId) {
        try {
            // Validation du fichier
            if (!this.validateFile(file)) return;

            this.showAlert('Upload en cours...', 'info');

            // Upload vers Supabase Storage
            const fileName = `${this.donationId}/${typeId}_${Date.now()}_${file.name}`;
            const { data: uploadData, error: uploadError } = await this.supabase.storage
                .from('donation-documents')
                .upload(fileName, file);

            if (uploadError) throw uploadError;

            // Enregistrer les métadonnées
            const { error: docError } = await this.supabase
                .from('gestion_donation_documents')
                .insert([{
                    donation_id: this.donationId,
                    type_document: typeId,
                    nom_fichier: fileName,
                    nom_original: file.name,
                    taille_fichier: file.size,
                    type_mime: file.type,
                    url_stockage: uploadData.path,
                    uploaded_by: this.currentUser.email
                }]);

            if (docError) throw docError;

            this.showAlert('Document uploadé avec succès', 'success');
            
            // Recharger les documents
            await this.loadDocuments();
            this.renderDocumentTypes();

        } catch (error) {
            console.error('Erreur upload:', error);
            this.showAlert('Erreur lors de l\'upload: ' + error.message, 'error');
        }
    }

    validateFile(file) {
        // Vérifier la taille (max 10MB)
        const maxSize = 10 * 1024 * 1024;
        if (file.size > maxSize) {
            this.showAlert('Le fichier est trop volumineux (max 10MB)', 'error');
            return false;
        }

        // Vérifier le type
        const allowedTypes = [
            'application/pdf',
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        ];

        if (!allowedTypes.includes(file.type)) {
            this.showAlert('Type de fichier non autorisé', 'error');
            return false;
        }

        return true;
    }

    async openDocumentModal(documentId) {
        try {
            const document = this.documents.find(doc => doc.id === documentId);
            if (!document) {
                this.showAlert('Document non trouvé', 'error');
                return;
            }

            // Obtenir l'URL signée pour l'affichage
            const { data: urlData, error } = await this.supabase.storage
                .from('donation-documents')
                .createSignedUrl(document.url_stockage, 3600); // 1 heure

            if (error) throw error;

            // Mettre à jour le modal
            document.getElementById('modalTitle').textContent = document.nom_original;
            
            const viewer = document.getElementById('documentViewer');
            if (document.type_mime.includes('pdf')) {
                viewer.innerHTML = `<iframe src="${urlData.signedUrl}" frameborder="0"></iframe>`;
            } else if (document.type_mime.includes('image')) {
                viewer.innerHTML = `<img src="${urlData.signedUrl}" alt="${document.nom_original}">`;
            } else {
                viewer.innerHTML = `
                    <div style="text-align: center; padding: 40px;">
                        <div style="font-size: 48px; margin-bottom: 15px;">${this.getFileIcon(document.type_mime)}</div>
                        <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">${document.nom_original}</div>
                        <div style="color: #718096;">Aperçu non disponible pour ce type de fichier</div>
                        <button class="btn btn-primary" onclick="downloadDocument('${documentId}')" style="margin-top: 15px;">
                            📥 Télécharger
                        </button>
                    </div>
                `;
            }

            // Afficher le modal
            document.getElementById('documentModal').style.display = 'block';

        } catch (error) {
            console.error('Erreur ouverture document:', error);
            this.showAlert('Erreur lors de l\'ouverture du document', 'error');
        }
    }

    async downloadDocument(documentId) {
        try {
            const document = this.documents.find(doc => doc.id === documentId);
            if (!document) {
                this.showAlert('Document non trouvé', 'error');
                return;
            }

            // Télécharger le fichier
            const { data, error } = await this.supabase.storage
                .from('donation-documents')
                .download(document.url_stockage);

            if (error) throw error;

            // Créer un lien de téléchargement
            const url = URL.createObjectURL(data);
            const a = document.createElement('a');
            a.href = url;
            a.download = document.nom_original;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

        } catch (error) {
            console.error('Erreur téléchargement:', error);
            this.showAlert('Erreur lors du téléchargement', 'error');
        }
    }

    async deleteDocument(documentId) {
        try {
            const document = this.documents.find(doc => doc.id === documentId);
            if (!document) {
                this.showAlert('Document non trouvé', 'error');
                return;
            }

            // Supprimer de la base de données
            const { error: dbError } = await this.supabase
                .from('gestion_donation_documents')
                .delete()
                .eq('id', documentId);

            if (dbError) throw dbError;

            // Supprimer du stockage
            const { error: storageError } = await this.supabase.storage
                .from('donation-documents')
                .remove([document.url_stockage]);

            if (storageError) {
                console.warn('Erreur suppression stockage:', storageError);
            }

            this.showAlert('Document supprimé avec succès', 'success');
            
            // Recharger les documents
            await this.loadDocuments();
            this.renderDocumentTypes();

        } catch (error) {
            console.error('Erreur suppression:', error);
            this.showAlert('Erreur lors de la suppression', 'error');
        }
    }

    showAlert(message, type = 'info') {
        if (typeof showAlert === 'function') {
            showAlert(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Instance globale
window.donationsDocuments = new DonationsDocuments();
window.DonationsDocuments = DonationsDocuments;

// Fonctions globales pour l'interface
window.triggerFileUpload = function(typeId) {
    document.getElementById(`fileInput_${typeId}`).click();
};

window.handleFileSelect = function(event, typeId) {
    const file = event.target.files[0];
    if (file && window.donationsDocuments) {
        donationsDocuments.uploadFile(file, typeId);
    }
};

window.handleFileDrop = function(event, typeId) {
    event.preventDefault();
    event.target.classList.remove('dragover');
    
    const file = event.dataTransfer.files[0];
    if (file && window.donationsDocuments) {
        donationsDocuments.uploadFile(file, typeId);
    }
};

window.handleDragOver = function(event) {
    event.preventDefault();
    event.target.classList.add('dragover');
};

window.handleDragLeave = function(event) {
    event.target.classList.remove('dragover');
};
