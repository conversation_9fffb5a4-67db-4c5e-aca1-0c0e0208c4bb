<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Commandes - IPT</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8fafc;
            color: #1f2937;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-info {
            background: #06b6d4;
            color: white;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
        }

        /* Onglets de vue */
        .view-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: #f8fafc;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            background: white;
            border-bottom-color: #059669;
            color: #059669;
        }

        /* Filtres */
        .filters-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .filters-header h3 {
            margin: 0;
            color: #374151;
        }

        .filters-content {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .filter-group input,
        .filter-group select {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .filters-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        /* Table */
        .table-view {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-container {
            width: 100%;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .table-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .table-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .page-size-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .commands-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .commands-table th {
            background: #f8fafc;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            white-space: nowrap;
            position: relative;
        }

        .commands-table th.sortable {
            cursor: pointer;
            user-select: none;
        }

        .commands-table th.sortable:hover {
            background: #f1f5f9;
        }

        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sort-indicator.asc::after {
            content: '↑';
            opacity: 1;
        }

        .sort-indicator.desc::after {
            content: '↓';
            opacity: 1;
        }

        .commands-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }

        .commands-table tbody tr:hover {
            background: #f8fafc;
        }

        /* Badges */
        .table-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-fournitures {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-equipements {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .badge-services {
            background: #fef3c7;
            color: #d97706;
        }

        .badge-maintenance {
            background: #fee2e2;
            color: #dc2626;
        }

        /* Statuts */
        .table-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-brouillon { background: #f3f4f6; color: #6b7280; }
        .status-soumise { background: #dbeafe; color: #1e40af; }
        .status-validee_chef { background: #fef3c7; color: #d97706; }
        .status-validee_dg { background: #fed7aa; color: #ea580c; }
        .status-approuvee_ms { background: #d1fae5; color: #059669; }
        .status-en_cours { background: #e0e7ff; color: #4338ca; }
        .status-expedie { background: #f3e8ff; color: #7c3aed; }
        .status-livre_complet { background: #dcfce7; color: #16a34a; }
        .status-cloture { background: #f1f5f9; color: #475569; }
        .status-annule { background: #fee2e2; color: #dc2626; }
        .status-refuse { background: #fecaca; color: #dc2626; }

        /* Actions */
        .actions-column {
            width: 120px;
            text-align: center;
        }

        .table-actions {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .btn-table {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }

        .btn-table:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .btn-view { background: #6b7280; color: white; }
        .btn-edit { background: #f59e0b; color: white; }
        .btn-validate { background: #10b981; color: white; }
        .btn-reject { background: #ef4444; color: white; }

        /* Pagination */
        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #059669;
            color: white;
            border-color: #059669;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .filters-row {
                grid-template-columns: 1fr;
            }

            .table-header {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .table-footer {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .commands-table {
                font-size: 12px;
            }

            .commands-table th,
            .commands-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1>📦 Gestion des Commandes - IPT</h1>
                <div class="header-actions">
                    <button id="btnNouvelleCommande" class="btn btn-primary">
                        ➕ Nouvelle Commande
                    </button>
                    <button id="btnExport" class="btn btn-info">
                        📊 Exporter
                    </button>
                    <button id="btnStatistiques" class="btn btn-secondary">
                        📈 Statistiques
                    </button>
                </div>
            </div>
        </header>

        <!-- Onglets de vue -->
        <div class="view-tabs">
            <button class="tab-btn active" onclick="switchView('table')">📊 Vue Tableau</button>
            <button class="tab-btn" onclick="switchView('cards')">🗃️ Vue Cartes</button>
        </div>

        <!-- Filtres avancés -->
        <div class="filters-section">
            <div class="filters-header">
                <h3>🔍 Filtres et Recherche</h3>
                <button id="btnToggleFilters" class="btn btn-secondary btn-sm">Afficher/Masquer</button>
            </div>
            <div class="filters-content" id="filtersContent">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>Recherche globale</label>
                        <input type="text" id="searchGlobal" placeholder="Rechercher dans tous les champs...">
                    </div>
                    <div class="filter-group">
                        <label>Statut</label>
                        <select id="filterStatut">
                            <option value="">Tous les statuts</option>
                            <option value="brouillon">Brouillon</option>
                            <option value="soumise">Soumise</option>
                            <option value="validee_chef">Validée Chef</option>
                            <option value="validee_dg">Validée DG</option>
                            <option value="approuvee_ms">Approuvée MS</option>
                            <option value="en_cours">En Cours</option>
                            <option value="expedie">Expédiée</option>
                            <option value="livre_complet">Livrée</option>
                            <option value="cloture">Clôturée</option>
                            <option value="annule">Annulée</option>
                            <option value="refuse">Refusée</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Type</label>
                        <select id="filterType">
                            <option value="">Tous les types</option>
                            <option value="fournitures">Fournitures</option>
                            <option value="equipements">Équipements</option>
                            <option value="services">Services</option>
                            <option value="maintenance">Maintenance</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Urgence</label>
                        <select id="filterUrgence">
                            <option value="">Toutes les urgences</option>
                            <option value="urgente">Urgente</option>
                            <option value="elevee">Élevée</option>
                            <option value="normale">Normale</option>
                            <option value="faible">Faible</option>
                        </select>
                    </div>
                </div>
                <div class="filters-row">
                    <div class="filter-group">
                        <label>Date de commande</label>
                        <input type="date" id="filterDateDebut" placeholder="Date début">
                    </div>
                    <div class="filter-group">
                        <label>à</label>
                        <input type="date" id="filterDateFin" placeholder="Date fin">
                    </div>
                    <div class="filter-group">
                        <label>Fournisseur</label>
                        <input type="text" id="filterFournisseur" placeholder="Nom du fournisseur">
                    </div>
                    <div class="filter-group">
                        <label>Demandeur</label>
                        <input type="text" id="filterDemandeur" placeholder="Service demandeur">
                    </div>
                </div>
                <div class="filters-actions">
                    <button id="btnApplyFilters" class="btn btn-primary">🔍 Appliquer</button>
                    <button id="btnClearFilters" class="btn btn-secondary">🗑️ Effacer</button>
                    <button id="btnRefresh" class="btn btn-secondary">🔄 Actualiser</button>
                </div>
            </div>
        </div>

        <!-- Vue Tableau -->
        <div id="tableView" class="table-view">
            <div class="table-container">
                <div class="table-header">
                    <div class="table-info">
                        <span id="tableResultsCount">0 résultats</span>
                        <span id="tableResultsInfo"></span>
                    </div>
                    <div class="table-controls">
                        <select id="pageSize" class="page-size-select">
                            <option value="10">10 par page</option>
                            <option value="25" selected>25 par page</option>
                            <option value="50">50 par page</option>
                            <option value="100">100 par page</option>
                        </select>
                    </div>
                </div>
                
                <div class="table-wrapper">
                    <table id="commandsTable" class="commands-table">
                        <thead>
                            <tr>
                                <th data-column="numero_commande" class="sortable">
                                    N° Commande <span class="sort-indicator"></span>
                                </th>
                                <th data-column="type_commande" class="sortable">
                                    Type <span class="sort-indicator"></span>
                                </th>
                                <th data-column="demandeur_service" class="sortable">
                                    Demandeur <span class="sort-indicator"></span>
                                </th>
                                <th data-column="fournisseur_nom" class="sortable">
                                    Fournisseur <span class="sort-indicator"></span>
                                </th>
                                <th data-column="statut" class="sortable">
                                    Statut <span class="sort-indicator"></span>
                                </th>
                                <th data-column="date_commande" class="sortable">
                                    Date Commande <span class="sort-indicator"></span>
                                </th>
                                <th data-column="date_livraison_prevue" class="sortable">
                                    Livraison Prévue <span class="sort-indicator"></span>
                                </th>
                                <th data-column="montant_ttc" class="sortable">
                                    Montant <span class="sort-indicator"></span>
                                </th>
                                <th class="actions-column">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="commandsTableBody">
                            <!-- Les données seront chargées ici -->
                        </tbody>
                    </table>
                </div>
                
                <div class="table-footer">
                    <div class="pagination-info">
                        <span id="paginationInfo">Affichage de 0 à 0 sur 0 résultats</span>
                    </div>
                    <div class="pagination-controls" id="paginationControls">
                        <!-- Les contrôles de pagination seront générés ici -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Vue Cartes (pour plus tard) -->
        <div id="cardsView" class="cards-view" style="display: none;">
            <div id="commandsList">
                <!-- Les commandes en cartes seront affichées ici -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/commands-table-manager.js"></script>
    
    <script>
        // Initialisation de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📦 Initialisation de la gestion des commandes...');
            
            try {
                // Initialiser Supabase
                if (typeof initializeSupabase === 'function') {
                    const { client, available } = await initializeSupabase();
                    
                    if (available) {
                        console.log('✅ Supabase connecté pour la gestion des commandes');
                        
                        // Initialiser le gestionnaire de table
                        if (typeof CommandsTableManager !== 'undefined') {
                            await commandsTableManager.initialize();
                            console.log('✅ Gestionnaire de table des commandes initialisé');
                        }
                    } else {
                        console.warn('⚠️ Supabase non disponible - mode dégradé');
                    }
                } else {
                    console.error('❌ Fonction initializeSupabase non trouvée');
                }
                
            } catch (error) {
                console.error('❌ Erreur initialisation gestion commandes:', error);
            }
        });

        // Fonction pour basculer entre les vues
        function switchView(viewType) {
            const tableView = document.getElementById('tableView');
            const cardsView = document.getElementById('cardsView');
            const tabBtns = document.querySelectorAll('.tab-btn');

            // Mettre à jour les onglets
            tabBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Basculer les vues
            if (viewType === 'table') {
                tableView.style.display = 'block';
                cardsView.style.display = 'none';
                
                // Charger la table si pas encore fait
                if (window.commandsTableManager && !window.commandsTableManager.data.length) {
                    commandsTableManager.loadData();
                }
            } else {
                tableView.style.display = 'none';
                cardsView.style.display = 'block';
                
                // Charger les cartes si pas encore fait
                // TODO: Implémenter la vue cartes
            }
        }

        // Fonction pour basculer les filtres
        function toggleFilters() {
            if (window.commandsTableManager) {
                commandsTableManager.toggleFilters();
            }
        }
    </script>
</body>
</html>
