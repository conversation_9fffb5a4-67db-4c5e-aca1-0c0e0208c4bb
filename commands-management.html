<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Commandes - IPT</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f8fafc;
            color: #1f2937;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
        }

        .header-actions {
            display: flex;
            gap: 15px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
        }

        .btn-secondary {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-info {
            background: #06b6d4;
            color: white;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
        }

        /* Onglets de vue */
        .view-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: #f8fafc;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            background: white;
            border-bottom-color: #059669;
            color: #059669;
        }

        /* Filtres */
        .filters-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .filters-header h3 {
            margin: 0;
            color: #374151;
        }

        .filters-content {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-size: 12px;
            font-weight: 600;
            color: #6b7280;
            margin-bottom: 5px;
            text-transform: uppercase;
        }

        .filter-group input,
        .filter-group select {
            padding: 10px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
            background: white;
        }

        .filter-group input:focus,
        .filter-group select:focus {
            outline: none;
            border-color: #059669;
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
        }

        .filters-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        /* Table */
        .table-view {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-container {
            width: 100%;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .table-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .table-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .page-size-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .commands-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .commands-table th {
            background: #f8fafc;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            white-space: nowrap;
            position: relative;
        }

        .commands-table th.sortable {
            cursor: pointer;
            user-select: none;
        }

        .commands-table th.sortable:hover {
            background: #f1f5f9;
        }

        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sort-indicator.asc::after {
            content: '↑';
            opacity: 1;
        }

        .sort-indicator.desc::after {
            content: '↓';
            opacity: 1;
        }

        .commands-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }

        .commands-table tbody tr:hover {
            background: #f8fafc;
        }

        /* Badges */
        .table-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-fournitures {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-equipements {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .badge-services {
            background: #fef3c7;
            color: #d97706;
        }

        .badge-maintenance {
            background: #fee2e2;
            color: #dc2626;
        }

        /* Statuts */
        .table-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-brouillon { background: #f3f4f6; color: #6b7280; }
        .status-soumise { background: #dbeafe; color: #1e40af; }
        .status-validee_chef { background: #fef3c7; color: #d97706; }
        .status-validee_dg { background: #fed7aa; color: #ea580c; }
        .status-approuvee_ms { background: #d1fae5; color: #059669; }
        .status-en_cours { background: #e0e7ff; color: #4338ca; }
        .status-expedie { background: #f3e8ff; color: #7c3aed; }
        .status-livre_complet { background: #dcfce7; color: #16a34a; }
        .status-cloture { background: #f1f5f9; color: #475569; }
        .status-annule { background: #fee2e2; color: #dc2626; }
        .status-refuse { background: #fecaca; color: #dc2626; }

        /* Actions */
        .actions-column {
            width: 120px;
            text-align: center;
        }

        .table-actions {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .btn-table {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }

        .btn-table:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .btn-view { background: #6b7280; color: white; }
        .btn-edit { background: #f59e0b; color: white; }
        .btn-validate { background: #10b981; color: white; }
        .btn-reject { background: #ef4444; color: white; }

        /* Pagination */
        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #059669;
            color: white;
            border-color: #059669;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .filters-row {
                grid-template-columns: 1fr;
            }

            .table-header {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .table-footer {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .commands-table {
                font-size: 12px;
            }

            .commands-table th,
            .commands-table td {
                padding: 8px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1>🔧 Administration des Commandes - IPT</h1>
                <div class="header-actions">
                    <a href="index.html" class="btn btn-secondary">
                        ⬅️ Retour Accueil
                    </a>
                </div>
            </div>
        </header>

        <!-- Interface Admin des Commandes -->
        <div id="commandsAdminContainer" class="module-zone">
            <!-- Le contenu sera généré par l'interface admin -->
        </div>


    </div>

    <!-- Scripts -->
    <script src="assets/js/commands-admin-interface.js"></script>

    <script>
        // Initialisation de la page admin
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔧 Initialisation de l\'interface admin des commandes...');

            try {
                // Initialiser Supabase
                if (typeof initializeSupabase === 'function') {
                    const { client, available } = await initializeSupabase();

                    if (available) {
                        console.log('✅ Supabase connecté pour l\'admin des commandes');

                        // Initialiser l'interface admin
                        if (typeof CommandsAdminInterface !== 'undefined') {
                            window.commandsAdminInterface = new CommandsAdminInterface();
                            await window.commandsAdminInterface.initialize();
                            console.log('✅ Interface admin des commandes initialisée');
                        }
                    } else {
                        console.warn('⚠️ Supabase non disponible - mode dégradé');
                        // Afficher un message d'erreur
                        document.getElementById('commandsAdminContainer').innerHTML = `
                            <div class="admin-access-denied">
                                <div class="access-denied-icon">🔌</div>
                                <h3>Connexion Base de Données Indisponible</h3>
                                <p>Impossible de se connecter à la base de données.</p>
                                <p>Veuillez vérifier votre connexion et réessayer.</p>
                            </div>
                        `;
                    }
                } else {
                    console.error('❌ Fonction initializeSupabase non trouvée');
                    document.getElementById('commandsAdminContainer').innerHTML = `
                        <div class="admin-access-denied">
                            <div class="access-denied-icon">⚠️</div>
                            <h3>Erreur de Configuration</h3>
                            <p>Configuration Supabase manquante.</p>
                            <p>Contactez l'administrateur système.</p>
                        </div>
                    `;
                }

            } catch (error) {
                console.error('❌ Erreur initialisation admin commandes:', error);
                document.getElementById('commandsAdminContainer').innerHTML = `
                    <div class="admin-access-denied">
                        <div class="access-denied-icon">❌</div>
                        <h3>Erreur d'Initialisation</h3>
                        <p>Une erreur est survenue lors de l'initialisation.</p>
                        <p>Détails: ${error.message}</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
