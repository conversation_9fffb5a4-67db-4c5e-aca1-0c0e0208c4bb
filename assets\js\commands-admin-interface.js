// Interface Administrative pour la Gestion des Commandes
// Accès restreint aux utilisateurs autorisés : admin123, namara, rym

class CommandsAdminInterface {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.isAuthorized = false;
        this.commands = [];
        
        // Utilisateurs autorisés pour l'administration des commandes
        this.authorizedUsers = [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>'
        ];
        
        // Configuration des statuts de commandes
        this.commandStatuses = {
            'brouillon': { label: '📝 Brouillon', color: '#6b7280' },
            'soumise': { label: '📤 Soumise', color: '#3b82f6' },
            'validee_chef': { label: '✅ Validée Chef', color: '#10b981' },
            'validee_dg': { label: '👨‍💼 Validée DG', color: '#059669' },
            'approuvee_ms': { label: '🏛️ Approuvée MS', color: '#0d9488' },
            'en_cours': { label: '🔄 En Cours', color: '#f59e0b' },
            'expedie': { label: '📦 Expédiée', color: '#8b5cf6' },
            'livre_partiel': { label: '📦 Livré Partiel', color: '#a855f7' },
            'livre': { label: '✅ Livrée', color: '#22c55e' },
            'annulee': { label: '❌ Annulée', color: '#ef4444' }
        };
    }

    async initialize() {
        try {
            console.log('🔧 Initialisation interface admin commandes...');
            
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                console.warn('⚠️ Supabase non disponible');
                return false;
            }

            // Vérifier l'authentification
            await this.checkAuthentication();
            
            if (!this.isAuthorized) {
                console.warn('⚠️ Utilisateur non autorisé pour l\'administration des commandes');
                return false;
            }

            // Charger les données
            await this.loadCommands();
            
            console.log('✅ Interface admin commandes initialisée');
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation interface admin:', error);
            return false;
        }
    }

    async checkAuthentication() {
        try {
            const { data: { user } } = await this.supabase.auth.getUser();
            
            if (user) {
                this.currentUser = user;
                
                // Vérifier si l'utilisateur est autorisé
                const userRole = user.user_metadata?.role_ipt;
                this.isAuthorized = this.authorizedUsers.includes(user.email) || 
                                  userRole === 'admin_systeme' ||
                                  user.user_metadata?.role === 'admin';
                
                console.log(`🔐 Utilisateur: ${user.email}, Autorisé: ${this.isAuthorized}`);
            }
            
        } catch (error) {
            console.error('❌ Erreur vérification authentification:', error);
            this.isAuthorized = false;
        }
    }

    async loadCommands() {
        try {
            console.log('📥 Chargement des commandes...');
            
            const { data, error } = await this.supabase
                .from('gestion_commandes')
                .select('*')
                .order('created_at', { ascending: false });
            
            if (error) {
                console.error('❌ Erreur chargement commandes:', error);
                return;
            }
            
            this.commands = data || [];
            console.log(`✅ ${this.commands.length} commandes chargées`);
            
        } catch (error) {
            console.error('❌ Erreur chargement commandes:', error);
            this.commands = [];
        }
    }

    renderAdminInterface() {
        if (!this.isAuthorized) {
            return `
                <div class="admin-access-denied">
                    <div class="access-denied-icon">🚫</div>
                    <h3>Accès Refusé</h3>
                    <p>Vous n'avez pas les permissions nécessaires pour accéder à l'interface d'administration des commandes.</p>
                    <p>Utilisateurs autorisés : admin123, namara, rym</p>
                </div>
            `;
        }

        return `
            <div class="commands-admin-interface">
                <div class="admin-header">
                    <h2>🔧 Administration des Commandes</h2>
                    <div class="admin-user-info">
                        <span class="admin-badge">👑 Admin</span>
                        <span class="admin-user">${this.currentUser?.email}</span>
                    </div>
                </div>

                <div class="admin-controls">
                    <button onclick="commandsAdmin.showAddCommandForm()" class="btn btn-primary">
                        ➕ Nouvelle Commande
                    </button>
                    <button onclick="commandsAdmin.exportCommands()" class="btn btn-secondary">
                        📊 Exporter
                    </button>
                    <button onclick="commandsAdmin.refreshData()" class="btn btn-info">
                        🔄 Actualiser
                    </button>
                </div>

                <div class="admin-stats">
                    <div class="stat-card">
                        <div class="stat-value">${this.commands.length}</div>
                        <div class="stat-label">Total Commandes</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${this.commands.filter(c => c.statut === 'en_cours').length}</div>
                        <div class="stat-label">En Cours</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${this.commands.filter(c => c.statut === 'livre').length}</div>
                        <div class="stat-label">Livrées</div>
                    </div>
                </div>

                <div class="admin-table-container">
                    ${this.renderCommandsTable()}
                </div>

                <div id="commandFormModal" class="modal" style="display: none;">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 id="modalTitle">Nouvelle Commande</h3>
                            <button onclick="commandsAdmin.closeModal()" class="modal-close">✖️</button>
                        </div>
                        <div class="modal-body">
                            ${this.renderCommandForm()}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderCommandsTable() {
        if (this.commands.length === 0) {
            return `
                <div class="no-data">
                    <div class="no-data-icon">📦</div>
                    <h3>Aucune commande</h3>
                    <p>Commencez par créer votre première commande</p>
                </div>
            `;
        }

        return `
            <table class="admin-table">
                <thead>
                    <tr>
                        <th>N° Commande</th>
                        <th>Fournisseur</th>
                        <th>Montant</th>
                        <th>Statut</th>
                        <th>Date Création</th>
                        <th>Demandeur</th>
                        <th>Actions Admin</th>
                    </tr>
                </thead>
                <tbody>
                    ${this.commands.map(command => this.renderCommandRow(command)).join('')}
                </tbody>
            </table>
        `;
    }

    renderCommandRow(command) {
        const status = this.commandStatuses[command.statut] || { label: command.statut, color: '#6b7280' };
        const createdDate = new Date(command.created_at).toLocaleDateString('fr-FR');
        
        return `
            <tr>
                <td><strong>${command.numero_commande || 'N/A'}</strong></td>
                <td>${command.fournisseur || 'N/A'}</td>
                <td>${command.montant_total ? command.montant_total + ' ' + (command.devise || 'TND') : 'N/A'}</td>
                <td>
                    <span class="status-badge" style="background-color: ${status.color}">
                        ${status.label}
                    </span>
                </td>
                <td>${createdDate}</td>
                <td>${command.demandeur_email || 'N/A'}</td>
                <td class="actions-cell">
                    <button onclick="commandsAdmin.editCommand('${command.id}')" class="btn-action btn-edit" title="Modifier">
                        ✏️
                    </button>
                    <button onclick="commandsAdmin.viewCommand('${command.id}')" class="btn-action btn-view" title="Voir détails">
                        👁️
                    </button>
                    <button onclick="commandsAdmin.deleteCommand('${command.id}')" class="btn-action btn-delete" title="Supprimer">
                        🗑️
                    </button>
                </td>
            </tr>
        `;
    }

    renderCommandForm() {
        return `
            <form id="commandForm" onsubmit="commandsAdmin.saveCommand(event)">
                <input type="hidden" id="commandId" value="">
                
                <div class="form-group">
                    <label for="numeroCommande">N° Commande *</label>
                    <input type="text" id="numeroCommande" required>
                </div>
                
                <div class="form-group">
                    <label for="fournisseur">Fournisseur *</label>
                    <input type="text" id="fournisseur" required>
                </div>
                
                <div class="form-group">
                    <label for="contactFournisseur">Contact Fournisseur</label>
                    <input type="text" id="contactFournisseur">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="montantTotal">Montant Total</label>
                        <input type="number" id="montantTotal" step="0.01" min="0">
                    </div>
                    <div class="form-group">
                        <label for="devise">Devise</label>
                        <select id="devise">
                            <option value="TND">TND</option>
                            <option value="EUR">EUR</option>
                            <option value="USD">USD</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="statut">Statut</label>
                    <select id="statut">
                        ${Object.entries(this.commandStatuses).map(([key, status]) => 
                            `<option value="${key}">${status.label}</option>`
                        ).join('')}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="demandeurEmail">Email Demandeur</label>
                    <input type="email" id="demandeurEmail">
                </div>
                
                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea id="notes" rows="3"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">💾 Sauvegarder</button>
                    <button type="button" onclick="commandsAdmin.closeModal()" class="btn btn-secondary">❌ Annuler</button>
                </div>
            </form>
        `;
    }

    // === MÉTHODES D'ACTION ===

    showAddCommandForm() {
        document.getElementById('modalTitle').textContent = 'Nouvelle Commande';
        document.getElementById('commandId').value = '';
        document.getElementById('commandForm').reset();
        document.getElementById('commandFormModal').style.display = 'flex';
    }

    async editCommand(commandId) {
        const command = this.commands.find(c => c.id === commandId);
        if (!command) return;

        document.getElementById('modalTitle').textContent = 'Modifier Commande';
        document.getElementById('commandId').value = commandId;

        // Remplir le formulaire
        document.getElementById('numeroCommande').value = command.numero_commande || '';
        document.getElementById('fournisseur').value = command.fournisseur || '';
        document.getElementById('contactFournisseur').value = command.contact_fournisseur || '';
        document.getElementById('montantTotal').value = command.montant_total || '';
        document.getElementById('devise').value = command.devise || 'TND';
        document.getElementById('statut').value = command.statut || 'brouillon';
        document.getElementById('demandeurEmail').value = command.demandeur_email || '';
        document.getElementById('notes').value = command.notes || '';

        document.getElementById('commandFormModal').style.display = 'flex';
    }

    async viewCommand(commandId) {
        const command = this.commands.find(c => c.id === commandId);
        if (!command) return;

        const status = this.commandStatuses[command.statut] || { label: command.statut, color: '#6b7280' };

        alert(`
📦 Détails de la Commande

N° Commande: ${command.numero_commande || 'N/A'}
Fournisseur: ${command.fournisseur || 'N/A'}
Contact: ${command.contact_fournisseur || 'N/A'}
Montant: ${command.montant_total || 'N/A'} ${command.devise || 'TND'}
Statut: ${status.label}
Demandeur: ${command.demandeur_email || 'N/A'}
Date création: ${new Date(command.created_at).toLocaleString('fr-FR')}
Notes: ${command.notes || 'Aucune note'}
        `);
    }

    async deleteCommand(commandId) {
        if (!confirm('⚠️ Êtes-vous sûr de vouloir supprimer cette commande ?')) {
            return;
        }

        try {
            const { error } = await this.supabase
                .from('gestion_commandes')
                .delete()
                .eq('id', commandId);

            if (error) {
                console.error('❌ Erreur suppression:', error);
                alert('❌ Erreur lors de la suppression');
                return;
            }

            await this.refreshData();
            alert('✅ Commande supprimée avec succès');

        } catch (error) {
            console.error('❌ Erreur suppression commande:', error);
            alert('❌ Erreur lors de la suppression');
        }
    }

    async saveCommand(event) {
        event.preventDefault();

        const commandId = document.getElementById('commandId').value;
        const isEdit = !!commandId;

        const commandData = {
            numero_commande: document.getElementById('numeroCommande').value,
            fournisseur: document.getElementById('fournisseur').value,
            contact_fournisseur: document.getElementById('contactFournisseur').value,
            montant_total: parseFloat(document.getElementById('montantTotal').value) || null,
            devise: document.getElementById('devise').value,
            statut: document.getElementById('statut').value,
            demandeur_email: document.getElementById('demandeurEmail').value,
            notes: document.getElementById('notes').value,
            updated_at: new Date().toISOString()
        };

        try {
            let result;

            if (isEdit) {
                result = await this.supabase
                    .from('gestion_commandes')
                    .update(commandData)
                    .eq('id', commandId);
            } else {
                commandData.created_at = new Date().toISOString();
                commandData.created_by = this.currentUser?.email;

                result = await this.supabase
                    .from('gestion_commandes')
                    .insert([commandData]);
            }

            if (result.error) {
                console.error('❌ Erreur sauvegarde:', result.error);
                alert('❌ Erreur lors de la sauvegarde');
                return;
            }

            this.closeModal();
            await this.refreshData();
            alert(`✅ Commande ${isEdit ? 'modifiée' : 'créée'} avec succès`);

        } catch (error) {
            console.error('❌ Erreur sauvegarde commande:', error);
            alert('❌ Erreur lors de la sauvegarde');
        }
    }

    closeModal() {
        document.getElementById('commandFormModal').style.display = 'none';
    }

    async refreshData() {
        await this.loadCommands();
        this.updateDisplay();
    }

    updateDisplay() {
        const container = document.getElementById('commandsAdminContainer');
        if (container) {
            container.innerHTML = this.renderAdminInterface();
        }
    }

    async exportCommands() {
        try {
            const csvContent = this.generateCSV();
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);

            link.setAttribute('href', url);
            link.setAttribute('download', `commandes_export_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';

            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('❌ Erreur export:', error);
            alert('❌ Erreur lors de l\'export');
        }
    }

    generateCSV() {
        const headers = ['N° Commande', 'Fournisseur', 'Contact', 'Montant', 'Devise', 'Statut', 'Demandeur', 'Date Création', 'Notes'];
        const rows = this.commands.map(command => [
            command.numero_commande || '',
            command.fournisseur || '',
            command.contact_fournisseur || '',
            command.montant_total || '',
            command.devise || '',
            command.statut || '',
            command.demandeur_email || '',
            new Date(command.created_at).toLocaleDateString('fr-FR'),
            (command.notes || '').replace(/"/g, '""')
        ]);

        return [headers, ...rows]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');
    }
}

// Instance globale
window.commandsAdmin = new CommandsAdminInterface();
