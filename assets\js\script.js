// Application de Gestion Interne - Script Principal
// Version avec intégration Supabase complète

// Fonction de vérification des bibliothèques
function checkLibraries() {
    const libraries = {
        'XLSX': typeof XLSX !== 'undefined',
        'jsPDF': typeof jsPDF !== 'undefined' || typeof window.jsPDF !== 'undefined',
        'Supabase': typeof supabase !== 'undefined' || typeof window.supabase !== 'undefined',
        'FontAwesome': document.querySelector('link[href*="font-awesome"]') !== null
    };

    console.log('📚 Vérification des bibliothèques:');
    Object.entries(libraries).forEach(([name, loaded]) => {
        const status = loaded ? '✅' : '❌';
        console.log(`${status} ${name}: ${loaded ? 'Chargé' : 'Non disponible'}`);
    });

    // Afficher un résumé
    const loadedCount = Object.values(libraries).filter(Boolean).length;
    const totalCount = Object.keys(libraries).length;
    console.log(`📊 Résumé: ${loadedCount}/${totalCount} bibliothèques chargées`);

    return libraries;
}

// Vérification immédiate
const libraryStatus = checkLibraries();

// Fonction pour attendre le chargement des bibliothèques
function waitForLibraries(maxWait = 5000) {
    return new Promise((resolve) => {
        const startTime = Date.now();

        function check() {
            const libraries = checkLibraries();
            const allLoaded = Object.values(libraries).every(Boolean);

            if (allLoaded || (Date.now() - startTime) > maxWait) {
                resolve(libraries);
            } else {
                setTimeout(check, 100);
            }
        }

        check();
    });
}

// Vérification du chargement des bibliothèques externes avec fallbacks
if (typeof XLSX === 'undefined') {
    console.warn('⚠️ XLSX library not loaded - Export Excel désactivé');
}
if (typeof jsPDF === 'undefined' && typeof window.jsPDF === 'undefined') {
    console.warn('⚠️ jsPDF library not loaded - Export PDF désactivé');
}
if (typeof supabase === 'undefined' && typeof window.supabase === 'undefined') {
    console.warn('⚠️ Supabase SDK not loaded - Mode local uniquement');
}

// Gestionnaire de base de données avec Supabase
window.DatabaseManager = {
    // Sauvegarder des données
    async save(table, data) {
        try {
            // Vérifier la disponibilité de SupabaseUtils
            const supabaseUtils = window.SupabaseUtils || SupabaseUtils;

            if (supabaseUtils && supabaseUtils.isAvailable()) {
                console.log(`💾 Sauvegarde ${table} vers Supabase...`);
                const result = await supabaseUtils.saveData(table, {
                    ...data,
                    id: data.id || generateId(),
                    created_at: data.created_at || new Date().toISOString(),
                    updated_at: new Date().toISOString()
                });

                if (result.success) {
                    console.log(`✅ ${table} sauvegardé avec succès`);
                    return result.data;
                } else {
                    console.warn(`⚠️ Erreur sauvegarde ${table}:`, result.error);
                    return null;
                }
            } else {
                // Fallback localStorage
                console.log(`💾 Sauvegarde ${table} en local...`);
                const key = `gestion_${table}`;
                const existing = JSON.parse(localStorage.getItem(key) || '[]');
                const newData = {
                    ...data,
                    id: data.id || generateId(),
                    created_at: data.created_at || new Date().toISOString(),
                    updated_at: new Date().toISOString()
                };
                existing.push(newData);
                localStorage.setItem(key, JSON.stringify(existing));
                return newData;
            }
        } catch (error) {
            console.error(`❌ Erreur sauvegarde ${table}:`, error);
            return null;
        }
    },

    // Charger des données
    async load(table, filters = {}) {
        try {
            // Vérifier la disponibilité de SupabaseUtils
            const supabaseUtils = window.SupabaseUtils || SupabaseUtils;

            if (supabaseUtils && supabaseUtils.isAvailable()) {
                console.log(`📥 Chargement ${table} depuis Supabase...`);
                const result = await supabaseUtils.loadData(table, filters);

                if (result.success) {
                    console.log(`✅ ${table} chargé: ${result.data.length} éléments`);
                    return result.data;
                } else {
                    console.warn(`⚠️ Erreur chargement ${table}:`, result.error);
                    return this.loadFromLocal(table);
                }
            } else {
                // Fallback localStorage
                return this.loadFromLocal(table);
            }
        } catch (error) {
            console.error(`❌ Erreur chargement ${table}:`, error);
            return this.loadFromLocal(table);
        }
    },

    // Charger depuis localStorage
    loadFromLocal(table) {
        try {
            const key = `gestion_${table}`;
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            console.log(`📥 ${table} chargé en local: ${data.length} éléments`);
            return data;
        } catch (error) {
            console.error(`❌ Erreur chargement local ${table}:`, error);
            return [];
        }
    },

    // Mettre à jour des données
    async update(table, id, updates) {
        try {
            // Vérifier la disponibilité de SupabaseUtils
            const supabaseUtils = window.SupabaseUtils || SupabaseUtils;

            if (supabaseUtils && supabaseUtils.isAvailable()) {
                console.log(`🔄 Mise à jour ${table}/${id} vers Supabase...`);
                const result = await supabaseUtils.updateData(table, id, {
                    ...updates,
                    updated_at: new Date().toISOString()
                });

                if (result.success) {
                    console.log(`✅ ${table}/${id} mis à jour avec succès`);
                    return result.data;
                } else {
                    console.warn(`⚠️ Erreur mise à jour ${table}/${id}:`, result.error);
                    return null;
                }
            } else {
                // Fallback localStorage
                const key = `gestion_${table}`;
                const data = JSON.parse(localStorage.getItem(key) || '[]');
                const index = data.findIndex(item => item.id === id);

                if (index !== -1) {
                    data[index] = { ...data[index], ...updates, updated_at: new Date().toISOString() };
                    localStorage.setItem(key, JSON.stringify(data));
                    return data[index];
                }
                return null;
            }
        } catch (error) {
            console.error(`❌ Erreur mise à jour ${table}/${id}:`, error);
            return null;
        }
    },

    // Supprimer des données
    async delete(table, id) {
        try {
            // Vérifier la disponibilité de SupabaseUtils
            const supabaseUtils = window.SupabaseUtils || SupabaseUtils;

            if (supabaseUtils && supabaseUtils.isAvailable()) {
                console.log(`🗑️ Suppression ${table}/${id} de Supabase...`);
                const result = await supabaseUtils.deleteData(table, id);

                if (result.success) {
                    console.log(`✅ ${table}/${id} supprimé avec succès`);
                    return true;
                } else {
                    console.warn(`⚠️ Erreur suppression ${table}/${id}:`, result.error);
                    return false;
                }
            } else {
                // Fallback localStorage
                const key = `gestion_${table}`;
                const data = JSON.parse(localStorage.getItem(key) || '[]');
                const filtered = data.filter(item => item.id !== id);
                localStorage.setItem(key, JSON.stringify(filtered));
                return true;
            }
        } catch (error) {
            console.error(`❌ Erreur suppression ${table}/${id}:`, error);
            return false;
        }
    },

    // Synchroniser toutes les données vers Supabase
    async syncAll() {
        // Vérifier la disponibilité de SupabaseUtils
        const supabaseUtils = window.SupabaseUtils || SupabaseUtils;

        if (supabaseUtils && supabaseUtils.isAvailable()) {
            console.log('🔄 Synchronisation complète vers Supabase...');
            const result = await supabaseUtils.syncToSupabase();

            if (result.success) {
                console.log('✅ Synchronisation terminée:', result.results);
                return result;
            } else {
                console.warn('⚠️ Erreur synchronisation:', result.error);
                return result;
            }
        } else {
            console.log('⚠️ Supabase non disponible pour la synchronisation');
            return { success: false, error: 'Supabase not available' };
        }
    }
};

// Fonction pour vérifier la disponibilité de DatabaseManager
function checkDatabaseManager() {
    if (typeof window.DatabaseManager === 'undefined') {
        console.error('❌ DatabaseManager non disponible');
        return false;
    }
    console.log('✅ DatabaseManager disponible');
    return true;
}

// Fonction pour obtenir DatabaseManager de manière sûre
function getDatabaseManager() {
    if (typeof window.DatabaseManager !== 'undefined') {
        return window.DatabaseManager;
    } else if (typeof DatabaseManager !== 'undefined') {
        return DatabaseManager;
    } else {
        console.error('❌ DatabaseManager non trouvé');
        return null;
    }
}

// Variables globales
let messages = [];
let commandes = [];
let pvs = [];
let utilisateurCourant = null;
let fichiersSelectionnes = [];
let membresConnectes = [];
let commandeEnCours = null;

// Utilisation des configurations
const utilisateurs = APP_CONFIG.demoUsers;
const products = APP_CONFIG.defaultProducts;
const MESSAGE_STORAGE_KEY = APP_CONFIG.storage.keys.messages;
const COMMANDE_STORAGE_KEY = APP_CONFIG.storage.keys.commands;
const PV_STORAGE_KEY = APP_CONFIG.storage.keys.pv;
const USER_STORAGE_KEY = APP_CONFIG.storage.keys.user;

console.log(`Application chargée avec ${Object.keys(utilisateurs).length} utilisateurs`);

// Fonctions d'authentification
async function login() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('loginError');
    
    if (!username || !password) {
        showError(errorDiv, 'Veuillez remplir tous les champs');
        return;
    }
    
    // Vérification des identifiants
    if (utilisateurs[username] && utilisateurs[username].password === password) {
        if (!utilisateurs[username].actif) {
            showError(errorDiv, 'Compte désactivé');
            return;
        }
        
        utilisateurCourant = {
            username: username,
            ...utilisateurs[username]
        };
        
        // Sauvegarder l'utilisateur connecté
        try {
            if (isSupabaseAvailable && supabaseClient) {
                // En production, utiliser Supabase Auth
                console.log('Connexion avec Supabase Auth (simulée)');
            } else {
                localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(utilisateurCourant));
            }
        } catch (error) {
            console.error('Erreur lors de la sauvegarde utilisateur:', error);
            localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(utilisateurCourant));
        }
        
        // Configurer l'interface utilisateur
        setupApplicationUI();
        
        // Charger les données
        await chargerDonnees();
        
        // Afficher l'application
        document.getElementById('login').style.display = 'none';
        document.getElementById('mainApp').style.display = 'block';
        
        // Envoyer message de connexion
        envoyerMessageAutomatique(`${username} s'est connecté(e)`);
        
        // Mettre à jour le statut
        updateStatus();
        updateMembersList();
        
        console.log(`Utilisateur ${username} connecté avec le rôle ${utilisateurCourant.role}`);
        
    } else {
        showError(errorDiv, ConfigUtils.getMessage('errors', 'loginFailed'));
    }
}

function deconnexion() {
    if (utilisateurCourant) {
        envoyerMessageAutomatique(`${utilisateurCourant.username} s'est déconnecté(e)`);
        
        try {
            if (isSupabaseAvailable && supabaseClient) {
                // En production, utiliser supabaseClient.auth.signOut()
                console.log('Déconnexion Supabase (simulée)');
            } else {
                localStorage.removeItem(USER_STORAGE_KEY);
            }
        } catch (error) {
            console.error('Erreur lors de la déconnexion:', error);
            localStorage.removeItem(USER_STORAGE_KEY);
        }
        
        utilisateurCourant = null;
        
        // Réinitialiser l'interface
        document.getElementById('login').style.display = 'block';
        document.getElementById('mainApp').style.display = 'none';
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        
        // Cacher toutes les zones modules
        hideAllModuleZones();
        
        console.log('Utilisateur déconnecté');
    }
}

function setupApplicationUI() {
    if (!utilisateurCourant) return;
    
    // Appliquer les restrictions de rôle
    applyRoleRestrictions();
    
    // Configurer l'interface selon le rôle
    setupUIForRole();
    
    // Charger les destinataires pour le chat
    chargerDestinataires();
    
    console.log(`Interface configurée pour le rôle: ${utilisateurCourant.role}`);
}

function applyRoleRestrictions() {
    const role = utilisateurCourant.role;
    
    // Gestion des boutons selon le rôle
    document.getElementById('gestionCommandesBtn').style.display = 
        canManageCommands() ? 'inline-block' : 'none';
    
    document.getElementById('gestionPvBtn').style.display = 
        canManagePV() ? 'inline-block' : 'none';
    
    document.getElementById('analyseBtn').style.display = 
        isAdmin() || isManager() ? 'inline-block' : 'none';
    
    // Le convertisseur et changement de mot de passe sont disponibles pour tous
}

function setupUIForRole() {
    const role = utilisateurCourant.role;

    // Configuration spécifique selon le rôle
    if (isDepot()) {
        // Les dépôts voient principalement la gestion des PV
        loadProductsForDepot();
    }

    if (isHygiene()) {
        // Les membres de l'équipe hygiène voient leur espace dédié
        console.log('Interface configurée pour l\'équipe hygiène');
        // Afficher automatiquement la zone hygiène
        setTimeout(() => {
            afficherZoneHygiene();
        }, 500);
    }

    if (isAdmin()) {
        // Les admins ont accès à toutes les fonctionnalités
        const clearBtn = document.getElementById('clearAllPvsBtn');
        if (clearBtn) clearBtn.style.display = 'inline-block';
    }
}

// Fonctions utilitaires de rôles
function canManageCommands() {
    return ConfigUtils.hasPermission(utilisateurCourant?.role, 'commands');
}

function canManagePV() {
    return ConfigUtils.hasPermission(utilisateurCourant?.role, 'pv') || 
           ConfigUtils.hasPermission(utilisateurCourant?.role, 'pv_view');
}

function canChat() {
    return ConfigUtils.hasPermission(utilisateurCourant?.role, 'chat');
}

function isAdmin() {
    return utilisateurCourant && utilisateurCourant.role === 'admin';
}

function isManager() {
    return utilisateurCourant && utilisateurCourant.role === 'manager';
}

function isFinance() {
    return utilisateurCourant && utilisateurCourant.role === 'finance';
}

function isDepot() {
    return utilisateurCourant && utilisateurCourant.role === 'depot';
}

function isHygiene() {
    return utilisateurCourant && utilisateurCourant.role === 'hygiene';
}

function isUser() {
    return utilisateurCourant && utilisateurCourant.role === 'user';
}

// Fonctions utilitaires
function showError(element, message) {
    element.textContent = message;
    element.style.display = 'block';
    setTimeout(() => {
        element.style.display = 'none';
    }, APP_CONFIG.notifications.duration);
}

function hideAllModuleZones() {
    const zones = ['analyseZone', 'convertisseur', 'gestionCommandesZone', 'gestionPvZone'];
    zones.forEach(zoneId => {
        const zone = document.getElementById(zoneId);
        if (zone) zone.style.display = 'none';
    });
}

function formatDate(date) {
    return ConfigUtils.formatDate(date, 'display');
}

function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Fonctions de fallback pour les exports
const ExportManager = {
    // Vérifier la disponibilité des bibliothèques
    isXLSXAvailable() {
        return typeof XLSX !== 'undefined';
    },

    isPDFAvailable() {
        return typeof jsPDF !== 'undefined' || typeof window.jsPDF !== 'undefined';
    },

    // Export Excel avec fallback
    exportToExcel(data, filename = 'export.xlsx') {
        if (this.isXLSXAvailable()) {
            try {
                const ws = XLSX.utils.json_to_sheet(data);
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, 'Données');
                XLSX.writeFile(wb, filename);
                showNotification('Export Excel réussi', 'success');
                return true;
            } catch (error) {
                console.error('Erreur export Excel:', error);
                this.fallbackExport(data, filename.replace('.xlsx', '.json'));
                return false;
            }
        } else {
            console.warn('XLSX non disponible, export JSON de fallback');
            this.fallbackExport(data, filename.replace('.xlsx', '.json'));
            return false;
        }
    },

    // Export PDF avec fallback
    exportToPDF(data, title = 'Export PDF') {
        if (this.isPDFAvailable()) {
            try {
                const jsPDFLib = typeof jsPDF !== 'undefined' ? jsPDF : window.jsPDF;
                const doc = new jsPDFLib.jsPDF();

                // Titre
                doc.setFontSize(16);
                doc.text(title, 20, 20);

                // Contenu simple
                doc.setFontSize(12);
                let yPosition = 40;

                if (Array.isArray(data)) {
                    data.forEach((item, index) => {
                        const text = typeof item === 'object' ? JSON.stringify(item) : String(item);
                        const lines = doc.splitTextToSize(text, 170);
                        doc.text(lines, 20, yPosition);
                        yPosition += lines.length * 7;

                        if (yPosition > 270) {
                            doc.addPage();
                            yPosition = 20;
                        }
                    });
                } else {
                    doc.text(String(data), 20, yPosition);
                }

                doc.save(`${title.replace(/\s+/g, '_')}.pdf`);
                showNotification('Export PDF réussi', 'success');
                return true;
            } catch (error) {
                console.error('Erreur export PDF:', error);
                this.fallbackExport(data, `${title.replace(/\s+/g, '_')}.json`);
                return false;
            }
        } else {
            console.warn('jsPDF non disponible, export JSON de fallback');
            this.fallbackExport(data, `${title.replace(/\s+/g, '_')}.json`);
            return false;
        }
    },

    // Export de fallback en JSON
    fallbackExport(data, filename = 'export.json') {
        try {
            const jsonData = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonData], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification(`Export JSON de fallback: ${filename}`, 'info');
            return true;
        } catch (error) {
            console.error('Erreur export fallback:', error);
            showNotification('Erreur lors de l\'export', 'error');
            return false;
        }
    },

    // Export CSV simple
    exportToCSV(data, filename = 'export.csv') {
        try {
            if (!Array.isArray(data) || data.length === 0) {
                throw new Error('Données invalides pour l\'export CSV');
            }

            // Obtenir les en-têtes
            const headers = Object.keys(data[0]);

            // Créer le contenu CSV
            let csvContent = headers.join(',') + '\n';

            data.forEach(row => {
                const values = headers.map(header => {
                    const value = row[header] || '';
                    // Échapper les guillemets et virgules
                    return typeof value === 'string' && (value.includes(',') || value.includes('"'))
                        ? `"${value.replace(/"/g, '""')}"`
                        : value;
                });
                csvContent += values.join(',') + '\n';
            });

            // Télécharger le fichier
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showNotification(`Export CSV réussi: ${filename}`, 'success');
            return true;
        } catch (error) {
            console.error('Erreur export CSV:', error);
            showNotification('Erreur lors de l\'export CSV', 'error');
            return false;
        }
    }
};

// Fonction pour afficher le statut Supabase
function showSupabaseStatus(status, message) {
    // Créer ou mettre à jour l'indicateur de statut
    let statusIndicator = document.getElementById('supabaseStatus');

    if (!statusIndicator) {
        statusIndicator = document.createElement('div');
        statusIndicator.id = 'supabaseStatus';
        statusIndicator.className = 'supabase-status';

        // Ajouter au header ou à un endroit visible
        const header = document.querySelector('.header') || document.querySelector('.container');
        if (header) {
            header.appendChild(statusIndicator);
        }
    }

    // Mettre à jour le contenu et la classe
    statusIndicator.className = `supabase-status ${status}`;
    statusIndicator.innerHTML = `
        <i class="fas fa-database"></i>
        <span>${message}</span>
        ${status === 'connected' ? '<i class="fas fa-check-circle"></i>' :
          status === 'disconnected' ? '<i class="fas fa-exclamation-triangle"></i>' :
          '<i class="fas fa-times-circle"></i>'}
    `;

    // Ajouter un bouton de synchronisation si connecté
    if (status === 'connected') {
        const syncBtn = document.createElement('button');
        syncBtn.className = 'sync-btn';
        syncBtn.innerHTML = '<i class="fas fa-sync"></i>';
        syncBtn.title = 'Synchroniser avec Supabase';
        syncBtn.onclick = () => syncWithSupabase();
        statusIndicator.appendChild(syncBtn);
    }
}

// Fonction de synchronisation manuelle
async function syncWithSupabase() {
    try {
        const syncBtn = document.querySelector('.sync-btn');
        if (syncBtn) {
            syncBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            syncBtn.disabled = true;
        }

        console.log('🔄 Démarrage de la synchronisation manuelle...');
        const dbManager = getDatabaseManager();

        if (dbManager) {
            const result = await dbManager.syncAll();

            if (result.success) {
                console.log('✅ Synchronisation réussie:', result.results);
                showNotification('Synchronisation réussie', 'success');
            } else {
                console.warn('⚠️ Erreur de synchronisation:', result.error);
                showNotification('Erreur de synchronisation', 'error');
            }
        } else {
            console.error('❌ DatabaseManager non disponible pour synchronisation');
            showNotification('DatabaseManager non disponible', 'error');
        }

    } catch (error) {
        console.error('❌ Erreur lors de la synchronisation:', error);
        showNotification('Erreur de synchronisation', 'error');
    } finally {
        const syncBtn = document.querySelector('.sync-btn');
        if (syncBtn) {
            syncBtn.innerHTML = '<i class="fas fa-sync"></i>';
            syncBtn.disabled = false;
        }
    }
}

// Fonction pour afficher des notifications
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
        <span>${message}</span>
    `;

    // Ajouter au body
    document.body.appendChild(notification);

    // Animation d'entrée
    setTimeout(() => notification.classList.add('show'), 100);

    // Suppression automatique
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Chargement des données avec gestion d'erreur améliorée
async function chargerDonnees() {
    try {
        console.log('📥 Chargement des données...');

        // Charger les données via DatabaseManager (gère automatiquement Supabase/localStorage)
        const [messagesData, commandesData, pvsData] = await Promise.all([
            DatabaseManager.load('messages'),
            DatabaseManager.load('commands'),
            DatabaseManager.load('pv')
        ]);

        // Assigner les données chargées
        messages = messagesData || [];
        commandes = commandesData || [];
        pvs = pvsData || [];

        console.log(`✅ Données chargées: ${messages.length} messages, ${commandes.length} commandes, ${pvs.length} PV`);
        
        // Afficher les données chargées
        afficherMessages();
        if (canManageCommands()) {
            afficherListeCommandes();
        }
        if (canManagePV()) {
            if (isDepot()) {
                loadProductsForDepot();
            } else {
                displayAdminPVs();
            }
        }
        
        console.log(`Données chargées: ${messages.length} messages, ${commandes.length} commandes, ${pvs.length} PV`);
        
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        
        // Fallback vers des données vides
        messages = [];
        commandes = [];
        pvs = [];
        
        // Afficher un message d'erreur à l'utilisateur
        const errorMsg = document.createElement('div');
        errorMsg.className = 'status-message error';
        errorMsg.textContent = 'Erreur lors du chargement des données. Mode hors ligne activé.';
        document.querySelector('.container').prepend(errorMsg);
        
        setTimeout(() => errorMsg.remove(), 5000);
    }
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 Initialisation de l\'application...');

    // Attendre le chargement des bibliothèques
    console.log('⏳ Attente du chargement des bibliothèques...');
    const libraries = await waitForLibraries(10000);

    // Afficher le statut final des bibliothèques
    const loadedCount = Object.values(libraries).filter(Boolean).length;
    const totalCount = Object.keys(libraries).length;
    console.log(`📊 Bibliothèques finales: ${loadedCount}/${totalCount} chargées`);

    // Initialiser Supabase en premier
    try {
        if (typeof initializeSupabase === 'function') {
            console.log('🔗 Initialisation de Supabase...');
            const supabaseResult = await initializeSupabase();

            if (supabaseResult.available) {
                console.log('✅ Supabase initialisé avec succès');
                showSupabaseStatus('connected', 'Connecté à Supabase');
            } else {
                console.log('⚠️ Supabase non disponible, mode local activé');
                showSupabaseStatus('disconnected', 'Mode local');
            }
        } else {
            console.warn('⚠️ Configuration Supabase non trouvée');
            showSupabaseStatus('error', 'Configuration manquante');
        }
    } catch (error) {
        console.error('❌ Erreur initialisation Supabase:', error);
        showSupabaseStatus('error', 'Erreur de connexion');
    }

    // Vérifier DatabaseManager
    if (checkDatabaseManager()) {
        console.log('✅ DatabaseManager initialisé et disponible');
    } else {
        console.warn('⚠️ DatabaseManager non disponible - Mode local uniquement');
    }

    // Vérifier si un utilisateur est déjà connecté
    const savedUser = localStorage.getItem(USER_STORAGE_KEY);
    if (savedUser) {
        try {
            utilisateurCourant = JSON.parse(savedUser);
            if (utilisateurCourant && utilisateurs[utilisateurCourant.username]) {
                console.log('👤 Utilisateur sauvegardé trouvé:', utilisateurCourant.username);

                setupApplicationUI();
                await chargerDonnees();
                document.getElementById('login').style.display = 'none';
                document.getElementById('mainApp').style.display = 'block';
                updateStatus();
                updateMembersList();
            }
        } catch (error) {
            console.error('❌ Erreur lors du chargement de l\'utilisateur sauvegardé:', error);
            localStorage.removeItem(USER_STORAGE_KEY);
        }
    }
    
    // Attacher les écouteurs d'événements
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                login();
            }
        });
    }
    
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                envoyerMessage();
            }
        });
    }
    
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            handleFileSelection(e);
        });
    }
    
    console.log('Application initialisée');
});

// Fonction pour gérer la sélection de fichiers
function handleFileSelection(event) {
    const files = Array.from(event.target.files);
    fichiersSelectionnes = files;
    
    const preview = document.getElementById('filePreview');
    if (!preview) return;
    
    preview.innerHTML = '';
    
    files.forEach(file => {
        // Vérifier la taille du fichier
        if (file.size > APP_CONFIG.limits.maxFileSize) {
            alert(`Le fichier ${file.name} est trop volumineux (max ${APP_CONFIG.limits.maxFileSize / 1024 / 1024}MB)`);
            return;
        }
        
        const fileItem = document.createElement('div');
        fileItem.className = 'file-preview-item';
        fileItem.textContent = file.name;
        preview.appendChild(fileItem);
    });
    
    if (files.length > 0) {
        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}

// Fonctions de chat et messagerie
async function envoyerMessage() {
    const messageInput = document.getElementById('messageInput');
    const destinataire = document.getElementById('destinataire').value;
    const contenu = messageInput.value.trim();

    if (!contenu && fichiersSelectionnes.length === 0) {
        alert('Veuillez saisir un message ou sélectionner un fichier');
        return;
    }

    if (contenu.length > APP_CONFIG.limits.maxMessageLength) {
        alert(`Message trop long (max ${APP_CONFIG.limits.maxMessageLength} caractères)`);
        return;
    }

    const message = {
        id: generateId(),
        expediteur: utilisateurCourant.username,
        destinataire: destinataire,
        contenu: contenu,
        timestamp: new Date().toISOString(),
        fichiers: fichiersSelectionnes.map(f => ({
            nom: f.name,
            taille: f.size,
            type: f.type
        })),
        lu: false
    };

    // Sauvegarder le message
    try {
        const dbManager = getDatabaseManager();
        if (dbManager) {
            const saved = await dbManager.save('messages', {
                expediteur: message.expediteur,
                destinataire: message.destinataire,
                contenu: message.contenu,
                timestamp: message.timestamp,
                fichier_nom: fichiersSelectionnes.length > 0 ? fichiersSelectionnes[0].name : null,
                fichier_data: fichiersSelectionnes.length > 0 ? fichiersSelectionnes[0].data : null,
                fichier_type: fichiersSelectionnes.length > 0 ? fichiersSelectionnes[0].type : null,
                lu: false
            });

            if (saved) {
                messages.push(message);
                console.log('✅ Message sauvegardé avec succès');
                showNotification('Message envoyé avec succès', 'success');
            } else {
                console.warn('⚠️ Erreur lors de la sauvegarde du message');
                messages.push(message); // Ajouter quand même le message localement
                showNotification('Message envoyé (mode local)', 'warning');
            }
        } else {
            console.error('❌ DatabaseManager non disponible');
            messages.push(message); // Ajouter quand même le message localement
            showNotification('Message envoyé (mode local)', 'warning');
        }
    } catch (error) {
        console.error('❌ Erreur lors de l\'envoi du message:', error);
        messages.push(message); // Ajouter quand même le message localement
        showNotification('Message envoyé (mode local)', 'warning');
    }

    // Réinitialiser l'interface
    messageInput.value = '';
    fichiersSelectionnes = [];
    const preview = document.getElementById('filePreview');
    if (preview) preview.style.display = 'none';
    const fileInput = document.getElementById('fileInput');
    if (fileInput) fileInput.value = '';

    // Rafraîchir l'affichage
    afficherMessages();
}

function envoyerMessageAutomatique(contenu) {
    const message = {
        id: generateId(),
        expediteur: 'Système',
        destinataire: 'Tous',
        contenu: contenu,
        timestamp: new Date().toISOString(),
        fichiers: [],
        lu: false,
        type: 'system'
    };

    messages.push(message);

    // Sauvegarder en arrière-plan
    const dbManager = getDatabaseManager();
    if (dbManager) {
        dbManager.save('messages', {
            expediteur: message.expediteur,
            destinataire: message.destinataire,
            contenu: message.contenu,
            timestamp: message.timestamp,
            lu: false
        }).catch(error => {
            console.error('❌ Erreur lors de la sauvegarde du message système:', error);
        });
    } else {
        console.warn('⚠️ DatabaseManager non disponible pour message système');
    }

    afficherMessages();
}

function afficherMessages() {
    const messagesContainer = document.getElementById('messages');
    if (!messagesContainer) return;

    messagesContainer.innerHTML = '';

    // Filtrer les messages selon le destinataire et le rôle
    const messagesFiltres = messages.filter(message => {
        if (message.destinataire === 'Tous') return true;
        if (message.expediteur === utilisateurCourant.username) return true;
        if (message.destinataire === utilisateurCourant.username) return true;
        return false;
    });

    // Trier par timestamp
    messagesFiltres.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    messagesFiltres.forEach(message => {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';

        if (message.expediteur === utilisateurCourant.username) {
            messageDiv.classList.add('own');
        }

        if (message.type === 'system') {
            messageDiv.classList.add('system');
        }

        const couleurExpediteur = utilisateurs[message.expediteur]?.couleur ||
                                 ConfigUtils.getRoleColor('user');

        let fichierHtml = '';
        if (message.fichiers && message.fichiers.length > 0) {
            fichierHtml = message.fichiers.map(f =>
                `<div class="message-file">📎 ${f.nom}</div>`
            ).join('');
        }

        messageDiv.innerHTML = `
            <div class="message-header" style="color: ${couleurExpediteur}">
                ${message.expediteur}
                <span class="message-time">${formatDate(message.timestamp)}</span>
                ${isAdmin() && message.expediteur !== 'Système' ?
                    `<button onclick="supprimerMessage('${message.id}')" class="action-button danger" style="margin-left: 10px;">Supprimer</button>` :
                    ''}
            </div>
            <div class="message-content">${message.contenu}</div>
            ${fichierHtml}
        `;

        messagesContainer.appendChild(messageDiv);
    });

    // Faire défiler vers le bas
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function chargerDestinataires() {
    const select = document.getElementById('destinataire');
    if (!select) return;

    select.innerHTML = '<option value="Tous">Tous</option>';

    Object.keys(utilisateurs).forEach(username => {
        if (username !== utilisateurCourant.username && utilisateurs[username].actif) {
            const option = document.createElement('option');
            option.value = username;
            option.textContent = username;
            select.appendChild(option);
        }
    });
}

function updateStatus() {
    const statusText = document.getElementById('statusText');
    const statusButton = document.querySelector('.status-button');

    if (!statusText || !statusButton) return;

    if (utilisateurCourant) {
        statusText.textContent = 'En ligne';
        statusButton.style.backgroundColor = '#28a745';
    } else {
        statusText.textContent = 'Hors ligne';
        statusButton.style.backgroundColor = '#dc3545';
    }
}

function updateMembersList() {
    const container = document.getElementById('membersContainer');
    if (!container) return;

    container.innerHTML = '';

    // Simuler les membres connectés (en production, cela viendrait de Supabase Realtime)
    const membresActifs = Object.keys(utilisateurs).filter(username =>
        utilisateurs[username].actif && username !== utilisateurCourant?.username
    );

    membresActifs.forEach(username => {
        const memberDiv = document.createElement('div');
        memberDiv.className = 'member-item';

        const user = utilisateurs[username];
        memberDiv.innerHTML = `
            <div class="member-status" style="background-color: ${user.couleur}"></div>
            <span class="member-name">${username}</span>
            <span class="member-role">${APP_CONFIG.roles[user.role]?.name || user.role}</span>
        `;

        container.appendChild(memberDiv);
    });
}

// Fonction admin pour supprimer un message
async function supprimerMessage(messageId) {
    if (!isAdmin()) {
        alert(ConfigUtils.getMessage('errors', 'accessDenied'));
        return;
    }

    if (confirm('Êtes-vous sûr de vouloir supprimer ce message ?')) {
        messages = messages.filter(m => m.id !== messageId);

        try {
            // En production, supprimer de Supabase
            const dbManager = getDatabaseManager();
            if (dbManager) {
                await dbManager.delete('messages', messageId);
                console.log('✅ Message supprimé avec succès');
            } else {
                console.warn('⚠️ DatabaseManager non disponible pour suppression');
            }
        } catch (error) {
            console.error('❌ Erreur lors de la suppression du message:', error);
        }

        afficherMessages();
    }
}

// Fonction de changement de mot de passe (simulée)
function changerMotDePasse() {
    const nouveauMotDePasse = prompt('Entrez votre nouveau mot de passe:');
    if (nouveauMotDePasse && nouveauMotDePasse.length >= APP_CONFIG.security.passwordMinLength) {
        // En production, cela se ferait via Supabase Auth
        utilisateurs[utilisateurCourant.username].password = nouveauMotDePasse;
        alert(ConfigUtils.getMessage('success', 'dataSaved'));

        // Envoyer notification
        envoyerMessageAutomatique(`${utilisateurCourant.username} a changé son mot de passe`);
    } else {
        alert(`Le mot de passe doit contenir au moins ${APP_CONFIG.security.passwordMinLength} caractères`);
    }
}

// Fonctions du convertisseur de devises
function afficherConvertisseur() {
    hideAllModuleZones();
    document.getElementById('convertisseur').style.display = 'block';
    afficherTauxDeChange();
}

async function convertirDevise() {
    const montant = parseFloat(document.getElementById('montant').value);
    const deviseSource = document.getElementById('deviseSource').value;
    const deviseCible = document.getElementById('deviseCible').value;
    const resultatDiv = document.getElementById('resultatConversion');

    if (!montant || montant <= 0) {
        resultatDiv.innerHTML = '<div class="status-message error">⚠️ Veuillez entrer un montant valide</div>';
        return;
    }

    if (deviseSource === deviseCible) {
        const symbol = APP_CONFIG.currencies.symbols[deviseSource] || deviseSource;
        resultatDiv.innerHTML = `
            <div class="status-message info">
                <strong>${montant.toFixed(2)} ${symbol} = ${montant.toFixed(2)} ${symbol}</strong>
                <br><small>Même devise sélectionnée</small>
            </div>
        `;
        return;
    }

    try {
        const taux = ConfigUtils.getExchangeRate(deviseSource, deviseCible);
        if (taux) {
            const resultat = montant * taux;
            const symbolSource = APP_CONFIG.currencies.symbols[deviseSource] || deviseSource;
            const symbolCible = APP_CONFIG.currencies.symbols[deviseCible] || deviseCible;
            const nameSource = APP_CONFIG.currencies.names[deviseSource] || deviseSource;
            const nameCible = APP_CONFIG.currencies.names[deviseCible] || deviseCible;

            resultatDiv.innerHTML = `
                <div class="status-message success">
                    <div class="conversion-main">
                        <strong>${montant.toFixed(2)} ${symbolSource} = ${resultat.toFixed(2)} ${symbolCible}</strong>
                    </div>
                    <div class="conversion-details">
                        <small>${nameSource} → ${nameCible}</small><br>
                        <small>Taux: 1 ${deviseSource} = ${taux.toFixed(4)} ${deviseCible}</small><br>
                        <small>Taux inverse: 1 ${deviseCible} = ${(1/taux).toFixed(4)} ${deviseSource}</small>
                    </div>
                </div>
            `;
        } else {
            resultatDiv.innerHTML = '<div class="status-message error">❌ Conversion non disponible pour cette paire de devises</div>';
        }
    } catch (error) {
        resultatDiv.innerHTML = '<div class="status-message error">❌ Erreur lors de la conversion</div>';
        console.error('Erreur conversion:', error);
    }
}

function inverserDevises() {
    const deviseSource = document.getElementById('deviseSource');
    const deviseCible = document.getElementById('deviseCible');

    const temp = deviseSource.value;
    deviseSource.value = deviseCible.value;
    deviseCible.value = temp;

    // Reconvertir automatiquement si un montant est saisi
    const montant = document.getElementById('montant').value;
    if (montant && parseFloat(montant) > 0) {
        convertirDevise();
    }
}

function afficherTauxDeChange() {
    const ratesGrid = document.getElementById('ratesGrid');
    if (!ratesGrid) return;

    const currencies = APP_CONFIG.currencies.supported;
    const baseCurrency = 'TND'; // Dinar tunisien comme référence

    let html = '<div class="rates-list">';

    currencies.forEach(currency => {
        if (currency !== baseCurrency) {
            const taux = ConfigUtils.getExchangeRate(baseCurrency, currency);
            const symbol = APP_CONFIG.currencies.symbols[currency] || currency;
            const name = APP_CONFIG.currencies.names[currency] || currency;

            if (taux) {
                html += `
                    <div class="rate-item">
                        <span class="rate-currency">${symbol} ${currency}</span>
                        <span class="rate-value">${taux.toFixed(4)}</span>
                        <span class="rate-name">${name}</span>
                    </div>
                `;
            }
        }
    });

    html += '</div>';
    html += '<p class="rates-note"><small>💡 Taux indicatifs - 1 د.ت TND = X devise</small></p>';

    ratesGrid.innerHTML = html;
}

// Fonctions d'analyse des messages
function toggleAnalyseZone() {
    hideAllModuleZones();
    document.getElementById('analyseZone').style.display = 'block';
}

function lancerAnalyseHistorique() {
    const resultatDiv = document.getElementById('resultatsAnalyse');
    resultatDiv.innerHTML = '<p>Analyse en cours...</p>';

    setTimeout(() => {
        const analyse = analyserProbleme();
        const recommandation = genererRecommandation(analyse);

        resultatDiv.innerHTML = `
            <div class="status-message info">
                <h3>Résultats de l'analyse</h3>
                <h4>Problèmes détectés:</h4>
                <ul>
                    ${analyse.problemes.map(p => `<li>${p}</li>`).join('')}
                </ul>
                <h4>Statistiques:</h4>
                <ul>
                    <li>Total messages: ${analyse.stats.totalMessages}</li>
                    <li>Messages système: ${analyse.stats.messagesSysteme}</li>
                    <li>Utilisateurs actifs: ${analyse.stats.utilisateursActifs}</li>
                    <li>Messages avec fichiers: ${analyse.stats.messagesAvecFichiers}</li>
                </ul>
                <h4>Recommandations:</h4>
                <ul>
                    ${recommandation.map(r => `<li>${r}</li>`).join('')}
                </ul>
            </div>
        `;
    }, 2000);
}

function analyserProbleme() {
    const problemes = [];
    const stats = {
        totalMessages: messages.length,
        messagesSysteme: messages.filter(m => m.type === 'system').length,
        utilisateursActifs: new Set(messages.map(m => m.expediteur)).size,
        messagesAvecFichiers: messages.filter(m => m.fichiers && m.fichiers.length > 0).length
    };

    // Analyser les problèmes potentiels
    if (stats.totalMessages === 0) {
        problemes.push('Aucun message dans l\'historique');
    }

    if (stats.utilisateursActifs < 2) {
        problemes.push('Peu d\'interaction entre utilisateurs');
    }

    const messagesRecents = messages.filter(m => {
        const messageDate = new Date(m.timestamp);
        const maintenant = new Date();
        const diffHeures = (maintenant - messageDate) / (1000 * 60 * 60);
        return diffHeures <= 24;
    });

    if (messagesRecents.length === 0) {
        problemes.push('Aucun message dans les dernières 24 heures');
    }

    // Vérifier les messages non lus
    const messagesNonLus = messages.filter(m => !m.lu && m.expediteur !== utilisateurCourant.username);
    if (messagesNonLus.length > 10) {
        problemes.push(`${messagesNonLus.length} messages non lus`);
    }

    if (problemes.length === 0) {
        problemes.push('Aucun problème détecté');
    }

    return { problemes, stats };
}

function genererRecommandation(analyse) {
    const recommandations = [];

    if (analyse.stats.totalMessages < 10) {
        recommandations.push('Encourager plus de communication entre les équipes');
    }

    if (analyse.stats.messagesAvecFichiers === 0) {
        recommandations.push('Utiliser la fonction de partage de fichiers pour améliorer la collaboration');
    }

    if (analyse.stats.utilisateursActifs < Object.keys(utilisateurs).length / 2) {
        recommandations.push('Sensibiliser les utilisateurs à l\'utilisation de la messagerie');
    }

    recommandations.push('Effectuer des analyses régulières pour optimiser la communication');
    recommandations.push('Former les utilisateurs aux bonnes pratiques de messagerie');

    return recommandations;
}
