<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام الإدارة الداخلية</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            direction: rtl;
        }
        
        .auth-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 900px;
            min-height: 600px;
            display: flex;
        }
        
        .auth-sidebar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .auth-sidebar h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .auth-sidebar p {
            font-size: 1.1em;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .auth-main {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .auth-header h2 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .auth-header p {
            color: #666;
            font-size: 1em;
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .auth-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
            color: #666;
        }
        
        .auth-tab.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }
        
        .auth-form {
            display: none;
        }
        
        .auth-form.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            background: white;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
        }
        
        .form-row .form-group {
            flex: 1;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }
        
        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .forgot-password a:hover {
            text-decoration: underline;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .loading.show {
            display: block;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .password-strength {
            margin-top: 5px;
            font-size: 12px;
        }
        
        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #28a745; }
        
        @media (max-width: 768px) {
            .auth-container {
                flex-direction: column;
                margin: 20px;
                max-width: none;
            }
            
            .auth-sidebar {
                padding: 30px 20px;
            }
            
            .auth-main {
                padding: 30px 20px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-sidebar">
            <h1>🏢 نظام الإدارة الداخلية</h1>
            <p>منصة شاملة لإدارة الرسائل والطلبات والمحاضر الداخلية بكفاءة وأمان عالي</p>
        </div>
        
        <div class="auth-main">
            <div class="auth-header">
                <h2>مرحباً بك</h2>
                <p>سجل دخولك للوصول إلى النظام</p>
            </div>
            
            <div class="auth-tabs">
                <div class="auth-tab active" onclick="switchTab('login')">تسجيل الدخول</div>
                <div class="auth-tab" onclick="switchTab('signup')">إنشاء حساب</div>
            </div>
            
            <!-- رسائل التنبيه -->
            <div id="alertContainer"></div>
            
            <!-- شاشة التحميل -->
            <div class="loading" id="loadingScreen">
                <div class="spinner"></div>
                <p>جاري المعالجة...</p>
            </div>
            
            <!-- نموذج تسجيل الدخول -->
            <form class="auth-form active" id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">البريد الإلكتروني</label>
                    <input type="email" id="loginEmail" name="email" required 
                           placeholder="أدخل بريدك الإلكتروني">
                </div>
                
                <div class="form-group">
                    <label for="loginPassword">كلمة المرور</label>
                    <input type="password" id="loginPassword" name="password" required 
                           placeholder="أدخل كلمة المرور">
                </div>
                
                <button type="submit" class="btn" id="loginBtn">
                    تسجيل الدخول
                </button>
                
                <div class="forgot-password">
                    <a href="#" onclick="showForgotPassword()">نسيت كلمة المرور؟</a>
                </div>
            </form>
            
            <!-- نموذج إنشاء حساب -->
            <form class="auth-form" id="signupForm">
                <div class="form-row">
                    <div class="form-group">
                        <label for="signupFirstName">الاسم الأول</label>
                        <input type="text" id="signupFirstName" name="prenom" required 
                               placeholder="الاسم الأول">
                    </div>
                    
                    <div class="form-group">
                        <label for="signupLastName">الاسم الأخير</label>
                        <input type="text" id="signupLastName" name="nom" required 
                               placeholder="الاسم الأخير">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="signupEmail">البريد الإلكتروني</label>
                    <input type="email" id="signupEmail" name="email" required 
                           placeholder="أدخل بريدك الإلكتروني">
                </div>
                
                <div class="form-group">
                    <label for="signupPassword">كلمة المرور</label>
                    <input type="password" id="signupPassword" name="password" required 
                           placeholder="أدخل كلمة مرور قوية" oninput="checkPasswordStrength(this.value)">
                    <div class="password-strength" id="passwordStrength"></div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="signupRole">الدور</label>
                        <select id="signupRole" name="role" required>
                            <option value="">اختر الدور</option>
                            <option value="user">مستخدم</option>
                            <option value="chef_service">رئيس قسم</option>
                            <option value="responsable_hygiene">مسؤول النظافة</option>
                            <option value="admin">مدير</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="signupService">القسم</label>
                        <input type="text" id="signupService" name="service" 
                               placeholder="اسم القسم (اختياري)">
                    </div>
                </div>
                
                <button type="submit" class="btn" id="signupBtn">
                    إنشاء حساب
                </button>
            </form>
            
            <!-- نموذج استعادة كلمة المرور -->
            <form class="auth-form" id="forgotForm" style="display: none;">
                <div class="form-group">
                    <label for="forgotEmail">البريد الإلكتروني</label>
                    <input type="email" id="forgotEmail" name="email" required 
                           placeholder="أدخل بريدك الإلكتروني">
                </div>
                
                <button type="submit" class="btn" id="forgotBtn">
                    إرسال رابط الاستعادة
                </button>
                
                <button type="button" class="btn btn-secondary" onclick="hideForgotPassword()">
                    العودة لتسجيل الدخول
                </button>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    
    <script>
        // متغيرات عامة
        let authSystem = null;
        
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 تهيئة صفحة تسجيل الدخول...');
            
            try {
                // تهيئة Supabase
                await initializeSupabase();
                
                // تهيئة نظام المصادقة
                if (typeof SupabaseAuth !== 'undefined' && window.supabaseClient) {
                    authSystem = new SupabaseAuth(SUPABASE_CONFIG, window.supabaseClient);
                    await authSystem.initialize();
                    
                    // إعداد callbacks
                    setupAuthCallbacks();
                    
                    console.log('✅ نظام المصادقة جاهز');
                } else {
                    showAlert('خطأ في تهيئة النظام', 'error');
                }
                
            } catch (error) {
                console.error('❌ خطأ في التهيئة:', error);
                showAlert('خطأ في تهيئة النظام: ' + error.message, 'error');
            }
        });
        
        // إعداد callbacks المصادقة
        function setupAuthCallbacks() {
            authSystem.on('onSignIn', (session) => {
                showAlert('تم تسجيل الدخول بنجاح!', 'success');
                setTimeout(() => {
                    window.location.href = 'index.html';
                }, 1500);
            });
            
            authSystem.on('onSignUp', (data) => {
                if (data.needsConfirmation) {
                    showAlert('تم إنشاء الحساب! يرجى تأكيد بريدك الإلكتروني.', 'info');
                } else {
                    showAlert('تم إنشاء الحساب وتسجيل الدخول بنجاح!', 'success');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                }
            });
            
            authSystem.on('onPasswordReset', (data) => {
                showAlert('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.', 'success');
                hideForgotPassword();
            });
            
            authSystem.on('onError', (error) => {
                showAlert(error.message || 'حدث خطأ غير متوقع', 'error');
                hideLoading();
            });
        }
        
        // تبديل التبويبات
        function switchTab(tab) {
            // إزالة الفئة النشطة من جميع التبويبات
            document.querySelectorAll('.auth-tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.auth-form').forEach(f => f.classList.remove('active'));
            
            // إضافة الفئة النشطة للتبويب المحدد
            event.target.classList.add('active');
            
            if (tab === 'login') {
                document.getElementById('loginForm').classList.add('active');
            } else if (tab === 'signup') {
                document.getElementById('signupForm').classList.add('active');
            }
            
            // مسح الرسائل
            clearAlerts();
        }
        
        // إظهار نموذج استعادة كلمة المرور
        function showForgotPassword() {
            document.querySelectorAll('.auth-form').forEach(f => f.style.display = 'none');
            document.getElementById('forgotForm').style.display = 'block';
            clearAlerts();
        }
        
        // إخفاء نموذج استعادة كلمة المرور
        function hideForgotPassword() {
            document.getElementById('forgotForm').style.display = 'none';
            document.getElementById('loginForm').classList.add('active');
            document.getElementById('loginForm').style.display = 'block';
            clearAlerts();
        }
        
        // معالج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!authSystem) {
                showAlert('نظام المصادقة غير جاهز', 'error');
                return;
            }
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            if (!email || !password) {
                showAlert('يرجى ملء جميع الحقول', 'error');
                return;
            }
            
            showLoading();
            
            try {
                const result = await authSystem.signInWithEmail(email, password);
                
                if (!result.success) {
                    showAlert(result.error, 'error');
                }
                
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showAlert('حدث خطأ غير متوقع', 'error');
            } finally {
                hideLoading();
            }
        });
        
        // معالج إنشاء حساب
        document.getElementById('signupForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!authSystem) {
                showAlert('نظام المصادقة غير جاهز', 'error');
                return;
            }
            
            const formData = new FormData(this);
            const userData = {
                prenom: formData.get('prenom'),
                nom: formData.get('nom'),
                role: formData.get('role'),
                service: formData.get('service')
            };
            
            const email = formData.get('email');
            const password = formData.get('password');
            
            if (!email || !password || !userData.prenom || !userData.nom || !userData.role) {
                showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }
            
            showLoading();
            
            try {
                const result = await authSystem.signUpWithEmail(email, password, userData);
                
                if (!result.success) {
                    showAlert(result.error, 'error');
                }
                
            } catch (error) {
                console.error('خطأ في إنشاء الحساب:', error);
                showAlert('حدث خطأ غير متوقع', 'error');
            } finally {
                hideLoading();
            }
        });
        
        // معالج استعادة كلمة المرور
        document.getElementById('forgotForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!authSystem) {
                showAlert('نظام المصادقة غير جاهز', 'error');
                return;
            }
            
            const email = document.getElementById('forgotEmail').value;
            
            if (!email) {
                showAlert('يرجى إدخال البريد الإلكتروني', 'error');
                return;
            }
            
            showLoading();
            
            try {
                const result = await authSystem.resetPassword(email);
                
                if (!result.success) {
                    showAlert(result.error, 'error');
                }
                
            } catch (error) {
                console.error('خطأ في استعادة كلمة المرور:', error);
                showAlert('حدث خطأ غير متوقع', 'error');
            } finally {
                hideLoading();
            }
        });
        
        // فحص قوة كلمة المرور
        function checkPasswordStrength(password) {
            const strengthElement = document.getElementById('passwordStrength');
            
            if (password.length === 0) {
                strengthElement.textContent = '';
                return;
            }
            
            let strength = 0;
            let feedback = [];
            
            // طول كلمة المرور
            if (password.length >= 8) strength++;
            else feedback.push('8 أحرف على الأقل');
            
            // أحرف كبيرة وصغيرة
            if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
            else feedback.push('أحرف كبيرة وصغيرة');
            
            // أرقام
            if (/\d/.test(password)) strength++;
            else feedback.push('أرقام');
            
            // رموز خاصة
            if (/[^a-zA-Z\d]/.test(password)) strength++;
            else feedback.push('رموز خاصة');
            
            // عرض النتيجة
            if (strength < 2) {
                strengthElement.className = 'password-strength strength-weak';
                strengthElement.textContent = 'ضعيفة - يحتاج: ' + feedback.join(', ');
            } else if (strength < 3) {
                strengthElement.className = 'password-strength strength-medium';
                strengthElement.textContent = 'متوسطة - يحتاج: ' + feedback.join(', ');
            } else {
                strengthElement.className = 'password-strength strength-strong';
                strengthElement.textContent = 'قوية ✓';
            }
        }
        
        // إظهار رسالة تنبيه
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            
            container.innerHTML = '';
            container.appendChild(alert);
            
            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
        
        // مسح الرسائل
        function clearAlerts() {
            document.getElementById('alertContainer').innerHTML = '';
        }
        
        // إظهار شاشة التحميل
        function showLoading() {
            document.getElementById('loadingScreen').classList.add('show');
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
        }
        
        // إخفاء شاشة التحميل
        function hideLoading() {
            document.getElementById('loadingScreen').classList.remove('show');
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
        }
    </script>
</body>
</html>
