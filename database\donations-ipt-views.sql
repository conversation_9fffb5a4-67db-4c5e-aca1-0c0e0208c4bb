-- =====================================================
-- VUES OPTIMISÉES - SYSTÈME DONS IPT
-- =====================================================

-- =====================================================
-- VUE COMPLÈTE DES DONS AVEC WORKFLOW
-- =====================================================

CREATE OR REPLACE VIEW donations_ipt_complete AS
SELECT 
    d.id,
    d.numero_don,
    d.type_don,
    
    -- Informations demandeur
    d.demandeur_nom,
    d.demandeur_prenom,
    d.demandeur_email,
    d.demandeur_service,
    d.demandeur_laboratoire,
    d.demandeur_telephone,
    
    -- Informations don
    d.motif_don,
    d.lieu_affectation,
    d.description_don,
    d.specifications_techniques,
    
    -- Donateur
    d.donateur_nom,
    d.donateur_contact,
    d.donateur_adresse,
    
    -- Valeur
    d.valeur_estimee,
    d.devise,
    d.quantite,
    d.unite,
    
    -- État et workflow
    d.statut,
    d.etape_actuelle,
    d.priorite,
    
    -- Dates
    d.date_demande,
    d.date_livraison_prevue,
    d.date_livraison_reelle,
    d.date_reception,
    d.date_affectation,
    
    -- Validations
    d.valide_appro_par,
    d.valide_appro_date,
    d.valide_appro_commentaires,
    d.avis_technique_par,
    d.avis_technique_date,
    d.avis_technique_commentaires,
    d.avis_technique_favorable,
    d.valide_dg_par,
    d.valide_dg_date,
    d.valide_dg_commentaires,
    d.valide_sd_achats_par,
    d.valide_sd_achats_date,
    d.valide_sd_achats_commentaires,
    
    -- Décision MS
    d.decision_ms,
    d.decision_ms_date,
    d.decision_ms_commentaires,
    d.decision_ms_reference,
    
    -- Réception et inventaire
    d.receptionne_par,
    d.receptionne_date,
    d.numero_inventaire,
    d.rve_responsable,
    d.service_affecte,
    d.laboratoire_affecte,
    d.responsable_affectation,
    
    -- Informations inventaire (si disponible)
    inv.code_barres,
    inv.etat_physique,
    inv.etat_fonctionnel,
    inv.emplacement_precis,
    inv.marque,
    inv.modele,
    inv.numero_serie,
    inv.annee_fabrication,
    
    -- Workflow actuel
    wf_current.nom_etape AS etape_actuelle_nom,
    wf_current.description_etape AS etape_actuelle_description,
    wf_current.role_responsable AS etape_responsable_role,
    wf_current.statut_etape AS etape_statut,
    wf_current.date_debut AS etape_date_debut,
    
    -- Calculs
    CASE 
        WHEN d.date_livraison_prevue IS NOT NULL AND d.date_livraison_prevue < CURRENT_DATE 
             AND d.statut NOT IN ('archive', 'refuse', 'annule', 'receptionne', 'inventorie', 'affecte')
        THEN true 
        ELSE false 
    END AS en_retard,
    
    CASE 
        WHEN d.created_at IS NOT NULL 
        THEN EXTRACT(DAYS FROM (CURRENT_DATE - d.created_at::date))
        ELSE 0 
    END AS jours_depuis_creation,
    
    -- Métadonnées
    d.created_by,
    d.created_at,
    d.updated_by,
    d.updated_at
    
FROM gestion_donations_ipt d
LEFT JOIN gestion_donation_inventaire inv ON d.id = inv.donation_id
LEFT JOIN gestion_donation_workflow wf_current ON d.id = wf_current.donation_id 
    AND wf_current.numero_etape = d.etape_actuelle
WHERE d.deleted_at IS NULL;

-- =====================================================
-- VUE POUR LA TABLE INTERACTIVE
-- =====================================================

CREATE OR REPLACE VIEW donations_ipt_table_view AS
SELECT 
    d.id,
    d.numero_don,
    d.type_don,
    d.demandeur_nom || ' ' || d.demandeur_prenom AS demandeur_complet,
    d.demandeur_service,
    d.demandeur_laboratoire,
    d.donateur_nom,
    d.statut,
    d.priorite,
    d.date_demande,
    d.date_livraison_prevue,
    d.date_reception,
    d.valeur_estimee,
    d.devise,
    d.description_don,
    
    -- Description courte pour l'affichage
    CASE 
        WHEN LENGTH(d.description_don) > 50 
        THEN SUBSTRING(d.description_don FROM 1 FOR 47) || '...'
        ELSE d.description_don 
    END AS description_courte,
    
    -- Informations de workflow
    d.etape_actuelle,
    wf.nom_etape AS etape_nom,
    wf.role_responsable,
    
    -- Calculs pour l'affichage
    CASE 
        WHEN d.date_livraison_prevue IS NOT NULL AND d.date_livraison_prevue < CURRENT_DATE 
             AND d.statut NOT IN ('archive', 'refuse', 'annule', 'receptionne', 'inventorie', 'affecte')
        THEN true 
        ELSE false 
    END AS en_retard,
    
    -- Nombre de documents
    (SELECT COUNT(*) FROM gestion_donation_documents doc WHERE doc.donation_id = d.id) AS nb_documents,
    
    -- Dernière action
    (SELECT h.description_action FROM gestion_donation_historique h 
     WHERE h.donation_id = d.id ORDER BY h.date_action DESC LIMIT 1) AS derniere_action,
    
    -- Formatage des dates
    TO_CHAR(d.date_demande, 'DD/MM/YYYY') AS date_demande_formatted,
    TO_CHAR(d.date_livraison_prevue, 'DD/MM/YYYY') AS date_livraison_formatted,
    TO_CHAR(d.date_reception, 'DD/MM/YYYY') AS date_reception_formatted,
    
    d.created_at,
    d.updated_at
    
FROM gestion_donations_ipt d
LEFT JOIN gestion_donation_workflow wf ON d.id = wf.donation_id 
    AND wf.numero_etape = d.etape_actuelle
WHERE d.deleted_at IS NULL
ORDER BY d.created_at DESC;

-- =====================================================
-- VUE STATISTIQUES GLOBALES
-- =====================================================

CREATE OR REPLACE VIEW donations_ipt_stats AS
SELECT 
    -- Compteurs globaux
    COUNT(*) AS total_dons,
    COUNT(*) FILTER (WHERE type_don = 'article') AS total_articles,
    COUNT(*) FILTER (WHERE type_don = 'equipement') AS total_equipements,
    
    -- Par statut
    COUNT(*) FILTER (WHERE statut = 'brouillon') AS brouillons,
    COUNT(*) FILTER (WHERE statut = 'soumis') AS soumis,
    COUNT(*) FILTER (WHERE statut IN ('valide_appro', 'avis_technique', 'valide_dg', 'valide_sd_achats')) AS en_validation,
    COUNT(*) FILTER (WHERE statut IN ('soumis_ms', 'approuve_ms')) AS chez_ministere,
    COUNT(*) FILTER (WHERE statut = 'receptionne') AS receptionnes,
    COUNT(*) FILTER (WHERE statut = 'inventorie') AS inventories,
    COUNT(*) FILTER (WHERE statut = 'affecte') AS affectes,
    COUNT(*) FILTER (WHERE statut = 'archive') AS archives,
    COUNT(*) FILTER (WHERE statut IN ('refuse', 'refuse_ms', 'annule')) AS refuses,
    
    -- En retard
    COUNT(*) FILTER (WHERE 
        date_livraison_prevue IS NOT NULL 
        AND date_livraison_prevue < CURRENT_DATE 
        AND statut NOT IN ('archive', 'refuse', 'annule', 'receptionne', 'inventorie', 'affecte')
    ) AS en_retard,
    
    -- Valeurs financières
    COALESCE(SUM(valeur_estimee), 0) AS valeur_totale,
    COALESCE(SUM(valeur_estimee) FILTER (WHERE statut NOT IN ('refuse', 'refuse_ms', 'annule')), 0) AS valeur_active,
    COALESCE(SUM(valeur_estimee) FILTER (WHERE statut = 'affecte'), 0) AS valeur_affectee,
    
    -- Délais moyens (en jours)
    ROUND(AVG(
        CASE WHEN statut IN ('affecte', 'archive') 
        THEN EXTRACT(DAYS FROM (COALESCE(date_affectation, updated_at::date) - created_at::date))
        ELSE NULL END
    ), 1) AS delai_moyen_traitement,
    
    -- Par priorité
    COUNT(*) FILTER (WHERE priorite = 'urgente') AS urgents,
    COUNT(*) FILTER (WHERE priorite = 'elevee') AS priorite_elevee,
    COUNT(*) FILTER (WHERE priorite = 'normale') AS priorite_normale,
    COUNT(*) FILTER (WHERE priorite = 'faible') AS priorite_faible,
    
    -- Période
    MIN(date_demande) AS premiere_demande,
    MAX(date_demande) AS derniere_demande,
    
    -- Mise à jour
    NOW() AS derniere_maj
    
FROM gestion_donations_ipt 
WHERE deleted_at IS NULL;

-- =====================================================
-- VUE WORKFLOW COMPLET
-- =====================================================

CREATE OR REPLACE VIEW donations_ipt_workflow_complete AS
SELECT 
    d.id AS donation_id,
    d.numero_don,
    d.type_don,
    d.demandeur_nom || ' ' || d.demandeur_prenom AS demandeur,
    d.demandeur_service,
    d.statut AS statut_donation,
    d.etape_actuelle,
    
    -- Informations de l'étape
    wf.id AS workflow_id,
    wf.numero_etape,
    wf.nom_etape,
    wf.description_etape,
    wf.role_responsable,
    wf.utilisateur_responsable,
    wf.statut_etape,
    wf.date_debut,
    wf.date_fin,
    wf.duree_traitement,
    wf.commentaires,
    
    -- Calculs
    CASE 
        WHEN wf.statut_etape = 'en_cours' AND wf.date_debut IS NOT NULL
        THEN EXTRACT(DAYS FROM (NOW() - wf.date_debut))
        ELSE NULL 
    END AS jours_en_cours,
    
    CASE 
        WHEN d.date_livraison_prevue IS NOT NULL AND d.date_livraison_prevue < CURRENT_DATE 
             AND d.statut NOT IN ('archive', 'refuse', 'annule', 'receptionne', 'inventorie', 'affecte')
        THEN true 
        ELSE false 
    END AS en_retard,
    
    -- Progression
    ROUND((wf.numero_etape::DECIMAL / 
        (SELECT MAX(numero_etape) FROM gestion_donation_workflow wf2 WHERE wf2.donation_id = d.id)
    ) * 100, 1) AS pourcentage_progression,
    
    d.created_at,
    wf.created_at AS workflow_created_at
    
FROM gestion_donations_ipt d
INNER JOIN gestion_donation_workflow wf ON d.id = wf.donation_id
WHERE d.deleted_at IS NULL
ORDER BY d.created_at DESC, wf.numero_etape;

-- =====================================================
-- VUE DONS EN RETARD
-- =====================================================

CREATE OR REPLACE VIEW donations_ipt_en_retard AS
SELECT 
    d.id,
    d.numero_don,
    d.type_don,
    d.demandeur_nom || ' ' || d.demandeur_prenom AS demandeur,
    d.demandeur_service,
    d.statut,
    d.date_demande,
    d.date_livraison_prevue,
    
    -- Calcul du retard
    CURRENT_DATE - d.date_livraison_prevue AS jours_retard,
    
    -- Étape actuelle
    wf.nom_etape AS etape_actuelle,
    wf.role_responsable,
    wf.date_debut AS etape_debut,
    
    -- Jours bloqué à l'étape actuelle
    CASE 
        WHEN wf.date_debut IS NOT NULL 
        THEN EXTRACT(DAYS FROM (NOW() - wf.date_debut))
        ELSE NULL 
    END AS jours_etape_actuelle,
    
    d.priorite,
    d.valeur_estimee,
    d.created_at
    
FROM gestion_donations_ipt d
LEFT JOIN gestion_donation_workflow wf ON d.id = wf.donation_id 
    AND wf.numero_etape = d.etape_actuelle
WHERE d.deleted_at IS NULL
    AND d.date_livraison_prevue IS NOT NULL 
    AND d.date_livraison_prevue < CURRENT_DATE 
    AND d.statut NOT IN ('archive', 'refuse', 'annule', 'receptionne', 'inventorie', 'affecte')
ORDER BY (CURRENT_DATE - d.date_livraison_prevue) DESC;

-- =====================================================
-- VUE ACTIONS UTILISATEUR PAR RÔLE
-- =====================================================

CREATE OR REPLACE VIEW donations_ipt_actions_by_role AS
SELECT 
    d.id,
    d.numero_don,
    d.type_don,
    d.demandeur_nom || ' ' || d.demandeur_prenom AS demandeur,
    d.demandeur_service,
    d.statut,
    d.priorite,
    
    -- Rôle responsable de l'action
    wf.role_responsable,
    wf.nom_etape,
    wf.description_etape,
    
    -- Urgence de l'action
    CASE 
        WHEN d.priorite = 'urgente' THEN true
        WHEN d.date_livraison_prevue IS NOT NULL AND d.date_livraison_prevue < CURRENT_DATE THEN true
        WHEN wf.date_debut IS NOT NULL AND EXTRACT(DAYS FROM (NOW() - wf.date_debut)) > 7 THEN true
        ELSE false 
    END AS action_urgente,
    
    -- Jours en attente
    CASE 
        WHEN wf.date_debut IS NOT NULL 
        THEN EXTRACT(DAYS FROM (NOW() - wf.date_debut))
        ELSE 0 
    END AS jours_attente,
    
    d.date_demande,
    d.created_at,
    wf.date_debut
    
FROM gestion_donations_ipt d
INNER JOIN gestion_donation_workflow wf ON d.id = wf.donation_id 
    AND wf.numero_etape = d.etape_actuelle
WHERE d.deleted_at IS NULL
    AND wf.statut_etape IN ('en_cours', 'en_attente')
    AND d.statut NOT IN ('archive', 'refuse', 'annule')
ORDER BY 
    CASE WHEN d.priorite = 'urgente' THEN 1 ELSE 2 END,
    wf.date_debut ASC;

-- =====================================================
-- COMMENTAIRES SUR LES VUES
-- =====================================================

COMMENT ON VIEW donations_ipt_complete IS 'Vue complète des dons avec toutes les informations de workflow et d''inventaire';
COMMENT ON VIEW donations_ipt_table_view IS 'Vue optimisée pour l''affichage en table interactive';
COMMENT ON VIEW donations_ipt_stats IS 'Statistiques globales du système de dons';
COMMENT ON VIEW donations_ipt_workflow_complete IS 'Vue détaillée du workflow avec progression';
COMMENT ON VIEW donations_ipt_en_retard IS 'Dons en retard de livraison';
COMMENT ON VIEW donations_ipt_actions_by_role IS 'Actions en attente par rôle utilisateur';
