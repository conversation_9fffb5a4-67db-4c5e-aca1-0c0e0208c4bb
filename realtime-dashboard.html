<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord Temps Réel - Gestion Interne</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .dashboard {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s;
        }

        .metric-card:hover {
            transform: translateY(-5px);
        }

        .metric-card.connected {
            border-left: 5px solid #28a745;
        }

        .metric-card.warning {
            border-left: 5px solid #ffc107;
        }

        .metric-card.disconnected {
            border-left: 5px solid #dc3545;
        }

        .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 10px;
            text-transform: uppercase;
            font-weight: 600;
        }

        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .metric-subtitle {
            font-size: 12px;
            color: #999;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.connected {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .status-indicator.warning {
            background: #ffc107;
        }

        .status-indicator.disconnected {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .activity-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .activity-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .activity-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: background 0.2s;
        }

        .activity-item:hover {
            background: #f8f9fa;
        }

        .activity-icon {
            font-size: 20px;
            margin-right: 12px;
            width: 30px;
            text-align: center;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .activity-subtitle {
            font-size: 12px;
            color: #666;
        }

        .activity-time {
            font-size: 11px;
            color: #999;
        }

        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .user-card {
            background: white;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 12px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .user-status {
            font-size: 12px;
            color: #666;
        }

        .controls {
            text-align: center;
            margin: 20px 0;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .notifications-panel {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .notification-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }

        .notification-item.success {
            border-left-color: #28a745;
        }

        .notification-item.warning {
            border-left-color: #ffc107;
        }

        .notification-item.error {
            border-left-color: #dc3545;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .chart {
            height: 200px;
            display: flex;
            align-items: end;
            justify-content: space-around;
            border-bottom: 2px solid #eee;
            padding: 20px 0;
        }

        .chart-bar {
            width: 30px;
            background: linear-gradient(to top, #667eea, #764ba2);
            border-radius: 4px 4px 0 0;
            transition: all 0.3s ease;
        }

        .chart-bar:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>⚡ Tableau de Bord Temps Réel</h1>
            <p>Surveillance en temps réel de la synchronisation entre navigateurs</p>
            <div class="controls">
                <button class="btn" onclick="refreshDashboard()">🔄 Actualiser</button>
                <button class="btn success" onclick="startRealtime()">▶️ Démarrer Temps Réel</button>
                <button class="btn danger" onclick="stopRealtime()">⏹️ Arrêter Temps Réel</button>
                <button class="btn secondary" onclick="forceSync()">🔄 Sync Forcée</button>
            </div>
        </div>

        <!-- Métriques principales -->
        <div class="metrics-grid">
            <div class="metric-card" id="realtimeCard">
                <div class="metric-title">
                    <span class="status-indicator" id="realtimeIndicator"></span>
                    Système Temps Réel
                </div>
                <div class="metric-value" id="realtimeStatus">Initialisation...</div>
                <div class="metric-subtitle">État du coordinateur</div>
            </div>

            <div class="metric-card" id="usersCard">
                <div class="metric-title">👥 Utilisateurs Connectés</div>
                <div class="metric-value" id="connectedUsersCount">0</div>
                <div class="metric-subtitle">Sessions actives</div>
            </div>

            <div class="metric-card" id="messagesCard">
                <div class="metric-title">💬 Messages Temps Réel</div>
                <div class="metric-value" id="realtimeMessagesCount">0</div>
                <div class="metric-subtitle">Reçus aujourd'hui</div>
            </div>

            <div class="metric-card" id="syncCard">
                <div class="metric-title">🔄 Synchronisations</div>
                <div class="metric-value" id="syncCount">0</div>
                <div class="metric-subtitle">Réussies / Échouées</div>
            </div>

            <div class="metric-card" id="notificationsCard">
                <div class="metric-title">🔔 Notifications</div>
                <div class="metric-value" id="notificationsCount">0</div>
                <div class="metric-subtitle">Non lues</div>
            </div>

            <div class="metric-card" id="performanceCard">
                <div class="metric-title">⚡ Performance</div>
                <div class="metric-value" id="performanceValue">--ms</div>
                <div class="metric-subtitle">Latence moyenne</div>
            </div>
        </div>

        <!-- Activité en temps réel -->
        <div class="activity-section">
            <div class="activity-panel">
                <div class="activity-header">
                    <h3>📊 Activité Récente</h3>
                    <button class="btn secondary" onclick="clearActivity()">Vider</button>
                </div>
                <div class="activity-list" id="activityList">
                    <!-- Les activités seront ajoutées dynamiquement -->
                </div>
            </div>

            <div class="activity-panel">
                <div class="activity-header">
                    <h3>🔔 Notifications Récentes</h3>
                    <button class="btn secondary" onclick="clearNotifications()">Vider</button>
                </div>
                <div class="activity-list" id="notificationsList">
                    <!-- Les notifications seront ajoutées dynamiquement -->
                </div>
            </div>
        </div>

        <!-- Utilisateurs connectés -->
        <div class="activity-panel">
            <div class="activity-header">
                <h3>👥 Utilisateurs Connectés</h3>
                <span id="lastPresenceUpdate">Dernière mise à jour: --</span>
            </div>
            <div class="users-grid" id="usersGrid">
                <!-- Les utilisateurs seront ajoutés dynamiquement -->
            </div>
        </div>

        <!-- Graphique d'activité -->
        <div class="chart-container">
            <h3>📈 Activité des 24 dernières heures</h3>
            <div class="chart" id="activityChart">
                <!-- Les barres du graphique seront générées dynamiquement -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/app-config.js"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/database-manager-fix.js"></script>
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>
    <script src="assets/js/supabase-migration.js"></script>
    <script src="assets/js/notification-system.js"></script>
    <script src="assets/js/realtime-ui-manager.js"></script>
    <script src="assets/js/user-presence.js"></script>
    <script src="assets/js/supabase-realtime.js"></script>
    <script src="assets/js/realtime-coordinator.js"></script>

    <script>
        // Variables globales du tableau de bord
        let dashboardInterval = null;
        let activityData = [];
        let notificationsData = [];
        let chartData = Array(24).fill(0); // 24 heures

        // Initialisation du tableau de bord
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 Initialisation du tableau de bord temps réel...');

            try {
                // Initialiser Supabase
                await initializeSupabase();

                // Démarrer les mises à jour périodiques
                startDashboardUpdates();

                // Configurer les callbacks temps réel
                setupRealtimeCallbacks();

                console.log('✅ Tableau de bord temps réel initialisé');

            } catch (error) {
                console.error('❌ Erreur initialisation tableau de bord:', error);
                addActivity('error', 'Erreur initialisation', error.message);
            }
        });

        // Démarrer les mises à jour périodiques
        function startDashboardUpdates() {
            dashboardInterval = setInterval(() => {
                updateMetrics();
                updateChart();
            }, 2000); // Mise à jour toutes les 2 secondes
        }

        // Configurer les callbacks temps réel
        function setupRealtimeCallbacks() {
            // Callback quand le système temps réel est prêt
            if (typeof SupabaseAdvanced !== 'undefined') {
                SupabaseAdvanced.onRealtimeEvent('onRealtimeReady', (data) => {
                    addActivity('success', 'Système temps réel prêt', 'Tous les modules initialisés');
                    updateRealtimeStatus('connected');
                });

                SupabaseAdvanced.onRealtimeEvent('onError', (error) => {
                    addActivity('error', 'Erreur temps réel', error.message);
                    updateRealtimeStatus('disconnected');
                });

                SupabaseAdvanced.onRealtimeEvent('onSyncComplete', (data) => {
                    addActivity('info', 'Synchronisation terminée', `Type: ${data.type}`);
                });
            }
        }

        // Mettre à jour les métriques
        function updateMetrics() {
            try {
                // Obtenir les statistiques temps réel
                const realtimeStats = typeof SupabaseAdvanced !== 'undefined'
                    ? SupabaseAdvanced.getRealtimeStats()
                    : null;

                if (realtimeStats) {
                    // Mettre à jour le statut du système temps réel
                    const isActive = realtimeStats.coordinator?.isActive || false;
                    updateRealtimeStatus(isActive ? 'connected' : 'disconnected');

                    // Utilisateurs connectés
                    const connectedUsers = SupabaseAdvanced.getConnectedUsers();
                    document.getElementById('connectedUsersCount').textContent = connectedUsers.length;
                    updateUsersGrid(connectedUsers);

                    // Messages temps réel
                    const messagesReceived = realtimeStats.coordinator?.syncState?.successCount || 0;
                    document.getElementById('realtimeMessagesCount').textContent = messagesReceived;

                    // Synchronisations
                    const syncSuccess = realtimeStats.coordinator?.syncState?.successCount || 0;
                    const syncErrors = realtimeStats.coordinator?.syncState?.errorCount || 0;
                    document.getElementById('syncCount').textContent = `${syncSuccess} / ${syncErrors}`;

                    // Notifications
                    const notificationsCount = realtimeStats.notifications?.unread || 0;
                    document.getElementById('notificationsCount').textContent = notificationsCount;

                    // Performance (simulée)
                    const latency = Math.floor(Math.random() * 50) + 10;
                    document.getElementById('performanceValue').textContent = latency + 'ms';
                }

            } catch (error) {
                console.warn('⚠️ Erreur mise à jour métriques:', error);
            }
        }

        // Mettre à jour le statut du système temps réel
        function updateRealtimeStatus(status) {
            const card = document.getElementById('realtimeCard');
            const indicator = document.getElementById('realtimeIndicator');
            const statusElement = document.getElementById('realtimeStatus');

            // Réinitialiser les classes
            card.className = 'metric-card';
            indicator.className = 'status-indicator';

            switch (status) {
                case 'connected':
                    card.classList.add('connected');
                    indicator.classList.add('connected');
                    statusElement.textContent = 'Actif';
                    break;
                case 'warning':
                    card.classList.add('warning');
                    indicator.classList.add('warning');
                    statusElement.textContent = 'Instable';
                    break;
                case 'disconnected':
                    card.classList.add('disconnected');
                    indicator.classList.add('disconnected');
                    statusElement.textContent = 'Inactif';
                    break;
            }
        }

        // Mettre à jour la grille des utilisateurs
        function updateUsersGrid(users) {
            const grid = document.getElementById('usersGrid');
            grid.innerHTML = '';

            users.forEach(user => {
                const userCard = document.createElement('div');
                userCard.className = 'user-card';

                const initials = (user.name || user.username || 'U').split(' ')
                    .map(n => n[0]).join('').toUpperCase().substring(0, 2);

                userCard.innerHTML = `
                    <div class="user-avatar">${initials}</div>
                    <div class="user-info">
                        <div class="user-name">${user.name || user.username}</div>
                        <div class="user-status">
                            <span class="status-indicator ${user.status || 'connected'}"></span>
                            ${user.status || 'En ligne'}
                        </div>
                    </div>
                `;

                grid.appendChild(userCard);
            });

            if (users.length === 0) {
                grid.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">Aucun utilisateur connecté</div>';
            }

            document.getElementById('lastPresenceUpdate').textContent =
                `Dernière mise à jour: ${new Date().toLocaleTimeString()}`;
        }

        // Ajouter une activité
        function addActivity(type, title, description) {
            const activity = {
                id: Date.now(),
                type,
                title,
                description,
                timestamp: new Date().toISOString()
            };

            activityData.unshift(activity);

            // Limiter à 50 activités
            if (activityData.length > 50) {
                activityData = activityData.slice(0, 50);
            }

            updateActivityList();

            // Mettre à jour le graphique
            const hour = new Date().getHours();
            chartData[hour]++;
        }

        // Mettre à jour la liste d'activité
        function updateActivityList() {
            const list = document.getElementById('activityList');
            list.innerHTML = '';

            activityData.slice(0, 10).forEach(activity => {
                const item = document.createElement('div');
                item.className = 'activity-item';

                const icons = {
                    success: '✅',
                    error: '❌',
                    warning: '⚠️',
                    info: 'ℹ️'
                };

                item.innerHTML = `
                    <div class="activity-icon">${icons[activity.type] || 'ℹ️'}</div>
                    <div class="activity-content">
                        <div class="activity-title">${activity.title}</div>
                        <div class="activity-subtitle">${activity.description}</div>
                        <div class="activity-time">${new Date(activity.timestamp).toLocaleTimeString()}</div>
                    </div>
                `;

                list.appendChild(item);
            });

            if (activityData.length === 0) {
                list.innerHTML = '<div style="text-align: center; color: #666; padding: 20px;">Aucune activité récente</div>';
            }
        }

        // Mettre à jour le graphique
        function updateChart() {
            const chart = document.getElementById('activityChart');
            chart.innerHTML = '';

            const maxValue = Math.max(...chartData, 1);

            chartData.forEach((value, index) => {
                const bar = document.createElement('div');
                bar.className = 'chart-bar';
                bar.style.height = `${(value / maxValue) * 160}px`;
                bar.title = `${index}h: ${value} activités`;
                chart.appendChild(bar);
            });
        }

        // Fonctions de contrôle
        async function refreshDashboard() {
            addActivity('info', 'Actualisation manuelle', 'Tableau de bord actualisé');
            updateMetrics();
        }

        async function startRealtime() {
            try {
                if (typeof SupabaseAdvanced !== 'undefined') {
                    await SupabaseAdvanced.startRealtime();
                    addActivity('success', 'Temps réel démarré', 'Système temps réel activé');
                }
            } catch (error) {
                addActivity('error', 'Erreur démarrage temps réel', error.message);
            }
        }

        async function stopRealtime() {
            try {
                if (typeof SupabaseAdvanced !== 'undefined') {
                    await SupabaseAdvanced.stopRealtime();
                    addActivity('warning', 'Temps réel arrêté', 'Système temps réel désactivé');
                    updateRealtimeStatus('disconnected');
                }
            } catch (error) {
                addActivity('error', 'Erreur arrêt temps réel', error.message);
            }
        }

        async function forceSync() {
            try {
                if (typeof SupabaseAdvanced !== 'undefined') {
                    const result = await SupabaseAdvanced.forceRealtimeSync();
                    addActivity('success', 'Synchronisation forcée', 'Synchronisation manuelle terminée');
                }
            } catch (error) {
                addActivity('error', 'Erreur synchronisation forcée', error.message);
            }
        }

        function clearActivity() {
            activityData = [];
            updateActivityList();
            addActivity('info', 'Activités vidées', 'Liste d\'activité réinitialisée');
        }

        function clearNotifications() {
            notificationsData = [];
            document.getElementById('notificationsList').innerHTML =
                '<div style="text-align: center; color: #666; padding: 20px;">Aucune notification récente</div>';
            addActivity('info', 'Notifications vidées', 'Liste de notifications réinitialisée');
        }

        // Nettoyage lors de la fermeture
        window.addEventListener('beforeunload', function() {
            if (dashboardInterval) {
                clearInterval(dashboardInterval);
            }
        });
    </script>
</body>
</html>