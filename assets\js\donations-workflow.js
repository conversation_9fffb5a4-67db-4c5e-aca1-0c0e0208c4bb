/**
 * Module de gestion du workflow de validation des dons IPT
 * Système complet de validation hiérarchique
 */

class DonationsWorkflow {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.userRole = null;
        this.donations = [];
        this.filteredDonations = [];
        this.pendingActions = [];
        this.currentDonation = null;
        this.realtimeSubscription = null;
    }

    async initialize() {
        console.log('🔄 Initialisation du gestionnaire de workflow...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            if (!this.currentUser) {
                throw new Error('Utilisateur non authentifié');
            }

            // Récupérer le rôle utilisateur
            this.userRole = this.currentUser.user_metadata?.role_ipt || 'demandeur';

            // Mettre à jour l'interface utilisateur
            this.updateUserInfo();

            // Charger les données
            await this.loadData();

            // Configurer les mises à jour en temps réel
            this.setupRealtimeSubscription();

            // Configurer le rafraîchissement automatique
            setInterval(() => this.loadData(), 60000); // Toutes les minutes

            console.log('✅ Gestionnaire de workflow initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation workflow:', error);
            throw error;
        }
    }

    updateUserInfo() {
        const roleNames = {
            'admin_systeme': 'Administrateur Système',
            'responsable_approvisionnement': 'Responsable Approvisionnement',
            'direction_technique': 'Direction Technique',
            'directeur_general': 'Directeur Général',
            'sous_direction_achats': 'Sous-Direction Achats',
            'receptionniste': 'Réceptionniste',
            'rve': 'Responsable Volet Équipement',
            'beneficiaire': 'Bénéficiaire',
            'demandeur': 'Demandeur'
        };

        document.getElementById('userRole').textContent = roleNames[this.userRole] || this.userRole;
        document.getElementById('userEmail').textContent = this.currentUser.email;
    }

    async loadData() {
        try {
            // Charger les statistiques
            await this.loadStatistics();

            // Charger les actions en attente pour l'utilisateur
            await this.loadPendingActions();

            // Charger tous les dons selon les permissions
            await this.loadAllDonations();

            // Charger les services pour les filtres
            await this.loadServices();

            // Appliquer les filtres
            this.applyFilters();

        } catch (error) {
            console.error('❌ Erreur chargement données workflow:', error);
            this.showAlert('Erreur lors du chargement des données', 'error');
        }
    }

    async loadStatistics() {
        try {
            const { data: stats, error } = await this.supabase
                .from('donations_ipt_stats')
                .select('*')
                .single();

            if (error) throw error;

            // Mettre à jour les statistiques
            document.getElementById('statTotal').textContent = stats.total_dons || 0;
            document.getElementById('statPending').textContent = stats.en_validation || 0;
            document.getElementById('statUrgent').textContent = stats.urgents || 0;
            document.getElementById('statCompleted').textContent = stats.affectes || 0;

        } catch (error) {
            console.error('Erreur chargement statistiques:', error);
            // Valeurs par défaut
            document.getElementById('statTotal').textContent = '0';
            document.getElementById('statPending').textContent = '0';
            document.getElementById('statUrgent').textContent = '0';
            document.getElementById('statCompleted').textContent = '0';
        }
    }

    async loadPendingActions() {
        try {
            const { data: actions, error } = await this.supabase
                .from('donations_ipt_actions_by_role')
                .select('*')
                .eq('role_responsable', this.userRole)
                .order('jours_attente', { ascending: false });

            if (error) throw error;

            this.pendingActions = actions || [];
            this.renderPendingActions();

        } catch (error) {
            console.error('Erreur chargement actions en attente:', error);
            this.pendingActions = [];
            this.renderPendingActions();
        }
    }

    async loadAllDonations() {
        try {
            let query = this.supabase
                .from('donations_ipt_workflow_complete')
                .select('*');

            // Filtrer selon le rôle de l'utilisateur
            switch (this.userRole) {
                case 'admin_systeme':
                    // Admin peut tout voir
                    break;
                case 'responsable_approvisionnement':
                case 'directeur_general':
                case 'sous_direction_achats':
                    // Peuvent voir tous les dons
                    break;
                case 'direction_technique':
                    // Seulement les équipements
                    query = query.eq('type_don', 'equipement');
                    break;
                case 'rve':
                case 'receptionniste':
                    // Dons à partir de leur étape
                    query = query.gte('numero_etape', this.getMinStepForRole());
                    break;
                default:
                    // Demandeurs et bénéficiaires : leurs propres dons
                    query = query.eq('demandeur', this.currentUser.email);
            }

            const { data: donations, error } = await query
                .order('created_at', { ascending: false })
                .limit(100);

            if (error) throw error;

            this.donations = donations || [];
            this.filteredDonations = [...this.donations];

        } catch (error) {
            console.error('Erreur chargement dons:', error);
            this.donations = [];
            this.filteredDonations = [];
        }
    }

    getMinStepForRole() {
        const minSteps = {
            'receptionniste': 7, // À partir de la réception
            'rve': 8 // À partir de l'inventaire
        };
        return minSteps[this.userRole] || 1;
    }

    async loadServices() {
        try {
            const { data: services, error } = await this.supabase
                .from('gestion_donations_ipt')
                .select('demandeur_service')
                .not('demandeur_service', 'is', null)
                .not('deleted_at', 'is', null);

            if (error) throw error;

            // Extraire les services uniques
            const uniqueServices = [...new Set(services.map(s => s.demandeur_service))];
            
            // Remplir le filtre des services
            const serviceFilter = document.getElementById('filterService');
            serviceFilter.innerHTML = '<option value="">Tous les services</option>';
            
            uniqueServices.forEach(service => {
                const option = document.createElement('option');
                option.value = service;
                option.textContent = service;
                serviceFilter.appendChild(option);
            });

        } catch (error) {
            console.error('Erreur chargement services:', error);
        }
    }

    renderPendingActions() {
        const container = document.getElementById('pendingContent');
        const badge = document.getElementById('pendingBadge');
        
        badge.textContent = this.pendingActions.length;

        if (this.pendingActions.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">✅</div>
                    <div class="empty-title">Aucune action en attente</div>
                    <div class="empty-description">
                        Toutes vos tâches de validation sont à jour.
                    </div>
                </div>
            `;
            return;
        }

        container.innerHTML = this.pendingActions.map(action => this.renderDonationCard(action, true)).join('');
    }

    renderAllDonations() {
        const container = document.getElementById('allDonationsContent');
        const badge = document.getElementById('totalBadge');
        
        badge.textContent = this.filteredDonations.length;

        if (this.filteredDonations.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📋</div>
                    <div class="empty-title">Aucun don trouvé</div>
                    <div class="empty-description">
                        Aucun don ne correspond aux critères de recherche.
                    </div>
                </div>
            `;
            return;
        }

        container.innerHTML = this.filteredDonations.map(donation => this.renderDonationCard(donation, false)).join('');
    }

    renderDonationCard(donation, isPending = false) {
        const isUrgent = donation.action_urgente || donation.priorite === 'urgente';
        const workflowSteps = this.generateWorkflowSteps(donation);
        
        return `
            <div class="donation-card ${isUrgent ? 'urgent' : ''}" onclick="viewDonationDetails('${donation.donation_id}')">
                <div class="donation-header">
                    <div class="donation-number">${donation.numero_don}</div>
                    <div class="donation-type ${donation.type_don}">${donation.type_don}</div>
                </div>
                
                <div class="donation-info">
                    <div class="info-item">
                        <div class="info-label">Demandeur</div>
                        <div class="info-value">${donation.demandeur}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Service</div>
                        <div class="info-value">${donation.demandeur_service}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Priorité</div>
                        <div class="info-value">${this.formatPriority(donation.priorite)}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Étape Actuelle</div>
                        <div class="info-value">${donation.nom_etape}</div>
                    </div>
                </div>

                <div class="donation-description">
                    <strong>Description :</strong> ${donation.description_don || 'Non spécifiée'}
                </div>

                <div class="workflow-progress">
                    ${workflowSteps}
                </div>

                <div class="donation-actions">
                    ${this.generateActionButtons(donation, isPending)}
                </div>
            </div>
        `;
    }

    generateWorkflowSteps(donation) {
        const totalSteps = donation.type_don === 'equipement' ? 10 : 9;
        const currentStep = donation.numero_etape;
        
        let steps = '';
        for (let i = 1; i <= totalSteps; i++) {
            let stepClass = 'workflow-step';
            let stepLabel = this.getStepLabel(i, donation.type_don);
            
            if (i < currentStep) {
                stepClass += ' completed';
            } else if (i === currentStep) {
                stepClass += ' current';
            } else {
                stepClass += ' pending';
            }
            
            steps += `
                <div class="${stepClass}">
                    <div class="workflow-circle">${i}</div>
                    <div class="workflow-label">${stepLabel}</div>
                </div>
            `;
        }
        
        return steps;
    }

    getStepLabel(step, type) {
        const labels = {
            1: 'Appro',
            2: type === 'equipement' ? 'Tech' : 'DG',
            3: type === 'equipement' ? 'DG' : 'Achats',
            4: type === 'equipement' ? 'Achats' : 'MS',
            5: type === 'equipement' ? 'MS' : 'Décision',
            6: type === 'equipement' ? 'Décision' : 'Réception',
            7: type === 'equipement' ? 'Réception' : 'Inventaire',
            8: type === 'equipement' ? 'Inventaire' : 'Affectation',
            9: type === 'equipement' ? 'Affectation' : 'Archive',
            10: 'Archive'
        };
        return labels[step] || step.toString();
    }

    formatPriority(priority) {
        const priorities = {
            'urgente': '🚨 Urgente',
            'elevee': '🔴 Élevée',
            'normale': '🟡 Normale',
            'faible': '🟢 Faible'
        };
        return priorities[priority] || priority;
    }

    generateActionButtons(donation, isPending) {
        const buttons = [];
        
        // Bouton de visualisation
        buttons.push(`
            <button class="btn btn-secondary" onclick="event.stopPropagation(); viewDonationDetails('${donation.donation_id}')">
                👁️ Voir Détails
            </button>
        `);

        // Boutons d'action selon le rôle et l'étape
        if (isPending && this.canValidate(donation)) {
            buttons.push(`
                <button class="btn btn-success" onclick="event.stopPropagation(); openValidationModal('${donation.donation_id}', 'approve')">
                    ✅ Valider
                </button>
            `);
            
            buttons.push(`
                <button class="btn btn-danger" onclick="event.stopPropagation(); openValidationModal('${donation.donation_id}', 'reject')">
                    ❌ Refuser
                </button>
            `);
        }

        return buttons.join('');
    }

    canValidate(donation) {
        // Vérifier si l'utilisateur peut valider ce don à cette étape
        const roleStepMapping = {
            'responsable_approvisionnement': [1],
            'direction_technique': [2],
            'directeur_general': donation.type_don === 'equipement' ? [3] : [2],
            'sous_direction_achats': donation.type_don === 'equipement' ? [4, 5] : [3, 4],
            'receptionniste': donation.type_don === 'equipement' ? [7] : [6],
            'rve': donation.type_don === 'equipement' ? [8, 9] : [7, 8]
        };

        const allowedSteps = roleStepMapping[this.userRole] || [];
        return allowedSteps.includes(donation.numero_etape);
    }

    applyFilters() {
        const typeFilter = document.getElementById('filterType').value;
        const priorityFilter = document.getElementById('filterPriority').value;
        const serviceFilter = document.getElementById('filterService').value;
        const searchFilter = document.getElementById('filterSearch').value.toLowerCase();

        this.filteredDonations = this.donations.filter(donation => {
            // Filtre par type
            if (typeFilter && donation.type_don !== typeFilter) return false;
            
            // Filtre par priorité
            if (priorityFilter && donation.priorite !== priorityFilter) return false;
            
            // Filtre par service
            if (serviceFilter && donation.demandeur_service !== serviceFilter) return false;
            
            // Filtre par recherche
            if (searchFilter) {
                const searchText = `${donation.numero_don} ${donation.demandeur} ${donation.donateur_nom || ''}`.toLowerCase();
                if (!searchText.includes(searchFilter)) return false;
            }
            
            return true;
        });

        this.renderAllDonations();
    }

    openValidationModal(donationId, action = 'approve') {
        const donation = this.donations.find(d => d.donation_id === donationId) || 
                        this.pendingActions.find(d => d.id === donationId);
        
        if (!donation) {
            this.showAlert('Don non trouvé', 'error');
            return;
        }

        this.currentDonation = donation;
        
        // Mettre à jour le modal
        document.getElementById('modalTitle').textContent = 
            action === 'approve' ? 'Validation du Don' : 'Refus du Don';
        
        document.getElementById('modalDonationInfo').innerHTML = `
            <strong>${donation.numero_don}</strong> - ${donation.type_don}<br>
            Demandeur: ${donation.demandeur}<br>
            Service: ${donation.demandeur_service}
        `;
        
        document.getElementById('modalAction').value = action;
        document.getElementById('modalComments').value = '';
        
        // Afficher le modal
        document.getElementById('validationModal').style.display = 'block';
    }

    async submitValidation() {
        const action = document.getElementById('modalAction').value;
        const comments = document.getElementById('modalComments').value.trim();
        
        // Validation
        if (action === 'reject' && !comments) {
            this.showAlert('Les commentaires sont requis pour un refus', 'error');
            return;
        }

        try {
            this.showLoading(true);
            
            const approved = action === 'approve';
            const donationId = this.currentDonation.donation_id || this.currentDonation.id;
            
            // Appeler la fonction de validation appropriée selon le rôle
            const { error } = await this.callValidationFunction(donationId, approved, comments);
            
            if (error) throw error;
            
            this.showAlert(
                approved ? 'Don validé avec succès' : 'Don refusé avec succès', 
                'success'
            );
            
            // Fermer le modal
            document.getElementById('validationModal').style.display = 'none';
            
            // Recharger les données
            await this.loadData();
            
        } catch (error) {
            console.error('Erreur validation:', error);
            this.showAlert('Erreur lors de la validation: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async callValidationFunction(donationId, approved, comments) {
        const functionMap = {
            'responsable_approvisionnement': 'validate_donation_approvisionnement',
            'direction_technique': 'provide_technical_advice',
            'directeur_general': 'validate_donation_dg',
            'sous_direction_achats': 'validate_donation_sd_achats'
        };

        const functionName = functionMap[this.userRole];
        if (!functionName) {
            throw new Error('Fonction de validation non trouvée pour ce rôle');
        }

        return await this.supabase.rpc(functionName, {
            p_donation_id: donationId,
            p_validator_email: this.currentUser.email,
            p_commentaires: comments,
            p_approved: approved
        });
    }

    setupRealtimeSubscription() {
        try {
            this.realtimeSubscription = this.supabase
                .channel('donations_workflow')
                .on('postgres_changes', 
                    { event: '*', schema: 'public', table: 'gestion_donations_ipt' },
                    (payload) => {
                        console.log('🔄 Mise à jour temps réel détectée:', payload);
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .on('postgres_changes',
                    { event: '*', schema: 'public', table: 'gestion_donation_workflow' },
                    (payload) => {
                        console.log('🔄 Mise à jour workflow détectée:', payload);
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .subscribe();

            console.log('✅ Abonnement temps réel configuré');

        } catch (error) {
            console.error('❌ Erreur configuration temps réel:', error);
        }
    }

    showLoading(show) {
        const submitBtn = document.getElementById('submitValidationBtn');
        if (show) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<div class="loading"><div class="spinner"></div>Validation...</div>';
        } else {
            submitBtn.disabled = false;
            submitBtn.innerHTML = 'Valider';
        }
    }

    showAlert(message, type = 'info') {
        if (typeof showAlert === 'function') {
            showAlert(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    destroy() {
        if (this.realtimeSubscription) {
            this.supabase.removeChannel(this.realtimeSubscription);
        }
    }
}

// Instance globale
window.donationsWorkflow = new DonationsWorkflow();
window.DonationsWorkflow = DonationsWorkflow;
