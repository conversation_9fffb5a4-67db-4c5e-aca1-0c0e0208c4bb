<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test d'Intégration Supabase - Gestion Interne</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #6c757d;
        }
        
        .test-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .test-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .test-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        .test-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .test-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 10px;
        }
        
        .status-success {
            background: #28a745;
            color: white;
        }
        
        .status-error {
            background: #dc3545;
            color: white;
        }
        
        .status-warning {
            background: #ffc107;
            color: black;
        }
        
        .status-pending {
            background: #6c757d;
            color: white;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            margin: 5px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .summary {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test d'Intégration Supabase</h1>
            <p>Vérification complète de tous les modules et fonctionnalités</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="runAllTests()">🚀 Lancer Tous les Tests</button>
            <button class="btn secondary" onclick="clearLogs()">🗑️ Vider Logs</button>
            <button class="btn secondary" onclick="exportResults()">📋 Exporter Résultats</button>
        </div>

        <!-- Grille des tests -->
        <div class="test-grid" id="testGrid">
            <!-- Les cartes de test seront générées dynamiquement -->
        </div>

        <!-- Résumé -->
        <div class="summary" id="summary" style="display: none;">
            <h3>📊 Résumé des Tests</h3>
            <div id="summaryContent"></div>
        </div>

        <!-- Logs -->
        <div class="log-container" id="testLog"></div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth-complete.js"></script>
    <script src="assets/js/supabase-storage-manager.js"></script>
    <script src="assets/js/supabase-realtime-complete.js"></script>
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>
    <script src="assets/js/supabase-migration.js"></script>
    <script src="assets/js/supabase-system-complete.js"></script>
    
    <script>
        // Variables globales
        let testResults = {};
        let totalTests = 0;
        let passedTests = 0;
        let failedTests = 0;

        // Définition des tests
        const tests = [
            {
                id: 'config',
                name: 'Configuration',
                description: 'Vérification du chargement des configurations',
                test: testConfiguration
            },
            {
                id: 'supabase_connection',
                name: 'Connexion Supabase',
                description: 'Test de connexion à Supabase',
                test: testSupabaseConnection
            },
            {
                id: 'auto_init',
                name: 'Auto-Initialisation',
                description: 'Test du module d\'auto-initialisation',
                test: testAutoInit
            },
            {
                id: 'monitor',
                name: 'Monitoring',
                description: 'Test du système de monitoring',
                test: testMonitoring
            },
            {
                id: 'sync',
                name: 'Synchronisation',
                description: 'Test de la synchronisation bidirectionnelle',
                test: testSynchronization
            },
            {
                id: 'migration',
                name: 'Migration',
                description: 'Test du système de migration',
                test: testMigration
            },
            {
                id: 'database_operations',
                name: 'Opérations Base de Données',
                description: 'Test des opérations CRUD',
                test: testDatabaseOperations
            },
            {
                id: 'error_handling',
                name: 'Gestion d\'Erreurs',
                description: 'Test de la gestion d\'erreurs et reconnexion',
                test: testErrorHandling
            }
        ];

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            generateTestCards();
            log('🚀 Tests d\'intégration prêts', 'info');
        });

        // Générer les cartes de test
        function generateTestCards() {
            const testGrid = document.getElementById('testGrid');
            
            tests.forEach(test => {
                const card = document.createElement('div');
                card.className = 'test-card';
                card.id = `card-${test.id}`;
                
                card.innerHTML = `
                    <div class="test-title">${test.name}</div>
                    <div class="test-status status-pending" id="status-${test.id}">En attente</div>
                    <div class="test-description">${test.description}</div>
                    <button class="btn" onclick="runSingleTest('${test.id}')">▶️ Tester</button>
                `;
                
                testGrid.appendChild(card);
            });
        }

        // Lancer tous les tests
        async function runAllTests() {
            log('🚀 Démarrage de tous les tests...', 'info');
            
            totalTests = tests.length;
            passedTests = 0;
            failedTests = 0;
            testResults = {};
            
            for (const test of tests) {
                await runSingleTest(test.id);
                // Petite pause entre les tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            showSummary();
            log(`✅ Tous les tests terminés: ${passedTests}/${totalTests} réussis`, 'info');
        }

        // Lancer un test individuel
        async function runSingleTest(testId) {
            const test = tests.find(t => t.id === testId);
            if (!test) return;
            
            log(`🧪 Test: ${test.name}...`, 'info');
            updateTestStatus(testId, 'running');
            
            try {
                const result = await test.test();
                
                if (result.success) {
                    updateTestStatus(testId, 'success');
                    testResults[testId] = { success: true, ...result };
                    passedTests++;
                    log(`✅ ${test.name}: ${result.message || 'Réussi'}`, 'success');
                } else {
                    updateTestStatus(testId, 'error');
                    testResults[testId] = { success: false, ...result };
                    failedTests++;
                    log(`❌ ${test.name}: ${result.error || 'Échec'}`, 'error');
                }
                
            } catch (error) {
                updateTestStatus(testId, 'error');
                testResults[testId] = { success: false, error: error.message };
                failedTests++;
                log(`❌ ${test.name}: ${error.message}`, 'error');
            }
        }

        // Mettre à jour le statut d'un test
        function updateTestStatus(testId, status) {
            const card = document.getElementById(`card-${testId}`);
            const statusElement = document.getElementById(`status-${testId}`);
            
            // Réinitialiser les classes
            card.className = 'test-card';
            statusElement.className = 'test-status';
            
            switch (status) {
                case 'running':
                    statusElement.classList.add('status-warning');
                    statusElement.textContent = 'En cours...';
                    break;
                case 'success':
                    card.classList.add('success');
                    statusElement.classList.add('status-success');
                    statusElement.textContent = '✅ Réussi';
                    break;
                case 'error':
                    card.classList.add('error');
                    statusElement.classList.add('status-error');
                    statusElement.textContent = '❌ Échec';
                    break;
                default:
                    statusElement.classList.add('status-pending');
                    statusElement.textContent = 'En attente';
            }
        }

        // Tests individuels
        async function testConfiguration() {
            const configsAvailable = {
                SUPABASE_CONFIG: typeof SUPABASE_CONFIG !== 'undefined',
                APP_CONFIG: typeof APP_CONFIG !== 'undefined',
                SupabaseAutoInit: typeof SupabaseAutoInit !== 'undefined',
                SupabaseMonitor: typeof SupabaseMonitor !== 'undefined',
                SupabaseSync: typeof SupabaseSync !== 'undefined',
                SupabaseMigration: typeof SupabaseMigration !== 'undefined'
            };
            
            const allLoaded = Object.values(configsAvailable).every(Boolean);
            
            return {
                success: allLoaded,
                message: allLoaded ? 'Toutes les configurations chargées' : 'Configurations manquantes',
                details: configsAvailable
            };
        }

        async function testSupabaseConnection() {
            try {
                if (typeof initializeSupabase !== 'function') {
                    throw new Error('initializeSupabase non disponible');
                }
                
                const result = await initializeSupabase();
                
                return {
                    success: result.available,
                    message: result.available ? 'Connexion Supabase établie' : 'Connexion échouée',
                    details: result
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testAutoInit() {
            try {
                if (typeof SupabaseAutoInit === 'undefined') {
                    throw new Error('SupabaseAutoInit non disponible');
                }
                
                const autoInit = new SupabaseAutoInit(SUPABASE_CONFIG);
                const status = autoInit.getStatus();
                
                return {
                    success: true,
                    message: 'Module auto-init disponible',
                    details: status
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testMonitoring() {
            try {
                if (typeof SupabaseMonitor === 'undefined') {
                    throw new Error('SupabaseMonitor non disponible');
                }
                
                const monitor = new SupabaseMonitor(SUPABASE_CONFIG);
                const metrics = monitor.getMetrics();
                
                return {
                    success: true,
                    message: 'Module monitoring disponible',
                    details: metrics
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testSynchronization() {
            try {
                if (typeof SupabaseSync === 'undefined') {
                    throw new Error('SupabaseSync non disponible');
                }
                
                // Test basique de création d'instance
                const sync = new SupabaseSync(SUPABASE_CONFIG, null);
                const stats = sync.getStats();
                
                return {
                    success: true,
                    message: 'Module synchronisation disponible',
                    details: stats
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testMigration() {
            try {
                if (typeof SupabaseMigration === 'undefined') {
                    throw new Error('SupabaseMigration non disponible');
                }
                
                const migration = new SupabaseMigration(SUPABASE_CONFIG, null);
                const migrations = migration.getAvailableMigrations();
                
                return {
                    success: true,
                    message: `${migrations.length} migrations disponibles`,
                    details: migrations
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testDatabaseOperations() {
            try {
                // Test des opérations de base avec localStorage
                const testData = {
                    id: 'test-' + Date.now(),
                    content: 'Test message',
                    timestamp: new Date().toISOString()
                };
                
                // Sauvegarder
                localStorage.setItem('test_integration', JSON.stringify([testData]));
                
                // Charger
                const loaded = JSON.parse(localStorage.getItem('test_integration') || '[]');
                
                // Nettoyer
                localStorage.removeItem('test_integration');
                
                const success = loaded.length === 1 && loaded[0].id === testData.id;
                
                return {
                    success,
                    message: success ? 'Opérations CRUD fonctionnelles' : 'Erreur opérations CRUD'
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        async function testErrorHandling() {
            try {
                // Test de gestion d'erreur basique
                const testError = new Error('Test error');
                
                // Vérifier que les erreurs sont bien capturées
                let errorCaught = false;
                try {
                    throw testError;
                } catch (e) {
                    errorCaught = true;
                }
                
                return {
                    success: errorCaught,
                    message: errorCaught ? 'Gestion d\'erreurs fonctionnelle' : 'Problème gestion d\'erreurs'
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        }

        // Afficher le résumé
        function showSummary() {
            const summary = document.getElementById('summary');
            const summaryContent = document.getElementById('summaryContent');
            
            const successRate = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
            
            summaryContent.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div style="text-align: center;">
                        <h4>Tests Total</h4>
                        <div style="font-size: 2em; color: #333;">${totalTests}</div>
                    </div>
                    <div style="text-align: center;">
                        <h4>Réussis</h4>
                        <div style="font-size: 2em; color: #28a745;">${passedTests}</div>
                    </div>
                    <div style="text-align: center;">
                        <h4>Échoués</h4>
                        <div style="font-size: 2em; color: #dc3545;">${failedTests}</div>
                    </div>
                    <div style="text-align: center;">
                        <h4>Taux de Réussite</h4>
                        <div style="font-size: 2em; color: ${successRate >= 80 ? '#28a745' : successRate >= 60 ? '#ffc107' : '#dc3545'};">${successRate}%</div>
                    </div>
                </div>
            `;
            
            summary.style.display = 'block';
        }

        // Fonctions utilitaires
        function log(message, level = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(`[${level.toUpperCase()}] ${message}`);
        }

        function clearLogs() {
            document.getElementById('testLog').innerHTML = '';
        }

        function exportResults() {
            const results = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: totalTests,
                    passed: passedTests,
                    failed: failedTests,
                    successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0
                },
                tests: testResults
            };
            
            const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `test-results-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            log('📋 Résultats exportés', 'info');
        }
    </script>
</body>
</html>
