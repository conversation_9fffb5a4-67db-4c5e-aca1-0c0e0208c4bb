/**
 * Module de gestion de la documentation workflow IPT
 * Système complet avec contrôles administrateur
 */

class WorkflowDocumentation {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.isAdmin = false;
        this.workflowSteps = [];
        this.editMode = false;
    }

    async initialize() {
        console.log('📋 Initialisation du gestionnaire de documentation workflow...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            // Vérifier les permissions admin
            this.checkAdminPermissions();

            // Charger les données du workflow
            await this.loadWorkflowData();

            // Rendre le tableau
            this.renderWorkflowTable();

            console.log('✅ Gestionnaire de documentation workflow initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation documentation workflow:', error);
            this.loadStaticData();
        }
    }

    checkAdminPermissions() {
        if (!this.currentUser) return;

        const authorizedUsers = [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
        
        const userRole = this.currentUser.user_metadata?.role_ipt;
        this.isAdmin = authorizedUsers.includes(this.currentUser.email) || userRole === 'admin_systeme';
    }

    async loadWorkflowData() {
        try {
            // Charger depuis la base de données si disponible
            const { data: steps, error } = await this.supabase
                .from('workflow_documentation_steps')
                .select('*')
                .order('step_number');

            if (error && error.code !== 'PGRST116') { // Table n'existe pas
                throw error;
            }

            if (steps && steps.length > 0) {
                this.workflowSteps = steps;
            } else {
                // Charger les données par défaut
                this.loadDefaultWorkflowData();
            }

        } catch (error) {
            console.error('Erreur chargement workflow:', error);
            this.loadDefaultWorkflowData();
        }
    }

    loadDefaultWorkflowData() {
        this.workflowSteps = [
            {
                step_number: 1,
                step_name: "Création de la Demande Initiale",
                step_description: "Le donneur/bénéficiaire crée la demande avec tous les documents justificatifs",
                responsible_role: "beneficiaire",
                input_documents: ["Facture", "BL (Bon de Livraison)", "Lettre du don"],
                actions: [
                    "Saisie des informations du don",
                    "Upload des documents justificatifs",
                    "Validation des données saisies",
                    "Soumission de la demande"
                ],
                output_documents: ["Accusé de réception", "Numéro de dossier"],
                next_role: "beneficiaire"
            },
            {
                step_number: 2,
                step_name: "Création du BP Bénéficiaire",
                step_description: "Le bénéficiaire génère le Bon de Prélèvement pour validation",
                responsible_role: "beneficiaire",
                input_documents: ["Dossier de demande", "Documents justificatifs"],
                actions: [
                    "Analyse de la demande",
                    "Génération du BP du bénéficiaire",
                    "Validation des spécifications",
                    "Transmission au magasinier"
                ],
                output_documents: ["BP du bénéficiaire", "Dossier validé"],
                next_role: "magasinier"
            },
            {
                step_number: 3,
                step_name: "Vérification Magasinier",
                step_description: "Le magasinier vérifie physiquement les articles/équipements",
                responsible_role: "magasinier",
                input_documents: ["BP du bénéficiaire", "Facture", "BL", "Lettre du don"],
                actions: [
                    "Localisation physique des articles",
                    "Vérification de la conformité",
                    "Contrôle de l'état physique",
                    "Documentation des observations",
                    "Validation de la disponibilité"
                ],
                output_documents: ["Rapport de vérification", "Photos des articles", "BP validé"],
                next_role: "transitaire"
            },
            {
                step_number: 4,
                step_name: "Validation Transitaire",
                step_description: "Le transitaire valide les aspects logistiques et transport",
                responsible_role: "transitaire",
                input_documents: ["Tous documents précédents", "Rapport de vérification"],
                actions: [
                    "Analyse des conditions de transport",
                    "Évaluation des risques logistiques",
                    "Planification de la livraison",
                    "Validation des moyens de transport",
                    "Génération des instructions"
                ],
                output_documents: ["Instructions de transport", "Planning de livraison", "Autorisation de transport"],
                next_role: "receptionniste"
            },
            {
                step_number: 5,
                step_name: "Processus de Réception",
                step_description: "Le réceptionniste effectue la réception physique avec documentation complète",
                responsible_role: "receptionniste",
                input_documents: ["Documentation complète", "Instructions de transport"],
                actions: [
                    "Contrôle de la livraison",
                    "Vérification de la conformité",
                    "Signature numérique du BL",
                    "Documentation de l'état",
                    "Confirmation de réception"
                ],
                output_documents: ["BL signé", "Rapport de réception", "Photos de réception"],
                next_role: "rve"
            },
            {
                step_number: 6,
                step_name: "Attribution d'Inventaire",
                step_description: "Le RVE traite l'inventaire et génère le Bon de Sortie",
                responsible_role: "rve",
                input_documents: ["Tous documents + BL signé"],
                actions: [
                    "Attribution du numéro d'inventaire",
                    "Génération du BS (Bon de Sortie)",
                    "Création des étiquettes code-barres",
                    "Mise à jour de l'inventaire",
                    "Préparation de l'affectation"
                ],
                output_documents: ["BS (Bon de Sortie)", "Étiquettes code-barres", "Fiche d'inventaire"],
                next_role: "rve"
            },
            {
                step_number: 7,
                step_name: "Gestion des Équipements",
                step_description: "Le RVE gère l'allocation des équipements avec copie du dossier et BS",
                responsible_role: "rve",
                input_documents: ["Copie du dossier complet", "BS"],
                actions: [
                    "Affectation au service destinataire",
                    "Coordination avec le service",
                    "Installation et configuration",
                    "Formation des utilisateurs",
                    "Tests de fonctionnement"
                ],
                output_documents: ["PV d'installation", "Certificat de formation", "Rapport d'affectation"],
                next_role: "rve"
            },
            {
                step_number: 8,
                step_name: "Traitement Final",
                step_description: "Le RVE finalise le processus et prépare pour la comptabilité",
                responsible_role: "rve",
                input_documents: ["Dossier complet", "PV d'installation"],
                actions: [
                    "Validation de l'installation",
                    "Génération du tableau récapitulatif",
                    "Vérification de la complétude",
                    "Préparation des documents comptables",
                    "Contrôle qualité final"
                ],
                output_documents: ["Tableau récap des dons", "Dossier finalisé", "Documents comptables"],
                next_role: "rve"
            },
            {
                step_number: 9,
                step_name: "Transmission Comptabilité",
                step_description: "Le RVE transmet le tableau récapitulatif et BS à la comptabilité",
                responsible_role: "rve",
                input_documents: ["Tableau récap des dons", "BS", "Dossier complet"],
                actions: [
                    "Vérification finale des documents",
                    "Préparation du bordereau de transmission",
                    "Transmission à la comptabilité",
                    "Suivi de la réception",
                    "Confirmation de l'intégration"
                ],
                output_documents: ["Bordereau de transmission", "Accusé de réception comptabilité"],
                next_role: "rve"
            },
            {
                step_number: 10,
                step_name: "Archivage/Clôture",
                step_description: "Le RVE finalise et archive le processus complet de don",
                responsible_role: "rve",
                input_documents: ["Dossier complet", "Confirmation comptabilité"],
                actions: [
                    "Constitution de l'archive complète",
                    "Vérification de l'intégrité",
                    "Archivage numérique sécurisé",
                    "Génération du certificat",
                    "Clôture définitive du dossier"
                ],
                output_documents: ["Archive complète", "Certificat d'archivage", "Rapport de clôture"],
                next_role: "archive"
            }
        ];
    }

    loadStaticData() {
        this.loadDefaultWorkflowData();
        this.renderWorkflowTable();
    }

    renderWorkflowTable() {
        const tbody = document.getElementById('workflowTableBody');
        if (!tbody) return;

        tbody.innerHTML = this.workflowSteps.map(step => this.renderStepRow(step)).join('');
    }

    renderStepRow(step) {
        const roleClass = step.responsible_role.toLowerCase();
        const roleName = this.getRoleName(step.responsible_role);
        const nextRoleName = step.next_role === 'archive' ? 'Archivage' : this.getRoleName(step.next_role);

        return `
            <tr>
                <td>
                    <div class="step-number">${step.step_number}</div>
                </td>
                <td>
                    <div class="step-name">${step.step_name}</div>
                    <div class="step-description">${step.step_description}</div>
                </td>
                <td>
                    <div class="role-badge ${roleClass}">${roleName}</div>
                </td>
                <td>
                    <ul class="document-list">
                        ${step.input_documents.map(doc => `<li class="input">${doc}</li>`).join('')}
                    </ul>
                </td>
                <td>
                    <ul class="action-list">
                        ${step.actions.map(action => `<li>${action}</li>`).join('')}
                    </ul>
                </td>
                <td>
                    <ul class="document-list">
                        ${step.output_documents.map(doc => `<li class="output">${doc}</li>`).join('')}
                    </ul>
                </td>
                <td>
                    <div class="next-role">${nextRoleName}</div>
                </td>
            </tr>
        `;
    }

    getRoleName(role) {
        const roleNames = {
            'demandeur': 'Demandeur',
            'beneficiaire': 'Bénéficiaire',
            'magasinier': 'Magasinier',
            'transitaire': 'Transitaire',
            'receptionniste': 'Réceptionniste',
            'rve': 'RVE',
            'archive': 'Archivage'
        };
        return roleNames[role] || role;
    }

    openStepModal(stepData = null) {
        if (!this.isAdmin) {
            this.showAlert('Accès non autorisé', 'error');
            return;
        }

        const modal = document.getElementById('stepModal');
        const form = document.getElementById('stepForm');
        
        if (stepData) {
            // Mode édition
            document.getElementById('modalTitle').textContent = 'Modifier l\'Étape';
            this.fillStepForm(stepData);
        } else {
            // Mode création
            document.getElementById('modalTitle').textContent = 'Ajouter une Étape';
            form.reset();
            document.getElementById('stepNumber').value = this.workflowSteps.length + 1;
        }
        
        modal.style.display = 'block';
    }

    fillStepForm(stepData) {
        document.getElementById('stepNumber').value = stepData.step_number;
        document.getElementById('stepName').value = stepData.step_name;
        document.getElementById('stepDescription').value = stepData.step_description;
        document.getElementById('responsibleRole').value = stepData.responsible_role;
        document.getElementById('inputDocuments').value = stepData.input_documents.join('\n');
        document.getElementById('actions').value = stepData.actions.join('\n');
        document.getElementById('outputDocuments').value = stepData.output_documents.join('\n');
        document.getElementById('nextRole').value = stepData.next_role;
    }

    async saveStep() {
        if (!this.isAdmin) {
            this.showAlert('Accès non autorisé', 'error');
            return;
        }

        try {
            const formData = this.collectStepFormData();
            
            // Validation
            if (!this.validateStepData(formData)) {
                return;
            }

            // Sauvegarder dans la base de données si disponible
            if (this.supabase) {
                await this.saveStepToDatabase(formData);
            }

            // Mettre à jour localement
            this.updateLocalStep(formData);

            // Re-rendre le tableau
            this.renderWorkflowTable();

            // Fermer le modal
            document.getElementById('stepModal').style.display = 'none';

            this.showAlert('Étape sauvegardée avec succès', 'success');

        } catch (error) {
            console.error('Erreur sauvegarde étape:', error);
            this.showAlert('Erreur lors de la sauvegarde', 'error');
        }
    }

    collectStepFormData() {
        return {
            step_number: parseInt(document.getElementById('stepNumber').value),
            step_name: document.getElementById('stepName').value.trim(),
            step_description: document.getElementById('stepDescription').value.trim(),
            responsible_role: document.getElementById('responsibleRole').value,
            input_documents: document.getElementById('inputDocuments').value
                .split('\n')
                .map(doc => doc.trim())
                .filter(doc => doc.length > 0),
            actions: document.getElementById('actions').value
                .split('\n')
                .map(action => action.trim())
                .filter(action => action.length > 0),
            output_documents: document.getElementById('outputDocuments').value
                .split('\n')
                .map(doc => doc.trim())
                .filter(doc => doc.length > 0),
            next_role: document.getElementById('nextRole').value
        };
    }

    validateStepData(data) {
        if (!data.step_name || !data.step_description || !data.responsible_role) {
            this.showAlert('Veuillez remplir tous les champs obligatoires', 'error');
            return false;
        }

        if (data.actions.length === 0) {
            this.showAlert('Veuillez spécifier au moins une action', 'error');
            return false;
        }

        return true;
    }

    async saveStepToDatabase(stepData) {
        // Créer la table si elle n'existe pas
        await this.createWorkflowTableIfNotExists();

        const { error } = await this.supabase
            .from('workflow_documentation_steps')
            .upsert([stepData], { onConflict: 'step_number' });

        if (error) throw error;
    }

    async createWorkflowTableIfNotExists() {
        // Cette fonction serait implémentée pour créer la table si nécessaire
        // Pour l'instant, on utilise le stockage local
    }

    updateLocalStep(stepData) {
        const existingIndex = this.workflowSteps.findIndex(step => step.step_number === stepData.step_number);
        
        if (existingIndex >= 0) {
            this.workflowSteps[existingIndex] = stepData;
        } else {
            this.workflowSteps.push(stepData);
            this.workflowSteps.sort((a, b) => a.step_number - b.step_number);
        }
    }

    validateAllSteps() {
        if (!this.isAdmin) {
            this.showAlert('Accès non autorisé', 'error');
            return;
        }

        // Validation de la cohérence du workflow
        const issues = [];

        // Vérifier la séquence des étapes
        for (let i = 0; i < this.workflowSteps.length; i++) {
            const expectedNumber = i + 1;
            if (this.workflowSteps[i].step_number !== expectedNumber) {
                issues.push(`Étape ${this.workflowSteps[i].step_number} : Numérotation incorrecte (attendu: ${expectedNumber})`);
            }
        }

        // Vérifier la cohérence des rôles
        for (let i = 0; i < this.workflowSteps.length - 1; i++) {
            const currentStep = this.workflowSteps[i];
            const nextStep = this.workflowSteps[i + 1];
            
            if (currentStep.next_role !== nextStep.responsible_role && currentStep.next_role !== 'archive') {
                issues.push(`Étape ${currentStep.step_number} : Incohérence de rôle avec l'étape suivante`);
            }
        }

        if (issues.length === 0) {
            this.showAlert('✅ Workflow validé avec succès - Aucun problème détecté', 'success');
        } else {
            this.showAlert(`⚠️ ${issues.length} problème(s) détecté(s) :\n${issues.join('\n')}`, 'error');
        }
    }

    enableEditMode() {
        if (!this.isAdmin) {
            this.showAlert('Accès non autorisé', 'error');
            return;
        }

        this.editMode = !this.editMode;
        
        if (this.editMode) {
            this.showAlert('Mode édition activé - Cliquez sur les étapes pour les modifier', 'info');
            this.addEditListeners();
        } else {
            this.showAlert('Mode édition désactivé', 'info');
            this.removeEditListeners();
        }
    }

    addEditListeners() {
        const rows = document.querySelectorAll('#workflowTableBody tr');
        rows.forEach((row, index) => {
            row.style.cursor = 'pointer';
            row.addEventListener('click', () => this.openStepModal(this.workflowSteps[index]));
        });
    }

    removeEditListeners() {
        const rows = document.querySelectorAll('#workflowTableBody tr');
        rows.forEach(row => {
            row.style.cursor = 'default';
            row.replaceWith(row.cloneNode(true));
        });
    }

    resetWorkflow() {
        if (!this.isAdmin) {
            this.showAlert('Accès non autorisé', 'error');
            return;
        }

        this.loadDefaultWorkflowData();
        this.renderWorkflowTable();
        this.showAlert('Workflow réinitialisé aux valeurs par défaut', 'success');
    }

    exportDocumentation() {
        // Générer un export PDF ou Excel du workflow
        const printWindow = window.open('', '_blank');
        const tableHTML = document.querySelector('.workflow-table-container').outerHTML;
        
        printWindow.document.write(`
            <html>
                <head>
                    <title>Documentation Workflow - Gestion des Dons IPT</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        table { width: 100%; border-collapse: collapse; }
                        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                        th { background-color: #f2f2f2; }
                        .step-number { background: #4299e1; color: white; padding: 5px; border-radius: 50%; }
                        .role-badge { background: #48bb78; color: white; padding: 2px 8px; border-radius: 10px; }
                    </style>
                </head>
                <body>
                    <h1>Documentation Workflow - Gestion des Dons IPT</h1>
                    <p>Institut Pasteur de Tunis - Processus complet en 10 étapes</p>
                    ${tableHTML}
                </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.print();
    }

    showAlert(message, type = 'info') {
        if (typeof showAlert === 'function') {
            showAlert(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Instance globale
window.workflowDocumentation = new WorkflowDocumentation();
window.WorkflowDocumentation = WorkflowDocumentation;
