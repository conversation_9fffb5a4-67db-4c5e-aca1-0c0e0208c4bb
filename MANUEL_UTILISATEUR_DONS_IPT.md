# 📖 Manuel Utilisateur - Système de Gestion des Dons IPT

## 🎯 Guide Complet du Workflow en 10 Étapes

**Institut Pasteur de Tunis**  
*Version 1.0 - Système de Gestion des Dons*

---

## 📋 Table des Matières

1. [Introduction Générale](#introduction)
2. [Accès au Système](#acces-systeme)
3. [Workflow Complet - 10 Étapes](#workflow-complet)
4. [Gestion des Erreurs](#gestion-erreurs)
5. [FAQ et Support](#faq-support)

---

## 🌟 Introduction Générale {#introduction}

### Vue d'Ensemble du Processus

Le système de gestion des dons IPT automatise complètement le processus administratif des dons d'articles et d'équipements. Il suit un workflow en **10 étapes séquentielles** impliquant **7 rôles différents** avec validation hiérarchique et traçabilité complète.

### Rôles et Responsabilités

| Rôle | Responsabilité | Étapes Concernées |
|------|----------------|-------------------|
| **Demandeur** | Création et suivi des demandes | Étape 1 |
| **Responsable Approvisionnement** | Validation initiale et BP | Étape 2 |
| **Magasinier** | Vérification physique | Étape 3 |
| **Transitaire** | Validation transport/logistique | Étape 4 |
| **Réceptionniste** | Réception et signature | Étape 5 |
| **RVE** | Inventaire et affectation | Étapes 6, 7, 8 |
| **Sous-Direction Achats** | Transmission comptabilité | Étape 9 |
| **Admin Système** | Archivage final | Étape 10 |

### Flux de Documents

```
Lettre de Don → Facture → BL → BP → BS → Tableau Récap → Archive
```

---

## 🔐 Accès au Système {#acces-systeme}

### Connexion Initiale

1. **URL d'accès** : `https://votre-domaine.com/donations-registration.html`
2. **Authentification** : Via Supabase Auth (email/mot de passe)
3. **Permissions** : Automatiquement attribuées selon le rôle

### Interface Principale

Après connexion, l'utilisateur accède à :
- **Dashboard personnalisé** selon son rôle
- **Actions en attente** spécifiques à ses responsabilités
- **Historique** des dons traités
- **Notifications** en temps réel

---

## 🔄 Workflow Complet - 10 Étapes {#workflow-complet}

# ÉTAPE 1 : Création du Dossier 📝

## 👤 Responsable : Demandeur

### Objectif
Créer une nouvelle demande de don avec toutes les informations requises et les documents justificatifs.

### Prérequis
- ✅ Compte utilisateur activé avec rôle "Demandeur"
- ✅ Documents justificatifs disponibles (PDF, JPG, PNG)
- ✅ Informations complètes sur le don demandé

### Procédure Détaillée

#### 1.1 Accès à l'Interface d'Enregistrement

**Navigation :**
```
Page d'accueil → "Nouveau Don" → Interface d'enregistrement
```

**URL directe :** `donations-registration.html`

#### 1.2 Sélection du Type de Don

**Interface :** Sélection par boutons radio

![Type de Don](captures/etape1-type-don.png)

**Options disponibles :**
- 📦 **Articles / Consommables** : Réactifs, consommables de laboratoire
- 🔬 **Équipements / Matériel** : Instruments, machines, équipements lourds

**⚠️ Important :** Le choix du type détermine le workflow (9 ou 10 étapes)

#### 1.3 Saisie des Informations du Demandeur

**Champs obligatoires :**
- **Nom** et **Prénom** (pré-remplis automatiquement)
- **Email** (récupéré du profil utilisateur)
- **Service** (liste déroulante des services IPT)
- **Laboratoire/Unité** (optionnel)
- **Téléphone** (optionnel mais recommandé)

**💡 Astuce :** Les informations du profil utilisateur sont automatiquement pré-remplies.

#### 1.4 Description Détaillée du Don

**Champs obligatoires :**

1. **Motif du don** (zone de texte libre)
   ```
   Exemple : "Remplacement d'équipement défaillant pour les analyses COVID-19"
   ```

2. **Lieu d'affectation** (précis)
   ```
   Exemple : "Laboratoire Microbiologie - Salle 101 - Paillasse 3"
   ```

3. **Description détaillée** (zone de texte étendue)
   ```
   Exemple : "Microscope électronique à balayage haute résolution 
   pour analyse microbiologique avec accessoires complets"
   ```

4. **Spécifications techniques** (pour équipements uniquement)
   ```
   Exemple : "Résolution: 1nm, Voltage: 30kV, Grossissement: x1000000"
   ```

#### 1.5 Informations du Donateur

**Champs obligatoires :**
- **Nom du donateur/fournisseur**
- **Contact** (email ou téléphone)
- **Adresse complète**

**Champs optionnels :**
- **Valeur estimée** (en TND, EUR, ou USD)
- **Quantité** et **Unité**
- **Priorité** (Normale, Élevée, Urgente, Faible)
- **Date de livraison souhaitée**

#### 1.6 Upload des Documents Justificatifs

**Interface de Upload :**

![Upload Documents](captures/etape1-upload.png)

**Documents requis :**

1. **📄 Lettre de Don** (OBLIGATOIRE)
   - Format : PDF, DOC, DOCX
   - Contenu : Lettre officielle du donateur

2. **🧾 Facture** (OPTIONNEL)
   - Format : PDF, JPG, PNG
   - Contenu : Facture ou devis des articles

3. **📦 Bon de Livraison** (OBLIGATOIRE si disponible)
   - Format : PDF, JPG, PNG
   - Contenu : BL signé par le transporteur

4. **📋 Bon de Prélèvement** (OPTIONNEL)
   - Format : PDF, JPG, PNG
   - Contenu : BP pour les équipements

**Méthodes d'upload :**
- **Clic** sur la zone d'upload
- **Glisser-déposer** les fichiers
- **Sélection multiple** possible

**Contraintes techniques :**
- Taille max : 10 MB par fichier
- Formats acceptés : PDF, JPG, JPEG, PNG, DOC, DOCX

#### 1.7 Validation et Soumission

**Vérifications automatiques :**
- ✅ Tous les champs obligatoires remplis
- ✅ Au moins un document uploadé
- ✅ Format et taille des fichiers conformes
- ✅ Email valide

**Options de sauvegarde :**

1. **💾 Sauvegarder en brouillon**
   - Sauvegarde automatique toutes les 30 secondes
   - Possibilité de reprendre plus tard
   - Statut : "brouillon"

2. **📤 Soumettre la demande**
   - Validation finale des données
   - Génération automatique du numéro de don
   - Initialisation du workflow
   - Statut : "soumis"

#### 1.8 Confirmation et Suivi

**Après soumission :**
- ✅ **Numéro de don généré** : Format `DON-YYYY-NNNN`
- ✅ **Email de confirmation** envoyé automatiquement
- ✅ **Workflow initialisé** avec la première étape
- ✅ **Notification** au Responsable Approvisionnement

**Suivi possible via :**
- Dashboard personnel
- Page de gestion des dons
- Notifications email

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Champ requis manquant" | Champ obligatoire vide | Remplir tous les champs marqués * |
| "Fichier trop volumineux" | Fichier > 10MB | Compresser ou diviser le fichier |
| "Format non supporté" | Extension non autorisée | Convertir en PDF, JPG ou PNG |
| "Email invalide" | Format email incorrect | Vérifier la syntaxe de l'email |
| "Erreur de sauvegarde" | Problème réseau/serveur | Réessayer ou contacter le support |

### Documents Générés Automatiquement

1. **Accusé de réception** (PDF)
   - Numéro de don
   - Résumé de la demande
   - Prochaines étapes

2. **Notification email** au demandeur
   - Confirmation de soumission
   - Numéro de suivi
   - Délai estimé de traitement

### Points de Contrôle

- ✅ **Validation des données** : Contrôle automatique des champs
- ✅ **Vérification des documents** : Format et taille
- ✅ **Génération du numéro** : Unique et séquentiel
- ✅ **Initialisation du workflow** : Première étape activée

---

# ÉTAPE 2 : Création du Bon de Prélèvement 📋

## 👤 Responsable : Responsable Approvisionnement

### Objectif
Valider la demande initiale et créer le Bon de Prélèvement (BP) si nécessaire pour les équipements.

### Prérequis
- ✅ Don au statut "soumis"
- ✅ Rôle "Responsable Approvisionnement"
- ✅ Accès à l'interface de workflow

### Procédure Détaillée

#### 2.1 Accès aux Dons en Attente

**Navigation :**
```
Dashboard → "Actions Requises" → Dons en attente de validation
```

**URL directe :** `donations-workflow.html`

**Interface :**

![Workflow Approvisionnement](captures/etape2-workflow.png)

#### 2.2 Examen du Dossier

**Informations disponibles :**
- 📋 **Détails complets** du don
- 📄 **Documents uploadés** par le demandeur
- 👤 **Informations du demandeur**
- 🏢 **Service demandeur**
- ⏰ **Date de soumission**

**Actions possibles :**
- 👁️ **Visualiser** les documents
- 📥 **Télécharger** les pièces justificatives
- 📝 **Ajouter des commentaires**

#### 2.3 Validation de la Demande

**Critères de validation :**
- ✅ **Conformité** de la demande aux procédures IPT
- ✅ **Complétude** des informations fournies
- ✅ **Pertinence** du don pour le service demandeur
- ✅ **Disponibilité** budgétaire si applicable

**Interface de validation :**

![Validation Approvisionnement](captures/etape2-validation.png)

**Options de décision :**

1. **✅ Approuver**
   - Commentaires optionnels
   - Passage à l'étape suivante
   - Notification automatique

2. **❌ Refuser**
   - Commentaires obligatoires
   - Justification du refus
   - Notification au demandeur

#### 2.4 Création du Bon de Prélèvement

**Conditions de création du BP :**
- Don de type "équipement"
- Validation approuvée
- Équipement nécessitant un prélèvement physique

**Génération automatique :**
- **Numéro BP** : Format `BP-YYYY-NNNN`
- **Date de création** : Automatique
- **Référence don** : Lien avec le don original
- **Responsable** : Responsable Approvisionnement

**Contenu du BP :**
```
BON DE PRÉLÈVEMENT N° BP-2024-0001

Don N° : DON-2024-0001
Date : [Date automatique]
Service demandeur : [Service]
Responsable : [Nom du responsable]

Description de l'équipement :
[Description détaillée]

Spécifications techniques :
[Spécifications si disponibles]

Lieu de prélèvement : [À remplir par le magasinier]
Date de prélèvement : [À remplir par le magasinier]

Signature Responsable Approvisionnement : [Signature numérique]
```

#### 2.5 Transmission à l'Étape Suivante

**Actions automatiques :**
- ✅ **Mise à jour du statut** : "valide_appro"
- ✅ **Notification** au Magasinier
- ✅ **Historique** de l'action enregistré
- ✅ **Email** de confirmation envoyé

**Workflow activé :**
- Étape 2 marquée "terminée"
- Étape 3 activée "en cours"
- Délai de traitement calculé

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Don non trouvé" | ID incorrect ou don supprimé | Vérifier l'ID ou contacter l'admin |
| "Permissions insuffisantes" | Rôle incorrect | Vérifier les droits d'accès |
| "Commentaires requis" | Refus sans justification | Ajouter des commentaires explicatifs |
| "Erreur génération BP" | Problème système | Réessayer ou contacter le support |

### Documents Générés Automatiquement

1. **Bon de Prélèvement** (PDF)
   - Numéro unique
   - Détails de l'équipement
   - Zones à remplir par le magasinier

2. **Notification email** au Magasinier
   - Nouveau don à traiter
   - Lien vers l'interface
   - Délai de traitement

3. **Mise à jour historique**
   - Action de validation enregistrée
   - Commentaires sauvegardés
   - Horodatage précis

### Points de Contrôle

- ✅ **Validation des permissions** : Seul le Responsable Approvisionnement peut valider
- ✅ **Contrôle de cohérence** : Vérification des données
- ✅ **Génération automatique** : BP créé si nécessaire
- ✅ **Notification en cascade** : Étape suivante informée

---

# ÉTAPE 3 : Traitement Magasinier 📦

## 👤 Responsable : Magasinier

### Objectif
Effectuer la vérification physique des articles/équipements et mettre à jour le statut dans le système.

### Prérequis
- ✅ Don au statut "valide_appro"
- ✅ Rôle "Magasinier"
- ✅ BP généré (si équipement)
- ✅ Accès physique aux articles/équipements

### Procédure Détaillée

#### 3.1 Réception de la Notification

**Notification reçue :**
- 📧 **Email automatique** avec détails du don
- 🔔 **Notification** dans l'interface système
- 📋 **BP à télécharger** (si équipement)

**Informations fournies :**
- Numéro de don
- Type d'articles/équipements
- Service demandeur
- Délai de traitement

#### 3.2 Accès à l'Interface Magasinier

**Navigation :**
```
Dashboard Magasinier → "Dons à traiter" → Sélection du don
```

**Interface spécialisée :**

![Interface Magasinier](captures/etape3-magasinier.png)

**Informations affichées :**
- 📋 **Détails complets** du don
- 📄 **Documents** associés (lettre de don, facture, etc.)
- 📦 **BP généré** (téléchargeable)
- 📍 **Localisation** des articles/équipements

#### 3.3 Vérification Physique

**Étapes de vérification :**

1. **Localisation des articles**
   - Utilisation du BP pour identifier
   - Vérification de l'emplacement
   - Contrôle de l'accessibilité

2. **Contrôle de conformité**
   - ✅ **Quantité** : Vérification du nombre d'articles
   - ✅ **État physique** : Contrôle visuel de l'état
   - ✅ **Spécifications** : Conformité aux descriptions
   - ✅ **Documentation** : Présence des manuels/certificats

3. **Évaluation de l'état**
   - **Excellent** : Neuf ou quasi-neuf
   - **Bon** : Légères traces d'usage
   - **Moyen** : Usage visible mais fonctionnel
   - **Mauvais** : Défauts importants
   - **Hors service** : Non fonctionnel

#### 3.4 Mise à Jour du Système

**Interface de saisie :**

![Saisie Magasinier](captures/etape3-saisie.png)

**Champs à remplir :**

1. **État physique** (obligatoire)
   - Sélection dans la liste déroulante
   - Commentaires détaillés si nécessaire

2. **Localisation précise** (obligatoire)
   ```
   Exemple : "Magasin Central - Allée B - Étagère 3 - Niveau 2"
   ```

3. **Observations** (optionnel)
   ```
   Exemple : "Équipement complet avec tous les accessoires. 
   Manuel d'utilisation en français disponible."
   ```

4. **Photos** (optionnel mais recommandé)
   - Upload direct depuis l'interface
   - Maximum 5 photos par don
   - Formats : JPG, PNG

#### 3.5 Validation du Traitement

**Contrôles avant validation :**
- ✅ **Vérification complète** effectuée
- ✅ **État physique** renseigné
- ✅ **Localisation** précise indiquée
- ✅ **Observations** saisies si nécessaire

**Options de validation :**

1. **✅ Valider le traitement**
   - Articles conformes et disponibles
   - Passage à l'étape Transitaire
   - Notification automatique

2. **⚠️ Signaler un problème**
   - Articles non conformes ou manquants
   - Retour au Responsable Approvisionnement
   - Justification obligatoire

3. **❌ Refuser le don**
   - Articles inutilisables ou dangereux
   - Arrêt du processus
   - Notification au demandeur

#### 3.6 Génération des Documents

**Documents créés automatiquement :**

1. **Rapport de vérification** (PDF)
   ```
   RAPPORT DE VÉRIFICATION MAGASINIER
   
   Don N° : DON-2024-0001
   Date de vérification : [Date]
   Magasinier : [Nom]
   
   Articles vérifiés :
   - Description : [Description]
   - Quantité vérifiée : [Nombre]
   - État physique : [État]
   - Localisation : [Emplacement]
   
   Observations :
   [Commentaires détaillés]
   
   Photos jointes : [Nombre]
   
   Signature numérique : [Signature]
   ```

2. **Mise à jour du BP** (si équipement)
   - Ajout des informations de vérification
   - Signature numérique du magasinier
   - Horodatage de l'intervention

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Articles non trouvés" | Localisation incorrecte | Recherche étendue ou contact fournisseur |
| "État non conforme" | Dégradation pendant stockage | Signalement et évaluation de réparation |
| "Quantité incorrecte" | Erreur de livraison | Vérification avec le fournisseur |
| "Documentation manquante" | Manuels/certificats absents | Contact fournisseur pour complément |
| "Erreur de saisie" | Problème technique | Sauvegarde et réessai |

### Documents Générés Automatiquement

1. **Rapport de vérification magasinier** (PDF)
2. **Photos de vérification** (si prises)
3. **Mise à jour du BP** avec signatures
4. **Notification email** au Transitaire

### Points de Contrôle

- ✅ **Vérification physique obligatoire** : Contrôle de tous les articles
- ✅ **Documentation complète** : État et localisation renseignés
- ✅ **Traçabilité** : Toutes les actions enregistrées
- ✅ **Qualité** : Validation de l'état avant transmission

---

# ÉTAPE 4 : Validation Transitaire 🚛

## 👤 Responsable : Transitaire

### Objectif
Contrôler la conformité transport/logistique et valider le transfert vers la réception.

### Prérequis
- ✅ Don au statut "chez_magasinier" (validé)
- ✅ Rôle "Transitaire"
- ✅ Rapport de vérification magasinier disponible
- ✅ Planning de transport établi

### Procédure Détaillée

#### 4.1 Réception de la Notification

**Notification automatique :**
- 📧 **Email** avec détails du don validé par le magasinier
- 📋 **Rapport de vérification** en pièce jointe
- 📅 **Délai de traitement** recommandé
- 🚛 **Informations logistiques** si disponibles

#### 4.2 Accès à l'Interface Transitaire

**Navigation :**
```
Dashboard Transitaire → "Validations en attente" → Sélection du don
```

**Interface spécialisée :**

![Interface Transitaire](captures/etape4-transitaire.png)

**Informations disponibles :**
- 📦 **Détails des articles** avec état physique
- 📍 **Localisation actuelle** (magasin)
- 🎯 **Destination finale** (service demandeur)
- 📋 **Rapport magasinier** complet
- 📷 **Photos** de vérification

#### 4.3 Contrôle de Conformité Transport

**Vérifications obligatoires :**

1. **Conformité logistique**
   - ✅ **Dimensions** : Compatibilité avec moyens de transport
   - ✅ **Poids** : Respect des limites de charge
   - ✅ **Fragilité** : Évaluation des risques de transport
   - ✅ **Conditions spéciales** : Température, humidité, etc.

2. **Documentation transport**
   - ✅ **Bon de livraison** original disponible
   - ✅ **Assurance transport** si nécessaire
   - ✅ **Autorisations spéciales** (équipements sensibles)
   - ✅ **Instructions de manutention**

3. **Planning et ressources**
   - ✅ **Disponibilité véhicule** adapté
   - ✅ **Personnel de manutention** suffisant
   - ✅ **Créneaux de livraison** compatibles
   - ✅ **Équipements de protection** si nécessaire

#### 4.4 Évaluation des Risques

**Grille d'évaluation :**

| Critère | Faible | Moyen | Élevé |
|---------|--------|-------|-------|
| **Fragilité** | Articles robustes | Équipements standards | Instruments précis |
| **Valeur** | < 1000 TND | 1000-10000 TND | > 10000 TND |
| **Urgence** | Normale | Élevée | Urgente |
| **Complexité** | Transport simple | Manutention spéciale | Installation requise |

**Actions selon le niveau de risque :**
- **Faible** : Transport standard
- **Moyen** : Précautions renforcées
- **Élevé** : Transport spécialisé requis

#### 4.5 Validation ou Refus

**Interface de décision :**

![Validation Transitaire](captures/etape4-validation.png)

**Options disponibles :**

1. **✅ Valider le transport**
   - Conditions de transport acceptables
   - Planning de livraison établi
   - Passage à l'étape Réception

2. **⚠️ Valider avec conditions**
   - Transport possible avec précautions
   - Instructions spéciales requises
   - Délai de livraison modifié

3. **❌ Refuser le transport**
   - Conditions de transport inadéquates
   - Risques trop élevés
   - Retour au magasinier pour modification

**Champs obligatoires :**
- **Décision** : Validation ou refus
- **Commentaires** : Justification détaillée
- **Instructions transport** : Précautions spéciales
- **Date de livraison prévue** : Planning établi

#### 4.6 Génération des Instructions

**Document créé automatiquement :**

```
INSTRUCTIONS DE TRANSPORT

Don N° : DON-2024-0001
Date de validation : [Date]
Transitaire : [Nom]

ARTICLES À TRANSPORTER :
- Description : [Détails]
- Quantité : [Nombre]
- État : [État physique]
- Localisation actuelle : [Magasin]

DESTINATION :
- Service : [Service demandeur]
- Adresse précise : [Localisation]
- Contact réception : [Réceptionniste]

INSTRUCTIONS SPÉCIALES :
[Instructions détaillées]

CONDITIONS DE TRANSPORT :
- Véhicule requis : [Type]
- Personnel : [Nombre]
- Équipements : [Matériel spécial]
- Précautions : [Mesures de sécurité]

PLANNING :
- Date de collecte : [Date]
- Heure de collecte : [Heure]
- Date de livraison : [Date]
- Heure de livraison : [Heure]

Signature transitaire : [Signature numérique]
```

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Transport impossible" | Articles trop volumineux | Recherche transport spécialisé |
| "Destination inaccessible" | Problème d'accès | Coordination avec le service |
| "Planning incompatible" | Conflits d'horaires | Reprogrammation de la livraison |
| "Équipement manquant" | Matériel spécial requis | Acquisition ou location |
| "Autorisation manquante" | Équipements réglementés | Demande d'autorisation préalable |

### Documents Générés Automatiquement

1. **Instructions de transport** (PDF)
2. **Planning de livraison** (PDF)
3. **Notification email** au Réceptionniste
4. **Mise à jour du statut** dans le système

### Points de Contrôle

- ✅ **Évaluation des risques** : Analyse complète des conditions
- ✅ **Conformité réglementaire** : Respect des normes transport
- ✅ **Planning optimisé** : Coordination des ressources
- ✅ **Instructions claires** : Documentation précise pour l'exécution

---

# ÉTAPE 5 : Réception 📦

## 👤 Responsable : Réceptionniste

### Objectif
Effectuer la réception physique des articles/équipements avec signature numérique du bon de livraison et documentation complète.

### Prérequis
- ✅ Don au statut "chez_transitaire" (validé)
- ✅ Rôle "Réceptionniste"
- ✅ Instructions de transport reçues
- ✅ Livraison programmée et confirmée

### Procédure Détaillée

#### 5.1 Préparation de la Réception

**Notification de livraison :**
- 📧 **Email automatique** avec planning de livraison
- 📋 **Instructions de transport** en pièce jointe
- 📅 **Créneaux de réception** confirmés
- 📞 **Contact transporteur** si nécessaire

**Préparatifs nécessaires :**
- ✅ **Espace de réception** préparé et dégagé
- ✅ **Équipements de manutention** disponibles
- ✅ **Personnel d'assistance** mobilisé si nécessaire
- ✅ **Matériel de contrôle** (balance, mètre, etc.)

#### 5.2 Accès à l'Interface de Réception

**Navigation :**
```
Dashboard Réceptionniste → "Livraisons du jour" → Sélection du don
```

**Interface spécialisée :**

![Interface Réception](captures/etape5-reception.png)

**Informations affichées :**
- 🚛 **Détails de livraison** (transporteur, heure prévue)
- 📦 **Articles attendus** avec quantités
- 📋 **Instructions spéciales** du transitaire
- 📄 **Documents** à vérifier (BL, facture, etc.)

#### 5.3 Contrôle de Livraison

**Vérifications à l'arrivée :**

1. **Contrôle du transporteur**
   - ✅ **Identité** : Vérification des documents du chauffeur
   - ✅ **Véhicule** : Conformité avec les instructions
   - ✅ **Bon de livraison** : Présence et conformité
   - ✅ **État du véhicule** : Propreté et conditions de transport

2. **Contrôle des articles**
   - ✅ **Quantité** : Décompte précis des articles
   - ✅ **État d'emballage** : Vérification de l'intégrité
   - ✅ **Étiquetage** : Conformité des identifications
   - ✅ **Documents joints** : Manuels, certificats, etc.

3. **Contrôle de conformité**
   - ✅ **Correspondance** : Articles vs bon de livraison
   - ✅ **Spécifications** : Conformité aux descriptions
   - ✅ **État apparent** : Absence de dommages visibles
   - ✅ **Accessoires** : Présence des éléments annexes

#### 5.4 Documentation de la Réception

**Interface de saisie :**

![Saisie Réception](captures/etape5-saisie.png)

**Champs obligatoires :**

1. **Date et heure de réception** (automatique)
2. **État de la livraison**
   - **Conforme** : Tout est en ordre
   - **Conforme avec réserves** : Problèmes mineurs
   - **Non conforme** : Problèmes majeurs

3. **Détails par article**
   ```
   Article 1 :
   - Quantité reçue : [Nombre]
   - État de l'emballage : [Bon/Endommagé]
   - État apparent : [Conforme/Non conforme]
   - Observations : [Commentaires]
   ```

4. **Photos de réception** (recommandées)
   - Vue d'ensemble de la livraison
   - Détails des articles
   - Dommages éventuels
   - État des emballages

#### 5.5 Signature Numérique du BL

**Interface de signature :**

![Signature Numérique](captures/etape5-signature.png)

**Processus de signature :**

1. **Affichage du BL** à signer
2. **Zone de signature tactile** activée
3. **Signature manuscrite** sur tablette/écran tactile
4. **Validation** de la signature
5. **Génération automatique** du BL signé

**Informations ajoutées automatiquement :**
- Date et heure de signature
- Nom du réceptionniste
- Statut de la réception
- Observations éventuelles

#### 5.6 Finalisation de la Réception

**Actions automatiques :**
- ✅ **Mise à jour du statut** : "receptionne"
- ✅ **Génération du BL signé** (PDF)
- ✅ **Notification** au RVE pour inventaire
- ✅ **Email de confirmation** au demandeur
- ✅ **Archivage** des documents de réception

**Documents remis au transporteur :**
- Copie du BL signé
- Accusé de réception
- Décharge de responsabilité

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Articles manquants" | Livraison incomplète | Réserves sur le BL et contact fournisseur |
| "Dommages constatés" | Transport défaillant | Photos et refus partiel |
| "Non-conformité" | Erreur de livraison | Vérification et contact transitaire |
| "Transporteur non autorisé" | Erreur de planning | Vérification identité et autorisation |
| "Signature impossible" | Problème technique | Signature papier et scan ultérieur |

### Documents Générés Automatiquement

1. **Bon de livraison signé** (PDF)
2. **Rapport de réception** (PDF)
3. **Photos de réception** (si prises)
4. **Notification email** au RVE

### Points de Contrôle

- ✅ **Contrôle exhaustif** : Vérification de tous les articles
- ✅ **Documentation complète** : Photos et observations
- ✅ **Signature sécurisée** : Authentification numérique
- ✅ **Traçabilité** : Enregistrement de toutes les actions

---

# ÉTAPE 6 : Création du Bon de Sortie 📋

## 👤 Responsable : RVE (Responsable Volet Équipement)

### Objectif
Attribuer le numéro d'inventaire, générer le Bon de Sortie (BS) pour affectation et créer les étiquettes code-barres.

### Prérequis
- ✅ Don au statut "receptionne"
- ✅ Rôle "RVE"
- ✅ Réception confirmée par le réceptionniste
- ✅ Accès au système d'inventaire

### Procédure Détaillée

#### 6.1 Notification et Accès

**Notification automatique :**
- 📧 **Email** avec détails de la réception
- 📋 **Rapport de réception** en pièce jointe
- 📷 **Photos** de la réception
- ⏰ **Délai de traitement** : 48h recommandées

**Navigation :**
```
Dashboard RVE → "Inventaires en attente" → Sélection du don
```

#### 6.2 Interface d'Inventaire RVE

**Informations disponibles :**

![Interface RVE](captures/etape6-rve.png)

- 📦 **Détails complets** du don reçu
- 📋 **Rapport de réception** avec photos
- 🎯 **Service destinataire** final
- 📍 **Localisation actuelle** (zone de réception)

#### 6.3 Attribution du Numéro d'Inventaire

**Génération automatique :**
- **Format** : `INV-YYYY-NNNN`
- **Séquence** : Numérotation continue par année
- **Unicité** : Contrôle automatique des doublons

**Exemple :**
```
Numéro d'inventaire : INV-2024-0156
Date d'attribution : 15/03/2024
Don associé : DON-2024-0001
RVE responsable : Dr. Ahmed Benali
```

#### 6.4 Saisie des Informations d'Inventaire

**Interface de saisie :**

![Saisie Inventaire](captures/etape6-inventaire.png)

**Champs obligatoires :**

1. **Informations techniques** (pour équipements)
   ```
   - Marque : [Fabricant]
   - Modèle : [Référence]
   - Numéro de série : [S/N]
   - Année de fabrication : [YYYY]
   ```

2. **Spécifications détaillées**
   ```
   - Caractéristiques techniques : [JSON structuré]
   - Accessoires inclus : [Liste]
   - Documentation : [Manuels, certificats]
   - Logiciels : [Si applicable]
   ```

3. **Affectation prévue**
   ```
   - Service destinataire : [Service final]
   - Laboratoire : [Unité spécifique]
   - Emplacement prévu : [Localisation précise]
   - Responsable service : [Contact]
   ```

#### 6.5 Génération du Bon de Sortie

**Création automatique du BS :**

```
BON DE SORTIE N° BS-2024-0001

Date : [Date automatique]
Don N° : DON-2024-0001
Inventaire N° : INV-2024-0156

ARTICLES SORTANTS :
- Description : [Description complète]
- Quantité : [Nombre]
- État : [État physique]
- Valeur : [Valeur estimée]

ORIGINE :
- Localisation actuelle : Zone de réception
- Responsable : Réceptionniste

DESTINATION :
- Service : [Service destinataire]
- Laboratoire : [Laboratoire]
- Emplacement : [Localisation précise]
- Responsable : [Contact service]

TRANSPORT :
- Date prévue : [Date]
- Moyen : [Interne/Externe]
- Responsable transport : [Nom]

Signature RVE : [Signature numérique]
Date de création : [Date/Heure]
```

#### 6.6 Génération des Étiquettes Code-Barres

**Système d'étiquetage :**

![Étiquettes Code-Barres](captures/etape6-etiquettes.png)

**Contenu des étiquettes :**
```
┌─────────────────────────────┐
│ INSTITUT PASTEUR DE TUNIS   │
│                             │
│ ████ ████ ████ ████ ████   │ ← Code-barres
│ INV-2024-0156               │
│                             │
│ [Description courte]        │
│ Service : [Service]         │
│ Date : [Date]               │
└─────────────────────────────┘
```

**Spécifications techniques :**
- **Format** : Code 128 ou QR Code
- **Taille** : 50x30mm (standard)
- **Matériau** : Étiquettes adhésives résistantes
- **Impression** : Imprimante thermique ou laser

**Informations encodées :**
- Numéro d'inventaire
- Numéro de don associé
- Service destinataire
- Date d'attribution

#### 6.7 Impression et Application

**Processus d'impression :**
1. **Génération automatique** du fichier d'impression
2. **Aperçu** des étiquettes avant impression
3. **Impression** sur imprimante dédiée
4. **Vérification** de la qualité d'impression

**Application des étiquettes :**
- **Emplacement** : Visible et accessible
- **Protection** : Film plastique si nécessaire
- **Duplication** : Étiquette de secours si équipement critique

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Numéro déjà attribué" | Problème de séquence | Régénération automatique |
| "Imprimante indisponible" | Panne matérielle | Impression différée ou manuelle |
| "Code-barres illisible" | Qualité d'impression | Réimpression avec paramètres ajustés |
| "Service inexistant" | Erreur de saisie | Vérification et correction |
| "Emplacement occupé" | Conflit de localisation | Recherche d'alternative |

### Documents Générés Automatiquement

1. **Bon de Sortie** (PDF)
2. **Étiquettes code-barres** (format imprimante)
3. **Fiche d'inventaire** (PDF)
4. **Notification email** au service destinataire

### Points de Contrôle

- ✅ **Numérotation unique** : Contrôle automatique des doublons
- ✅ **Informations complètes** : Toutes les données techniques saisies
- ✅ **Étiquetage conforme** : Code-barres lisible et durable
- ✅ **Traçabilité** : Lien complet don → inventaire → affectation

---

# ÉTAPE 7 : Traitement RVE 🎯

## 👤 Responsable : Responsable Volet Équipement (RVE)

### Objectif
Effectuer l'affectation définitive au service/laboratoire destinataire, mettre à jour l'inventaire et procéder aux configurations nécessaires.

### Prérequis
- ✅ Don au statut "inventorie"
- ✅ BS généré et étiquettes créées
- ✅ Service destinataire confirmé
- ✅ Emplacement final préparé

### Procédure Détaillée

#### 7.1 Coordination avec le Service Destinataire

**Contact préalable :**
- 📞 **Appel** au responsable du service
- 📧 **Email** de confirmation avec détails
- 📅 **Planification** de la livraison interne
- 🔧 **Évaluation** des besoins d'installation

**Informations à confirmer :**
- Date et heure de livraison
- Emplacement précis d'installation
- Personnel d'assistance requis
- Besoins en configuration/formation

#### 7.2 Interface d'Affectation RVE

**Navigation :**
```
Dashboard RVE → "Affectations en cours" → Sélection du don
```

**Interface spécialisée :**

![Interface Affectation](captures/etape7-affectation.png)

**Informations disponibles :**
- 📋 **BS généré** avec tous les détails
- 🏷️ **Étiquettes** imprimées et prêtes
- 📍 **Localisation actuelle** et destination
- 👥 **Contacts** du service destinataire

#### 7.3 Transport Interne

**Organisation du transport :**

1. **Préparation**
   - ✅ **Vérification** de l'état des articles
   - ✅ **Emballage** de protection si nécessaire
   - ✅ **Matériel de transport** adapté
   - ✅ **Personnel** d'accompagnement

2. **Exécution**
   - 🚛 **Transport** vers le service destinataire
   - 📦 **Déballage** sur site
   - 🏷️ **Application** des étiquettes définitives
   - 📍 **Positionnement** à l'emplacement final

#### 7.4 Installation et Configuration

**Pour les équipements :**

1. **Installation physique**
   - ✅ **Positionnement** selon les spécifications
   - ✅ **Raccordements** (électricité, réseau, etc.)
   - ✅ **Mise en service** initiale
   - ✅ **Tests** de fonctionnement

2. **Configuration logicielle**
   - ✅ **Installation** des logiciels requis
   - ✅ **Paramétrage** selon les besoins
   - ✅ **Connexion** aux systèmes existants
   - ✅ **Tests** de compatibilité

3. **Formation utilisateur**
   - ✅ **Démonstration** des fonctionnalités
   - ✅ **Formation** du personnel utilisateur
   - ✅ **Remise** de la documentation
   - ✅ **Support** initial assuré

#### 7.5 Mise à Jour de l'Inventaire

**Interface de finalisation :**

![Finalisation Inventaire](captures/etape7-finalisation.png)

**Informations à saisir :**

1. **Localisation finale**
   ```
   - Bâtiment : [Nom du bâtiment]
   - Étage : [Niveau]
   - Salle : [Numéro/Nom]
   - Position précise : [Description]
   ```

2. **État après installation**
   ```
   - État fonctionnel : [Opérationnel/En test/En panne]
   - Configuration : [Standard/Personnalisée]
   - Accessibilité : [Libre/Restreinte]
   - Maintenance : [Programmée/À la demande]
   ```

3. **Responsabilités**
   ```
   - Responsable principal : [Nom/Email]
   - Utilisateurs autorisés : [Liste]
   - Contact maintenance : [Service/Externe]
   - Garantie : [Durée/Conditions]
   ```

#### 7.6 Validation de l'Affectation

**Contrôles finaux :**
- ✅ **Installation complète** et fonctionnelle
- ✅ **Formation** des utilisateurs effectuée
- ✅ **Documentation** remise et comprise
- ✅ **Tests** de fonctionnement validés

**Signature d'acceptation :**
- **Responsable service** : Confirmation de réception
- **RVE** : Validation de l'installation
- **Utilisateur principal** : Acceptation de la formation

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Emplacement inadéquat" | Contraintes techniques | Recherche d'alternative |
| "Installation impossible" | Problème technique | Support technique spécialisé |
| "Formation insuffisante" | Complexité de l'équipement | Formation complémentaire |
| "Tests non concluants" | Défaut de fonctionnement | Diagnostic et réparation |
| "Refus du service" | Non-conformité aux attentes | Médiation et solution |

### Documents Générés Automatiquement

1. **Procès-verbal d'installation** (PDF)
2. **Fiche de localisation** mise à jour
3. **Certificat de formation** (si applicable)
4. **Rapport d'affectation** (PDF)

### Points de Contrôle

- ✅ **Installation conforme** : Respect des spécifications
- ✅ **Formation complète** : Utilisateurs opérationnels
- ✅ **Documentation** : Manuels et procédures remis
- ✅ **Acceptation** : Signatures de validation obtenues

---

# ÉTAPE 8 : Récapitulatif 📊

## 👤 Responsable : RVE/Admin

### Objectif
Générer le tableau récapitulatif final, valider toutes les étapes et préparer les documents comptables.

### Prérequis
- ✅ Don au statut "affecte"
- ✅ Installation terminée et validée
- ✅ Toutes les signatures obtenues
- ✅ Documentation complète disponible

### Procédure Détaillée

#### 8.1 Génération du Tableau Récapitulatif

**Navigation :**
```
Dashboard RVE → "Récapitulatifs à générer" → Sélection du don
```

**Interface de récapitulatif :**

![Interface Récapitulatif](captures/etape8-recapitulatif.png)

**Contenu du tableau récapitulatif :**

```
TABLEAU RÉCAPITULATIF DU DON
N° DON-2024-0001

═══════════════════════════════════════════════════════════════

INFORMATIONS GÉNÉRALES
─────────────────────────────────────────────────────────────
• Numéro de don : DON-2024-0001
• Date de création : [Date]
• Type de don : [Article/Équipement]
• Statut final : AFFECTÉ

DEMANDEUR
─────────────────────────────────────────────────────────────
• Nom : [Nom Prénom]
• Service : [Service]
• Email : [Email]
• Date de demande : [Date]

DONATEUR
─────────────────────────────────────────────────────────────
• Nom : [Nom du donateur]
• Contact : [Contact]
• Adresse : [Adresse complète]

DESCRIPTION DU DON
─────────────────────────────────────────────────────────────
• Description : [Description détaillée]
• Quantité : [Nombre] [Unité]
• Valeur estimée : [Montant] [Devise]
• Spécifications : [Spécifications techniques]

WORKFLOW DE VALIDATION
─────────────────────────────────────────────────────────────
1. ✅ Création dossier      : [Date] - [Demandeur]
2. ✅ Validation Appro      : [Date] - [Responsable]
3. ✅ Traitement Magasinier : [Date] - [Magasinier]
4. ✅ Validation Transitaire: [Date] - [Transitaire]
5. ✅ Réception            : [Date] - [Réceptionniste]
6. ✅ Création BS          : [Date] - [RVE]
7. ✅ Affectation          : [Date] - [RVE]
8. ✅ Récapitulatif        : [Date] - [RVE/Admin]

DOCUMENTS ASSOCIÉS
─────────────────────────────────────────────────────────────
• Lettre de don : ✅ [Nom fichier]
• Facture : ✅ [Nom fichier]
• Bon de livraison : ✅ [Nom fichier] - Signé le [Date]
• Bon de prélèvement : ✅ [Nom fichier]
• Bon de sortie : ✅ BS-2024-0001

INVENTAIRE
─────────────────────────────────────────────────────────────
• Numéro d'inventaire : INV-2024-0156
• Date d'attribution : [Date]
• Étiquettes code-barres : ✅ Appliquées

AFFECTATION FINALE
─────────────────────────────────────────────────────────────
• Service destinataire : [Service]
• Laboratoire : [Laboratoire]
• Emplacement : [Localisation précise]
• Responsable : [Nom responsable]
• Date d'installation : [Date]
• État : OPÉRATIONNEL

SIGNATURES ET VALIDATIONS
─────────────────────────────────────────────────────────────
• Demandeur : ✅ [Date signature]
• Responsable Appro : ✅ [Date signature]
• Magasinier : ✅ [Date signature]
• Transitaire : ✅ [Date signature]
• Réceptionniste : ✅ [Date signature]
• RVE : ✅ [Date signature]
• Responsable service : ✅ [Date signature]

DÉLAIS DE TRAITEMENT
─────────────────────────────────────────────────────────────
• Durée totale : [X] jours
• Délai moyen par étape : [X] jours
• Respect des délais : ✅ CONFORME

═══════════════════════════════════════════════════════════════

Document généré automatiquement le [Date/Heure]
Par le système de gestion des dons IPT
```

#### 8.2 Validation des Données

**Contrôles automatiques :**
- ✅ **Complétude** : Toutes les étapes validées
- ✅ **Cohérence** : Données cohérentes entre étapes
- ✅ **Signatures** : Toutes les signatures présentes
- ✅ **Documents** : Tous les documents requis uploadés
- ✅ **Délais** : Respect des délais de traitement

**Contrôles manuels :**
- ✅ **Vérification visuelle** du récapitulatif
- ✅ **Validation** des informations critiques
- ✅ **Correction** des erreurs éventuelles
- ✅ **Approbation** finale du document

#### 8.3 Préparation des Documents Comptables

**Documents générés pour la comptabilité :**

1. **Fiche comptable du don**
   ```
   FICHE COMPTABLE - DON N° DON-2024-0001

   VALORISATION :
   • Valeur déclarée : [Montant] [Devise]
   • Valeur en TND : [Montant converti]
   • Date de valorisation : [Date]
   • Taux de change : [Taux si applicable]

   IMPUTATION COMPTABLE :
   • Compte d'immobilisation : [Numéro compte]
   • Centre de coût : [Service destinataire]
   • Code analytique : [Code]

   AMORTISSEMENT :
   • Durée d'amortissement : [Années]
   • Méthode : [Linéaire/Dégressive]
   • Valeur résiduelle : [Montant]
   ```

2. **Certificat de don** (pour le donateur)
   ```
   CERTIFICAT DE DON

   L'Institut Pasteur de Tunis certifie avoir reçu de :
   [Nom du donateur]
   [Adresse]

   Le don suivant :
   • Description : [Description]
   • Valeur : [Montant] [Devise]
   • Date de réception : [Date]

   Ce don a été affecté au service [Service] et est
   opérationnel depuis le [Date].

   Fait à Tunis, le [Date]
   Signature et cachet IPT
   ```

#### 8.4 Archivage Préparatoire

**Préparation du dossier d'archivage :**
- 📁 **Dossier numérique** complet constitué
- 📄 **Index des documents** généré
- 🔒 **Vérification de l'intégrité** des fichiers
- 📊 **Métadonnées** complètes renseignées

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Données manquantes" | Étape incomplète | Retour à l'étape concernée |
| "Incohérence détectée" | Erreur de saisie | Correction et revalidation |
| "Signature manquante" | Processus non finalisé | Obtention de la signature |
| "Valorisation incorrecte" | Erreur de calcul | Recalcul avec les bons taux |

### Documents Générés Automatiquement

1. **Tableau récapitulatif complet** (PDF)
2. **Fiche comptable** (PDF)
3. **Certificat de don** (PDF)
4. **Index des documents** (PDF)

### Points de Contrôle

- ✅ **Exhaustivité** : Toutes les informations présentes
- ✅ **Exactitude** : Données vérifiées et validées
- ✅ **Conformité** : Respect des procédures IPT
- ✅ **Traçabilité** : Historique complet disponible

---

# ÉTAPE 9 : Envoi Comptabilité 💰

## 👤 Responsable : Sous-Direction Achats

### Objectif
Transmettre le dossier complet à la comptabilité, vérifier les pièces justificatives et assurer le suivi de l'intégration comptable.

### Prérequis
- ✅ Don au statut "recapitulatif_genere"
- ✅ Tableau récapitulatif validé
- ✅ Documents comptables préparés
- ✅ Rôle "Sous-Direction Achats"

### Procédure Détaillée

#### 9.1 Réception du Dossier

**Notification automatique :**
- 📧 **Email** avec dossier complet en pièces jointes
- 📋 **Tableau récapitulatif** pour vérification
- 💰 **Fiche comptable** pré-remplie
- 📄 **Tous les documents** justificatifs

#### 9.2 Interface Sous-Direction Achats

**Navigation :**
```
Dashboard SD Achats → "Dossiers comptabilité" → Sélection du don
```

**Interface spécialisée :**

![Interface SD Achats](captures/etape9-sd-achats.png)

**Informations disponibles :**
- 📊 **Récapitulatif complet** du don
- 💰 **Valorisation** et imputation comptable
- 📁 **Dossier documentaire** complet
- ✅ **Checklist** de vérification

#### 9.3 Vérification des Pièces Justificatives

**Checklist de contrôle :**

```
CONTRÔLE DES PIÈCES JUSTIFICATIVES
Don N° DON-2024-0001

DOCUMENTS OBLIGATOIRES :
☐ Lettre de don originale
☐ Facture ou devis (si disponible)
☐ Bon de livraison signé
☐ Bon de prélèvement (si applicable)
☐ Bon de sortie signé
☐ Tableau récapitulatif complet

SIGNATURES REQUISES :
☐ Demandeur (création)
☐ Responsable Approvisionnement
☐ Magasinier
☐ Transitaire
☐ Réceptionniste
☐ RVE (inventaire et affectation)
☐ Responsable service destinataire

INFORMATIONS COMPTABLES :
☐ Valorisation du don
☐ Imputation comptable
☐ Centre de coût
☐ Code analytique
☐ Durée d'amortissement

CONFORMITÉ RÉGLEMENTAIRE :
☐ Respect des procédures IPT
☐ Conformité fiscale
☐ Traçabilité complète
☐ Archivage préparé
```

#### 9.4 Validation Comptable

**Interface de validation :**

![Validation Comptable](captures/etape9-validation.png)

**Champs à vérifier/compléter :**

1. **Valorisation finale**
   ```
   - Valeur en TND : [Montant]
   - Taux de change : [Si applicable]
   - Date de valorisation : [Date]
   - Méthode de valorisation : [Déclarative/Expertise]
   ```

2. **Imputation comptable**
   ```
   - Compte principal : [Numéro]
   - Sous-compte : [Numéro]
   - Centre de coût : [Code]
   - Code analytique : [Code]
   ```

3. **Paramètres d'amortissement**
   ```
   - Durée : [Années]
   - Méthode : [Linéaire/Dégressive]
   - Date de début : [Date mise en service]
   - Valeur résiduelle : [Montant]
   ```

#### 9.5 Transmission à la Comptabilité

**Préparation du dossier comptable :**

1. **Dossier physique** (si requis)
   - Impression des documents principaux
   - Classement chronologique
   - Index des pièces

2. **Dossier numérique**
   - Archive ZIP complète
   - Métadonnées structurées
   - Signature numérique du dossier

**Méthodes de transmission :**
- 📧 **Email sécurisé** avec accusé de réception
- 💾 **Transfert de fichiers** via serveur sécurisé
- 📁 **Dépôt** dans le système comptable
- 📋 **Remise physique** avec décharge

#### 9.6 Suivi de l'Intégration

**Interface de suivi :**

![Suivi Comptable](captures/etape9-suivi.png)

**Étapes de suivi :**
1. **Transmission** : Dossier envoyé à la comptabilité
2. **Réception** : Accusé de réception comptabilité
3. **Vérification** : Contrôle par le service comptable
4. **Intégration** : Saisie dans le système comptable
5. **Validation** : Validation finale comptable

**Délais de traitement :**
- Transmission : Immédiate
- Réception : 24h maximum
- Vérification : 3 jours ouvrés
- Intégration : 5 jours ouvrés
- Validation : 7 jours ouvrés

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Pièce manquante" | Document non fourni | Retour pour complément |
| "Valorisation incorrecte" | Erreur de calcul | Recalcul et correction |
| "Imputation erronée" | Mauvais compte | Correction avec comptabilité |
| "Refus comptabilité" | Non-conformité | Analyse et correction |

### Documents Générés Automatiquement

1. **Bordereau de transmission** (PDF)
2. **Accusé de réception** (PDF)
3. **Fiche de suivi** (PDF)
4. **Archive complète** (ZIP)

### Points de Contrôle

- ✅ **Complétude** : Tous les documents présents
- ✅ **Conformité** : Respect des règles comptables
- ✅ **Traçabilité** : Suivi de la transmission
- ✅ **Validation** : Intégration comptable confirmée

---

# ÉTAPE 10 : Archivage 📁

## 👤 Responsable : Admin Système

### Objectif
Effectuer l'archivage numérique définitif du dossier complet, sauvegarder tous les documents et signatures, et clôturer définitivement le processus.

### Prérequis
- ✅ Don au statut "transmis_comptabilite"
- ✅ Intégration comptable confirmée
- ✅ Tous les documents finalisés
- ✅ Rôle "Admin Système"

### Procédure Détaillée

#### 10.1 Préparation de l'Archivage

**Notification d'archivage :**
- 📧 **Email automatique** de fin de processus
- ✅ **Confirmation** de l'intégration comptable
- 📊 **Bilan complet** du traitement
- ⏰ **Délai total** de traitement calculé

#### 10.2 Interface d'Archivage

**Navigation :**
```
Dashboard Admin → "Archivage en attente" → Sélection du don
```

**Interface d'archivage :**

![Interface Archivage](captures/etape10-archivage.png)

**Vue d'ensemble du dossier :**
- 📋 **Résumé complet** du don
- 📄 **Liste exhaustive** des documents
- ✅ **Statut** de chaque étape
- 📊 **Métriques** de performance

#### 10.3 Constitution de l'Archive Définitive

**Structure de l'archive :**

```
ARCHIVE_DON-2024-0001/
├── 01_CREATION/
│   ├── demande_initiale.pdf
│   ├── lettre_don.pdf
│   ├── facture.pdf
│   └── documents_demandeur/
├── 02_VALIDATION/
│   ├── validation_appro.pdf
│   ├── bon_prelevement.pdf
│   └── commentaires_validation.pdf
├── 03_MAGASINIER/
│   ├── rapport_verification.pdf
│   ├── photos_verification/
│   └── signature_magasinier.pdf
├── 04_TRANSITAIRE/
│   ├── instructions_transport.pdf
│   ├── validation_transitaire.pdf
│   └── planning_livraison.pdf
├── 05_RECEPTION/
│   ├── bon_livraison_signe.pdf
│   ├── rapport_reception.pdf
│   ├── photos_reception/
│   └── signature_receptionniste.pdf
├── 06_INVENTAIRE/
│   ├── bon_sortie.pdf
│   ├── etiquettes_codes_barres.pdf
│   ├── fiche_inventaire.pdf
│   └── signature_rve.pdf
├── 07_AFFECTATION/
│   ├── proces_verbal_installation.pdf
│   ├── fiche_localisation.pdf
│   ├── certificat_formation.pdf
│   └── signature_service.pdf
├── 08_RECAPITULATIF/
│   ├── tableau_recapitulatif.pdf
│   ├── fiche_comptable.pdf
│   └── certificat_don.pdf
├── 09_COMPTABILITE/
│   ├── bordereau_transmission.pdf
│   ├── accuse_reception_comptabilite.pdf
│   └── confirmation_integration.pdf
├── 10_ARCHIVAGE/
│   ├── index_archive.pdf
│   ├── certificat_archivage.pdf
│   └── metadonnees.json
└── SIGNATURES/
    ├── signatures_numeriques.pdf
    └── certificats_authenticite.pdf
```

#### 10.4 Vérification de l'Intégrité

**Contrôles automatiques :**
- ✅ **Présence** de tous les documents requis
- ✅ **Intégrité** des fichiers (checksums)
- ✅ **Lisibilité** de tous les documents
- ✅ **Validité** des signatures numériques
- ✅ **Cohérence** des métadonnées

**Contrôles manuels :**
- ✅ **Vérification visuelle** de l'archive
- ✅ **Test d'ouverture** des documents principaux
- ✅ **Validation** de la structure
- ✅ **Contrôle** des permissions d'accès

#### 10.5 Génération des Métadonnées

**Fichier de métadonnées (JSON) :**

```json
{
  "don": {
    "numero": "DON-2024-0001",
    "type": "equipement",
    "date_creation": "2024-03-01T10:00:00Z",
    "date_archivage": "2024-03-15T16:30:00Z",
    "duree_traitement_jours": 14,
    "statut_final": "archive"
  },
  "demandeur": {
    "nom": "Dr. Sarah Mansouri",
    "service": "Laboratoire Microbiologie",
    "email": "<EMAIL>"
  },
  "donateur": {
    "nom": "Société MedEquip",
    "contact": "<EMAIL>",
    "valeur_don": 15000.00,
    "devise": "TND"
  },
  "workflow": {
    "etapes_completees": 10,
    "signatures_obtenues": 7,
    "documents_archives": 25,
    "delai_respecte": true
  },
  "inventaire": {
    "numero": "INV-2024-0156",
    "localisation_finale": "Lab Micro - Salle 101",
    "responsable": "Dr. Ahmed Benali",
    "etat": "operationnel"
  },
  "archivage": {
    "date": "2024-03-15T16:30:00Z",
    "admin": "<EMAIL>",
    "taille_archive": "45.2 MB",
    "checksum": "sha256:a1b2c3d4e5f6...",
    "retention_annees": 10
  }
}
```

#### 10.6 Archivage Définitif

**Stockage sécurisé :**
- 🔒 **Chiffrement** de l'archive complète
- 💾 **Sauvegarde** sur serveurs redondants
- ☁️ **Réplication** cloud sécurisée
- 📅 **Planification** de la rétention (10 ans)

**Indexation :**
- 🔍 **Index de recherche** mis à jour
- 🏷️ **Tags** et catégories appliqués
- 📊 **Statistiques** d'archivage mises à jour
- 📋 **Registre** d'archivage complété

#### 10.7 Clôture Définitive

**Actions finales :**
- ✅ **Statut** mis à jour : "archive"
- 📧 **Notifications** de clôture envoyées
- 📊 **Métriques** de performance calculées
- 🏆 **Certificat** d'archivage généré

**Certificat d'archivage :**

```
CERTIFICAT D'ARCHIVAGE NUMÉRIQUE

Don N° : DON-2024-0001
Date d'archivage : 15/03/2024 à 16:30

RÉSUMÉ DU PROCESSUS :
• Durée totale : 14 jours
• Étapes complétées : 10/10
• Documents archivés : 25
• Signatures obtenues : 7/7

CONFORMITÉ :
✅ Toutes les étapes validées
✅ Tous les documents présents
✅ Toutes les signatures obtenues
✅ Intégration comptable confirmée

ARCHIVAGE :
• Taille de l'archive : 45.2 MB
• Checksum : sha256:a1b2c3d4e5f6...
• Localisation : Serveur sécurisé IPT
• Durée de rétention : 10 ans

Ce certificat atteste que le don DON-2024-0001
a été traité conformément aux procédures IPT
et archivé de manière sécurisée et pérenne.

Fait à Tunis, le 15/03/2024
Système de Gestion des Dons IPT
Administrateur : <EMAIL>
```

### Cas d'Erreur et Solutions

| Erreur | Cause | Solution |
|--------|-------|----------|
| "Archive corrompue" | Erreur de transfert | Régénération de l'archive |
| "Document manquant" | Étape incomplète | Retour pour complément |
| "Signature invalide" | Problème de certificat | Revalidation des signatures |
| "Espace insuffisant" | Stockage plein | Extension de l'espace |

### Documents Générés Automatiquement

1. **Archive complète** (ZIP chiffrée)
2. **Index de l'archive** (PDF)
3. **Certificat d'archivage** (PDF)
4. **Rapport de clôture** (PDF)

### Points de Contrôle

- ✅ **Intégrité** : Archive complète et non corrompue
- ✅ **Sécurité** : Chiffrement et accès contrôlé
- ✅ **Pérennité** : Sauvegarde redondante assurée
- ✅ **Conformité** : Respect des obligations légales

---

## 🎯 Gestion des Erreurs {#gestion-erreurs}

### Erreurs Communes et Solutions

#### Erreurs de Connexion
- **Symptôme** : "Impossible de se connecter"
- **Cause** : Problème réseau ou serveur
- **Solution** : Vérifier la connexion, réessayer, contacter l'admin

#### Erreurs de Permissions
- **Symptôme** : "Accès refusé"
- **Cause** : Rôle insuffisant ou session expirée
- **Solution** : Vérifier les droits, se reconnecter

#### Erreurs de Validation
- **Symptôme** : "Données invalides"
- **Cause** : Champs mal remplis ou manquants
- **Solution** : Vérifier tous les champs obligatoires

#### Erreurs d'Upload
- **Symptôme** : "Échec de téléversement"
- **Cause** : Fichier trop volumineux ou format incorrect
- **Solution** : Vérifier taille et format, réessayer

### Support et Assistance

**Contact Support :**
- 📧 **Email** : <EMAIL>
- 📞 **Téléphone** : +216 71 XXX XXX
- 💬 **Chat** : Interface système (bouton aide)

**Heures de Support :**
- **Lundi-Vendredi** : 8h00-17h00
- **Urgences** : 24h/7j (admin système)

---

## ❓ FAQ et Support {#faq-support}

### Questions Fréquentes

**Q1 : Combien de temps prend le processus complet ?**
R : En moyenne 10-15 jours ouvrés selon la complexité du don.

**Q2 : Puis-je modifier une demande après soumission ?**
R : Non, mais vous pouvez ajouter des commentaires à chaque étape.

**Q3 : Comment suivre l'avancement de ma demande ?**
R : Via votre dashboard personnel ou les notifications email.

**Q4 : Que faire si un document est refusé ?**
R : Corriger selon les commentaires et re-soumettre.

**Q5 : Puis-je annuler une demande en cours ?**
R : Oui, via l'interface ou en contactant l'admin.

### Bonnes Pratiques

1. **Préparation** : Rassembler tous les documents avant de commencer
2. **Précision** : Remplir tous les champs avec précision
3. **Suivi** : Consulter régulièrement les notifications
4. **Communication** : Répondre rapidement aux demandes de clarification
5. **Documentation** : Conserver une copie de tous les documents

---

**📖 Manuel Utilisateur - Système de Gestion des Dons IPT**
*Version 1.0 - Institut Pasteur de Tunis*
*Document complet et opérationnel pour tous les utilisateurs*
