<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interface Admin Commandes</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container">
        <h1>🧪 Test Interface Admin Commandes</h1>
        
        <div class="test-section">
            <h2>Simulation Utilisateur Admin</h2>
            <button onclick="simulateAdminUser()" class="primary">Simuler Admin (admin123)</button>
            <button onclick="simulateRegularUser()" class="secondary">Simuler Utilisateur Normal</button>
            <button onclick="clearSimulation()" class="danger">Effacer Simulation</button>
        </div>

        <div id="commandsAdminContainer" class="module-zone">
            <!-- Le contenu sera généré par l'interface admin -->
        </div>

        <div class="test-info">
            <h3>Informations de Test</h3>
            <div id="testInfo">
                <p>Utilisateur actuel: <span id="currentUser">Non connecté</span></p>
                <p>Permissions: <span id="userPermissions">Aucune</span></p>
                <p>Interface chargée: <span id="interfaceStatus">Non</span></p>
            </div>
        </div>
    </div>

    <!-- Scripts nécessaires -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/commands-admin-interface.js"></script>

    <script>
        // Variables de test
        let mockUser = null;
        let adminInterface = null;

        // Simulation d'un utilisateur admin
        function simulateAdminUser() {
            mockUser = {
                email: '<EMAIL>',
                user_metadata: {
                    role_ipt: 'admin'
                }
            };

            // Simuler Supabase
            window.supabaseClient = {
                auth: {
                    getUser: () => Promise.resolve({ data: { user: mockUser } })
                },
                from: (table) => ({
                    select: () => ({
                        eq: () => Promise.resolve({ data: [], error: null })
                    }),
                    insert: () => Promise.resolve({ data: [], error: null }),
                    update: () => ({
                        eq: () => Promise.resolve({ data: [], error: null })
                    }),
                    delete: () => ({
                        eq: () => Promise.resolve({ data: [], error: null })
                    })
                })
            };

            updateTestInfo();
            initializeAdminInterface();
        }

        // Simulation d'un utilisateur normal
        function simulateRegularUser() {
            mockUser = {
                email: '<EMAIL>',
                user_metadata: {
                    role_ipt: 'user'
                }
            };

            updateTestInfo();
            initializeAdminInterface();
        }

        // Effacer la simulation
        function clearSimulation() {
            mockUser = null;
            window.supabaseClient = null;
            adminInterface = null;
            document.getElementById('commandsAdminContainer').innerHTML = '';
            updateTestInfo();
        }

        // Initialiser l'interface admin
        function initializeAdminInterface() {
            try {
                adminInterface = new CommandsAdminInterface();
                adminInterface.initialize();
                document.getElementById('interfaceStatus').textContent = 'Oui';
            } catch (error) {
                console.error('Erreur initialisation:', error);
                document.getElementById('interfaceStatus').textContent = 'Erreur: ' + error.message;
            }
        }

        // Mettre à jour les informations de test
        function updateTestInfo() {
            document.getElementById('currentUser').textContent = mockUser ? mockUser.email : 'Non connecté';
            document.getElementById('userPermissions').textContent = mockUser ? 
                (mockUser.email.includes('admin123') || mockUser.email.includes('namara') || mockUser.email.includes('rym') ? 'Admin' : 'Lecture seule') : 
                'Aucune';
        }

        // Initialisation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Page de test chargée');
            updateTestInfo();
        });
    </script>
</body>
</html>
