# 🎁 Système de Gestion des Dons IPT

## Vue d'ensemble

Le système de gestion des dons IPT permet de gérer le processus complet des dons (articles et équipements) selon la procédure officielle de l'Institut Pasteur de Tunis.

## 📋 Fonctionnalités

### Workflow des Dons
- **Articles** : Demandeur → DG → MS → Réception
- **Équipements** : Demandeur → DG → DT (avis technique) → MS → Réception → Inventaire (RVE)

### Gestion des Documents
- Demande explicative
- Lettre de don
- Bon de prélèvement (BP)
- Factures
- Bon de livraison (BL)
- Bon de sortie (BS)
- Autorisation MS
- Avis technique DT

### Suivi et Reporting
- Tableau de bord avec statistiques
- Historique complet du workflow
- Notifications automatiques
- Génération de rapports

## 🚀 Installation

### 1. Prérequis
- Projet Supabase configuré
- Tables utilisateurs existantes (`gestion_users`)
- Système d'authentification fonctionnel

### 2. Installation de la Base de Données

Exécutez les scripts SQL dans l'ordre suivant dans l'éditeur SQL de Supabase :

```sql
-- 1. Structures de base
-- Copier et exécuter : donations-complete-setup.sql

-- 2. Fonctions de workflow
-- Copier et exécuter : donations-functions.sql

-- 3. Fonctions complémentaires
-- Copier et exécuter : donations-functions-part2.sql

-- 4. Politiques de sécurité
-- Copier et exécuter : donations-rls-policies.sql

-- 5. Données de test (optionnel)
-- Copier et exécuter : donations-test-data.sql
```

### 3. Configuration du Stockage

Créez les buckets de stockage dans Supabase :

```sql
-- Bucket pour les documents de dons
INSERT INTO storage.buckets (id, name, public) 
VALUES ('documents', 'documents', false);

-- Politiques de stockage
CREATE POLICY "Authenticated users can upload documents" ON storage.objects
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can view their documents" ON storage.objects
FOR SELECT USING (auth.uid()::text = (storage.foldername(name))[1]);
```

### 4. Configuration des Rôles

Assurez-vous que les rôles suivants existent dans votre table `gestion_users` :
- `dg` : Directeur Général
- `dt` : Direction Technique
- `ms` : Ministère de la Santé
- `rve` : Responsable du Volet Équipement
- `receptionniste` : Réceptionniste du magasin
- `transitaire` : Transitaire

## 📱 Utilisation

### Interface Web

1. **Accès** : `donations-management.html`
2. **Tests** : `test-donations-system.html`

### Création d'un Don

1. Cliquer sur "➕ Nouveau Don"
2. Sélectionner le type (Article/Équipement)
3. Remplir les informations du donneur
4. Ajouter les articles/équipements
5. Joindre les documents requis
6. Soumettre au DG

### Workflow d'Approbation

#### Pour les Articles :
1. **Demandeur** : Création et soumission
2. **DG** : Validation
3. **MS** : Autorisation
4. **Réceptionniste** : Réception

#### Pour les Équipements :
1. **Demandeur** : Création et soumission
2. **DG** : Validation
3. **DT** : Avis technique
4. **MS** : Autorisation
5. **Réceptionniste** : Réception
6. **RVE** : Attribution numéro d'inventaire

## 🔧 Configuration Technique

### Tables Principales

```sql
-- Table des dons
gestion_donations
├── id (UUID)
├── numero_don (TEXT) -- DON-YYYY-NNNN
├── type_don (article|equipement)
├── statut (brouillon|soumis_dg|avis_dt|soumis_ms|approuve_ms|recu|affecte)
└── ...

-- Table des items
gestion_donation_items
├── donation_id (FK)
├── nom_item
├── numero_inventaire (pour équipements)
└── ...

-- Table des documents
gestion_donation_documents
├── donation_id (FK)
├── type_document
├── file_path (Supabase Storage)
└── ...

-- Table du workflow
gestion_donation_workflow
├── donation_id (FK)
├── etape
├── acteur_nom
└── ...
```

### Fonctions Utilitaires

```sql
-- Génération de numéros
SELECT generate_donation_number(); -- DON-2024-0001
SELECT generate_inventory_number(); -- NI-2024-000001

-- Workflow
SELECT submit_donation_to_dg(donation_id, user_email);
SELECT validate_donation_by_dg(donation_id, user_email, avis, approve);
SELECT provide_technical_advice(donation_id, user_email, avis, approve);
SELECT authorize_donation_by_ms(donation_id, user_email, decision, approve);
SELECT mark_donation_received(donation_id, user_email, observations);

-- Statistiques
SELECT * FROM donations_stats;
SELECT get_donations_statistics('2024-01-01', '2024-12-31', 'equipement');
```

### Vues Disponibles

```sql
-- Vue complète des dons
SELECT * FROM donations_complete;

-- Statistiques globales
SELECT * FROM donations_stats;
```

## 🔒 Sécurité

### Row Level Security (RLS)

- **Utilisateurs** : Accès à leurs propres dons
- **Managers** : Accès selon leur rôle dans le workflow
- **Admins** : Accès complet

### Permissions par Rôle

| Rôle | Créer | Valider | Avis Tech | Autoriser | Recevoir | Inventaire |
|------|-------|---------|-----------|-----------|----------|------------|
| User | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| DG | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| DT | ✅ | ❌ | ✅ | ❌ | ❌ | ❌ |
| MS | ✅ | ❌ | ❌ | ✅ | ❌ | ❌ |
| Réceptionniste | ✅ | ❌ | ❌ | ❌ | ✅ | ❌ |
| RVE | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ |
| Admin | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

## 📊 Monitoring et Tests

### Tests Automatisés

Utilisez `test-donations-system.html` pour :
- Vérifier la connexion Supabase
- Tester l'existence des tables
- Valider les fonctions SQL
- Tester les opérations CRUD
- Vérifier les politiques RLS

### Métriques Disponibles

- Nombre total de dons
- Répartition par type (articles/équipements)
- Statuts des dons
- Valeur totale des dons
- Temps moyen de traitement
- Taux d'approbation

## 🐛 Dépannage

### Problèmes Courants

1. **Tables manquantes** : Exécuter `donations-complete-setup.sql`
2. **Fonctions manquantes** : Exécuter `donations-functions.sql` et `donations-functions-part2.sql`
3. **Erreurs de permissions** : Vérifier les politiques RLS
4. **Upload de fichiers** : Vérifier la configuration du bucket `documents`

### Logs et Debug

```javascript
// Activer les logs détaillés
console.log('Debug mode activé');
window.donationsManager.debug = true;
```

## 📞 Support

Pour toute question ou problème :
1. Vérifier les logs de la console
2. Tester avec `test-donations-system.html`
3. Consulter la documentation Supabase
4. Contacter l'équipe de développement

## 🔄 Mises à Jour

### Version 1.0.0 (Actuelle)
- Workflow complet articles/équipements
- Gestion des documents
- Interface utilisateur
- Tests automatisés
- Politiques de sécurité

### Roadmap
- [ ] Notifications email automatiques
- [ ] Export PDF des bons de sortie
- [ ] Intégration avec système d'inventaire existant
- [ ] API REST pour intégrations externes
- [ ] Application mobile
