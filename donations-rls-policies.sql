-- Politiques RLS pour le système de gestion des dons IPT
-- À exécuter après la création des tables principales

-- =====================================================
-- ACTIVATION DE RLS SUR TOUTES LES TABLES
-- =====================================================

ALTER TABLE gestion_donations ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_workflow ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_donation_approvals ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- FONCTION UTILITAIRE POUR OBTENIR LE RÔLE UTILISATEUR
-- =====================================================

CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    -- Récupérer le rôle depuis les métadonnées utilisateur ou depuis une table
    RETURN COALESCE(
        (auth.jwt() ->> 'user_metadata' ->> 'role'),
        (SELECT role FROM gestion_users WHERE email = auth.email()),
        'user'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_user_email()
RETURNS TEXT AS $$
BEGIN
    RETURN auth.email();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_DONATIONS
-- =====================================================

-- Lecture : Tous peuvent voir leurs propres dons + managers peuvent voir tous
CREATE POLICY "donations_select_policy" ON gestion_donations
    FOR SELECT USING (
        -- L'utilisateur peut voir ses propres dons
        demandeur_email = get_user_email()
        OR
        -- Les managers et admins peuvent voir tous les dons
        get_user_role() IN ('admin', 'chef_service', 'responsable_hygiene', 'dg', 'rve', 'transitaire')
        OR
        -- Les utilisateurs impliqués dans le workflow peuvent voir le don
        created_by = get_user_email()
        OR approuve_par_dg = get_user_email()
        OR avis_technique_par = get_user_email()
        OR decision_ms_par = get_user_email()
    );

-- Insertion : Utilisateurs authentifiés peuvent créer des dons
CREATE POLICY "donations_insert_policy" ON gestion_donations
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
        AND demandeur_email = get_user_email()
        AND created_by = get_user_email()
    );

-- Mise à jour : Créateur + managers selon le statut
CREATE POLICY "donations_update_policy" ON gestion_donations
    FOR UPDATE USING (
        -- Le créateur peut modifier tant que le statut est brouillon
        (created_by = get_user_email() AND statut = 'brouillon')
        OR
        -- DG peut valider et soumettre
        (get_user_role() IN ('admin', 'dg') AND statut IN ('brouillon', 'soumis_dg'))
        OR
        -- DT peut donner avis technique pour équipements
        (get_user_role() IN ('admin', 'dt') AND type_don = 'equipement' AND statut = 'avis_dt')
        OR
        -- MS peut approuver/refuser
        (get_user_role() IN ('admin', 'ms') AND statut IN ('soumis_ms'))
        OR
        -- RVE peut gérer la réception et affectation
        (get_user_role() IN ('admin', 'rve') AND statut IN ('approuve_ms', 'en_expedition', 'recu'))
        OR
        -- Réceptionniste peut marquer comme reçu
        (get_user_role() IN ('admin', 'receptionniste') AND statut = 'en_expedition')
    );

-- Suppression : Seuls les créateurs (statut brouillon) et admins
CREATE POLICY "donations_delete_policy" ON gestion_donations
    FOR DELETE USING (
        (created_by = get_user_email() AND statut = 'brouillon')
        OR get_user_role() = 'admin'
    );

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_DONATION_ITEMS
-- =====================================================

-- Lecture : Basée sur l'accès au don parent
CREATE POLICY "donation_items_select_policy" ON gestion_donation_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                d.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'responsable_hygiene', 'dg', 'rve', 'transitaire')
                OR d.created_by = get_user_email()
            )
        )
    );

-- Insertion : Si on peut modifier le don parent
CREATE POLICY "donation_items_insert_policy" ON gestion_donation_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                (d.created_by = get_user_email() AND d.statut = 'brouillon')
                OR get_user_role() IN ('admin', 'rve')
            )
        )
    );

-- Mise à jour : Créateur (brouillon) + RVE pour inventaire
CREATE POLICY "donation_items_update_policy" ON gestion_donation_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                (d.created_by = get_user_email() AND d.statut = 'brouillon')
                OR get_user_role() IN ('admin', 'rve')
            )
        )
    );

-- Suppression : Créateur (brouillon) + admins
CREATE POLICY "donation_items_delete_policy" ON gestion_donation_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                (d.created_by = get_user_email() AND d.statut = 'brouillon')
                OR get_user_role() = 'admin'
            )
        )
    );

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_DONATION_DOCUMENTS
-- =====================================================

-- Lecture : Basée sur l'accès au don parent
CREATE POLICY "donation_documents_select_policy" ON gestion_donation_documents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                d.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'responsable_hygiene', 'dg', 'rve', 'transitaire')
                OR d.created_by = get_user_email()
            )
        )
    );

-- Insertion : Utilisateurs autorisés selon le type de document
CREATE POLICY "donation_documents_insert_policy" ON gestion_donation_documents
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
        AND uploaded_by = get_user_email()
        AND EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                d.created_by = get_user_email()
                OR get_user_role() IN ('admin', 'dg', 'dt', 'rve', 'transitaire', 'receptionniste')
            )
        )
    );

-- Mise à jour : Uploader + admins
CREATE POLICY "donation_documents_update_policy" ON gestion_donation_documents
    FOR UPDATE USING (
        uploaded_by = get_user_email()
        OR get_user_role() = 'admin'
    );

-- Suppression : Uploader + admins
CREATE POLICY "donation_documents_delete_policy" ON gestion_donation_documents
    FOR DELETE USING (
        uploaded_by = get_user_email()
        OR get_user_role() = 'admin'
    );

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_DONATION_WORKFLOW
-- =====================================================

-- Lecture : Tous ceux qui ont accès au don
CREATE POLICY "donation_workflow_select_policy" ON gestion_donation_workflow
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                d.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'responsable_hygiene', 'dg', 'rve', 'transitaire')
                OR d.created_by = get_user_email()
            )
        )
    );

-- Insertion : Système automatique + utilisateurs autorisés
CREATE POLICY "donation_workflow_insert_policy" ON gestion_donation_workflow
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
        AND acteur_nom = get_user_email()
    );

-- Pas de mise à jour ni suppression pour l'historique (sauf admins)
CREATE POLICY "donation_workflow_update_policy" ON gestion_donation_workflow
    FOR UPDATE USING (get_user_role() = 'admin');

CREATE POLICY "donation_workflow_delete_policy" ON gestion_donation_workflow
    FOR DELETE USING (get_user_role() = 'admin');

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_DONATION_APPROVALS
-- =====================================================

-- Lecture : Tous ceux qui ont accès au don
CREATE POLICY "donation_approvals_select_policy" ON gestion_donation_approvals
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM gestion_donations d 
            WHERE d.id = donation_id 
            AND (
                d.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'responsable_hygiene', 'dg', 'rve', 'transitaire')
                OR d.created_by = get_user_email()
            )
        )
    );

-- Insertion : Système automatique
CREATE POLICY "donation_approvals_insert_policy" ON gestion_donation_approvals
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL
    );

-- Mise à jour : Selon le type d'approbation
CREATE POLICY "donation_approvals_update_policy" ON gestion_donation_approvals
    FOR UPDATE USING (
        (type_approbation = 'validation_dg' AND get_user_role() IN ('admin', 'dg'))
        OR (type_approbation = 'avis_technique_dt' AND get_user_role() IN ('admin', 'dt'))
        OR (type_approbation = 'autorisation_ms' AND get_user_role() IN ('admin', 'ms'))
    );

-- Suppression : Admins seulement
CREATE POLICY "donation_approvals_delete_policy" ON gestion_donation_approvals
    FOR DELETE USING (get_user_role() = 'admin');

-- =====================================================
-- POLITIQUES POUR LES VUES
-- =====================================================

-- Les vues héritent automatiquement des politiques des tables sous-jacentes
-- Mais on peut créer des politiques spécifiques si nécessaire

-- =====================================================
-- COMMENTAIRES
-- =====================================================

COMMENT ON POLICY "donations_select_policy" ON gestion_donations IS 'Permet la lecture des dons selon les droits utilisateur';
COMMENT ON POLICY "donations_insert_policy" ON gestion_donations IS 'Permet la création de dons par les utilisateurs authentifiés';
COMMENT ON POLICY "donations_update_policy" ON gestion_donations IS 'Permet la modification selon le rôle et le statut du don';
COMMENT ON POLICY "donations_delete_policy" ON gestion_donations IS 'Permet la suppression par le créateur (brouillon) ou admin';
