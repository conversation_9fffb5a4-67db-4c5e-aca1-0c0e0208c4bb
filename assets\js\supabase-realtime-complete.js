// Système de temps réel Supabase complet
// Gestion des subscriptions, présence utilisateur, et notifications en temps réel

class SupabaseRealtimeComplete {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.subscriptions = new Map();
        this.presenceChannels = new Map();
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = this.config.realtime?.maxReconnectAttempts || 10;
        
        // État des utilisateurs en ligne
        this.onlineUsers = new Map();
        this.currentUserPresence = null;
        
        // Callbacks pour les événements
        this.callbacks = {
            onConnect: [],
            onDisconnect: [],
            onReconnect: [],
            onError: [],
            onDataChange: [],
            onPresenceUpdate: [],
            onNotification: []
        };
    }

    // Initialiser le système temps réel
    async initialize() {
        console.log('⚡ Initialisation du système temps réel...');
        
        try {
            // Configurer les subscriptions par défaut
            await this.setupDefaultSubscriptions();
            
            // Initialiser la présence utilisateur
            await this.initializeUserPresence();
            
            // Configurer la reconnexion automatique
            this.setupReconnection();
            
            console.log('✅ Système temps réel initialisé');
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation temps réel:', error);
            this.triggerCallback('onError', error);
            return false;
        }
    }

    // Configurer les subscriptions par défaut
    async setupDefaultSubscriptions() {
        const tables = [
            // Tables principales
            'gestion_messages',
            'gestion_commands',
            'gestion_command_items',
            'gestion_pv',
            'gestion_products',
            'user_profiles',
            'notifications',
            'activity_logs',

            // Tables étendues
            'gestion_users',
            'gestion_messages_v2',
            'gestion_commands_extended',
            'gestion_pv_detailed',
            'gestion_products_detailed',
            'gestion_migrations'
        ];

        console.log('📡 Configuration des subscriptions pour', tables.length, 'tables...');

        for (const table of tables) {
            try {
                await this.subscribeToTable(table);
                // Petite pause pour éviter la surcharge
                await new Promise(resolve => setTimeout(resolve, 100));
            } catch (error) {
                console.error(`❌ Erreur subscription ${table}:`, error);
            }
        }

        console.log('✅ Toutes les subscriptions configurées');
    }

    // S'abonner aux changements d'une table
    async subscribeToTable(tableName, filters = {}) {
        try {
            console.log(`📡 Abonnement à la table: ${tableName}`);

            // Vérifier si déjà abonné
            if (this.subscriptions.has(tableName)) {
                console.log(`⚠️ Déjà abonné à ${tableName}`);
                return this.subscriptions.get(tableName);
            }

            const channelName = `realtime_${tableName}_${Date.now()}`;

            // Créer le canal avec configuration détaillée
            const channel = this.client
                .channel(channelName, {
                    config: {
                        presence: {
                            key: tableName
                        }
                    }
                })
                .on('postgres_changes', {
                    event: '*',
                    schema: 'public',
                    table: tableName,
                    ...filters
                }, (payload) => {
                    console.log(`🔄 Changement reçu pour ${tableName}:`, payload.eventType, payload);
                    this.handleTableChange(tableName, payload);
                })
                .subscribe((status, err) => {
                    console.log(`📡 Statut subscription ${tableName}:`, status);

                    if (status === 'SUBSCRIBED') {
                        console.log(`✅ Abonné avec succès à ${tableName}`);
                        this.isConnected = true;
                        this.reconnectAttempts = 0;
                        this.triggerCallback('onConnect', { table: tableName });
                    } else if (status === 'CHANNEL_ERROR') {
                        console.error(`❌ Erreur abonnement ${tableName}:`, err);
                        this.handleConnectionError();
                    } else if (status === 'TIMED_OUT') {
                        console.warn(`⏰ Timeout abonnement ${tableName}`);
                        this.handleConnectionError();
                    } else if (status === 'CLOSED') {
                        console.warn(`🔒 Canal fermé ${tableName}`);
                        this.subscriptions.delete(tableName);
                    }
                });

            this.subscriptions.set(tableName, channel);

            // Ajouter un timeout de sécurité
            setTimeout(() => {
                if (channel.state !== 'joined') {
                    console.warn(`⚠️ Timeout subscription ${tableName}, retry...`);
                    this.retrySubscription(tableName, filters);
                }
            }, 10000);

            return channel;

        } catch (error) {
            console.error(`❌ Erreur abonnement ${tableName}:`, error);
            throw error;
        }
    }

    // Réessayer une subscription
    async retrySubscription(tableName, filters = {}) {
        console.log(`🔄 Retry subscription ${tableName}...`);

        // Supprimer l'ancienne subscription
        const oldChannel = this.subscriptions.get(tableName);
        if (oldChannel) {
            oldChannel.unsubscribe();
            this.subscriptions.delete(tableName);
        }

        // Attendre un peu avant de réessayer
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Réessayer
        return this.subscribeToTable(tableName, filters);
    }

    // Gérer les changements de table
    handleTableChange(tableName, payload) {
        console.log(`🔄 Changement détecté dans ${tableName}:`, payload.eventType, payload);

        const changeData = {
            table: tableName,
            eventType: payload.eventType,
            old: payload.old,
            new: payload.new,
            timestamp: new Date(),
            commit_timestamp: payload.commit_timestamp
        };

        // Déclencher les callbacks spécifiques
        this.triggerCallback('onDataChange', changeData);

        // Traitement spécifique selon la table
        switch (tableName) {
            // Tables principales
            case 'gestion_messages':
                this.handleMessageChange(changeData);
                break;
            case 'gestion_commands':
                this.handleCommandChange(changeData);
                break;
            case 'gestion_command_items':
                this.handleCommandItemChange(changeData);
                break;
            case 'gestion_pv':
                this.handlePVChange(changeData);
                break;
            case 'gestion_products':
                this.handleProductChange(changeData);
                break;
            case 'user_profiles':
                this.handleUserProfileChange(changeData);
                break;
            case 'notifications':
                this.handleNotificationChange(changeData);
                break;
            case 'activity_logs':
                this.handleActivityLogChange(changeData);
                break;

            // Tables étendues
            case 'gestion_users':
                this.handleUsersLegacyChange(changeData);
                break;
            case 'gestion_messages_v2':
                this.handleMessagesV2Change(changeData);
                break;
            case 'gestion_commands_extended':
                this.handleCommandsExtendedChange(changeData);
                break;
            case 'gestion_pv_detailed':
                this.handlePVDetailedChange(changeData);
                break;
            case 'gestion_products_detailed':
                this.handleProductsDetailedChange(changeData);
                break;
            case 'gestion_migrations':
                this.handleMigrationsChange(changeData);
                break;

            default:
                console.log(`📊 Changement générique dans ${tableName}`);
        }

        // Émettre un événement DOM personnalisé pour chaque table
        this.emitDOMEvent(tableName, changeData);
    }

    // Émettre un événement DOM personnalisé
    emitDOMEvent(tableName, changeData) {
        const eventName = `${tableName}Update`;
        const customEvent = new CustomEvent(eventName, {
            detail: changeData,
            bubbles: true
        });

        document.dispatchEvent(customEvent);
        console.log(`📡 Événement DOM émis: ${eventName}`);
    }

    // Gérer les changements de messages
    handleMessageChange(changeData) {
        console.log('📨 Changement message:', changeData);

        if (changeData.eventType === 'INSERT') {
            // Nouveau message
            this.showNotification('Nouveau message', {
                body: changeData.new.titre,
                icon: '💬',
                tag: 'new-message'
            });

            console.log('📨 Nouveau message créé:', changeData.new.titre);
        } else if (changeData.eventType === 'UPDATE') {
            console.log('📨 Message mis à jour:', changeData.new.titre);

            this.showNotification('Message modifié', {
                body: changeData.new.titre,
                icon: '✏️',
                tag: 'message-updated'
            });
        } else if (changeData.eventType === 'DELETE') {
            console.log('📨 Message supprimé:', changeData.old.titre);

            this.showNotification('Message supprimé', {
                body: changeData.old.titre,
                icon: '🗑️',
                tag: 'message-deleted'
            });
        }

        // Mettre à jour l'interface
        this.updateMessagesUI(changeData);
    }

    // Gérer les changements de commandes
    handleCommandChange(changeData) {
        console.log('📦 Changement commande:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouvelle commande', {
                body: `Commande ${changeData.new.numero_commande}`,
                icon: '📦',
                tag: 'new-command'
            });
        } else if (changeData.eventType === 'UPDATE' && changeData.new.statut !== changeData.old?.statut) {
            // Changement de statut
            this.showNotification('Commande mise à jour', {
                body: `${changeData.new.numero_commande}: ${changeData.new.statut}`,
                icon: '📦',
                tag: 'command-update'
            });
        } else if (changeData.eventType === 'DELETE') {
            this.showNotification('Commande supprimée', {
                body: `Commande ${changeData.old.numero_commande}`,
                icon: '🗑️',
                tag: 'command-deleted'
            });
        }

        this.updateCommandsUI(changeData);
    }

    // Gérer les changements d'articles de commande
    handleCommandItemChange(changeData) {
        console.log('📋 Changement article commande:', changeData);
        this.updateCommandItemsUI(changeData);
    }

    // Gérer les changements de PV
    handlePVChange(changeData) {
        console.log('📄 Changement PV:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouveau PV', {
                body: changeData.new.titre,
                icon: '📄',
                tag: 'new-pv'
            });
        }

        this.updatePVUI(changeData);
    }

    // Gérer les changements de produits
    handleProductChange(changeData) {
        console.log('🛍️ Changement produit:', changeData);
        this.updateProductsUI(changeData);
    }

    // Gérer les changements de profils utilisateur
    handleUserProfileChange(changeData) {
        console.log('👤 Changement profil utilisateur:', changeData);
        this.updateUserProfilesUI(changeData);
    }

    // Gérer les changements de notifications
    handleNotificationChange(changeData) {
        console.log('🔔 Changement notification:', changeData);

        if (changeData.eventType === 'INSERT') {
            // Nouvelle notification
            const notification = changeData.new;

            // Vérifier si c'est pour l'utilisateur actuel
            const currentUser = this.getCurrentUser();
            if (currentUser && notification.user_id === currentUser.id) {
                this.showNotification(notification.titre, {
                    body: notification.message,
                    icon: this.getNotificationIcon(notification.type),
                    tag: `notification-${notification.id}`
                });

                this.triggerCallback('onNotification', notification);
            }
        }

        this.updateNotificationsUI(changeData);
    }

    // Gérer les changements de logs d'activité
    handleActivityLogChange(changeData) {
        console.log('📊 Changement log activité:', changeData);
        // Les logs sont généralement en lecture seule, pas besoin de notification
    }

    // Handlers pour les nouvelles tables étendues

    // Gérer les changements d'utilisateurs legacy
    handleUsersLegacyChange(changeData) {
        console.log('👥 Changement utilisateur legacy:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouvel utilisateur', {
                body: `${changeData.new.prenom} ${changeData.new.nom}`,
                icon: '👤',
                tag: 'new-user-legacy'
            });
        } else if (changeData.eventType === 'UPDATE') {
            this.showNotification('Utilisateur modifié', {
                body: `${changeData.new.prenom} ${changeData.new.nom}`,
                icon: '✏️',
                tag: 'user-updated-legacy'
            });
        }

        this.updateUsersLegacyUI(changeData);
    }

    // Gérer les changements de messages v2
    handleMessagesV2Change(changeData) {
        console.log('📨 Changement message v2:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouveau message v2', {
                body: `De: ${changeData.new.sender_name}`,
                icon: '📧',
                tag: 'new-message-v2'
            });
        } else if (changeData.eventType === 'UPDATE' && changeData.new.is_read !== changeData.old?.is_read) {
            console.log('📧 Message v2 marqué comme lu');
        }

        this.updateMessagesV2UI(changeData);
    }

    // Gérer les changements de commandes étendues
    handleCommandsExtendedChange(changeData) {
        console.log('📦 Changement commande étendue:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouvelle commande étendue', {
                body: `${changeData.new.numero_commande} - ${changeData.new.fournisseur}`,
                icon: '📦',
                tag: 'new-command-extended'
            });
        } else if (changeData.eventType === 'UPDATE' && changeData.new.statut !== changeData.old?.statut) {
            this.showNotification('Commande étendue mise à jour', {
                body: `${changeData.new.numero_commande}: ${changeData.new.statut}`,
                icon: '📝',
                tag: 'command-extended-updated'
            });
        }

        this.updateCommandsExtendedUI(changeData);
    }

    // Gérer les changements de PV détaillés
    handlePVDetailedChange(changeData) {
        console.log('📄 Changement PV détaillé:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouveau PV détaillé', {
                body: `${changeData.new.numero_pv} - ${changeData.new.type_pv}`,
                icon: '📄',
                tag: 'new-pv-detailed'
            });
        } else if (changeData.eventType === 'UPDATE' && changeData.new.statut !== changeData.old?.statut) {
            this.showNotification('PV détaillé mis à jour', {
                body: `${changeData.new.numero_pv}: ${changeData.new.statut}`,
                icon: '✅',
                tag: 'pv-detailed-updated'
            });
        }

        this.updatePVDetailedUI(changeData);
    }

    // Gérer les changements de produits détaillés
    handleProductsDetailedChange(changeData) {
        console.log('🛍️ Changement produit détaillé:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouveau produit détaillé', {
                body: `${changeData.new.nom} - ${changeData.new.categorie}`,
                icon: '🛍️',
                tag: 'new-product-detailed'
            });
        }

        this.updateProductsDetailedUI(changeData);
    }

    // Gérer les changements de migrations
    handleMigrationsChange(changeData) {
        console.log('🔧 Changement migration:', changeData);

        if (changeData.eventType === 'INSERT') {
            this.showNotification('Nouvelle migration', {
                body: `Version ${changeData.new.version}`,
                icon: '🔧',
                tag: 'new-migration'
            });
        }

        this.updateMigrationsUI(changeData);
    }

    // Initialiser la présence utilisateur
    async initializeUserPresence() {
        const currentUser = this.getCurrentUser();
        if (!currentUser) return;
        
        try {
            console.log('👥 Initialisation de la présence utilisateur...');
            
            const presenceChannel = this.client
                .channel('user_presence')
                .on('presence', { event: 'sync' }, () => {
                    this.handlePresenceSync();
                })
                .on('presence', { event: 'join' }, ({ key, newPresences }) => {
                    this.handleUserJoin(key, newPresences);
                })
                .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
                    this.handleUserLeave(key, leftPresences);
                })
                .subscribe(async (status) => {
                    if (status === 'SUBSCRIBED') {
                        // Envoyer notre présence
                        await this.updateUserPresence();
                        console.log('✅ Présence utilisateur active');
                    }
                });
            
            this.presenceChannels.set('user_presence', presenceChannel);
            
            // Mettre à jour la présence périodiquement
            setInterval(() => {
                this.updateUserPresence();
            }, 30000); // Toutes les 30 secondes
            
        } catch (error) {
            console.error('❌ Erreur initialisation présence:', error);
        }
    }

    // Mettre à jour la présence utilisateur
    async updateUserPresence() {
        const currentUser = this.getCurrentUser();
        if (!currentUser) return;
        
        try {
            const presenceData = {
                user_id: currentUser.id,
                online_at: new Date().toISOString(),
                page: window.location.pathname,
                user_agent: navigator.userAgent.substring(0, 100)
            };
            
            const channel = this.presenceChannels.get('user_presence');
            if (channel) {
                await channel.track(presenceData);
                this.currentUserPresence = presenceData;
            }
            
        } catch (error) {
            console.warn('⚠️ Erreur mise à jour présence:', error);
        }
    }

    // Gérer la synchronisation de présence
    handlePresenceSync() {
        const channel = this.presenceChannels.get('user_presence');
        if (!channel) return;
        
        const state = channel.presenceState();
        this.onlineUsers.clear();
        
        Object.keys(state).forEach(key => {
            const presences = state[key];
            if (presences.length > 0) {
                const presence = presences[0];
                this.onlineUsers.set(key, presence);
            }
        });
        
        console.log(`👥 ${this.onlineUsers.size} utilisateurs en ligne`);
        this.triggerCallback('onPresenceUpdate', {
            type: 'sync',
            onlineUsers: Array.from(this.onlineUsers.values())
        });
    }

    // Gérer l'arrivée d'un utilisateur
    handleUserJoin(key, newPresences) {
        newPresences.forEach(presence => {
            this.onlineUsers.set(key, presence);
            console.log(`👋 Utilisateur connecté: ${presence.user_id}`);
        });
        
        this.triggerCallback('onPresenceUpdate', {
            type: 'join',
            users: newPresences,
            onlineUsers: Array.from(this.onlineUsers.values())
        });
    }

    // Gérer le départ d'un utilisateur
    handleUserLeave(key, leftPresences) {
        leftPresences.forEach(presence => {
            this.onlineUsers.delete(key);
            console.log(`👋 Utilisateur déconnecté: ${presence.user_id}`);
        });
        
        this.triggerCallback('onPresenceUpdate', {
            type: 'leave',
            users: leftPresences,
            onlineUsers: Array.from(this.onlineUsers.values())
        });
    }

    // Obtenir l'utilisateur actuel
    getCurrentUser() {
        try {
            return this.client.auth.getUser()?.data?.user || null;
        } catch {
            return null;
        }
    }

    // Afficher une notification système
    showNotification(title, options = {}) {
        // Vérifier les permissions de notification
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                new Notification(title, {
                    body: options.body || '',
                    icon: options.icon || '/favicon.ico',
                    tag: options.tag || 'default',
                    requireInteraction: options.requireInteraction || false
                });
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification(title, options);
                    }
                });
            }
        }
        
        // Notification dans l'interface également
        this.showInAppNotification(title, options);
    }

    // Afficher une notification dans l'interface
    showInAppNotification(title, options = {}) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${options.type || 'info'}`;
        notification.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                ${options.body ? `<div class="notification-body">${options.body}</div>` : ''}
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;
        
        // Ajouter au conteneur de notifications
        let container = document.getElementById('notifications-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notifications-container';
            container.className = 'notifications-container';
            document.body.appendChild(container);
        }
        
        container.appendChild(notification);
        
        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    }

    // Obtenir l'icône selon le type de notification
    getNotificationIcon(type) {
        const icons = {
            'info': 'ℹ️',
            'success': '✅',
            'warning': '⚠️',
            'error': '❌'
        };
        return icons[type] || 'ℹ️';
    }

    // Obtenir l'icône selon le type de message
    getMessageTypeIcon(type) {
        const icons = {
            'general': '📝',
            'urgent': '🚨',
            'info': 'ℹ️',
            'warning': '⚠️'
        };
        return icons[type] || '📝';
    }

    // Obtenir la couleur selon le statut de commande
    getCommandStatusColor(status) {
        const colors = {
            'en_attente': '#ffc107',
            'validee': '#17a2b8',
            'livree': '#28a745',
            'annulee': '#dc3545'
        };
        return colors[status] || '#6c757d';
    }

    // Calculer le temps écoulé
    getTimeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) {
            return 'À l\'instant';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `Il y a ${minutes} minute${minutes > 1 ? 's' : ''}`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `Il y a ${hours} heure${hours > 1 ? 's' : ''}`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
        }
    }

    // Méthodes de mise à jour de l'interface pour les nouvelles tables

    // Mettre à jour l'interface des utilisateurs legacy
    updateUsersLegacyUI(changeData) {
        const event = new CustomEvent('gestion_usersUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des messages v2
    updateMessagesV2UI(changeData) {
        const event = new CustomEvent('gestion_messages_v2Update', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des commandes étendues
    updateCommandsExtendedUI(changeData) {
        const event = new CustomEvent('gestion_commands_extendedUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des PV détaillés
    updatePVDetailedUI(changeData) {
        const event = new CustomEvent('gestion_pv_detailedUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des produits détaillés
    updateProductsDetailedUI(changeData) {
        const event = new CustomEvent('gestion_products_detailedUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des migrations
    updateMigrationsUI(changeData) {
        const event = new CustomEvent('gestion_migrationsUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des messages
    updateMessagesUI(changeData) {
        console.log('🔄 Mise à jour UI messages:', changeData);

        const event = new CustomEvent('messagesUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);

        // Mise à jour directe si des éléments existent
        this.updateMessagesList(changeData);
    }

    // Mettre à jour l'interface des commandes
    updateCommandsUI(changeData) {
        console.log('🔄 Mise à jour UI commandes:', changeData);

        const event = new CustomEvent('commandsUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);

        // Mise à jour directe si des éléments existent
        this.updateCommandsList(changeData);
    }

    // Mettre à jour l'interface des articles de commande
    updateCommandItemsUI(changeData) {
        const event = new CustomEvent('commandItemsUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des PV
    updatePVUI(changeData) {
        const event = new CustomEvent('pvUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des produits
    updateProductsUI(changeData) {
        const event = new CustomEvent('productsUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des profils utilisateur
    updateUserProfilesUI(changeData) {
        const event = new CustomEvent('userProfilesUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);
    }

    // Mettre à jour l'interface des notifications
    updateNotificationsUI(changeData) {
        console.log('🔄 Mise à jour UI notifications:', changeData);

        const event = new CustomEvent('notificationsUpdate', {
            detail: changeData,
            bubbles: true
        });
        document.dispatchEvent(event);

        // Mise à jour directe si des éléments existent
        this.updateNotificationsList(changeData);
    }

    // Mise à jour directe de la liste des messages
    updateMessagesList(changeData) {
        const messagesList = document.getElementById('messagesList');
        if (!messagesList) return;

        if (changeData.eventType === 'INSERT') {
            // Ajouter le nouveau message
            const messageElement = this.createMessageElement(changeData.new);
            messagesList.insertBefore(messageElement, messagesList.firstChild);

            // Animation d'apparition
            messageElement.style.opacity = '0';
            messageElement.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                messageElement.style.transition = 'all 0.3s ease';
                messageElement.style.opacity = '1';
                messageElement.style.transform = 'translateY(0)';
            }, 10);

        } else if (changeData.eventType === 'UPDATE') {
            // Mettre à jour le message existant
            const existingElement = document.querySelector(`[data-message-id="${changeData.new.id}"]`);
            if (existingElement) {
                const updatedElement = this.createMessageElement(changeData.new);
                existingElement.replaceWith(updatedElement);

                // Animation de mise à jour
                updatedElement.style.backgroundColor = '#fff3cd';
                setTimeout(() => {
                    updatedElement.style.transition = 'background-color 1s ease';
                    updatedElement.style.backgroundColor = '';
                }, 100);
            }

        } else if (changeData.eventType === 'DELETE') {
            // Supprimer le message
            const existingElement = document.querySelector(`[data-message-id="${changeData.old.id}"]`);
            if (existingElement) {
                existingElement.style.transition = 'all 0.3s ease';
                existingElement.style.opacity = '0';
                existingElement.style.transform = 'translateX(-100%)';
                setTimeout(() => existingElement.remove(), 300);
            }
        }
    }

    // Créer un élément de message
    createMessageElement(message) {
        const messageElement = document.createElement('div');
        messageElement.className = 'message-item realtime-message';
        messageElement.setAttribute('data-message-id', message.id);

        const typeIcon = this.getMessageTypeIcon(message.type);
        const timeAgo = this.getTimeAgo(new Date(message.created_at));

        messageElement.innerHTML = `
            <div class="message-header">
                <span class="message-type">${typeIcon} ${message.type}</span>
                <span class="message-time">${timeAgo}</span>
            </div>
            <h4 class="message-title">${message.titre}</h4>
            <p class="message-content">${message.contenu}</p>
            <div class="message-meta">
                <span class="message-priority">Priorité: ${message.priorite}</span>
                <span class="message-status">${message.statut}</span>
            </div>
        `;

        return messageElement;
    }

    // Mise à jour directe de la liste des commandes
    updateCommandsList(changeData) {
        const commandsList = document.getElementById('commandsList');
        if (!commandsList) return;

        if (changeData.eventType === 'INSERT') {
            const commandElement = this.createCommandElement(changeData.new);
            commandsList.insertBefore(commandElement, commandsList.firstChild);

            // Animation d'apparition
            commandElement.style.opacity = '0';
            setTimeout(() => {
                commandElement.style.transition = 'opacity 0.3s ease';
                commandElement.style.opacity = '1';
            }, 10);

        } else if (changeData.eventType === 'UPDATE') {
            const existingElement = document.querySelector(`[data-command-id="${changeData.new.id}"]`);
            if (existingElement) {
                const updatedElement = this.createCommandElement(changeData.new);
                existingElement.replaceWith(updatedElement);
            }

        } else if (changeData.eventType === 'DELETE') {
            const existingElement = document.querySelector(`[data-command-id="${changeData.old.id}"]`);
            if (existingElement) {
                existingElement.style.transition = 'opacity 0.3s ease';
                existingElement.style.opacity = '0';
                setTimeout(() => existingElement.remove(), 300);
            }
        }
    }

    // Créer un élément de commande
    createCommandElement(command) {
        const commandElement = document.createElement('div');
        commandElement.className = 'command-item realtime-command';
        commandElement.setAttribute('data-command-id', command.id);

        const statusColor = this.getCommandStatusColor(command.statut);
        const timeAgo = this.getTimeAgo(new Date(command.created_at));

        commandElement.innerHTML = `
            <div class="command-header">
                <span class="command-number">${command.numero_commande}</span>
                <span class="command-status" style="color: ${statusColor}">${command.statut}</span>
            </div>
            <div class="command-details">
                <p><strong>Fournisseur:</strong> ${command.fournisseur}</p>
                <p><strong>Montant:</strong> ${command.montant_total}€</p>
                <p><strong>Date:</strong> ${command.date_commande}</p>
            </div>
            <div class="command-meta">
                <span class="command-time">${timeAgo}</span>
            </div>
        `;

        return commandElement;
    }

    // Mise à jour directe de la liste des notifications
    updateNotificationsList(changeData) {
        const notificationsList = document.getElementById('notificationsList');
        if (!notificationsList) return;

        if (changeData.eventType === 'INSERT') {
            const notificationElement = this.createNotificationElement(changeData.new);
            notificationsList.insertBefore(notificationElement, notificationsList.firstChild);
        }
    }

    // Créer un élément de notification
    createNotificationElement(notification) {
        const notificationElement = document.createElement('div');
        notificationElement.className = `notification-item realtime-notification notification-${notification.type}`;
        notificationElement.setAttribute('data-notification-id', notification.id);

        const icon = this.getNotificationIcon(notification.type);
        const timeAgo = this.getTimeAgo(new Date(notification.created_at));

        notificationElement.innerHTML = `
            <div class="notification-icon">${icon}</div>
            <div class="notification-content">
                <h5 class="notification-title">${notification.titre}</h5>
                <p class="notification-message">${notification.message}</p>
                <span class="notification-time">${timeAgo}</span>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        return notificationElement;
    }

    // Configurer la reconnexion automatique
    setupReconnection() {
        // Écouter les événements de connexion/déconnexion
        window.addEventListener('online', () => {
            console.log('🌐 Connexion internet rétablie');
            this.handleReconnection();
        });
        
        window.addEventListener('offline', () => {
            console.log('🌐 Connexion internet perdue');
            this.handleConnectionError();
        });
    }

    // Gérer les erreurs de connexion
    handleConnectionError() {
        this.isConnected = false;
        this.triggerCallback('onDisconnect');
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
            console.log(`🔄 Tentative de reconnexion dans ${delay}ms...`);
            
            setTimeout(() => {
                this.attemptReconnection();
            }, delay);
            
            this.reconnectAttempts++;
        } else {
            console.error('❌ Nombre maximum de tentatives de reconnexion atteint');
        }
    }

    // Tenter une reconnexion
    async attemptReconnection() {
        try {
            console.log('🔄 Tentative de reconnexion...');
            
            // Réinitialiser les subscriptions
            await this.reinitializeSubscriptions();
            
            console.log('✅ Reconnexion réussie');
            this.triggerCallback('onReconnect');
            
        } catch (error) {
            console.error('❌ Échec de reconnexion:', error);
            this.handleConnectionError();
        }
    }

    // Gérer la reconnexion
    async handleReconnection() {
        if (!this.isConnected) {
            await this.attemptReconnection();
        }
    }

    // Réinitialiser les subscriptions
    async reinitializeSubscriptions() {
        // Fermer les anciennes subscriptions
        this.subscriptions.forEach(channel => {
            channel.unsubscribe();
        });
        this.subscriptions.clear();
        
        // Recréer les subscriptions
        await this.setupDefaultSubscriptions();
        await this.initializeUserPresence();
    }

    // Se désabonner d'une table
    unsubscribeFromTable(tableName) {
        const channel = this.subscriptions.get(tableName);
        if (channel) {
            channel.unsubscribe();
            this.subscriptions.delete(tableName);
            console.log(`📡 Désabonnement de ${tableName}`);
        }
    }

    // Obtenir les utilisateurs en ligne
    getOnlineUsers() {
        return Array.from(this.onlineUsers.values());
    }

    // Obtenir le statut de connexion
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            onlineUsersCount: this.onlineUsers.size,
            subscriptionsCount: this.subscriptions.size
        };
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Supprimer un callback
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    // Déclencher un callback
    triggerCallback(event, data = null) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Nettoyer les ressources
    destroy() {
        // Fermer toutes les subscriptions
        this.subscriptions.forEach(channel => {
            channel.unsubscribe();
        });
        this.subscriptions.clear();
        
        // Fermer les canaux de présence
        this.presenceChannels.forEach(channel => {
            channel.unsubscribe();
        });
        this.presenceChannels.clear();
        
        // Nettoyer les callbacks
        Object.keys(this.callbacks).forEach(key => {
            this.callbacks[key] = [];
        });
        
        console.log('🧹 Système temps réel nettoyé');
    }
}

// Export pour utilisation
if (typeof window !== 'undefined') {
    window.SupabaseRealtimeComplete = SupabaseRealtimeComplete;
}
