-- Fonctions utilitaires pour le système de gestion des dons IPT - Partie 2

-- =====================================================
-- FONCTION POUR MARQUER UN DON COMME REÇU
-- =====================================================

CREATE OR REPLACE FUNCTION mark_donation_received(
    p_donation_id UUID,
    p_user_email TEXT,
    p_observations TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    donation_record RECORD;
    success BOOLEAN := FALSE;
BEGIN
    -- Vérifier que le don existe et peut être marqué comme reçu
    SELECT * INTO donation_record
    FROM gestion_donations
    WHERE id = p_donation_id
    AND statut IN ('approuve_ms', 'en_expedition');
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Don non trouvé ou non recevable';
    END IF;
    
    -- Mettre à jour le don
    UPDATE gestion_donations
    SET statut = 'recu',
        date_reception = NOW(),
        observations = COALESCE(p_observations, observations),
        updated_by = p_user_email
    WHERE id = p_donation_id;
    
    -- Enregistrer l'action dans le workflow
    PERFORM log_donation_workflow(
        p_donation_id, 'reception', donation_record.statut, 'recu',
        p_user_email, 'receptionniste', 'Réception du don',
        p_observations
    );
    
    -- Notifier le demandeur et le RVE
    PERFORM create_donation_notification(
        donation_record.demandeur_email,
        'Don reçu',
        'Votre don (' || donation_record.numero_don || ') a été reçu et est en cours de traitement.',
        p_donation_id,
        'success'
    );
    
    -- Si c'est un équipement, notifier le RVE pour l'inventaire
    IF donation_record.type_don = 'equipement' THEN
        PERFORM create_donation_notification(
            '<EMAIL>',
            'Équipement à inventorier',
            'Un don d''équipement (' || donation_record.numero_don || ') nécessite l''attribution d''un numéro d''inventaire.',
            p_donation_id,
            'pending'
        );
    END IF;
    
    success := TRUE;
    RETURN success;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors de la réception: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR AFFECTER UN NUMÉRO D'INVENTAIRE
-- =====================================================

CREATE OR REPLACE FUNCTION assign_inventory_number(
    p_item_id UUID,
    p_user_email TEXT,
    p_service_affecte TEXT,
    p_responsable_affecte TEXT
)
RETURNS TEXT AS $$
DECLARE
    item_record RECORD;
    donation_record RECORD;
    inventory_number TEXT;
    success BOOLEAN := FALSE;
BEGIN
    -- Vérifier que l'item existe et peut recevoir un NI
    SELECT di.*, d.statut, d.type_don, d.numero_don
    INTO item_record
    FROM gestion_donation_items di
    JOIN gestion_donations d ON di.donation_id = d.id
    WHERE di.id = p_item_id
    AND d.statut = 'recu'
    AND d.type_don = 'equipement'
    AND di.numero_inventaire IS NULL;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Item non trouvé ou numéro d''inventaire déjà attribué';
    END IF;
    
    -- Générer le numéro d'inventaire
    inventory_number := generate_inventory_number();
    
    -- Mettre à jour l'item
    UPDATE gestion_donation_items
    SET numero_inventaire = inventory_number,
        service_affecte = p_service_affecte,
        responsable_affecte = p_responsable_affecte,
        date_affectation = NOW()
    WHERE id = p_item_id;
    
    -- Vérifier si tous les items du don ont un NI
    IF NOT EXISTS (
        SELECT 1 FROM gestion_donation_items di
        JOIN gestion_donations d ON di.donation_id = d.id
        WHERE d.id = item_record.donation_id
        AND d.type_don = 'equipement'
        AND di.numero_inventaire IS NULL
    ) THEN
        -- Tous les équipements ont un NI, marquer le don comme affecté
        UPDATE gestion_donations
        SET statut = 'affecte',
            date_affectation = NOW(),
            updated_by = p_user_email
        WHERE id = item_record.donation_id;
        
        -- Enregistrer l'action dans le workflow
        PERFORM log_donation_workflow(
            item_record.donation_id, 'affectation', 'recu', 'affecte',
            p_user_email, 'rve', 'Affectation complète',
            'Tous les équipements ont reçu leur numéro d''inventaire'
        );
    END IF;
    
    RETURN inventory_number;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors de l''attribution du NI: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR GÉNÉRER UN BON DE SORTIE (BS)
-- =====================================================

CREATE OR REPLACE FUNCTION generate_bon_sortie(
    p_donation_id UUID,
    p_user_email TEXT
)
RETURNS JSONB AS $$
DECLARE
    donation_record RECORD;
    items_data JSONB;
    bon_sortie JSONB;
BEGIN
    -- Récupérer les informations du don
    SELECT * INTO donation_record
    FROM gestion_donations
    WHERE id = p_donation_id
    AND statut IN ('recu', 'affecte');
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Don non trouvé ou non éligible pour BS';
    END IF;
    
    -- Récupérer les items du don
    SELECT jsonb_agg(
        jsonb_build_object(
            'nom_item', nom_item,
            'description', description,
            'quantite', quantite,
            'unite', unite,
            'marque', marque,
            'modele', modele,
            'numero_serie', numero_serie,
            'numero_inventaire', numero_inventaire,
            'service_affecte', service_affecte,
            'responsable_affecte', responsable_affecte
        )
    ) INTO items_data
    FROM gestion_donation_items
    WHERE donation_id = p_donation_id;
    
    -- Construire le bon de sortie
    bon_sortie := jsonb_build_object(
        'numero_don', donation_record.numero_don,
        'date_generation', NOW(),
        'type_don', donation_record.type_don,
        'demandeur', jsonb_build_object(
            'nom', donation_record.demandeur_nom,
            'service', donation_record.demandeur_service,
            'email', donation_record.demandeur_email
        ),
        'donneur', jsonb_build_object(
            'nom', donation_record.donneur_nom,
            'contact', donation_record.donneur_contact
        ),
        'items', items_data,
        'observations', donation_record.observations,
        'genere_par', p_user_email,
        'date_reception', donation_record.date_reception,
        'date_affectation', donation_record.date_affectation
    );
    
    -- Enregistrer l'action dans le workflow
    PERFORM log_donation_workflow(
        p_donation_id, 'generation_bs', donation_record.statut, donation_record.statut,
        p_user_email, 'rve', 'Génération du Bon de Sortie',
        'Bon de sortie généré pour le don'
    );
    
    RETURN bon_sortie;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors de la génération du BS: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTION POUR OBTENIR LES STATISTIQUES DES DONS
-- =====================================================

CREATE OR REPLACE FUNCTION get_donations_statistics(
    p_date_debut DATE DEFAULT NULL,
    p_date_fin DATE DEFAULT NULL,
    p_type_don TEXT DEFAULT NULL
)
RETURNS JSONB AS $$
DECLARE
    stats JSONB;
    date_debut DATE;
    date_fin DATE;
BEGIN
    -- Définir les dates par défaut
    date_debut := COALESCE(p_date_debut, DATE_TRUNC('year', CURRENT_DATE)::DATE);
    date_fin := COALESCE(p_date_fin, CURRENT_DATE);
    
    -- Construire les statistiques
    WITH donation_stats AS (
        SELECT 
            COUNT(*) as total_dons,
            COUNT(CASE WHEN type_don = 'article' THEN 1 END) as total_articles,
            COUNT(CASE WHEN type_don = 'equipement' THEN 1 END) as total_equipements,
            COUNT(CASE WHEN statut = 'brouillon' THEN 1 END) as brouillons,
            COUNT(CASE WHEN statut = 'soumis_dg' THEN 1 END) as en_attente_dg,
            COUNT(CASE WHEN statut = 'avis_dt' THEN 1 END) as en_attente_dt,
            COUNT(CASE WHEN statut = 'soumis_ms' THEN 1 END) as en_attente_ms,
            COUNT(CASE WHEN statut = 'approuve_ms' THEN 1 END) as approuves,
            COUNT(CASE WHEN statut LIKE 'refuse%' THEN 1 END) as refuses,
            COUNT(CASE WHEN statut = 'recu' THEN 1 END) as recus,
            COUNT(CASE WHEN statut = 'affecte' THEN 1 END) as affectes,
            SUM(COALESCE(valeur_estimee, 0)) as valeur_totale,
            AVG(
                CASE 
                    WHEN date_decision_ms IS NOT NULL AND date_demande IS NOT NULL 
                    THEN EXTRACT(DAYS FROM (date_decision_ms - date_demande))
                END
            ) as duree_moyenne_traitement
        FROM gestion_donations
        WHERE date_demande BETWEEN date_debut AND date_fin
        AND (p_type_don IS NULL OR type_don = p_type_don)
    )
    SELECT jsonb_build_object(
        'periode', jsonb_build_object(
            'debut', date_debut,
            'fin', date_fin,
            'type_filtre', p_type_don
        ),
        'totaux', jsonb_build_object(
            'total_dons', total_dons,
            'total_articles', total_articles,
            'total_equipements', total_equipements,
            'valeur_totale', valeur_totale,
            'duree_moyenne_traitement_jours', ROUND(duree_moyenne_traitement, 1)
        ),
        'statuts', jsonb_build_object(
            'brouillons', brouillons,
            'en_attente_dg', en_attente_dg,
            'en_attente_dt', en_attente_dt,
            'en_attente_ms', en_attente_ms,
            'approuves', approuves,
            'refuses', refuses,
            'recus', recus,
            'affectes', affectes
        ),
        'taux', jsonb_build_object(
            'taux_approbation', 
            CASE WHEN (approuves + refuses) > 0 
                THEN ROUND((approuves::DECIMAL / (approuves + refuses)) * 100, 1)
                ELSE 0 
            END,
            'taux_completion',
            CASE WHEN total_dons > 0 
                THEN ROUND((affectes::DECIMAL / total_dons) * 100, 1)
                ELSE 0 
            END
        )
    ) INTO stats
    FROM donation_stats;
    
    RETURN stats;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Erreur lors du calcul des statistiques: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;
