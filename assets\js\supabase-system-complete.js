// Système Supabase complet - Coordinateur principal
// Initialisation et gestion de tous les modules Supabase

class SupabaseSystemComplete {
    constructor() {
        this.config = null;
        this.client = null;
        this.modules = {
            auth: null,
            storage: null,
            realtime: null,
            autoInit: null,
            monitor: null,
            sync: null,
            migration: null
        };
        
        this.isInitialized = false;
        this.initializationPromise = null;
        
        // État du système
        this.systemState = {
            status: 'disconnected',
            lastCheck: null,
            errors: [],
            modules: {}
        };
        
        // Callbacks globaux
        this.callbacks = {
            onSystemReady: [],
            onSystemError: [],
            onModuleReady: [],
            onModuleError: [],
            onAuthStateChange: [],
            onDataChange: [],
            onConnectionChange: []
        };
    }

    // Initialisation complète du système
    async initialize() {
        if (this.initializationPromise) {
            return this.initializationPromise;
        }
        
        this.initializationPromise = this._performInitialization();
        return this.initializationPromise;
    }

    // Effectuer l'initialisation
    async _performInitialization() {
        try {
            console.log('🚀 Initialisation du système Supabase complet...');
            
            // Étape 1: Charger la configuration
            await this.loadConfiguration();
            
            // Étape 2: Initialiser le client Supabase
            await this.initializeClient();
            
            // Étape 3: Initialiser les modules principaux
            await this.initializeModules();
            
            // Étape 4: Configurer les événements globaux
            this.setupGlobalEvents();
            
            // Étape 5: Démarrer la surveillance du système
            this.startSystemMonitoring();
            
            this.isInitialized = true;
            this.systemState.status = 'ready';
            this.systemState.lastCheck = new Date();
            
            console.log('✅ Système Supabase complet initialisé');
            this.triggerCallback('onSystemReady', this.getSystemStatus());
            
            return { success: true, modules: this.modules };
            
        } catch (error) {
            console.error('❌ Erreur initialisation système:', error);
            this.systemState.status = 'error';
            this.systemState.errors.push({
                message: error.message,
                timestamp: new Date(),
                type: 'initialization'
            });
            
            this.triggerCallback('onSystemError', error);
            throw error;
        }
    }

    // Charger la configuration
    async loadConfiguration() {
        if (typeof SUPABASE_CONFIG === 'undefined') {
            throw new Error('Configuration Supabase non trouvée');
        }
        
        this.config = SUPABASE_CONFIG;
        console.log('✅ Configuration chargée');
    }

    // Initialiser le client Supabase
    async initializeClient() {
        try {
            // Vérifier la disponibilité de la librairie Supabase
            const supabaseLib = typeof supabase !== 'undefined' ? supabase :
                               typeof window.supabase !== 'undefined' ? window.supabase : null;
            
            if (!supabaseLib) {
                throw new Error('Librairie Supabase non chargée');
            }
            
            // Créer le client
            this.client = supabaseLib.createClient(
                this.config.url,
                this.config.anonKey
            );
            
            // Tester la connexion
            const { data, error } = await this.client.auth.getSession();
            if (error && error.message !== 'Invalid session') {
                throw error;
            }
            
            console.log('✅ Client Supabase initialisé');
            
        } catch (error) {
            console.error('❌ Erreur initialisation client:', error);
            throw error;
        }
    }

    // Initialiser tous les modules
    async initializeModules() {
        const moduleInitializers = [
            { name: 'auth', initializer: this.initializeAuth.bind(this) },
            { name: 'storage', initializer: this.initializeStorage.bind(this) },
            { name: 'realtime', initializer: this.initializeRealtime.bind(this) },
            { name: 'autoInit', initializer: this.initializeAutoInit.bind(this) },
            { name: 'monitor', initializer: this.initializeMonitor.bind(this) },
            { name: 'sync', initializer: this.initializeSync.bind(this) },
            { name: 'migration', initializer: this.initializeMigration.bind(this) }
        ];
        
        for (const { name, initializer } of moduleInitializers) {
            try {
                await initializer();
                this.systemState.modules[name] = { status: 'ready', error: null };
                this.triggerCallback('onModuleReady', { module: name });
            } catch (error) {
                console.warn(`⚠️ Module ${name} non initialisé:`, error);
                this.systemState.modules[name] = { status: 'error', error: error.message };
                this.triggerCallback('onModuleError', { module: name, error });
            }
        }
    }

    // Initialiser le module d'authentification
    async initializeAuth() {
        if (typeof SupabaseAuthComplete !== 'undefined') {
            this.modules.auth = new SupabaseAuthComplete(this.config, this.client);
            
            // Configurer les callbacks d'authentification
            this.modules.auth.on('onSignIn', (data) => {
                console.log('👤 Utilisateur connecté:', data.profile?.nom);
                this.triggerCallback('onAuthStateChange', { type: 'signIn', data });
            });
            
            this.modules.auth.on('onSignOut', () => {
                console.log('👋 Utilisateur déconnecté');
                this.triggerCallback('onAuthStateChange', { type: 'signOut' });
            });
            
            this.modules.auth.on('onError', (error) => {
                console.error('❌ Erreur authentification:', error);
            });
            
            await this.modules.auth.initialize();
            console.log('✅ Module authentification initialisé');
        }
    }

    // Initialiser le module de stockage
    async initializeStorage() {
        if (typeof SupabaseStorageManager !== 'undefined') {
            this.modules.storage = new SupabaseStorageManager(this.config, this.client);
            
            // Configurer les callbacks de stockage
            this.modules.storage.on('onUploadComplete', (result) => {
                console.log('📤 Fichier uploadé:', result.file.name);
            });
            
            this.modules.storage.on('onUploadError', (error) => {
                console.error('❌ Erreur upload:', error);
            });
            
            await this.modules.storage.initialize();
            console.log('✅ Module stockage initialisé');
        }
    }

    // Initialiser le module temps réel
    async initializeRealtime() {
        if (typeof SupabaseRealtimeComplete !== 'undefined') {
            this.modules.realtime = new SupabaseRealtimeComplete(this.config, this.client);
            
            // Configurer les callbacks temps réel
            this.modules.realtime.on('onConnect', () => {
                console.log('⚡ Temps réel connecté');
                this.triggerCallback('onConnectionChange', { status: 'connected' });
            });
            
            this.modules.realtime.on('onDisconnect', () => {
                console.log('⚡ Temps réel déconnecté');
                this.triggerCallback('onConnectionChange', { status: 'disconnected' });
            });
            
            this.modules.realtime.on('onDataChange', (data) => {
                this.triggerCallback('onDataChange', data);
            });
            
            await this.modules.realtime.initialize();
            console.log('✅ Module temps réel initialisé');
        }
    }

    // Initialiser le module auto-init
    async initializeAutoInit() {
        if (typeof SupabaseAutoInit !== 'undefined' && this.config.autoInit) {
            this.modules.autoInit = new SupabaseAutoInit(this.config);
            this.modules.autoInit.client = this.client;
            
            const result = await this.modules.autoInit.initialize();
            if (result.success) {
                console.log('✅ Module auto-init initialisé');
            } else {
                console.warn('⚠️ Auto-init partiellement réussi');
            }
        }
    }

    // Initialiser le module de monitoring
    async initializeMonitor() {
        if (typeof SupabaseMonitor !== 'undefined' && this.config.monitoring?.enabled) {
            this.modules.monitor = new SupabaseMonitor(this.config);
            await this.modules.monitor.startMonitoring(this.client);
            console.log('✅ Module monitoring initialisé');
        }
    }

    // Initialiser le module de synchronisation
    async initializeSync() {
        if (typeof SupabaseSync !== 'undefined' && this.config.features?.realTimeSync) {
            this.modules.sync = new SupabaseSync(this.config, this.client);
            console.log('✅ Module synchronisation initialisé');
        }
    }

    // Initialiser le module de migration
    async initializeMigration() {
        if (typeof SupabaseMigration !== 'undefined' && this.config.migrations?.enabled) {
            this.modules.migration = new SupabaseMigration(this.config, this.client);
            
            if (this.config.migrations.autoRun) {
                const result = await this.modules.migration.runMigrations();
                console.log(`✅ Migrations exécutées: ${result.migrationsRun}`);
            }
        }
    }

    // Configurer les événements globaux
    setupGlobalEvents() {
        // Écouter les changements de visibilité de la page
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                this.handlePageVisible();
            } else {
                this.handlePageHidden();
            }
        });
        
        // Écouter les événements de connexion
        window.addEventListener('online', () => {
            console.log('🌐 Connexion rétablie');
            this.handleConnectionRestored();
        });
        
        window.addEventListener('offline', () => {
            console.log('🌐 Connexion perdue');
            this.handleConnectionLost();
        });
        
        // Écouter les erreurs globales
        window.addEventListener('error', (event) => {
            this.handleGlobalError(event.error);
        });
        
        // Écouter les erreurs de promesses non gérées
        window.addEventListener('unhandledrejection', (event) => {
            this.handleGlobalError(event.reason);
        });
    }

    // Gérer la page visible
    handlePageVisible() {
        console.log('👁️ Page visible');
        
        // Réactiver les modules si nécessaire
        if (this.modules.realtime) {
            this.modules.realtime.updateUserPresence();
        }
        
        // Vérifier l'état du système
        this.checkSystemHealth();
    }

    // Gérer la page cachée
    handlePageHidden() {
        console.log('👁️ Page cachée');
        
        // Optimiser les ressources
        if (this.modules.monitor) {
            this.modules.monitor.pauseMonitoring();
        }
    }

    // Gérer la restauration de connexion
    async handleConnectionRestored() {
        try {
            // Réinitialiser les modules qui en ont besoin
            if (this.modules.realtime) {
                await this.modules.realtime.handleReconnection();
            }
            
            if (this.modules.sync) {
                await this.modules.sync.resumeSync();
            }
            
        } catch (error) {
            console.error('❌ Erreur restauration connexion:', error);
        }
    }

    // Gérer la perte de connexion
    handleConnectionLost() {
        // Passer en mode hors ligne
        if (this.modules.sync) {
            this.modules.sync.pauseSync();
        }
        
        this.systemState.status = 'offline';
    }

    // Gérer les erreurs globales
    handleGlobalError(error) {
        console.error('❌ Erreur globale:', error);
        
        this.systemState.errors.push({
            message: error.message || error.toString(),
            timestamp: new Date(),
            type: 'global',
            stack: error.stack
        });
        
        // Limiter le nombre d'erreurs stockées
        if (this.systemState.errors.length > 50) {
            this.systemState.errors = this.systemState.errors.slice(-25);
        }
    }

    // Démarrer la surveillance du système
    startSystemMonitoring() {
        setInterval(() => {
            this.checkSystemHealth();
        }, 60000); // Vérifier toutes les minutes
        
        console.log('📊 Surveillance du système démarrée');
    }

    // Vérifier la santé du système
    async checkSystemHealth() {
        try {
            this.systemState.lastCheck = new Date();
            
            // Vérifier la connexion Supabase
            const { error } = await this.client.auth.getSession();
            
            if (error && error.message !== 'Invalid session') {
                throw error;
            }
            
            // Vérifier l'état des modules
            const moduleStatuses = {};
            Object.keys(this.modules).forEach(name => {
                const module = this.modules[name];
                moduleStatuses[name] = module ? 'active' : 'inactive';
            });
            
            this.systemState.status = 'healthy';
            this.systemState.modules = moduleStatuses;
            
        } catch (error) {
            console.warn('⚠️ Problème de santé système:', error);
            this.systemState.status = 'degraded';
        }
    }

    // Obtenir le statut du système
    getSystemStatus() {
        return {
            isInitialized: this.isInitialized,
            systemState: this.systemState,
            modules: Object.keys(this.modules).reduce((acc, name) => {
                acc[name] = {
                    available: !!this.modules[name],
                    status: this.systemState.modules[name] || 'unknown'
                };
                return acc;
            }, {}),
            config: {
                url: this.config?.url,
                features: this.config?.features,
                realtime: this.config?.realtime?.enabled
            }
        };
    }

    // Obtenir un module spécifique
    getModule(name) {
        return this.modules[name] || null;
    }

    // Redémarrer un module
    async restartModule(name) {
        try {
            console.log(`🔄 Redémarrage du module ${name}...`);
            
            // Nettoyer le module existant
            if (this.modules[name] && typeof this.modules[name].destroy === 'function') {
                this.modules[name].destroy();
            }
            
            // Réinitialiser le module
            const initializer = this[`initialize${name.charAt(0).toUpperCase() + name.slice(1)}`];
            if (typeof initializer === 'function') {
                await initializer.call(this);
                console.log(`✅ Module ${name} redémarré`);
                return true;
            }
            
            throw new Error(`Initialiseur non trouvé pour ${name}`);
            
        } catch (error) {
            console.error(`❌ Erreur redémarrage ${name}:`, error);
            return false;
        }
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Déclencher un callback
    triggerCallback(event, data = null) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback ${event}:`, error);
                }
            });
        }
    }

    // Nettoyer le système
    destroy() {
        // Nettoyer tous les modules
        Object.values(this.modules).forEach(module => {
            if (module && typeof module.destroy === 'function') {
                module.destroy();
            }
        });
        
        // Nettoyer les callbacks
        Object.keys(this.callbacks).forEach(key => {
            this.callbacks[key] = [];
        });
        
        this.isInitialized = false;
        console.log('🧹 Système Supabase nettoyé');
    }
}

// Instance globale du système
let supabaseSystem = null;

// Fonction d'initialisation globale
async function initializeSupabaseComplete() {
    if (!supabaseSystem) {
        supabaseSystem = new SupabaseSystemComplete();
    }
    
    return await supabaseSystem.initialize();
}

// Fonction pour obtenir le système
function getSupabaseSystem() {
    return supabaseSystem;
}

// Export pour utilisation
if (typeof window !== 'undefined') {
    window.SupabaseSystemComplete = SupabaseSystemComplete;
    window.initializeSupabaseComplete = initializeSupabaseComplete;
    window.getSupabaseSystem = getSupabaseSystem;
    
    // Auto-initialisation si demandée
    if (typeof SUPABASE_CONFIG !== 'undefined' && SUPABASE_CONFIG.autoInit) {
        document.addEventListener('DOMContentLoaded', () => {
            initializeSupabaseComplete().catch(error => {
                console.error('❌ Erreur auto-initialisation:', error);
            });
        });
    }
}
