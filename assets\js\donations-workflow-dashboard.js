/**
 * Module de tableau de bord workflow pour la gestion des dons IPT
 */

class WorkflowDashboard {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.donations = [];
        this.stats = {};
        this.refreshInterval = null;
    }

    /**
     * Initialiser le dashboard
     */
    async initialize() {
        console.log('📊 Initialisation du dashboard workflow...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            if (window.authSystem) {
                this.currentUser = await window.authSystem.getCurrentUser();
            }

            if (!this.currentUser) {
                throw new Error('Utilisateur non authentifié');
            }

            // Configurer les événements
            this.setupEventListeners();

            // Charger les données initiales
            await this.loadDashboardData();

            // Configurer le rafraîchissement automatique (toutes les 30 secondes)
            this.refreshInterval = setInterval(() => {
                this.loadDashboardData();
            }, 30000);

            console.log('✅ Dashboard workflow initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation dashboard:', error);
            throw error;
        }
    }

    /**
     * Configurer les écouteurs d'événements
     */
    setupEventListeners() {
        // Filtres
        document.getElementById('filterStatut')?.addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('filterType')?.addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('filterResponsable')?.addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('filterEtat')?.addEventListener('change', () => {
            this.applyFilters();
        });

        // Étapes du timeline
        document.querySelectorAll('.step-item').forEach(step => {
            step.addEventListener('click', (e) => {
                const stepNumber = e.target.dataset.step;
                this.showStepDetails(stepNumber);
            });
        });
    }

    /**
     * Charger les données du dashboard
     */
    async loadDashboardData() {
        try {
            console.log('📥 Chargement des données dashboard...');

            // Charger les dons avec workflow détaillé
            const { data: donations, error: donationsError } = await this.supabase
                .from('donations_workflow_complete')
                .select('*')
                .not('statut', 'eq', 'archive')
                .order('created_at', { ascending: false });

            if (donationsError) throw donationsError;

            this.donations = donations || [];

            // Charger les statistiques
            await this.loadStatistics();

            // Mettre à jour l'affichage
            this.updateStatistics();
            this.renderDonations();
            this.updateTimeline();

            console.log(`✅ ${this.donations.length} dons chargés`);

        } catch (error) {
            console.error('❌ Erreur chargement données:', error);
        }
    }

    /**
     * Charger les statistiques
     */
    async loadStatistics() {
        try {
            // Statistiques globales
            const totalDons = this.donations.length;
            
            // Dons en retard
            const { data: donsEnRetard, error: retardError } = await this.supabase
                .from('donations_en_retard')
                .select('count');

            if (retardError) throw retardError;

            // Temps moyen de traitement
            const tempsTotal = this.donations.reduce((sum, don) => {
                return sum + (don.duree_traitement_jours || 0);
            }, 0);
            const tempsMoyen = totalDons > 0 ? Math.round(tempsTotal / totalDons) : 0;

            // Taux de completion
            const donsCompletes = this.donations.filter(don => 
                don.etapes_terminees === don.etapes_totales
            ).length;
            const tauxCompletion = totalDons > 0 ? Math.round((donsCompletes / totalDons) * 100) : 0;

            this.stats = {
                totalDons,
                donsEnRetard: donsEnRetard?.length || 0,
                tempsMoyen,
                tauxCompletion
            };

        } catch (error) {
            console.error('❌ Erreur chargement statistiques:', error);
            this.stats = {
                totalDons: this.donations.length,
                donsEnRetard: 0,
                tempsMoyen: 0,
                tauxCompletion: 0
            };
        }
    }

    /**
     * Mettre à jour l'affichage des statistiques
     */
    updateStatistics() {
        document.getElementById('totalDons').textContent = this.stats.totalDons;
        document.getElementById('donsEnRetard').textContent = this.stats.donsEnRetard;
        document.getElementById('tempsMovenTraitement').textContent = this.stats.tempsMoyen;
        document.getElementById('tauxCompletion').textContent = `${this.stats.tauxCompletion}%`;
    }

    /**
     * Mettre à jour la timeline des étapes
     */
    updateTimeline() {
        const stepItems = document.querySelectorAll('.step-item');
        
        // Réinitialiser les classes
        stepItems.forEach(item => {
            item.className = 'step-item';
        });

        // Analyser les dons pour déterminer l'état de chaque étape
        const stepStats = {};
        for (let i = 1; i <= 10; i++) {
            stepStats[i] = {
                total: 0,
                completed: 0,
                current: 0,
                delayed: 0
            };
        }

        this.donations.forEach(don => {
            if (don.etapes) {
                don.etapes.forEach(etape => {
                    const stepNum = etape.numero;
                    stepStats[stepNum].total++;
                    
                    if (etape.statut === 'termine') {
                        stepStats[stepNum].completed++;
                    } else if (etape.statut === 'en_cours') {
                        stepStats[stepNum].current++;
                        if (etape.en_retard) {
                            stepStats[stepNum].delayed++;
                        }
                    }
                });
            }
        });

        // Appliquer les styles aux étapes
        stepItems.forEach((item, index) => {
            const stepNum = index + 1;
            const stats = stepStats[stepNum];
            
            if (stats.delayed > 0) {
                item.classList.add('delayed');
            } else if (stats.current > 0) {
                item.classList.add('current');
            } else if (stats.completed > 0) {
                item.classList.add('completed');
            }
        });
    }

    /**
     * Afficher les dons
     */
    renderDonations() {
        const container = document.getElementById('donationsGrid');
        if (!container) return;

        if (this.donations.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <h3>Aucun don en cours</h3>
                    <p>Tous les dons ont été traités ou aucun don n'est en cours de traitement.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.donations.map(don => this.renderDonationCard(don)).join('');
    }

    /**
     * Générer le HTML d'une carte de don
     */
    renderDonationCard(don) {
        const currentStep = this.getCurrentStep(don);
        const progress = this.calculateProgress(don);
        const isDelayed = this.isDonationDelayed(don);
        
        return `
            <div class="donation-workflow-card" data-id="${don.id}">
                <div class="card-header">
                    <div class="card-title">${don.numero_don}</div>
                    <div class="card-status ${don.statut}">
                        ${this.getStatusLabel(don.statut)}
                    </div>
                </div>
                
                <div class="donation-info">
                    <div><strong>Type:</strong> ${don.type_don}</div>
                    <div><strong>Donneur:</strong> ${don.donneur_nom}</div>
                    <div><strong>Demandeur:</strong> ${don.demandeur_nom}</div>
                    <div><strong>Service:</strong> ${don.demandeur_service}</div>
                </div>
                
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progress}%"></div>
                </div>
                <div style="text-align: center; font-size: 12px; color: #6b7280;">
                    Progression: ${don.etapes_terminees || 0}/${don.etapes_totales || 10} étapes (${progress}%)
                </div>
                
                ${currentStep ? `
                    <div class="current-step">
                        <div class="step-info">
                            <div>
                                <div class="step-name">Étape ${currentStep.numero}: ${currentStep.nom}</div>
                                <div style="font-size: 12px; color: #6b7280;">
                                    Responsable: ${currentStep.responsable_role}
                                    ${currentStep.responsable_nom ? ` (${currentStep.responsable_nom})` : ''}
                                </div>
                            </div>
                            <div class="step-duration">
                                ${isDelayed ? 
                                    '<span class="delayed-indicator">EN RETARD</span>' : 
                                    '<span class="on-time-indicator">DANS LES TEMPS</span>'
                                }
                            </div>
                        </div>
                        
                        ${this.canUserActOnStep(currentStep) ? `
                            <div class="step-actions">
                                <button onclick="workflowDashboard.completeStep('${don.id}', ${currentStep.numero})" 
                                        class="btn-step btn-complete">
                                    ✅ Terminer l'étape
                                </button>
                                <button onclick="workflowDashboard.blockStep('${don.id}', ${currentStep.numero})" 
                                        class="btn-step btn-block">
                                    ⚠️ Bloquer
                                </button>
                            </div>
                        ` : ''}
                    </div>
                ` : ''}
                
                <div style="margin-top: 15px; text-align: center;">
                    <button onclick="workflowDashboard.showWorkflowDetails('${don.id}')" 
                            class="btn-step btn-details">
                        📋 Voir détails complets
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Obtenir l'étape actuelle d'un don
     */
    getCurrentStep(don) {
        if (!don.etapes) return null;
        return don.etapes.find(etape => etape.statut === 'en_cours');
    }

    /**
     * Calculer le pourcentage de progression
     */
    calculateProgress(don) {
        if (!don.etapes_totales || don.etapes_totales === 0) return 0;
        return Math.round(((don.etapes_terminees || 0) / don.etapes_totales) * 100);
    }

    /**
     * Vérifier si un don est en retard
     */
    isDonationDelayed(don) {
        if (!don.etapes) return false;
        const currentStep = this.getCurrentStep(don);
        return currentStep ? currentStep.en_retard : false;
    }

    /**
     * Obtenir le libellé du statut
     */
    getStatusLabel(statut) {
        const labels = {
            'bp_cree': 'BP Créé',
            'chez_magasinier': 'Chez Magasinier',
            'chez_transitaire': 'Chez Transitaire',
            'chez_receptionniste': 'Chez Réceptionniste',
            'bs_cree': 'BS Créé',
            'chez_rve': 'Chez RVE',
            'recap_cree': 'Récap Créé',
            'envoye_comptabilite': 'Envoyé Comptabilité'
        };
        return labels[statut] || statut;
    }

    /**
     * Vérifier si l'utilisateur peut agir sur une étape
     */
    canUserActOnStep(step) {
        const userRole = this.currentUser?.user_metadata?.role || 'user';
        const userEmail = this.currentUser?.email;
        
        // Admin peut tout faire
        if (userRole === 'admin') return true;
        
        // Vérifier selon le rôle et l'étape
        const roleMapping = {
            'magasinier': ['magasinier'],
            'transitaire': ['transitaire'],
            'receptionniste': ['receptionniste'],
            'rve': ['rve']
        };
        
        return roleMapping[userRole]?.includes(step.responsable_role) || false;
    }

    /**
     * Terminer une étape
     */
    async completeStep(donationId, stepNumber) {
        try {
            const commentaire = prompt('Commentaire (optionnel):') || '';
            
            const { error } = await this.supabase
                .rpc('advance_to_next_step', {
                    p_donation_id: donationId,
                    p_current_step: stepNumber,
                    p_user_email: this.currentUser.email,
                    p_commentaire: commentaire
                });

            if (error) throw error;

            // Recharger les données
            await this.loadDashboardData();
            
            // Notification
            this.showNotification('Étape terminée avec succès!', 'success');

        } catch (error) {
            console.error('❌ Erreur completion étape:', error);
            this.showNotification('Erreur lors de la completion de l\'étape', 'error');
        }
    }

    /**
     * Bloquer une étape
     */
    async blockStep(donationId, stepNumber) {
        try {
            const raison = prompt('Raison du blocage:');
            if (!raison) return;
            
            const { error } = await this.supabase
                .from('gestion_donation_etapes')
                .update({
                    statut_etape: 'bloque',
                    commentaire: raison,
                    updated_at: new Date().toISOString()
                })
                .eq('donation_id', donationId)
                .eq('numero_etape', stepNumber);

            if (error) throw error;

            // Recharger les données
            await this.loadDashboardData();
            
            // Notification
            this.showNotification('Étape bloquée', 'warning');

        } catch (error) {
            console.error('❌ Erreur blocage étape:', error);
            this.showNotification('Erreur lors du blocage de l\'étape', 'error');
        }
    }

    /**
     * Afficher les détails complets du workflow
     */
    async showWorkflowDetails(donationId) {
        try {
            // Récupérer les détails complets
            const { data, error } = await this.supabase
                .rpc('get_donation_detailed_status', {
                    p_donation_id: donationId
                });

            if (error) throw error;

            this.renderWorkflowModal(data);

        } catch (error) {
            console.error('❌ Erreur chargement détails:', error);
            this.showNotification('Erreur lors du chargement des détails', 'error');
        }
    }

    /**
     * Afficher les détails d'une étape
     */
    showStepDetails(stepNumber) {
        const donsEtape = this.donations.filter(don => {
            if (!don.etapes) return false;
            const currentStep = this.getCurrentStep(don);
            return currentStep && currentStep.numero == stepNumber;
        });

        console.log(`Étape ${stepNumber}: ${donsEtape.length} dons en cours`);
        // Ici on pourrait ouvrir un modal avec les détails de l'étape
    }

    /**
     * Appliquer les filtres
     */
    applyFilters() {
        const statutFilter = document.getElementById('filterStatut').value;
        const typeFilter = document.getElementById('filterType').value;
        const responsableFilter = document.getElementById('filterResponsable').value;
        const etatFilter = document.getElementById('filterEtat').value;

        let filteredDonations = [...this.donations];

        if (statutFilter) {
            filteredDonations = filteredDonations.filter(don => don.statut === statutFilter);
        }

        if (typeFilter) {
            filteredDonations = filteredDonations.filter(don => don.type_don === typeFilter);
        }

        if (responsableFilter) {
            filteredDonations = filteredDonations.filter(don => {
                const currentStep = this.getCurrentStep(don);
                return currentStep && currentStep.responsable_role === responsableFilter;
            });
        }

        if (etatFilter) {
            filteredDonations = filteredDonations.filter(don => {
                const isDelayed = this.isDonationDelayed(don);
                return etatFilter === 'en_retard' ? isDelayed : !isDelayed;
            });
        }

        // Temporairement remplacer les donations pour l'affichage
        const originalDonations = this.donations;
        this.donations = filteredDonations;
        this.renderDonations();
        this.donations = originalDonations;
    }

    /**
     * Afficher une notification
     */
    showNotification(message, type = 'info') {
        // Utiliser le système de notifications existant si disponible
        if (typeof showMessage === 'function') {
            showMessage(message, type);
        } else {
            alert(message);
        }
    }

    /**
     * Nettoyer les ressources
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
    }
}

// Instance globale
window.workflowDashboard = new WorkflowDashboard();
window.WorkflowDashboard = WorkflowDashboard;
