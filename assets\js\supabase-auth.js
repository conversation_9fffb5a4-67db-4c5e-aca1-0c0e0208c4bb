// نظام المصادقة Supabase
// Supabase Authentication System

class SupabaseAuth {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.currentUser = null;
        this.isAuthenticated = false;
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 ساعة
        
        // callbacks للأحداث
        this.callbacks = {
            onSignIn: [],
            onSignOut: [],
            onSignUp: [],
            onPasswordReset: [],
            onUserUpdate: [],
            onError: [],
            onSessionExpired: []
        };
        
        // إعدادات المصادقة
        this.authSettings = {
            requireEmailConfirmation: true,
            allowSignUp: true,
            passwordMinLength: 8,
            sessionPersistence: true,
            autoRefreshToken: true,
            redirectTo: window.location.origin
        };
    }

    // تهيئة نظام المصادقة
    async initialize() {
        console.log('🔐 تهيئة نظام المصادقة Supabase...');
        
        try {
            // التحقق من الجلسة الحالية
            await this.checkCurrentSession();
            
            // إعداد مراقب تغيير حالة المصادقة
            this.setupAuthStateListener();
            
            // إعداد تحديث الرمز المميز التلقائي
            if (this.authSettings.autoRefreshToken) {
                this.setupTokenRefresh();
            }
            
            console.log('✅ نظام المصادقة جاهز');
            return true;
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة المصادقة:', error);
            this.triggerCallback('onError', error);
            return false;
        }
    }

    // التحقق من الجلسة الحالية
    async checkCurrentSession() {
        try {
            const { data: { session }, error } = await this.client.auth.getSession();
            
            if (error) {
                console.error('خطأ في جلب الجلسة:', error);
                return null;
            }
            
            if (session) {
                await this.handleSuccessfulAuth(session);
                console.log('✅ جلسة نشطة موجودة');
            } else {
                console.log('ℹ️ لا توجد جلسة نشطة');
            }
            
            return session;
            
        } catch (error) {
            console.error('❌ خطأ في التحقق من الجلسة:', error);
            return null;
        }
    }

    // إعداد مراقب حالة المصادقة
    setupAuthStateListener() {
        this.client.auth.onAuthStateChange(async (event, session) => {
            console.log(`🔐 تغيير حالة المصادقة: ${event}`);
            
            switch (event) {
                case 'SIGNED_IN':
                    await this.handleSuccessfulAuth(session);
                    this.triggerCallback('onSignIn', session);
                    break;
                    
                case 'SIGNED_OUT':
                    this.handleSignOut();
                    this.triggerCallback('onSignOut');
                    break;
                    
                case 'TOKEN_REFRESHED':
                    console.log('🔄 تم تحديث الرمز المميز');
                    await this.handleSuccessfulAuth(session);
                    break;
                    
                case 'USER_UPDATED':
                    if (session) {
                        await this.handleSuccessfulAuth(session);
                        this.triggerCallback('onUserUpdate', session.user);
                    }
                    break;
                    
                case 'PASSWORD_RECOVERY':
                    console.log('🔑 طلب استعادة كلمة المرور');
                    break;
            }
        });
    }

    // تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
    async signInWithEmail(email, password) {
        try {
            console.log(`🔐 محاولة تسجيل الدخول: ${email}`);
            
            const { data, error } = await this.client.auth.signInWithPassword({
                email: email.trim().toLowerCase(),
                password: password
            });
            
            if (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error);
                this.triggerCallback('onError', error);
                return { success: false, error: this.translateError(error) };
            }
            
            console.log('✅ تم تسجيل الدخول بنجاح');
            return { success: true, user: data.user, session: data.session };
            
        } catch (error) {
            console.error('❌ خطأ غير متوقع في تسجيل الدخول:', error);
            this.triggerCallback('onError', error);
            return { success: false, error: 'حدث خطأ غير متوقع' };
        }
    }

    // إنشاء حساب جديد
    async signUpWithEmail(email, password, userData = {}) {
        try {
            console.log(`📝 إنشاء حساب جديد: ${email}`);
            
            // التحقق من صحة البيانات
            const validation = this.validateSignUpData(email, password, userData);
            if (!validation.valid) {
                return { success: false, error: validation.error };
            }
            
            const { data, error } = await this.client.auth.signUp({
                email: email.trim().toLowerCase(),
                password: password,
                options: {
                    data: {
                        nom: userData.nom || '',
                        prenom: userData.prenom || '',
                        role: userData.role || 'user',
                        service: userData.service || '',
                        created_at: new Date().toISOString()
                    },
                    emailRedirectTo: this.authSettings.redirectTo
                }
            });
            
            if (error) {
                console.error('❌ خطأ في إنشاء الحساب:', error);
                this.triggerCallback('onError', error);
                return { success: false, error: this.translateError(error) };
            }
            
            console.log('✅ تم إنشاء الحساب بنجاح');
            this.triggerCallback('onSignUp', data);
            
            return { 
                success: true, 
                user: data.user, 
                session: data.session,
                needsConfirmation: !data.session
            };
            
        } catch (error) {
            console.error('❌ خطأ غير متوقع في إنشاء الحساب:', error);
            this.triggerCallback('onError', error);
            return { success: false, error: 'حدث خطأ غير متوقع' };
        }
    }

    // تسجيل الخروج
    async signOut() {
        try {
            console.log('🚪 تسجيل الخروج...');
            
            const { error } = await this.client.auth.signOut();
            
            if (error) {
                console.error('❌ خطأ في تسجيل الخروج:', error);
                this.triggerCallback('onError', error);
                return { success: false, error: this.translateError(error) };
            }
            
            console.log('✅ تم تسجيل الخروج بنجاح');
            return { success: true };
            
        } catch (error) {
            console.error('❌ خطأ غير متوقع في تسجيل الخروج:', error);
            this.triggerCallback('onError', error);
            return { success: false, error: 'حدث خطأ غير متوقع' };
        }
    }

    // إعادة تعيين كلمة المرور
    async resetPassword(email) {
        try {
            console.log(`🔑 طلب إعادة تعيين كلمة المرور: ${email}`);
            
            const { error } = await this.client.auth.resetPasswordForEmail(
                email.trim().toLowerCase(),
                {
                    redirectTo: `${this.authSettings.redirectTo}/reset-password.html`
                }
            );
            
            if (error) {
                console.error('❌ خطأ في إعادة تعيين كلمة المرور:', error);
                this.triggerCallback('onError', error);
                return { success: false, error: this.translateError(error) };
            }
            
            console.log('✅ تم إرسال رابط إعادة التعيين');
            this.triggerCallback('onPasswordReset', { email });
            return { success: true };
            
        } catch (error) {
            console.error('❌ خطأ غير متوقع في إعادة تعيين كلمة المرور:', error);
            this.triggerCallback('onError', error);
            return { success: false, error: 'حدث خطأ غير متوقع' };
        }
    }

    // تحديث كلمة المرور
    async updatePassword(newPassword) {
        try {
            console.log('🔐 تحديث كلمة المرور...');
            
            if (newPassword.length < this.authSettings.passwordMinLength) {
                return { 
                    success: false, 
                    error: `كلمة المرور يجب أن تكون ${this.authSettings.passwordMinLength} أحرف على الأقل` 
                };
            }
            
            const { error } = await this.client.auth.updateUser({
                password: newPassword
            });
            
            if (error) {
                console.error('❌ خطأ في تحديث كلمة المرور:', error);
                this.triggerCallback('onError', error);
                return { success: false, error: this.translateError(error) };
            }
            
            console.log('✅ تم تحديث كلمة المرور بنجاح');
            return { success: true };
            
        } catch (error) {
            console.error('❌ خطأ غير متوقع في تحديث كلمة المرور:', error);
            this.triggerCallback('onError', error);
            return { success: false, error: 'حدث خطأ غير متوقع' };
        }
    }

    // تحديث بيانات المستخدم
    async updateUserData(userData) {
        try {
            console.log('👤 تحديث بيانات المستخدم...');
            
            const { error } = await this.client.auth.updateUser({
                data: {
                    ...userData,
                    updated_at: new Date().toISOString()
                }
            });
            
            if (error) {
                console.error('❌ خطأ في تحديث البيانات:', error);
                this.triggerCallback('onError', error);
                return { success: false, error: this.translateError(error) };
            }
            
            console.log('✅ تم تحديث البيانات بنجاح');
            return { success: true };
            
        } catch (error) {
            console.error('❌ خطأ غير متوقع في تحديث البيانات:', error);
            this.triggerCallback('onError', error);
            return { success: false, error: 'حدث خطأ غير متوقع' };
        }
    }

    // التعامل مع المصادقة الناجحة
    async handleSuccessfulAuth(session) {
        this.currentUser = session.user;
        this.isAuthenticated = true;
        
        // حفظ بيانات المستخدم في localStorage
        if (this.authSettings.sessionPersistence) {
            localStorage.setItem('currentUser', JSON.stringify({
                id: session.user.id,
                email: session.user.email,
                username: session.user.user_metadata?.username || session.user.email,
                nom: session.user.user_metadata?.nom || '',
                prenom: session.user.user_metadata?.prenom || '',
                role: session.user.user_metadata?.role || 'user',
                service: session.user.user_metadata?.service || '',
                last_sign_in: session.user.last_sign_in_at,
                created_at: session.user.created_at
            }));
        }
        
        // تحديث واجهة المستخدم
        this.updateUIForAuthenticatedUser();
        
        console.log('✅ تم تسجيل الدخول:', session.user.email);
    }

    // التعامل مع تسجيل الخروج
    handleSignOut() {
        this.currentUser = null;
        this.isAuthenticated = false;
        
        // مسح البيانات المحفوظة
        localStorage.removeItem('currentUser');
        
        // تحديث واجهة المستخدم
        this.updateUIForUnauthenticatedUser();
        
        console.log('✅ تم تسجيل الخروج');
    }

    // تحديث واجهة المستخدم للمستخدم المسجل
    updateUIForAuthenticatedUser() {
        // إخفاء نماذج تسجيل الدخول
        const loginForms = document.querySelectorAll('.auth-form, .login-form');
        loginForms.forEach(form => form.style.display = 'none');
        
        // إظهار المحتوى الرئيسي
        const mainContent = document.querySelector('.main-content, #main-app');
        if (mainContent) {
            mainContent.style.display = 'block';
        }
        
        // تحديث معلومات المستخدم في الواجهة
        this.updateUserInfoInUI();
    }

    // تحديث واجهة المستخدم للمستخدم غير المسجل
    updateUIForUnauthenticatedUser() {
        // إظهار نماذج تسجيل الدخول
        const loginForms = document.querySelectorAll('.auth-form, .login-form');
        loginForms.forEach(form => form.style.display = 'block');
        
        // إخفاء المحتوى الرئيسي
        const mainContent = document.querySelector('.main-content, #main-app');
        if (mainContent) {
            mainContent.style.display = 'none';
        }
    }

    // تحديث معلومات المستخدم في الواجهة
    updateUserInfoInUI() {
        if (!this.currentUser) return;
        
        const userNameElements = document.querySelectorAll('.user-name, #user-name');
        const userEmailElements = document.querySelectorAll('.user-email, #user-email');
        const userRoleElements = document.querySelectorAll('.user-role, #user-role');
        
        const userData = this.currentUser.user_metadata || {};
        const displayName = userData.prenom && userData.nom 
            ? `${userData.prenom} ${userData.nom}` 
            : this.currentUser.email;
        
        userNameElements.forEach(el => el.textContent = displayName);
        userEmailElements.forEach(el => el.textContent = this.currentUser.email);
        userRoleElements.forEach(el => el.textContent = userData.role || 'مستخدم');
    }

    // التحقق من صحة بيانات التسجيل
    validateSignUpData(email, password, userData) {
        // التحقق من البريد الإلكتروني
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            return { valid: false, error: 'البريد الإلكتروني غير صحيح' };
        }
        
        // التحقق من كلمة المرور
        if (password.length < this.authSettings.passwordMinLength) {
            return { 
                valid: false, 
                error: `كلمة المرور يجب أن تكون ${this.authSettings.passwordMinLength} أحرف على الأقل` 
            };
        }
        
        // التحقق من الاسم
        if (!userData.nom || !userData.prenom) {
            return { valid: false, error: 'الاسم الأول والأخير مطلوبان' };
        }
        
        return { valid: true };
    }

    // ترجمة رسائل الخطأ
    translateError(error) {
        const errorMessages = {
            'Invalid login credentials': 'بيانات تسجيل الدخول غير صحيحة',
            'Email not confirmed': 'البريد الإلكتروني غير مؤكد',
            'User already registered': 'المستخدم مسجل مسبقاً',
            'Password should be at least 6 characters': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
            'Invalid email': 'البريد الإلكتروني غير صحيح',
            'Signup is disabled': 'التسجيل معطل حالياً',
            'Email rate limit exceeded': 'تم تجاوز حد إرسال الرسائل',
            'Too many requests': 'طلبات كثيرة جداً، حاول لاحقاً'
        };
        
        return errorMessages[error.message] || error.message || 'حدث خطأ غير متوقع';
    }

    // إعداد تحديث الرمز المميز التلقائي
    setupTokenRefresh() {
        setInterval(async () => {
            if (this.isAuthenticated) {
                try {
                    const { data, error } = await this.client.auth.refreshSession();
                    if (error) {
                        console.warn('⚠️ فشل في تحديث الرمز المميز:', error);
                        this.triggerCallback('onSessionExpired');
                    }
                } catch (error) {
                    console.warn('⚠️ خطأ في تحديث الرمز المميز:', error);
                }
            }
        }, 30 * 60 * 1000); // كل 30 دقيقة
    }

    // إضافة callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // إزالة callback
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    // تشغيل callbacks
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`خطأ في callback ${event}:`, error);
                }
            });
        }
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // التحقق من حالة المصادقة
    isUserAuthenticated() {
        return this.isAuthenticated;
    }

    // الحصول على الجلسة الحالية
    async getCurrentSession() {
        try {
            const { data: { session }, error } = await this.client.auth.getSession();
            return error ? null : session;
        } catch (error) {
            console.error('خطأ في جلب الجلسة:', error);
            return null;
        }
    }

    // الحصول على إحصائيات المصادقة
    getAuthStats() {
        return {
            isAuthenticated: this.isAuthenticated,
            currentUser: this.currentUser ? {
                id: this.currentUser.id,
                email: this.currentUser.email,
                lastSignIn: this.currentUser.last_sign_in_at,
                createdAt: this.currentUser.created_at
            } : null,
            settings: this.authSettings
        };
    }
}

// تصدير الكلاس
if (typeof window !== 'undefined') {
    window.SupabaseAuth = SupabaseAuth;
}
