// Gestionnaire de présence utilisateur en temps réel
// Affichage des utilisateurs connectés et de leur activité

class UserPresence {
    constructor(config, client) {
        this.config = config;
        this.client = client;
        this.isActive = false;
        this.currentUser = null;
        this.connectedUsers = new Map();
        this.presenceChannel = null;
        this.activityTimeout = 300000; // 5 minutes d'inactivité
        this.lastActivity = Date.now();
        
        // Statuts de présence
        this.presenceStates = {
            online: { color: '#28a745', icon: '🟢', label: 'En ligne' },
            away: { color: '#ffc107', icon: '🟡', label: 'Absent' },
            busy: { color: '#dc3545', icon: '🔴', label: 'Occupé' },
            offline: { color: '#6c757d', icon: '⚫', label: 'Hors ligne' }
        };
        
        // Callbacks
        this.callbacks = {
            onUserJoin: [],
            onUserLeave: [],
            onUserUpdate: [],
            onPresenceChange: []
        };
        
        // Configuration de l'interface
        this.uiConfig = {
            showPresenceIndicator: true,
            showUserList: true,
            showActivityStatus: true,
            updateInterval: 30000 // 30 secondes
        };
    }

    // Initialiser la présence utilisateur
    async initialize(currentUser) {
        if (this.isActive) {
            console.log('⚠️ Présence utilisateur déjà active');
            return;
        }

        this.currentUser = currentUser;
        console.log(`👤 Initialisation présence pour ${currentUser.username}...`);
        
        try {
            // Configurer le canal de présence
            await this.setupPresenceChannel();
            
            // Configurer la détection d'activité
            this.setupActivityDetection();
            
            // Créer l'interface de présence
            this.createPresenceUI();
            
            // Démarrer les mises à jour périodiques
            this.startPeriodicUpdates();
            
            this.isActive = true;
            console.log('✅ Présence utilisateur active');
            
        } catch (error) {
            console.error('❌ Erreur initialisation présence:', error);
            throw error;
        }
    }

    // Configurer le canal de présence
    async setupPresenceChannel() {
        try {
            this.presenceChannel = this.client
                .channel('user_presence', {
                    config: {
                        presence: {
                            key: this.currentUser.username
                        }
                    }
                })
                .on('presence', { event: 'sync' }, () => {
                    this.handlePresenceSync();
                })
                .on('presence', { event: 'join' }, ({ key, newPresences }) => {
                    this.handleUserJoin(key, newPresences);
                })
                .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
                    this.handleUserLeave(key, leftPresences);
                })
                .subscribe(async (status) => {
                    if (status === 'SUBSCRIBED') {
                        await this.trackUserPresence();
                        console.log('✅ Canal de présence configuré');
                    } else if (status === 'CHANNEL_ERROR') {
                        console.error('❌ Erreur canal de présence');
                    }
                });
            
        } catch (error) {
            console.error('❌ Erreur configuration canal présence:', error);
            throw error;
        }
    }

    // Suivre la présence de l'utilisateur actuel
    async trackUserPresence() {
        const presenceData = {
            user_id: this.currentUser.id || this.currentUser.username,
            username: this.currentUser.username,
            name: `${this.currentUser.prenom} ${this.currentUser.nom}`,
            role: this.currentUser.role,
            status: 'online',
            last_seen: new Date().toISOString(),
            joined_at: new Date().toISOString(),
            activity: 'active'
        };
        
        try {
            await this.presenceChannel.track(presenceData);
            console.log('👤 Présence utilisateur trackée');
        } catch (error) {
            console.error('❌ Erreur tracking présence:', error);
        }
    }

    // Gérer la synchronisation de présence
    handlePresenceSync() {
        const state = this.presenceChannel.presenceState();
        console.log('🔄 Synchronisation présence:', state);
        
        // Mettre à jour la liste des utilisateurs connectés
        this.updateConnectedUsers(state);
        
        // Mettre à jour l'interface
        this.updatePresenceUI();
    }

    // Gérer l'arrivée d'un utilisateur
    handleUserJoin(key, newPresences) {
        console.log(`👋 Utilisateur connecté: ${key}`);
        
        newPresences.forEach(presence => {
            this.connectedUsers.set(key, {
                ...presence,
                joinedAt: new Date().toISOString()
            });
            
            // Afficher notification
            if (key !== this.currentUser.username) {
                this.showPresenceNotification({
                    type: 'join',
                    user: presence,
                    message: `${presence.name} s'est connecté`
                });
            }
        });
        
        this.updatePresenceUI();
        this.triggerCallback('onUserJoin', { key, presences: newPresences });
    }

    // Gérer le départ d'un utilisateur
    handleUserLeave(key, leftPresences) {
        console.log(`👋 Utilisateur déconnecté: ${key}`);
        
        const user = this.connectedUsers.get(key);
        if (user) {
            this.connectedUsers.delete(key);
            
            // Afficher notification
            if (key !== this.currentUser.username) {
                this.showPresenceNotification({
                    type: 'leave',
                    user: user,
                    message: `${user.name} s'est déconnecté`
                });
            }
        }
        
        this.updatePresenceUI();
        this.triggerCallback('onUserLeave', { key, presences: leftPresences });
    }

    // Mettre à jour les utilisateurs connectés
    updateConnectedUsers(state) {
        this.connectedUsers.clear();
        
        Object.entries(state).forEach(([key, presences]) => {
            if (presences && presences.length > 0) {
                const latestPresence = presences[presences.length - 1];
                this.connectedUsers.set(key, {
                    ...latestPresence,
                    isCurrentUser: key === this.currentUser.username
                });
            }
        });
        
        console.log(`👥 ${this.connectedUsers.size} utilisateur(s) connecté(s)`);
    }

    // Configurer la détection d'activité
    setupActivityDetection() {
        // Événements d'activité
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        
        const updateActivity = () => {
            this.lastActivity = Date.now();
            this.updateUserStatus('online');
        };
        
        activityEvents.forEach(event => {
            document.addEventListener(event, updateActivity, true);
        });
        
        // Vérification périodique d'inactivité
        setInterval(() => {
            const timeSinceActivity = Date.now() - this.lastActivity;
            
            if (timeSinceActivity > this.activityTimeout) {
                this.updateUserStatus('away');
            }
        }, 60000); // Vérifier toutes les minutes
        
        // Détection de visibilité de la page
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.updateUserStatus('away');
            } else {
                this.updateUserStatus('online');
                this.lastActivity = Date.now();
            }
        });
    }

    // Mettre à jour le statut utilisateur
    async updateUserStatus(status) {
        if (!this.presenceChannel || !this.isActive) return;
        
        try {
            const currentPresence = this.connectedUsers.get(this.currentUser.username);
            if (currentPresence && currentPresence.status === status) return;
            
            const updatedPresence = {
                ...currentPresence,
                status: status,
                last_seen: new Date().toISOString(),
                activity: status === 'online' ? 'active' : 'inactive'
            };
            
            await this.presenceChannel.track(updatedPresence);
            console.log(`👤 Statut mis à jour: ${status}`);
            
        } catch (error) {
            console.error('❌ Erreur mise à jour statut:', error);
        }
    }

    // Créer l'interface de présence
    createPresenceUI() {
        // Indicateur de présence dans l'en-tête
        this.createPresenceIndicator();
        
        // Liste des utilisateurs connectés
        this.createUsersList();
        
        // Widget de présence flottant
        this.createPresenceWidget();
    }

    // Créer l'indicateur de présence
    createPresenceIndicator() {
        const header = document.querySelector('.header') || document.querySelector('header');
        if (!header) return;
        
        const indicator = document.createElement('div');
        indicator.id = 'presence-indicator';
        indicator.style.cssText = `
            position: absolute;
            top: 10px;
            right: 150px;
            display: flex;
            align-items: center;
            background: white;
            padding: 5px 10px;
            border-radius: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 12px;
            cursor: pointer;
        `;
        
        indicator.onclick = () => this.togglePresenceWidget();
        header.appendChild(indicator);
    }

    // Créer la liste des utilisateurs
    createUsersList() {
        if (document.getElementById('users-presence-list')) return;
        
        const usersList = document.createElement('div');
        usersList.id = 'users-presence-list';
        usersList.style.cssText = `
            position: fixed;
            top: 100px;
            right: 20px;
            width: 250px;
            max-height: 400px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 9998;
            display: none;
            overflow: hidden;
        `;
        
        usersList.innerHTML = `
            <div style="padding: 15px; border-bottom: 1px solid #eee; background: #f8f9fa;">
                <h6 style="margin: 0; font-weight: bold;">👥 Utilisateurs connectés</h6>
            </div>
            <div id="users-list-content" style="max-height: 300px; overflow-y: auto;"></div>
        `;
        
        document.body.appendChild(usersList);
    }

    // Créer le widget de présence
    createPresenceWidget() {
        if (document.getElementById('presence-widget')) return;
        
        const widget = document.createElement('div');
        widget.id = 'presence-widget';
        widget.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 9999;
            transition: all 0.3s ease;
        `;
        
        widget.innerHTML = '👥';
        widget.onclick = () => this.toggleUsersList();
        
        widget.onmouseenter = () => {
            widget.style.transform = 'scale(1.1)';
        };
        
        widget.onmouseleave = () => {
            widget.style.transform = 'scale(1)';
        };
        
        document.body.appendChild(widget);
        
        // Badge de notification
        const badge = document.createElement('div');
        badge.id = 'presence-badge';
        badge.style.cssText = `
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 11px;
            font-weight: bold;
            display: none;
            align-items: center;
            justify-content: center;
        `;
        
        widget.appendChild(badge);
    }

    // Mettre à jour l'interface de présence
    updatePresenceUI() {
        this.updatePresenceIndicator();
        this.updateUsersList();
        this.updatePresenceWidget();
    }

    // Mettre à jour l'indicateur de présence
    updatePresenceIndicator() {
        const indicator = document.getElementById('presence-indicator');
        if (!indicator) return;
        
        const onlineCount = Array.from(this.connectedUsers.values())
            .filter(user => user.status === 'online').length;
        
        const currentUserStatus = this.connectedUsers.get(this.currentUser.username)?.status || 'online';
        const statusConfig = this.presenceStates[currentUserStatus];
        
        indicator.innerHTML = `
            <span style="margin-right: 5px;">${statusConfig.icon}</span>
            <span>${onlineCount} en ligne</span>
        `;
    }

    // Mettre à jour la liste des utilisateurs
    updateUsersList() {
        const listContent = document.getElementById('users-list-content');
        if (!listContent) return;
        
        listContent.innerHTML = '';
        
        // Trier les utilisateurs par statut et nom
        const sortedUsers = Array.from(this.connectedUsers.entries())
            .sort(([, a], [, b]) => {
                if (a.status !== b.status) {
                    const statusOrder = { online: 0, away: 1, busy: 2, offline: 3 };
                    return statusOrder[a.status] - statusOrder[b.status];
                }
                return a.name.localeCompare(b.name);
            });
        
        sortedUsers.forEach(([username, user]) => {
            const userElement = document.createElement('div');
            userElement.style.cssText = `
                padding: 10px 15px;
                border-bottom: 1px solid #f0f0f0;
                display: flex;
                align-items: center;
                cursor: pointer;
                transition: background 0.2s;
            `;
            
            userElement.onmouseenter = () => {
                userElement.style.background = '#f8f9fa';
            };
            
            userElement.onmouseleave = () => {
                userElement.style.background = 'white';
            };
            
            const statusConfig = this.presenceStates[user.status];
            const isCurrentUser = user.isCurrentUser;
            
            userElement.innerHTML = `
                <div style="margin-right: 10px; font-size: 16px;">${statusConfig.icon}</div>
                <div style="flex: 1;">
                    <div style="font-weight: ${isCurrentUser ? 'bold' : 'normal'}; font-size: 14px;">
                        ${user.name} ${isCurrentUser ? '(Vous)' : ''}
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        ${user.role} • ${statusConfig.label}
                    </div>
                    <div style="font-size: 11px; color: #999;">
                        ${this.formatLastSeen(user.last_seen)}
                    </div>
                </div>
            `;
            
            listContent.appendChild(userElement);
        });
        
        if (sortedUsers.length === 0) {
            listContent.innerHTML = `
                <div style="padding: 20px; text-align: center; color: #666;">
                    Aucun utilisateur connecté
                </div>
            `;
        }
    }

    // Mettre à jour le widget de présence
    updatePresenceWidget() {
        const badge = document.getElementById('presence-badge');
        if (!badge) return;
        
        const onlineCount = this.connectedUsers.size;
        
        if (onlineCount > 1) { // Plus que l'utilisateur actuel
            badge.textContent = onlineCount - 1;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }

    // Basculer la liste des utilisateurs
    toggleUsersList() {
        const usersList = document.getElementById('users-presence-list');
        if (usersList) {
            usersList.style.display = usersList.style.display === 'none' ? 'block' : 'none';
        }
    }

    // Basculer le widget de présence
    togglePresenceWidget() {
        this.toggleUsersList();
    }

    // Afficher une notification de présence
    showPresenceNotification(options) {
        if (typeof window.showUINotification === 'function') {
            window.showUINotification({
                title: 'Présence utilisateur',
                body: options.message,
                icon: options.type === 'join' ? '👋' : '👋',
                type: 'system',
                autoHide: true
            });
        }
    }

    // Formater la dernière activité
    formatLastSeen(lastSeen) {
        const date = new Date(lastSeen);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // Moins d'1 minute
            return 'À l\'instant';
        } else if (diff < 3600000) { // Moins d'1 heure
            return `Il y a ${Math.floor(diff / 60000)}m`;
        } else if (diff < 86400000) { // Moins d'1 jour
            return `Il y a ${Math.floor(diff / 3600000)}h`;
        } else {
            return date.toLocaleDateString();
        }
    }

    // Démarrer les mises à jour périodiques
    startPeriodicUpdates() {
        setInterval(() => {
            if (this.isActive) {
                this.updatePresenceUI();
            }
        }, this.uiConfig.updateInterval);
    }

    // Arrêter la présence
    async stop() {
        this.isActive = false;
        
        if (this.presenceChannel) {
            await this.presenceChannel.untrack();
            this.presenceChannel.unsubscribe();
        }
        
        console.log('🛑 Présence utilisateur arrêtée');
    }

    // Ajouter un callback
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    // Déclencher les callbacks
    triggerCallback(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Erreur callback présence ${event}:`, error);
                }
            });
        }
    }

    // Obtenir les statistiques
    getStats() {
        return {
            isActive: this.isActive,
            connectedUsers: this.connectedUsers.size,
            currentUserStatus: this.connectedUsers.get(this.currentUser?.username)?.status || 'offline',
            lastActivity: this.lastActivity
        };
    }

    // Obtenir les utilisateurs connectés
    getConnectedUsers() {
        return Array.from(this.connectedUsers.values());
    }
}

// Fonction globale pour mettre à jour la présence
window.updateUserPresenceUI = function(users) {
    if (window.userPresence) {
        window.userPresence.updatePresenceUI();
    }
};

// Export de la classe
if (typeof window !== 'undefined') {
    window.UserPresence = UserPresence;
}
