/* Variables CSS globales */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --background-color: #ffffff;
    --text-color: #212529;
    --border-color: #dee2e6;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Styles généraux */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Indicateur de statut de connexion */
.connection-status {
    position: fixed;
    top: 10px;
    right: 10px;
    background: white;
    padding: 10px 15px;
    border-radius: 20px;
    box-shadow: var(--shadow);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    border: 1px solid var(--border-color);
}

/* Section de connexion */
#login {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
}

#login h1 {
    color: white;
    margin-bottom: 30px;
    text-align: center;
}

.login-form {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 500px;
}

.login-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

.login-form input {
    width: 100%;
    padding: 12px;
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 16px;
}

.login-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* Comptes de test dans la page de connexion */
.test-accounts {
    margin-top: 30px;
    padding: 20px;
    background: var(--light-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.test-accounts h3 {
    margin: 0 0 15px 0;
    color: var(--primary-color);
    font-size: 16px;
}

.account-list {
    display: grid;
    gap: 8px;
}

.account-item {
    padding: 8px 12px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
}

.account-item:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

/* En-tête de l'application */
.header {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.header h1 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.main-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.status-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.status-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--success-color);
    color: white;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Zones modules */
.module-zone {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
}

.module-zone h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 10px;
}

/* Zone de contenu principal */
#mainContentArea {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    margin-bottom: 20px;
}

/* Zone de chat */
#chatArea {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    height: 500px;
}

#messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    border-bottom: 1px solid var(--border-color);
}

.message {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    background: var(--light-color);
}

.message.own {
    background: var(--primary-color);
    color: white;
    margin-left: 20%;
}

.message.system {
    background: var(--warning-color);
    color: var(--dark-color);
    font-style: italic;
    text-align: center;
}

.message-header {
    font-weight: bold;
    margin-bottom: 5px;
    font-size: 14px;
}

.message-time {
    font-size: 12px;
    opacity: 0.7;
    margin-left: 10px;
}

.message-content {
    margin-bottom: 5px;
}

.message-file {
    background: rgba(255,255,255,0.2);
    padding: 5px 10px;
    border-radius: 5px;
    margin-top: 5px;
    font-size: 12px;
}

#inputGroup {
    display: flex;
    padding: 15px;
    gap: 10px;
    align-items: center;
}

#inputGroup select,
#inputGroup input[type="text"] {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

#inputGroup select {
    min-width: 120px;
}

#inputGroup input[type="text"] {
    flex: 1;
}

.file-preview {
    padding: 10px 15px;
    background: var(--light-color);
    border-top: 1px solid var(--border-color);
    font-size: 12px;
}

.file-preview-item {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    margin: 2px;
}

/* Liste des membres */
.members-list {
    background: white;
    border-radius: 10px;
    box-shadow: var(--shadow);
    padding: 20px;
    height: 500px;
    overflow-y: auto;
}

.members-list h3 {
    color: var(--primary-color);
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.member-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px;
    border-radius: 5px;
    margin-bottom: 5px;
}

.member-item:hover {
    background: var(--light-color);
}

.member-status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.member-name {
    font-weight: 500;
}

.member-role {
    font-size: 12px;
    color: var(--secondary-color);
    margin-left: auto;
}

/* Tableaux */
.table-container {
    overflow-x: auto;
    margin: 20px 0;
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
}

tr:hover {
    background: var(--light-color);
}

.table-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

/* Boutons */
button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

button:active {
    transform: translateY(0);
}

.primary {
    background: var(--primary-color);
    color: white;
}

.primary:hover {
    background: #0056b3;
}

.secondary {
    background: var(--secondary-color);
    color: white;
}

.secondary:hover {
    background: #545b62;
}

.success {
    background: var(--success-color);
    color: white;
}

.success:hover {
    background: #1e7e34;
}

.danger {
    background: var(--danger-color);
    color: white;
}

.danger:hover {
    background: #c82333;
}

.warning {
    background: var(--warning-color);
    color: var(--dark-color);
}

.warning:hover {
    background: #e0a800;
}

.action-button {
    padding: 5px 10px;
    font-size: 12px;
    margin: 2px;
}

/* Messages d'erreur et de statut */
.error-message {
    color: var(--danger-color);
    margin-top: 10px;
    padding: 10px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 5px;
    display: none;
}

.status-message {
    padding: 10px;
    border-radius: 5px;
    margin: 10px 0;
}

.status-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Formulaires */
.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.form-header h3 {
    color: var(--primary-color);
    margin: 0;
}

fieldset {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    background: var(--light-color);
}

fieldset:disabled {
    opacity: 0.6;
    background: #f5f5f5;
}

legend {
    font-weight: bold;
    color: var(--primary-color);
    padding: 0 10px;
}

.form-row {
    margin-bottom: 15px;
}

.form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-row input,
.form-row select,
.form-row textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 14px;
}

.form-row input:focus,
.form-row select:focus,
.form-row textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

/* Produits dans les commandes */
.produit-item {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    align-items: center;
}

.produit-item input {
    flex: 1;
}

.produit-item button {
    padding: 8px 12px;
    background: var(--success-color);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

.produit-item button:hover {
    background: #1e7e34;
}

/* Gestion des PV */
.pv-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.depot-global-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.pv-admin-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.pv-admin-actions input[type="text"] {
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}

.pv-list {
    max-height: 600px;
    overflow-y: auto;
}

.pv-item {
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 15px;
    overflow: hidden;
}

.pv-header {
    background: var(--light-color);
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.pv-header:hover {
    background: #e9ecef;
}

.pv-info {
    display: flex;
    gap: 20px;
    align-items: center;
}

.pv-numero {
    font-weight: bold;
    color: var(--primary-color);
}

.pv-depot {
    color: var(--secondary-color);
}

.pv-date {
    font-size: 12px;
    color: var(--secondary-color);
}

.pv-actions-inline {
    display: flex;
    gap: 5px;
}

.pv-details {
    padding: 15px;
    background: white;
    display: none;
}

.pv-details.show {
    display: block;
}

.pv-produits {
    margin-top: 10px;
}

.pv-produit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid var(--border-color);
}

.pv-produit-item:last-child {
    border-bottom: none;
}

.produit-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.produit-status.conforme {
    background: #d4edda;
    color: #155724;
}

.produit-status.non-conforme {
    background: #f8d7da;
    color: #721c24;
}

.produit-status.a-verifier {
    background: #fff3cd;
    color: #856404;
}

.produit-status.en_attente {
    background: #fff3cd;
    color: #856404;
}

.produit-status.soumise {
    background: #d1ecf1;
    color: #0c5460;
}

/* Styles pour les produits PV avec catégories */
.category-header {
    background: var(--primary-color) !important;
    color: white !important;
}

.category-header:hover {
    background: var(--primary-color) !important;
}

.category-title {
    font-weight: bold;
    padding: 15px !important;
    border-bottom: none !important;
}

.product-row {
    transition: all 0.3s ease;
}

.product-row:hover {
    background: #f0f8ff !important;
    transform: translateX(2px);
}

.product-name {
    font-weight: 500;
    margin-bottom: 2px;
}

.product-category {
    color: var(--secondary-color);
    font-style: italic;
}

/* Styles pour les cellules d'expiration */
.expiration-cell {
    text-align: center;
    padding: 8px;
    border-radius: 6px;
    min-width: 140px;
}

.expiration-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin-bottom: 4px;
}

.expiration-icon {
    font-size: 1.2em;
}

.expiration-date {
    font-weight: 600;
    font-size: 0.9em;
}

.expiration-status {
    display: block;
    font-weight: bold;
    font-size: 0.75em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.days-info {
    display: block;
    font-size: 0.7em;
    opacity: 0.8;
    font-style: italic;
}

/* Statuts d'expiration */
.expiration-cell.expired {
    background-color: #ffebee;
    border-left: 4px solid #d32f2f;
}

.expiration-cell.expired .expiration-status {
    color: #d32f2f;
}

.expiration-cell.expiring-today {
    background-color: #ffebee;
    border-left: 4px solid #d32f2f;
    animation: pulse-red 2s infinite;
}

.expiration-cell.expiring-today .expiration-status {
    color: #d32f2f;
}

.expiration-cell.expiring-critical {
    background-color: #fff3e0;
    border-left: 4px solid #f57c00;
}

.expiration-cell.expiring-critical .expiration-status {
    color: #f57c00;
}

.expiration-cell.expiring-soon {
    background-color: #fff8e1;
    border-left: 4px solid #fbc02d;
}

.expiration-cell.expiring-soon .expiration-status {
    color: #f57f17;
}

.expiration-cell.expiring-month {
    background-color: #fff8e1;
    border-left: 4px solid #fbc02d;
}

.expiration-cell.expiring-month .expiration-status {
    color: #f57f17;
}

.expiration-cell.valid {
    background-color: #e8f5e8;
    border-left: 4px solid #388e3c;
}

.expiration-cell.valid .expiration-status {
    color: #388e3c;
}

.expiration-cell.no-date {
    background-color: #f5f5f5;
    border-left: 4px solid #9e9e9e;
}

.expiration-cell.no-date .expiration-status {
    color: #757575;
}

/* Animation pour les produits qui expirent aujourd'hui */
@keyframes pulse-red {
    0% { background-color: #ffebee; }
    50% { background-color: #ffcdd2; }
    100% { background-color: #ffebee; }
}

/* Styles pour la modification des dates d'expiration */
.expiry-date-input {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 4px 6px;
    font-size: 0.85em;
    width: 130px;
    background: white;
    transition: all 0.2s ease;
}

.expiry-date-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.expiry-actions {
    display: flex;
    gap: 4px;
    margin-top: 6px;
    justify-content: center;
}

.btn-mini {
    padding: 2px 6px;
    font-size: 0.7em;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.btn-mini.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-mini.btn-success:hover {
    background-color: #218838;
    transform: translateY(-1px);
}

.btn-mini.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-mini.btn-danger:hover {
    background-color: #c82333;
    transform: translateY(-1px);
}

/* Responsive pour les actions d'expiration */
@media (max-width: 768px) {
    .expiry-actions {
        flex-direction: column;
        gap: 2px;
    }

    .btn-mini {
        font-size: 0.65em;
        padding: 1px 4px;
    }

    .expiry-date-input {
        width: 110px;
        font-size: 0.8em;
    }
}

/* Styles pour l'affichage des IDs de produits */
.product-id-cell {
    text-align: center;
    padding: 8px;
    min-width: 100px;
}

.product-id-display {
    margin-bottom: 6px;
    padding: 4px 8px;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 4px;
    border-left: 3px solid var(--primary-color);
}

.product-id-display strong {
    color: var(--primary-color);
    font-size: 0.85em;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Styles pour les contrôles de quantité */
.quantity-cell {
    text-align: center;
    padding: 8px;
    min-width: 120px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    margin-bottom: 4px;
}

.qty-btn {
    width: 28px;
    height: 28px;
    border: 1px solid #ddd;
    background: #f8f9fa;
    color: #495057;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.qty-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.qty-btn:active {
    transform: translateY(0);
    background: #dee2e6;
}

.qty-minus {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.qty-plus {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.quantity-input {
    width: 60px;
    height: 28px;
    text-align: center;
    border: 1px solid #ddd;
    border-left: none;
    border-right: none;
    background: white;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    outline: none;
}

.quantity-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.quantity-unit {
    display: block;
    font-size: 0.7em;
    color: #6c757d;
    font-style: italic;
    margin-top: 2px;
}

.quantity-cell.has-quantity .quantity-unit {
    color: var(--success-color);
    font-weight: 500;
}

.quantity-cell.has-quantity .quantity-controls {
    background: rgba(40, 167, 69, 0.1);
    border-radius: 6px;
    padding: 2px;
}

.quantity-cell.has-quantity .qty-btn {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.quantity-cell.has-quantity .qty-btn:hover {
    background: #c3e6cb;
    border-color: #b8dacc;
}

.quantity-cell.has-quantity .quantity-input {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* Responsive pour les contrôles de quantité */
@media (max-width: 768px) {
    .product-id-cell {
        min-width: 80px;
        padding: 6px;
    }

    .product-id-display {
        padding: 2px 4px;
        margin-bottom: 4px;
    }

    .product-id-display strong {
        font-size: 0.75em;
    }

    .quantity-cell {
        min-width: 100px;
        padding: 6px;
    }

    .qty-btn {
        width: 24px;
        height: 24px;
        font-size: 12px;
    }

    .quantity-input {
        width: 50px;
        height: 24px;
        font-size: 12px;
    }

    .quantity-unit {
        font-size: 0.65em;
    }
}

/* Styles pour les statuts de produits */
.status-cell {
    text-align: center;
    padding: 8px;
    min-width: 120px;
}

.status-display {
    margin-bottom: 6px;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border: 1px solid transparent;
}

.status-badge.status-valid {
    background-color: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.status-badge.status-expired {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.status-badge.status-verified {
    background-color: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

.status-badge.status-unknown {
    background-color: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

.status-select {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.85em;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.status-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.status-select:hover {
    border-color: #adb5bd;
}

/* Responsive pour les statuts */
@media (max-width: 768px) {
    .status-cell {
        min-width: 100px;
        padding: 6px;
    }

    .status-badge {
        font-size: 0.75em;
        padding: 2px 6px;
    }

    .status-select {
        font-size: 0.8em;
        padding: 2px 4px;
    }
}

/* Styles pour le tableau des PV consultés */
.pv-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.pv-table th {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pv-table td {
    padding: 10px 8px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.pv-row:hover {
    background-color: #f8f9fa;
}

.pv-numero-cell strong {
    color: var(--primary-color);
    font-weight: 600;
}

.depot-badge {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.count-badge {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #ef6c00;
    padding: 4px 8px;
    border-radius: 50%;
    font-weight: 600;
    min-width: 24px;
    display: inline-block;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.pv-actions-cell {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #138496;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

/* Styles pour les détails des PV */
.pv-details-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-left: 4px solid var(--primary-color);
}

.pv-details-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.pv-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.pv-info-item {
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
}

.pv-info-item strong {
    color: var(--primary-color);
    display: block;
    margin-bottom: 5px;
}

.products-detail-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 6px;
    overflow: hidden;
}

.products-detail-table th {
    background: #f8f9fa;
    color: #495057;
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.products-detail-table td {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
}

.products-detail-table tr:hover {
    background-color: #f8f9fa;
}

.pv-notes {
    background: #e7f3ff;
    padding: 15px;
    border-radius: 6px;
    margin: 15px 0;
    border-left: 4px solid #007bff;
}

.pv-notes h5 {
    color: #007bff;
    margin-bottom: 10px;
}

.pv-actions-detail {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 2px solid #e9ecef;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

/* Bouton spécial pour l'équipe hygiène */
.hygiene-btn {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hygiene-btn:hover {
    background: linear-gradient(135deg, #218838, #1ea080) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(40,167,69,0.4);
}

/* Responsive pour les PV */
@media (max-width: 768px) {
    .pv-table th,
    .pv-table td {
        padding: 6px 4px;
        font-size: 0.85em;
    }

    .pv-actions-cell {
        flex-direction: column;
        gap: 2px;
    }

    .btn-mini {
        font-size: 0.7em;
        padding: 2px 4px;
    }

    .pv-info-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .pv-actions-detail {
        flex-direction: column;
        align-items: center;
    }

    .btn-primary {
        width: 100%;
        max-width: 300px;
    }
}

/* ========================================
   STYLES POUR LA ZONE HYGIÈNE
   ======================================== */

/* En-tête de la zone hygiène */
.hygiene-header {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #4caf50;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.hygiene-info h2 {
    color: #2e7d32;
    margin: 0 0 10px 0;
    font-size: 1.5em;
}

.hygiene-info p {
    margin: 5px 0;
    color: #388e3c;
}

.hygiene-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-export-pdf {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-export-pdf:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220,53,69,0.3);
}

.btn-export-excel {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-export-excel:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40,167,69,0.3);
}

/* Filtres hygiène */
.hygiene-filters {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    align-items: center;
}

.hygiene-filters input,
.hygiene-filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
    min-width: 150px;
}

.hygiene-filters input:focus,
.hygiene-filters select:focus {
    outline: none;
    border-color: #4caf50;
    box-shadow: 0 0 0 2px rgba(76,175,80,0.25);
}

/* Tableau hygiène */
.hygiene-table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.hygiene-table th {
    background: linear-gradient(135deg, #4caf50, #388e3c);
    color: white;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.hygiene-table td {
    padding: 10px 8px;
    text-align: center;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.hygiene-pv-row:hover {
    background-color: #f1f8e9;
}

.nonconforme-badge {
    padding: 4px 8px;
    border-radius: 50%;
    font-weight: 600;
    min-width: 24px;
    display: inline-block;
}

.nonconforme-badge.has-issues {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #d32f2f;
    border: 1px solid #f8bbd9;
}

.nonconforme-badge.no-issues {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

/* Statistiques hygiène */
.hygiene-stats {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.stat-item.pending {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-color: #ffc107;
}

.stat-item.valid {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-color: #28a745;
}

.stat-item.rejected {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border-color: #dc3545;
}

.stat-item.conformity {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border-color: #17a2b8;
}

.stat-item.issues {
    background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
    border-color: #f39c12;
}

.stat-number {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9em;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Analyse détaillée des PV */
.hygiene-analysis-header {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #4caf50;
}

.hygiene-stats-summary {
    display: flex;
    justify-content: space-around;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 2px solid #e9ecef;
}

.summary-item {
    text-align: center;
    padding: 10px;
}

.summary-item.conformes .summary-number {
    color: #28a745;
}

.summary-item.non-conformes .summary-number {
    color: #dc3545;
}

.summary-item.taux .summary-number {
    color: #17a2b8;
}

.summary-number {
    font-size: 1.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.summary-label {
    font-size: 0.8em;
    color: #6c757d;
    text-transform: uppercase;
}

/* Tableau d'analyse des produits */
.hygiene-analysis-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 6px;
    overflow: hidden;
}

.hygiene-analysis-table th {
    background: #4caf50;
    color: white;
    padding: 10px 8px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9em;
}

.hygiene-analysis-table td {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.product-analysis-row:hover {
    background-color: #f1f8e9;
}

.expiration-analysis {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
}

.expiration-analysis .expiration-date {
    font-weight: 500;
    font-size: 0.9em;
}

.expiration-analysis .expiration-status {
    font-size: 0.7em;
    text-transform: uppercase;
    font-weight: bold;
}

/* Badges de risque hygiène */
.risk-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.risk-badge.risk-critical {
    background-color: #ffebee;
    color: #d32f2f;
    border: 1px solid #f8bbd9;
}

.risk-badge.risk-high {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc80;
}

.risk-badge.risk-medium {
    background-color: #fff8e1;
    color: #f57f17;
    border: 1px solid #fff176;
}

.risk-badge.risk-low {
    background-color: #e8f5e8;
    color: #388e3c;
    border: 1px solid #a5d6a7;
}

.risk-badge.risk-unknown {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
}

/* Amélioration du tableau hygiène */
.hygiene-table {
    font-size: 0.9em;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.hygiene-table th {
    background: linear-gradient(135deg, #4caf50, #2e7d32);
    font-size: 0.85em;
    padding: 15px 10px;
    text-align: center;
    white-space: nowrap;
}

.hygiene-table td {
    padding: 12px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #e8f5e8;
}

.hygiene-pv-row:nth-child(even) {
    background-color: #fafafa;
}

.hygiene-pv-row:hover {
    background-color: #e8f5e8;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(76,175,80,0.2);
    transition: all 0.2s ease;
}

.no-data {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
    font-size: 1.1em;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 8px;
}

.no-data::before {
    content: "📭";
    display: block;
    font-size: 3em;
    margin-bottom: 10px;
}

/* Boutons d'export améliorés */
.btn-export-pdf,
.btn-export-excel {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.btn-export-pdf:hover {
    background: linear-gradient(135deg, #c82333, #a71e2a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(220,53,69,0.4);
}

.btn-export-excel:hover {
    background: linear-gradient(135deg, #20c997, #17a085);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(40,167,69,0.4);
}

/* Responsive pour le tableau hygiène */
@media (max-width: 1200px) {
    .hygiene-table {
        font-size: 0.8em;
    }

    .hygiene-table th,
    .hygiene-table td {
        padding: 8px 6px;
    }
}

@media (max-width: 768px) {
    .hygiene-table {
        font-size: 0.75em;
    }

    .hygiene-table th,
    .hygiene-table td {
        padding: 6px 4px;
    }

    .btn-export-pdf,
    .btn-export-excel {
        padding: 8px 12px;
        font-size: 0.85em;
    }
}

/* Convertisseur */
.converter-form {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    background: var(--light-color);
    padding: 20px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.converter-form input,
.converter-form select {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.converter-form input:focus,
.converter-form select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.converter-form input {
    width: 150px;
    font-weight: 500;
}

.converter-form select {
    min-width: 200px;
    font-weight: 500;
}

.converter-arrow {
    font-size: 18px;
    font-weight: bold;
    color: var(--primary-color);
}

.conversion-result {
    margin: 20px 0;
}

.conversion-main {
    font-size: 18px;
    margin-bottom: 10px;
}

.conversion-details {
    opacity: 0.8;
}

.currency-info {
    background: white;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    margin-top: 20px;
}

.currency-info h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
}

.rates-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.rate-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: var(--light-color);
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

.rate-currency {
    font-weight: bold;
    color: var(--primary-color);
}

.rate-value {
    font-weight: 500;
    color: var(--success-color);
}

.rate-name {
    font-size: 12px;
    color: var(--secondary-color);
}

.rates-note {
    text-align: center;
    color: var(--secondary-color);
    margin: 0;
}

/* Copyright */
.copyright {
    text-align: center;
    padding: 20px;
    color: var(--secondary-color);
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        align-items: stretch;
    }

    .main-buttons {
        justify-content: center;
        margin: 15px 0;
    }

    .status-container {
        justify-content: center;
    }

    #mainContentArea {
        grid-template-columns: 1fr;
    }

    #inputGroup {
        flex-wrap: wrap;
    }

    #inputGroup select,
    #inputGroup input[type="text"] {
        min-width: 200px;
    }

    .table-actions {
        justify-content: center;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions button {
        width: 100%;
    }

    .produit-item {
        flex-direction: column;
    }

    .produit-item input,
    .produit-item button {
        width: 100%;
    }

    .converter-form {
        flex-direction: column;
    }

    .converter-form input,
    .converter-form select {
        width: 100%;
    }

    .pv-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .pv-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .login-form {
        padding: 20px;
    }

    .main-buttons {
        flex-direction: column;
    }

    .main-buttons button {
        width: 100%;
        margin-bottom: 5px;
    }

    #inputGroup {
        flex-direction: column;
    }

    #inputGroup select,
    #inputGroup input[type="text"],
    #inputGroup button {
        width: 100%;
        margin-bottom: 5px;
    }

    .pv-actions,
    .depot-global-actions,
    .pv-admin-actions {
        flex-direction: column;
    }

    .pv-actions button,
    .depot-global-actions button,
    .pv-admin-actions button {
        width: 100%;
    }
}

/* Styles pour l'intégration Supabase */

/* Indicateur de statut Supabase */
.supabase-status {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 25px;
    padding: 10px 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.supabase-status.connected {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
    color: #065f46;
}

.supabase-status.disconnected {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    color: #92400e;
}

.supabase-status.error {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
    color: #991b1b;
}

.supabase-status i:first-child {
    font-size: 16px;
}

.supabase-status i:last-child {
    font-size: 12px;
}

.sync-btn {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
    margin-left: 5px;
}

.sync-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

.sync-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Notifications */
.notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: white;
    border-radius: 8px;
    padding: 15px 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    max-width: 300px;
    border-left: 4px solid #6b7280;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: #10b981;
    color: #065f46;
    background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
}

.notification.error {
    border-left-color: #ef4444;
    color: #991b1b;
    background: linear-gradient(135deg, #fef2f2, #fee2e2);
}

.notification.info {
    border-left-color: #3b82f6;
    color: #1e40af;
    background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.notification i {
    font-size: 16px;
    flex-shrink: 0;
}

/* Indicateurs de base de données */
.database-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    background: rgba(0, 0, 0, 0.05);
    margin-left: 10px;
}

.database-indicator.supabase {
    background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
    color: #065f46;
}

.database-indicator.local {
    background: linear-gradient(135deg, #fffbeb, #fef3c7);
    color: #92400e;
}

/* Boutons de synchronisation */
.sync-button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.sync-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

.sync-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.sync-button i {
    font-size: 12px;
}

/* Responsive pour Supabase */
@media (max-width: 768px) {
    .supabase-status {
        position: relative;
        top: auto;
        right: auto;
        margin: 10px 0;
        width: 100%;
        justify-content: center;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

/* ========================================
   STYLES POUR LA DOCUMENTATION WORKFLOW IPT
   ======================================== */

/* Badge de phase */
.workflow-phase-badge {
    font-size: 0.7em;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
    padding: 2px 6px;
    border-radius: 10px;
    margin-top: 4px;
    font-weight: 500;
    text-align: center;
    border: 1px solid #90caf9;
}

/* Indicateurs spéciaux pour équipements/articles */
.workflow-equipment-only,
.workflow-article-only {
    display: inline-block;
    font-size: 0.7em;
    padding: 2px 6px;
    border-radius: 8px;
    margin: 2px 0;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.workflow-equipment-only {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #ef6c00;
    border: 1px solid #ffcc80;
}

.workflow-article-only {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    border: 1px solid #a5d6a7;
}

/* Amélioration des badges de rôles pour IPT */
.workflow-role-badge.demandeur_beneficiaire {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #0d47a1;
    border: 1px solid #90caf9;
}

.workflow-role-badge.sd_achats {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
    color: #4a148c;
    border: 1px solid #ce93d8;
}

.workflow-role-badge.direction_technique {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #e65100;
    border: 1px solid #ffcc80;
}

.workflow-role-badge.directeur_general {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #b71c1c;
    border: 1px solid #ef9a9a;
}

.workflow-role-badge.ministere_sante {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #1b5e20;
    border: 1px solid #a5d6a7;
}

.workflow-role-badge.receptionniste_magasin {
    background: linear-gradient(135deg, #fff8e1, #fff176);
    color: #f57f17;
    border: 1px solid #fff59d;
}

.workflow-role-badge.magasinier {
    background: linear-gradient(135deg, #fce4ec, #f8bbd9);
    color: #880e4f;
    border: 1px solid #f48fb1;
}

.workflow-role-badge.rve {
    background: linear-gradient(135deg, #e0f2f1, #b2dfdb);
    color: #004d40;
    border: 1px solid #80cbc4;
}

/* Styles pour les étapes du workflow */
.workflow-step-number {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9em;
    margin-bottom: 5px;
}

.workflow-step-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
    font-size: 0.95em;
}

.workflow-step-description {
    font-size: 0.85em;
    color: #6c757d;
    line-height: 1.4;
    margin-bottom: 8px;
}

/* Listes de documents et actions */
.workflow-document-list,
.workflow-action-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.workflow-document-list li,
.workflow-action-list li {
    font-size: 0.8em;
    padding: 3px 0;
    border-bottom: 1px solid #f8f9fa;
    line-height: 1.3;
}

.workflow-document-list li:last-child,
.workflow-action-list li:last-child {
    border-bottom: none;
}

.workflow-document-list li.input {
    color: #17a2b8;
    font-weight: 500;
}

.workflow-document-list li.output {
    color: #28a745;
    font-weight: 500;
}

.workflow-action-list li {
    color: #495057;
}

.workflow-next-role {
    font-size: 0.85em;
    font-weight: 500;
    color: var(--secondary-color);
    text-align: center;
    padding: 5px;
    background: #f8f9fa;
    border-radius: 4px;
}

/* Responsive pour la documentation workflow */
@media (max-width: 768px) {
    .workflow-phase-badge {
        font-size: 0.65em;
        padding: 1px 4px;
    }

    .workflow-equipment-only,
    .workflow-article-only {
        font-size: 0.65em;
        padding: 1px 4px;
    }

    .workflow-step-number {
        width: 25px;
        height: 25px;
        font-size: 0.8em;
    }

    .workflow-step-name {
        font-size: 0.9em;
    }

    .workflow-step-description {
        font-size: 0.8em;
    }

    .workflow-document-list li,
    .workflow-action-list li {
        font-size: 0.75em;
    }
}
