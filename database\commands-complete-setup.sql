-- =====================================================
-- INSTALLATION COMPLÈTE DU SYSTÈME DE GESTION DES COMMANDES
-- Script d'installation unique pour toutes les composantes
-- =====================================================

-- Afficher le début de l'installation
\echo '🚀 Début de l''installation du système de gestion des commandes IPT'
\echo '=================================================================='

-- =====================================================
-- ÉTAPE 1: CRÉATION DES TABLES
-- =====================================================

\echo '📋 Étape 1/6: Création des tables...'

\i database/commands-schema.sql

\echo '✅ Tables créées avec succès'

-- =====================================================
-- ÉTAPE 2: CRÉATION DES FONCTIONS
-- =====================================================

\echo '⚙️ Étape 2/6: Création des fonctions...'

\i database/commands-functions.sql

\echo '✅ Fonctions créées avec succès'

-- =====================================================
-- ÉTAPE 3: CONFIGURATION DES POLITIQUES RLS
-- =====================================================

\echo '🔒 Étape 3/6: Configuration des politiques RLS...'

\i database/commands-rls-policies.sql

\echo '✅ Politiques RLS configurées avec succès'

-- =====================================================
-- ÉTAPE 4: CRÉATION DES VUES
-- =====================================================

\echo '👁️ Étape 4/6: Création des vues...'

\i database/commands-views.sql

\echo '✅ Vues créées avec succès'

-- =====================================================
-- ÉTAPE 5: DONNÉES DE RÉFÉRENCE ET EXEMPLES
-- =====================================================

\echo '📊 Étape 5/6: Insertion des données de référence...'

-- Insérer des données d'exemple pour les tests
INSERT INTO gestion_commandes (
    numero_commande, type_commande, demandeur_nom, demandeur_service, demandeur_email,
    fournisseur_nom, fournisseur_contact, objet_commande, justification,
    montant_ht, montant_tva, montant_ttc, date_commande, date_livraison_prevue,
    statut, etape_actuelle, created_by
) VALUES 
(
    'CMD-2024-0001', 'fournitures', 'Dr. Ahmed Ben Ali', 'Service Cardiologie', '<EMAIL>',
    'MediSupply SARL', '<EMAIL>', 'Fournitures médicales urgentes',
    'Renouvellement stock pour service cardiologie', 1500.000, 285.000, 1785.000,
    CURRENT_DATE, CURRENT_DATE + INTERVAL '15 days', 'soumise', 2, '<EMAIL>'
),
(
    'CMD-2024-0002', 'equipements', 'Dr. Fatma Trabelsi', 'Service Radiologie', '<EMAIL>',
    'TechMed International', '<EMAIL>', 'Équipement radiologique',
    'Remplacement équipement défaillant', 25000.000, 4750.000, 29750.000,
    CURRENT_DATE - INTERVAL '5 days', CURRENT_DATE + INTERVAL '30 days', 'validee_chef', 3, '<EMAIL>'
),
(
    'CMD-2024-0003', 'services', 'M. Karim Mansouri', 'Service Maintenance', '<EMAIL>',
    'CleanCare Services', '<EMAIL>', 'Services de nettoyage spécialisé',
    'Contrat annuel de nettoyage des blocs opératoires', 8000.000, 1520.000, 9520.000,
    CURRENT_DATE - INTERVAL '10 days', CURRENT_DATE + INTERVAL '7 days', 'en_cours', 5, '<EMAIL>'
);

-- Récupérer les IDs des commandes créées
DO $$
DECLARE
    cmd1_id UUID;
    cmd2_id UUID;
    cmd3_id UUID;
BEGIN
    -- Récupérer les IDs
    SELECT id INTO cmd1_id FROM gestion_commandes WHERE numero_commande = 'CMD-2024-0001';
    SELECT id INTO cmd2_id FROM gestion_commandes WHERE numero_commande = 'CMD-2024-0002';
    SELECT id INTO cmd3_id FROM gestion_commandes WHERE numero_commande = 'CMD-2024-0003';
    
    -- Initialiser le workflow pour chaque commande
    PERFORM initialize_command_workflow(cmd1_id, '<EMAIL>');
    PERFORM initialize_command_workflow(cmd2_id, '<EMAIL>');
    PERFORM initialize_command_workflow(cmd3_id, '<EMAIL>');
    
    -- Ajouter des items d'exemple
    INSERT INTO gestion_commande_items (commande_id, designation, description, quantite_commandee, unite, prix_unitaire_ht) VALUES
    (cmd1_id, 'Seringues 10ml', 'Seringues jetables stériles', 1000, 'pièce', 0.50),
    (cmd1_id, 'Gants latex', 'Gants d''examen en latex', 50, 'boîte', 15.00),
    (cmd1_id, 'Compresses stériles', 'Compresses 10x10cm stériles', 200, 'paquet', 2.50),
    
    (cmd2_id, 'Scanner portable', 'Scanner radiologique portable dernière génération', 1, 'unité', 25000.00),
    
    (cmd3_id, 'Service nettoyage', 'Nettoyage spécialisé blocs opératoires', 12, 'mois', 666.67);
    
    RAISE NOTICE '✅ Données d''exemple insérées avec succès';
END $$;

\echo '✅ Données de référence insérées avec succès'

-- =====================================================
-- ÉTAPE 6: VALIDATION DE L'INSTALLATION
-- =====================================================

\echo '🔍 Étape 6/6: Validation de l''installation...'

-- Vérifier les tables
DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
    view_count INTEGER;
    policy_count INTEGER;
    data_count INTEGER;
BEGIN
    -- Compter les tables
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('gestion_commandes', 'gestion_commande_items', 'gestion_commande_documents', 'gestion_commande_workflow');
    
    -- Compter les fonctions
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name LIKE '%command%';
    
    -- Compter les vues
    SELECT COUNT(*) INTO view_count
    FROM information_schema.views
    WHERE table_schema = 'public'
    AND table_name LIKE 'commands_%';
    
    -- Compter les politiques
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename LIKE 'gestion_commande%';
    
    -- Compter les données
    SELECT COUNT(*) INTO data_count
    FROM gestion_commandes;
    
    -- Afficher le résumé
    RAISE NOTICE '📊 RÉSUMÉ DE L''INSTALLATION:';
    RAISE NOTICE '   Tables créées: %/4', table_count;
    RAISE NOTICE '   Fonctions créées: %', function_count;
    RAISE NOTICE '   Vues créées: %/5', view_count;
    RAISE NOTICE '   Politiques RLS: %', policy_count;
    RAISE NOTICE '   Commandes d''exemple: %', data_count;
    
    -- Vérifier que tout est OK
    IF table_count = 4 AND view_count = 5 AND policy_count >= 16 AND data_count >= 3 THEN
        RAISE NOTICE '✅ INSTALLATION RÉUSSIE - Système de gestion des commandes opérationnel!';
    ELSE
        RAISE WARNING '⚠️ INSTALLATION INCOMPLÈTE - Vérifiez les erreurs ci-dessus';
    END IF;
END $$;

-- =====================================================
-- TESTS DE FONCTIONNEMENT
-- =====================================================

\echo '🧪 Tests de fonctionnement...'

-- Test de génération de numéro
DO $$
DECLARE
    new_number TEXT;
BEGIN
    SELECT generate_command_number() INTO new_number;
    RAISE NOTICE 'Test génération numéro: %', new_number;
END $$;

-- Test des statistiques
DO $$
DECLARE
    stats RECORD;
BEGIN
    SELECT * INTO stats FROM commands_stats;
    RAISE NOTICE 'Test statistiques: % commandes total, % en cours', stats.total_commandes, stats.commandes_en_cours;
END $$;

-- Test des vues
DO $$
DECLARE
    view_test INTEGER;
BEGIN
    SELECT COUNT(*) INTO view_test FROM commands_complete;
    RAISE NOTICE 'Test vue complète: % enregistrements', view_test;
    
    SELECT COUNT(*) INTO view_test FROM commands_table_view;
    RAISE NOTICE 'Test vue table: % enregistrements', view_test;
END $$;

-- =====================================================
-- INSTRUCTIONS POST-INSTALLATION
-- =====================================================

\echo ''
\echo '🎉 INSTALLATION TERMINÉE AVEC SUCCÈS!'
\echo '===================================='
\echo ''
\echo '📋 PROCHAINES ÉTAPES:'
\echo '1. Vérifiez que toutes les tables sont créées dans Supabase'
\echo '2. Configurez les permissions utilisateurs si nécessaire'
\echo '3. Testez les fonctionnalités via l''interface web'
\echo '4. Personnalisez les données de référence selon vos besoins'
\echo ''
\echo '📊 TABLES CRÉÉES:'
\echo '- gestion_commandes (table principale)'
\echo '- gestion_commande_items (détails des items)'
\echo '- gestion_commande_documents (documents associés)'
\echo '- gestion_commande_workflow (suivi du workflow)'
\echo ''
\echo '👁️ VUES DISPONIBLES:'
\echo '- commands_complete (vue complète)'
\echo '- commands_table_view (pour la table interactive)'
\echo '- commands_stats (statistiques globales)'
\echo '- commands_en_retard (commandes en retard)'
\echo '- commands_workflow_complete (workflow détaillé)'
\echo ''
\echo '⚙️ FONCTIONS PRINCIPALES:'
\echo '- generate_command_number() (génération numéros)'
\echo '- initialize_command_workflow() (initialisation workflow)'
\echo '- validate_command_by_chef() (validation chef)'
\echo '- validate_command_by_dg() (validation DG)'
\echo '- approve_command_by_ms() (approbation MS)'
\echo '- mark_command_shipped() (marquage expédition)'
\echo '- mark_command_delivered() (marquage livraison)'
\echo '- get_commands_statistics() (statistiques)'
\echo ''
\echo '🔒 SÉCURITÉ:'
\echo '- Politiques RLS activées sur toutes les tables'
\echo '- Permissions par rôle configurées'
\echo '- Accès sécurisé selon les responsabilités'
\echo ''
\echo '🧪 POUR TESTER:'
\echo '1. Connectez-vous à l''interface web'
\echo '2. Créez une nouvelle commande'
\echo '3. Testez le workflow de validation'
\echo '4. Vérifiez les statistiques en temps réel'
\echo ''
\echo '📞 SUPPORT:'
\echo 'En cas de problème, vérifiez les logs et consultez la documentation'
\echo ''

-- Fin du script
\echo '✅ Script d''installation terminé'
