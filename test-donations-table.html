<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Table Gestion Dons - IPT</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    <script src="assets/js/donations-table-manager.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>I', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 25px;
        }
        
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.2s;
        }
        
        .test-btn.primary {
            background: #3b82f6;
            color: white;
        }
        
        .test-btn.secondary {
            background: #6b7280;
            color: white;
        }
        
        .test-btn.success {
            background: #10b981;
            color: white;
        }
        
        .test-btn:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-warning { background: #f59e0b; }
        .status-info { background: #3b82f6; }
        
        .test-result {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .result-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        
        .result-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
        
        .result-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #3b82f6;
        }
        
        .table-container {
            margin-top: 20px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }
        
        /* Styles de table simplifiés pour le test */
        .test-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .test-table th,
        .test-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .test-table th {
            background: #f8fafc;
            font-weight: 600;
            color: #374151;
        }
        
        .test-table tbody tr:hover {
            background: #f8fafc;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #3b82f6;
        }
        
        .metric-label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            margin-top: 5px;
        }
        
        .log-container {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
        }
        
        .log-timestamp {
            color: #9ca3af;
        }
        
        .log-level-info { color: #60a5fa; }
        .log-level-success { color: #34d399; }
        .log-level-error { color: #f87171; }
        .log-level-warning { color: #fbbf24; }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- Header -->
        <div class="test-header">
            <h1>🧪 Test Table de Gestion des Dons</h1>
            <p>Interface de test pour valider les fonctionnalités de la table interactive</p>
        </div>

        <!-- Statut de connexion -->
        <div class="test-section">
            <h3>🔗 Statut de Connexion</h3>
            <div id="connectionStatus">
                <span class="status-indicator status-warning"></span>
                <span>Vérification en cours...</span>
            </div>
            <div id="userInfo" style="margin-top: 10px; font-size: 14px; color: #6b7280;">
                Utilisateur non connecté
            </div>
        </div>

        <!-- Contrôles de test -->
        <div class="test-section">
            <h3>🎮 Contrôles de Test</h3>
            <div class="test-controls">
                <button class="test-btn primary" onclick="initializeTable()">
                    🚀 Initialiser la Table
                </button>
                <button class="test-btn secondary" onclick="loadTestData()">
                    📊 Charger Données Test
                </button>
                <button class="test-btn secondary" onclick="testFilters()">
                    🔍 Tester Filtres
                </button>
                <button class="test-btn secondary" onclick="testSorting()">
                    📈 Tester Tri
                </button>
                <button class="test-btn secondary" onclick="testPagination()">
                    📄 Tester Pagination
                </button>
                <button class="test-btn success" onclick="testExport()">
                    📤 Tester Export
                </button>
                <button class="test-btn secondary" onclick="clearLogs()">
                    🗑️ Effacer Logs
                </button>
            </div>
        </div>

        <!-- Métriques -->
        <div class="test-section">
            <h3>📊 Métriques de Performance</h3>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="metricTotalRows">0</div>
                    <div class="metric-label">Total Lignes</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metricFilteredRows">0</div>
                    <div class="metric-label">Lignes Filtrées</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metricLoadTime">0ms</div>
                    <div class="metric-label">Temps Chargement</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="metricRenderTime">0ms</div>
                    <div class="metric-label">Temps Rendu</div>
                </div>
            </div>
        </div>

        <!-- Aperçu de la table -->
        <div class="test-section">
            <h3>👁️ Aperçu de la Table</h3>
            <div id="tablePreview">
                <p>La table sera affichée ici après initialisation...</p>
            </div>
        </div>

        <!-- Résultats des tests -->
        <div class="test-section">
            <h3>✅ Résultats des Tests</h3>
            <div id="testResults">
                <p>Aucun test exécuté pour le moment.</p>
            </div>
        </div>

        <!-- Console de logs -->
        <div class="test-section">
            <h3>📝 Console de Logs</h3>
            <div class="log-container" id="logContainer">
                <div class="log-entry">
                    <span class="log-timestamp">[00:00:00]</span>
                    <span class="log-level-info">[INFO]</span>
                    Interface de test initialisée
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales pour les tests
        let testTableManager = null;
        let testStartTime = 0;
        let testMetrics = {
            totalRows: 0,
            filteredRows: 0,
            loadTime: 0,
            renderTime: 0
        };

        // Initialisation
        document.addEventListener('DOMContentLoaded', async function() {
            log('Initialisation de l\'interface de test', 'info');
            await checkConnection();
        });

        // Fonction de logging
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('logContainer');
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">[${timestamp}]</span>
                <span class="log-level-${level}">[${level.toUpperCase()}]</span>
                ${message}
            `;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Vérifier la connexion
        async function checkConnection() {
            try {
                const supabase = window.supabaseClient || window.supabase;
                if (!supabase) {
                    throw new Error('Client Supabase non disponible');
                }

                const { data: { user } } = await supabase.auth.getUser();
                
                const statusEl = document.getElementById('connectionStatus');
                const userInfoEl = document.getElementById('userInfo');
                
                if (user) {
                    statusEl.innerHTML = `
                        <span class="status-indicator status-success"></span>
                        <span>Connecté à Supabase</span>
                    `;
                    userInfoEl.textContent = `Utilisateur: ${user.email} (${user.user_metadata?.role || 'user'})`;
                    log(`Utilisateur connecté: ${user.email}`, 'success');
                } else {
                    statusEl.innerHTML = `
                        <span class="status-indicator status-warning"></span>
                        <span>Supabase disponible mais utilisateur non connecté</span>
                    `;
                    userInfoEl.textContent = 'Veuillez vous connecter pour tester toutes les fonctionnalités';
                    log('Utilisateur non connecté', 'warning');
                }
                
            } catch (error) {
                const statusEl = document.getElementById('connectionStatus');
                statusEl.innerHTML = `
                    <span class="status-indicator status-error"></span>
                    <span>Erreur de connexion: ${error.message}</span>
                `;
                log(`Erreur de connexion: ${error.message}`, 'error');
            }
        }

        // Initialiser la table
        async function initializeTable() {
            try {
                log('Initialisation du gestionnaire de table...', 'info');
                testStartTime = performance.now();
                
                if (typeof DonationsTableManager === 'undefined') {
                    throw new Error('Classe DonationsTableManager non trouvée');
                }
                
                testTableManager = new DonationsTableManager();
                await testTableManager.initialize();
                
                const initTime = performance.now() - testStartTime;
                testMetrics.loadTime = Math.round(initTime);
                
                updateMetrics();
                addTestResult('Initialisation réussie', 'success', `Temps: ${testMetrics.loadTime}ms`);
                log(`Table initialisée en ${testMetrics.loadTime}ms`, 'success');
                
                // Afficher un aperçu
                displayTablePreview();
                
            } catch (error) {
                addTestResult('Erreur d\'initialisation', 'error', error.message);
                log(`Erreur initialisation: ${error.message}`, 'error');
            }
        }

        // Charger des données de test
        async function loadTestData() {
            if (!testTableManager) {
                addTestResult('Table non initialisée', 'error', 'Initialisez d\'abord la table');
                return;
            }
            
            try {
                log('Chargement des données de test...', 'info');
                testStartTime = performance.now();
                
                await testTableManager.loadData();
                
                const loadTime = performance.now() - testStartTime;
                testMetrics.loadTime = Math.round(loadTime);
                testMetrics.totalRows = testTableManager.data.length;
                testMetrics.filteredRows = testTableManager.filteredData.length;
                
                updateMetrics();
                addTestResult('Données chargées', 'success', `${testMetrics.totalRows} lignes en ${testMetrics.loadTime}ms`);
                log(`${testMetrics.totalRows} lignes chargées en ${testMetrics.loadTime}ms`, 'success');
                
                displayTablePreview();
                
            } catch (error) {
                addTestResult('Erreur de chargement', 'error', error.message);
                log(`Erreur chargement: ${error.message}`, 'error');
            }
        }

        // Tester les filtres
        function testFilters() {
            if (!testTableManager) {
                addTestResult('Table non initialisée', 'error');
                return;
            }
            
            log('Test des filtres...', 'info');
            
            // Simuler l'application de filtres
            const originalCount = testTableManager.filteredData.length;
            
            // Test filtre par statut
            testTableManager.filters = { statut: 'brouillon' };
            testTableManager.applyFilters();
            
            const filteredCount = testTableManager.filteredData.length;
            testMetrics.filteredRows = filteredCount;
            
            updateMetrics();
            addTestResult('Filtres testés', 'success', `${originalCount} → ${filteredCount} lignes`);
            log(`Filtres appliqués: ${originalCount} → ${filteredCount} lignes`, 'success');
        }

        // Tester le tri
        function testSorting() {
            if (!testTableManager) {
                addTestResult('Table non initialisée', 'error');
                return;
            }
            
            log('Test du tri...', 'info');
            testStartTime = performance.now();
            
            // Tester le tri par différentes colonnes
            testTableManager.handleSort('created_at');
            testTableManager.handleSort('numero_don');
            testTableManager.handleSort('statut');
            
            const sortTime = performance.now() - testStartTime;
            
            addTestResult('Tri testé', 'success', `Temps: ${Math.round(sortTime)}ms`);
            log(`Tri testé en ${Math.round(sortTime)}ms`, 'success');
        }

        // Tester la pagination
        function testPagination() {
            if (!testTableManager) {
                addTestResult('Table non initialisée', 'error');
                return;
            }
            
            log('Test de la pagination...', 'info');
            
            const totalPages = Math.ceil(testTableManager.filteredData.length / testTableManager.pageSize);
            
            // Tester navigation entre pages
            testTableManager.goToPage(1);
            testTableManager.goToPage(Math.min(2, totalPages));
            testTableManager.goToPage(totalPages);
            
            addTestResult('Pagination testée', 'success', `${totalPages} pages disponibles`);
            log(`Pagination testée: ${totalPages} pages`, 'success');
        }

        // Tester l'export
        function testExport() {
            if (!testTableManager) {
                addTestResult('Table non initialisée', 'error');
                return;
            }
            
            log('Test de l\'export...', 'info');
            
            try {
                const csvData = testTableManager.generateCSV();
                const lines = csvData.split('\n').length - 1;
                
                addTestResult('Export testé', 'success', `${lines} lignes exportées`);
                log(`Export CSV généré: ${lines} lignes`, 'success');
                
                // Optionnel: déclencher le téléchargement
                // testTableManager.downloadCSV(csvData, 'test_export.csv');
                
            } catch (error) {
                addTestResult('Erreur d\'export', 'error', error.message);
                log(`Erreur export: ${error.message}`, 'error');
            }
        }

        // Afficher un aperçu de la table
        function displayTablePreview() {
            if (!testTableManager || !testTableManager.data.length) {
                return;
            }
            
            const preview = document.getElementById('tablePreview');
            const sampleData = testTableManager.data.slice(0, 5);
            
            let tableHTML = `
                <div class="table-container">
                    <table class="test-table">
                        <thead>
                            <tr>
                                <th>N° Don</th>
                                <th>Type</th>
                                <th>Donateur</th>
                                <th>Statut</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            sampleData.forEach(don => {
                tableHTML += `
                    <tr>
                        <td>${don.numero_don || don.code_don || 'N/A'}</td>
                        <td>${don.type_don || 'N/A'}</td>
                        <td>${don.donneur_nom || 'N/A'}</td>
                        <td>${don.statut || 'N/A'}</td>
                        <td>${don.date_creation_formatted || new Date(don.created_at).toLocaleDateString('fr-FR')}</td>
                    </tr>
                `;
            });
            
            tableHTML += `
                        </tbody>
                    </table>
                </div>
                <p style="margin-top: 10px; font-size: 14px; color: #6b7280;">
                    Aperçu des ${sampleData.length} premières lignes sur ${testTableManager.data.length} total
                </p>
            `;
            
            preview.innerHTML = tableHTML;
        }

        // Mettre à jour les métriques
        function updateMetrics() {
            document.getElementById('metricTotalRows').textContent = testMetrics.totalRows;
            document.getElementById('metricFilteredRows').textContent = testMetrics.filteredRows;
            document.getElementById('metricLoadTime').textContent = testMetrics.loadTime + 'ms';
            document.getElementById('metricRenderTime').textContent = testMetrics.renderTime + 'ms';
        }

        // Ajouter un résultat de test
        function addTestResult(title, type, details = '') {
            const container = document.getElementById('testResults');
            
            if (container.innerHTML.includes('Aucun test')) {
                container.innerHTML = '';
            }
            
            const result = document.createElement('div');
            result.className = `test-result result-${type}`;
            result.innerHTML = `
                <strong>${title}</strong>
                ${details ? `<br><small>${details}</small>` : ''}
            `;
            
            container.appendChild(result);
        }

        // Effacer les logs
        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            log('Logs effacés', 'info');
        }
    </script>
</body>
</html>
