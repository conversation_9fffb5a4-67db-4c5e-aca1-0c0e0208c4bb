-- =====================================================
-- VUES OPTIMISÉES POUR LA GESTION DES COMMANDES
-- Vues pour l'affichage et les statistiques
-- =====================================================

-- =====================================================
-- VUE COMPLÈTE DES COMMANDES AVEC WORKFLOW
-- =====================================================

CREATE OR REPLACE VIEW commands_complete AS
SELECT 
    c.id,
    c.numero_commande,
    c.type_commande,
    
    -- Informations demandeur
    c.demandeur_nom,
    c.demandeur_service,
    c.demandeur_email,
    c.demandeur_telephone,
    
    -- Informations fournisseur
    c.fournisseur_nom,
    c.fournisseur_contact,
    c.fournisseur_adresse,
    c.fournisseur_telephone,
    c.fournisseur_email,
    
    -- Détails commande
    c.objet_commande,
    c.justification,
    c.urgence,
    
    -- Montants
    c.montant_ht,
    c.montant_tva,
    c.montant_ttc,
    c.devise,
    
    -- Dates
    c.date_commande,
    c.date_livraison_prevue,
    c.date_livraison_reelle,
    c.date_limite_livraison,
    
    -- Statut et workflow
    c.statut,
    c.etape_actuelle,
    
    -- Validations
    c.validee_chef_par,
    c.validee_chef_date,
    c.validee_chef_commentaire,
    c.validee_dg_par,
    c.validee_dg_date,
    c.validee_dg_commentaire,
    c.approuvee_ms_par,
    c.approuvee_ms_date,
    c.approuvee_ms_commentaire,
    
    -- Livraison
    c.lieu_livraison,
    c.responsable_reception,
    c.observations_reception,
    
    -- Métadonnées
    c.created_by,
    c.created_at,
    c.updated_by,
    c.updated_at,
    
    -- Informations calculées
    CASE 
        WHEN c.date_livraison_prevue IS NOT NULL AND c.date_livraison_prevue < CURRENT_DATE 
             AND c.statut NOT IN ('livre_complet', 'cloture', 'annule') 
        THEN TRUE 
        ELSE FALSE 
    END as en_retard,
    
    CASE 
        WHEN c.date_livraison_reelle IS NOT NULL AND c.date_livraison_prevue IS NOT NULL
        THEN c.date_livraison_reelle - c.date_livraison_prevue
        ELSE NULL
    END as retard_jours,
    
    -- Nombre d'items
    COALESCE(items_stats.nombre_items, 0) as nombre_items,
    COALESCE(items_stats.items_livres, 0) as items_livres,
    COALESCE(items_stats.items_en_attente, 0) as items_en_attente,
    
    -- Progression en pourcentage
    CASE 
        WHEN c.statut = 'cloture' THEN 100
        WHEN c.statut = 'annule' THEN 0
        ELSE (c.etape_actuelle - 1) * 10
    END as progression_pourcentage,
    
    -- Informations du workflow actuel
    current_step.nom_etape as etape_actuelle_nom,
    current_step.description_etape as etape_actuelle_description,
    current_step.responsable_email as etape_responsable_email,
    current_step.date_debut as etape_date_debut,
    current_step.date_limite as etape_date_limite,
    
    -- Nombre de documents
    COALESCE(docs_count.nombre_documents, 0) as nombre_documents

FROM gestion_commandes c

-- Statistiques des items
LEFT JOIN (
    SELECT 
        commande_id,
        COUNT(*) as nombre_items,
        COUNT(*) FILTER (WHERE quantite_livree >= quantite_commandee) as items_livres,
        COUNT(*) FILTER (WHERE quantite_livree < quantite_commandee) as items_en_attente
    FROM gestion_commande_items
    GROUP BY commande_id
) items_stats ON c.id = items_stats.commande_id

-- Étape actuelle du workflow
LEFT JOIN gestion_commande_workflow current_step ON (
    c.id = current_step.commande_id 
    AND current_step.numero_etape = c.etape_actuelle
)

-- Nombre de documents
LEFT JOIN (
    SELECT 
        commande_id,
        COUNT(*) as nombre_documents
    FROM gestion_commande_documents
    GROUP BY commande_id
) docs_count ON c.id = docs_count.commande_id

WHERE c.deleted_at IS NULL
ORDER BY c.created_at DESC;

-- =====================================================
-- VUE POUR LA TABLE INTERACTIVE
-- =====================================================

CREATE OR REPLACE VIEW commands_table_view AS
SELECT 
    c.id,
    c.numero_commande,
    c.type_commande,
    c.demandeur_nom,
    c.demandeur_service,
    c.fournisseur_nom,
    c.statut,
    c.date_commande,
    c.date_livraison_prevue,
    c.montant_ttc,
    c.devise,
    c.etape_actuelle,
    c.urgence,
    c.created_at,
    
    -- Code de commande (identique au numéro pour compatibilité)
    c.numero_commande as code_commande,
    
    -- Description générée à partir de l'objet
    CASE 
        WHEN LENGTH(c.objet_commande) > 50 
        THEN LEFT(c.objet_commande, 47) || '...'
        ELSE c.objet_commande
    END as description_courte,
    
    -- Statut en retard
    CASE 
        WHEN c.date_livraison_prevue IS NOT NULL AND c.date_livraison_prevue < CURRENT_DATE 
             AND c.statut NOT IN ('livre_complet', 'cloture', 'annule') 
        THEN TRUE 
        ELSE FALSE 
    END as en_retard,
    
    -- Progression
    CASE 
        WHEN c.statut = 'cloture' THEN 100
        WHEN c.statut = 'annule' THEN 0
        ELSE (c.etape_actuelle - 1) * 10
    END as progression_pourcentage,
    
    -- Date formatée
    TO_CHAR(c.date_commande, 'DD/MM/YYYY') as date_commande_formatted,
    TO_CHAR(c.date_livraison_prevue, 'DD/MM/YYYY') as date_livraison_formatted,
    
    -- Informations pour les actions
    c.created_by,
    c.demandeur_email,
    
    -- Workflow info
    workflow_info.etapes_info,
    
    -- Dernière activité
    GREATEST(
        c.updated_at,
        COALESCE(workflow_info.derniere_activite_workflow, c.updated_at)
    ) as derniere_activite

FROM gestion_commandes c

-- Informations du workflow
LEFT JOIN (
    SELECT 
        commande_id,
        jsonb_agg(
            jsonb_build_object(
                'numero', numero_etape,
                'nom', nom_etape,
                'statut', statut_etape,
                'date_debut', date_debut,
                'date_fin', date_fin,
                'en_retard', CASE 
                    WHEN statut_etape = 'en_cours' AND date_limite < NOW() THEN true
                    ELSE false
                END
            )
            ORDER BY numero_etape
        ) as etapes_info,
        MAX(updated_at) as derniere_activite_workflow
    FROM gestion_commande_workflow 
    GROUP BY commande_id
) workflow_info ON c.id = workflow_info.commande_id

WHERE c.deleted_at IS NULL
ORDER BY c.created_at DESC;

-- =====================================================
-- VUE DES STATISTIQUES GLOBALES
-- =====================================================

CREATE OR REPLACE VIEW commands_stats AS
SELECT 
    COUNT(*) as total_commandes,
    COUNT(*) FILTER (WHERE statut = 'brouillon') as commandes_brouillon,
    COUNT(*) FILTER (WHERE statut IN ('soumise', 'validee_chef', 'validee_dg', 'approuvee_ms', 'en_cours')) as commandes_en_cours,
    COUNT(*) FILTER (WHERE statut = 'expedie') as commandes_expedie,
    COUNT(*) FILTER (WHERE statut IN ('livre_complet', 'cloture')) as commandes_livrees,
    COUNT(*) FILTER (WHERE statut = 'annule') as commandes_annulees,
    COUNT(*) FILTER (WHERE statut = 'refuse') as commandes_refusees,
    
    -- Par type
    COUNT(*) FILTER (WHERE type_commande = 'fournitures') as total_fournitures,
    COUNT(*) FILTER (WHERE type_commande = 'equipements') as total_equipements,
    COUNT(*) FILTER (WHERE type_commande = 'services') as total_services,
    COUNT(*) FILTER (WHERE type_commande = 'maintenance') as total_maintenance,
    
    -- Par urgence
    COUNT(*) FILTER (WHERE urgence = 'urgente') as commandes_urgentes,
    COUNT(*) FILTER (WHERE urgence = 'elevee') as commandes_prioritaires,
    COUNT(*) FILTER (WHERE urgence = 'normale') as commandes_normales,
    COUNT(*) FILTER (WHERE urgence = 'faible') as commandes_faibles,
    
    -- Commandes en retard
    COUNT(*) FILTER (WHERE 
        date_livraison_prevue IS NOT NULL 
        AND date_livraison_prevue < CURRENT_DATE 
        AND statut NOT IN ('livre_complet', 'cloture', 'annule')
    ) as commandes_en_retard,
    
    -- Montants
    COALESCE(SUM(montant_ht), 0) as montant_total_ht,
    COALESCE(SUM(montant_tva), 0) as montant_total_tva,
    COALESCE(SUM(montant_ttc), 0) as montant_total_ttc,
    
    -- Moyennes
    COALESCE(AVG(montant_ttc), 0) as montant_moyen_ttc,
    COALESCE(AVG(EXTRACT(DAYS FROM (date_livraison_reelle - date_commande))), 0) as delai_moyen_livraison,
    
    -- Taux de réussite
    CASE 
        WHEN COUNT(*) > 0 THEN 
            ROUND((COUNT(*) FILTER (WHERE statut IN ('livre_complet', 'cloture'))::DECIMAL / COUNT(*)) * 100, 2)
        ELSE 0 
    END as taux_livraison,
    
    CASE 
        WHEN COUNT(*) > 0 THEN 
            ROUND((COUNT(*) FILTER (WHERE statut IN ('annule', 'refuse'))::DECIMAL / COUNT(*)) * 100, 2)
        ELSE 0 
    END as taux_echec

FROM gestion_commandes
WHERE deleted_at IS NULL;

-- =====================================================
-- VUE DES COMMANDES EN RETARD
-- =====================================================

CREATE OR REPLACE VIEW commands_en_retard AS
SELECT 
    c.*,
    CURRENT_DATE - c.date_livraison_prevue as jours_retard,
    CASE 
        WHEN CURRENT_DATE - c.date_livraison_prevue <= 7 THEN 'leger'
        WHEN CURRENT_DATE - c.date_livraison_prevue <= 30 THEN 'modere'
        ELSE 'grave'
    END as niveau_retard
FROM gestion_commandes c
WHERE c.deleted_at IS NULL
    AND c.date_livraison_prevue IS NOT NULL 
    AND c.date_livraison_prevue < CURRENT_DATE 
    AND c.statut NOT IN ('livre_complet', 'cloture', 'annule')
ORDER BY c.date_livraison_prevue ASC;

-- =====================================================
-- VUE DU WORKFLOW DÉTAILLÉ
-- =====================================================

CREATE OR REPLACE VIEW commands_workflow_complete AS
SELECT 
    c.id as commande_id,
    c.numero_commande,
    c.type_commande,
    c.statut as statut_commande,
    c.demandeur_nom,
    c.demandeur_service,
    c.fournisseur_nom,
    c.montant_ttc,
    
    -- Informations de l'étape
    w.id as workflow_id,
    w.numero_etape,
    w.nom_etape,
    w.description_etape,
    w.statut_etape,
    w.responsable_email,
    w.responsable_role,
    w.date_debut,
    w.date_fin,
    w.date_limite,
    w.resultat,
    w.commentaire,
    
    -- Calculs
    CASE 
        WHEN w.statut_etape = 'en_cours' AND w.date_limite IS NOT NULL AND w.date_limite < NOW() 
        THEN TRUE 
        ELSE FALSE 
    END as etape_en_retard,
    
    CASE 
        WHEN w.date_debut IS NOT NULL AND w.date_fin IS NOT NULL 
        THEN EXTRACT(DAYS FROM (w.date_fin - w.date_debut))
        WHEN w.date_debut IS NOT NULL AND w.statut_etape = 'en_cours'
        THEN EXTRACT(DAYS FROM (NOW() - w.date_debut))
        ELSE NULL
    END as duree_etape_jours,
    
    c.created_at as commande_created_at,
    w.created_at as etape_created_at

FROM gestion_commandes c
INNER JOIN gestion_commande_workflow w ON c.id = w.commande_id
WHERE c.deleted_at IS NULL
ORDER BY c.created_at DESC, w.numero_etape ASC;

-- =====================================================
-- PERMISSIONS SUR LES VUES
-- =====================================================

-- Accorder les permissions de lecture aux utilisateurs authentifiés
GRANT SELECT ON commands_complete TO authenticated;
GRANT SELECT ON commands_table_view TO authenticated;
GRANT SELECT ON commands_stats TO authenticated;
GRANT SELECT ON commands_en_retard TO authenticated;
GRANT SELECT ON commands_workflow_complete TO authenticated;

-- =====================================================
-- COMMENTAIRES SUR LES VUES
-- =====================================================

COMMENT ON VIEW commands_complete IS 'Vue complète des commandes avec toutes les informations et calculs';
COMMENT ON VIEW commands_table_view IS 'Vue optimisée pour l''affichage de la table interactive';
COMMENT ON VIEW commands_stats IS 'Statistiques globales des commandes';
COMMENT ON VIEW commands_en_retard IS 'Commandes en retard de livraison';
COMMENT ON VIEW commands_workflow_complete IS 'Vue détaillée du workflow des commandes';

-- =====================================================
-- VALIDATION DES VUES
-- =====================================================

DO $$
DECLARE
    view_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO view_count
    FROM information_schema.views
    WHERE table_schema = 'public'
    AND table_name IN ('commands_complete', 'commands_table_view', 'commands_stats', 'commands_en_retard', 'commands_workflow_complete');
    
    IF view_count = 5 THEN
        RAISE NOTICE '✅ Toutes les vues de commandes ont été créées avec succès';
    ELSE
        RAISE EXCEPTION '❌ Erreur : Seulement % vues créées sur 5 attendues', view_count;
    END IF;
END $$;
