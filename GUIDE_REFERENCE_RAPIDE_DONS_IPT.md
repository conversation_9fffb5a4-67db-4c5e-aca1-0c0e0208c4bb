# 🚀 Guide de Référence Rapide - Système de Gestion des Dons IPT

## 📋 Workflow en un Coup d'Œil

### 🔄 Les 10 Étapes du Processus

| Étape | Responsable | Action Principale | Durée Moyenne | Documents Générés |
|-------|-------------|-------------------|---------------|-------------------|
| **1** | Demandeur | Création du dossier | 1 jour | Accusé de réception |
| **2** | Responsable Appro | Validation + BP | 1-2 jours | Bon de Prélèvement |
| **3** | Magasinier | Vérification physique | 1-2 jours | Rapport de vérification |
| **4** | Transitaire | Validation transport | 1 jour | Instructions transport |
| **5** | Réceptionniste | Réception + signature | 1 jour | BL signé |
| **6** | RVE | Inventaire + étiquettes | 1-2 jours | Bon de Sortie |
| **7** | RVE | Affectation finale | 2-3 jours | PV d'installation |
| **8** | RVE/Admin | Récapitulatif | 1 jour | Tableau récapitulatif |
| **9** | SD Achats | Envoi comptabilité | 2-3 jours | Bordereau transmission |
| **10** | Admin Système | Archivage définitif | 1 jour | Certificat d'archivage |

**⏱️ Durée totale moyenne : 10-15 jours ouvrés**

---

## 👥 Rôles et Responsabilités

### 🎯 Matrice des Permissions

| Rôle | Créer | Valider | Modifier | Consulter | Archiver |
|------|-------|---------|----------|-----------|----------|
| **Demandeur** | ✅ | ❌ | ⚠️* | ✅ | ❌ |
| **Responsable Appro** | ❌ | ✅ | ❌ | ✅ | ❌ |
| **Magasinier** | ❌ | ✅ | ❌ | ✅ | ❌ |
| **Transitaire** | ❌ | ✅ | ❌ | ✅ | ❌ |
| **Réceptionniste** | ❌ | ✅ | ❌ | ✅ | ❌ |
| **RVE** | ❌ | ✅ | ✅ | ✅ | ❌ |
| **SD Achats** | ❌ | ✅ | ❌ | ✅ | ❌ |
| **Admin Système** | ✅ | ✅ | ✅ | ✅ | ✅ |

*⚠️ Modification limitée aux brouillons uniquement*

---

## 📄 Documents Requis par Étape

### 📋 Checklist Complète

#### Étape 1 - Création (Demandeur)
- ✅ **Lettre de don** (OBLIGATOIRE)
- ⚠️ **Facture** (OPTIONNEL)
- ✅ **Bon de livraison** (SI DISPONIBLE)
- ⚠️ **Bon de prélèvement** (OPTIONNEL)

#### Étape 2 - Validation Appro
- ✅ **Bon de Prélèvement** (AUTO-GÉNÉRÉ pour équipements)

#### Étape 3 - Magasinier
- ✅ **Rapport de vérification** (AUTO-GÉNÉRÉ)
- ⚠️ **Photos de vérification** (RECOMMANDÉES)

#### Étape 4 - Transitaire
- ✅ **Instructions de transport** (AUTO-GÉNÉRÉES)

#### Étape 5 - Réception
- ✅ **BL signé numériquement** (AUTO-GÉNÉRÉ)
- ✅ **Rapport de réception** (AUTO-GÉNÉRÉ)

#### Étape 6 - Inventaire RVE
- ✅ **Bon de Sortie** (AUTO-GÉNÉRÉ)
- ✅ **Étiquettes code-barres** (AUTO-GÉNÉRÉES)

#### Étape 7 - Affectation RVE
- ✅ **PV d'installation** (AUTO-GÉNÉRÉ)

#### Étape 8 - Récapitulatif
- ✅ **Tableau récapitulatif** (AUTO-GÉNÉRÉ)
- ✅ **Fiche comptable** (AUTO-GÉNÉRÉE)

#### Étape 9 - Comptabilité
- ✅ **Bordereau de transmission** (AUTO-GÉNÉRÉ)

#### Étape 10 - Archivage
- ✅ **Archive complète** (AUTO-GÉNÉRÉE)
- ✅ **Certificat d'archivage** (AUTO-GÉNÉRÉ)

---

## 🔗 URLs et Accès Rapides

### 🌐 Interfaces Principales

| Interface | URL | Rôles Autorisés |
|-----------|-----|-----------------|
| **Enregistrement** | `donations-registration.html` | Demandeur, Admin |
| **Workflow** | `donations-workflow.html` | Tous sauf Demandeur |
| **Documents** | `donations-documents.html` | Tous |
| **Gestion** | `donations-management.html` | Tous |

### 🔑 Comptes de Test

| Rôle | Email | Mot de passe |
|------|-------|--------------|
| **Demandeur** | `<EMAIL>` | `demo123` |
| **Responsable Appro** | `<EMAIL>` | `demo123` |
| **Magasinier** | `<EMAIL>` | `demo123` |
| **Transitaire** | `<EMAIL>` | `demo123` |
| **Réceptionniste** | `<EMAIL>` | `demo123` |
| **RVE** | `<EMAIL>` | `demo123` |
| **SD Achats** | `<EMAIL>` | `demo123` |
| **Admin** | `<EMAIL>` | `demo123` |

---

## ⚡ Actions Rapides par Rôle

### 👤 Demandeur
```
1. Ouvrir donations-registration.html
2. Sélectionner type de don (Article/Équipement)
3. Remplir informations demandeur (pré-remplies)
4. Décrire le don en détail
5. Uploader documents (lettre de don obligatoire)
6. Soumettre ou sauvegarder en brouillon
```

### 👨‍💼 Responsable Approvisionnement
```
1. Ouvrir donations-workflow.html
2. Consulter "Actions Requises"
3. Examiner le dossier complet
4. Valider ou refuser avec commentaires
5. BP généré automatiquement si équipement
```

### 📦 Magasinier
```
1. Recevoir notification de don validé
2. Localiser physiquement les articles
3. Vérifier quantité, état, conformité
4. Saisir observations dans le système
5. Prendre photos si nécessaire
6. Valider le traitement
```

### 🚛 Transitaire
```
1. Analyser les conditions de transport
2. Évaluer les risques logistiques
3. Établir planning de livraison
4. Générer instructions de transport
5. Valider ou refuser avec conditions
```

### 📥 Réceptionniste
```
1. Préparer la zone de réception
2. Contrôler la livraison à l'arrivée
3. Vérifier conformité articles/BL
4. Documenter l'état de réception
5. Signer numériquement le BL
```

### 🏷️ RVE (Inventaire)
```
1. Attribuer numéro d'inventaire
2. Saisir spécifications techniques
3. Générer Bon de Sortie
4. Créer étiquettes code-barres
5. Imprimer et appliquer étiquettes
```

### 🎯 RVE (Affectation)
```
1. Coordonner avec service destinataire
2. Organiser transport interne
3. Installer et configurer équipement
4. Former les utilisateurs
5. Obtenir signature d'acceptation
```

### 📊 RVE/Admin (Récapitulatif)
```
1. Générer tableau récapitulatif
2. Vérifier complétude du dossier
3. Préparer documents comptables
4. Valider toutes les signatures
5. Transmettre à SD Achats
```

### 💰 Sous-Direction Achats
```
1. Contrôler pièces justificatives
2. Vérifier valorisation comptable
3. Compléter imputation comptable
4. Transmettre à la comptabilité
5. Suivre l'intégration comptable
```

### 🗄️ Admin Système
```
1. Constituer archive complète
2. Vérifier intégrité des documents
3. Générer métadonnées
4. Chiffrer et sauvegarder
5. Émettre certificat d'archivage
```

---

## 🚨 Codes d'Erreur Fréquents

### ❌ Erreurs de Validation

| Code | Message | Solution |
|------|---------|----------|
| **E001** | "Champ requis manquant" | Remplir tous les champs marqués * |
| **E002** | "Email invalide" | Vérifier format email |
| **E003** | "Fichier trop volumineux" | Réduire taille < 10MB |
| **E004** | "Format non supporté" | Utiliser PDF, JPG, PNG, DOC |
| **E005** | "Permissions insuffisantes" | Vérifier rôle utilisateur |

### ⚠️ Erreurs de Workflow

| Code | Message | Solution |
|------|---------|----------|
| **W001** | "Don non trouvé" | Vérifier numéro de don |
| **W002** | "Étape non autorisée" | Attendre validation précédente |
| **W003** | "Signature manquante" | Compléter signature requise |
| **W004** | "Document manquant" | Uploader document obligatoire |
| **W005** | "Délai dépassé" | Contacter responsable |

### 🔧 Erreurs Techniques

| Code | Message | Solution |
|------|---------|----------|
| **T001** | "Connexion impossible" | Vérifier réseau |
| **T002** | "Session expirée" | Se reconnecter |
| **T003** | "Erreur serveur" | Réessayer ou contacter admin |
| **T004** | "Upload échoué" | Vérifier connexion et réessayer |
| **T005** | "Signature impossible" | Vérifier navigateur/tactile |

---

## 📞 Contacts et Support

### 🆘 Support Technique
- **Email** : <EMAIL>
- **Téléphone** : +216 71 XXX XXX
- **Heures** : Lun-Ven 8h-17h

### 👨‍💼 Responsables Métier
- **Responsable Appro** : <EMAIL>
- **Chef Magasinier** : <EMAIL>
- **Responsable RVE** : <EMAIL>
- **SD Achats** : <EMAIL>

### 🔧 Administration Système
- **Admin Principal** : <EMAIL>
- **Urgences 24h/7j** : +216 XX XXX XXX

---

## 📊 Indicateurs de Performance

### ⏱️ Délais Standards
- **Création → Validation Appro** : 1-2 jours
- **Validation → Réception** : 3-5 jours
- **Réception → Affectation** : 3-5 jours
- **Affectation → Archivage** : 2-3 jours
- **TOTAL MOYEN** : 10-15 jours

### 📈 Objectifs de Qualité
- **Taux de validation** : > 85%
- **Délai de traitement** : < 15 jours
- **Satisfaction utilisateur** : > 90%
- **Disponibilité système** : > 99.5%

### 🎯 KPIs de Suivi
- Nombre de dons traités/mois
- Délai moyen par étape
- Taux d'erreur par type
- Satisfaction par rôle

---

**🚀 Guide de Référence Rapide - Système de Gestion des Dons IPT**  
*Version 1.0 - Institut Pasteur de Tunis*  
*Aide-mémoire pour tous les utilisateurs*
