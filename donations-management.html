<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Dons - IPT</title>
    
    <!-- CSS existant -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">
    <link rel="stylesheet" href="assets/css/donations-style.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <!-- Configuration et modules existants -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>
    <script src="assets/js/supabase-database.js"></script>
    
    <style>
        .donations-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .donation-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
        }
        
        .donation-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .donation-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-brouillon { background: #f3f4f6; color: #6b7280; }
        .status-soumis_dg { background: #fef3c7; color: #d97706; }
        .status-avis_dt { background: #dbeafe; color: #2563eb; }
        .status-soumis_ms { background: #fde68a; color: #d97706; }
        .status-approuve_ms { background: #d1fae5; color: #059669; }
        .status-refuse_ms { background: #fee2e2; color: #dc2626; }
        .status-recu { background: #e0e7ff; color: #4338ca; }
        .status-affecte { background: #dcfce7; color: #16a34a; }
        
        .donation-type {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        
        .type-article { background: #e0f2fe; color: #0277bd; }
        .type-equipement { background: #f3e5f5; color: #7b1fa2; }
        
        .donation-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn-action {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s;
        }
        
        .btn-primary { background: #3b82f6; color: white; }
        .btn-success { background: #10b981; color: white; }
        .btn-warning { background: #f59e0b; color: white; }
        .btn-danger { background: #ef4444; color: white; }
        .btn-secondary { background: #6b7280; color: white; }
        
        .btn-action:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #374151;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }
        
        .items-container {
            border: 1px solid #e5e7eb;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .item-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f9fafb;
            border-radius: 4px;
        }
        
        .workflow-timeline {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8fafc;
            border-radius: 4px;
            border-left: 4px solid #3b82f6;
        }
        
        .timeline-date {
            font-size: 12px;
            color: #6b7280;
            margin-right: 15px;
            min-width: 120px;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .timeline-action {
            font-weight: bold;
            color: #1f2937;
        }
        
        .timeline-comment {
            font-size: 13px;
            color: #6b7280;
            margin-top: 2px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #3b82f6;
        }
        
        .stat-label {
            color: #6b7280;
            font-size: 14px;
            margin-top: 5px;
        }

        /* Styles pour la table de gestion manuelle */
        .view-tabs {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .tab-btn {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: #f8fafc;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab-btn.active {
            background: white;
            border-bottom-color: #3b82f6;
            color: #3b82f6;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .filters-header h3 {
            margin: 0;
            color: #374151;
        }

        .filters-content {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .filters-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 15px;
        }

        .table-view {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-container {
            width: 100%;
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .table-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .table-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .page-size-select {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            background: white;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .donations-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .donations-table th {
            background: #f8fafc;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            white-space: nowrap;
            position: relative;
        }

        .donations-table th.sortable {
            cursor: pointer;
            user-select: none;
        }

        .donations-table th.sortable:hover {
            background: #f1f5f9;
        }

        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }

        .sort-indicator.asc::after {
            content: '↑';
            opacity: 1;
        }

        .sort-indicator.desc::after {
            content: '↓';
            opacity: 1;
        }

        .donations-table td {
            padding: 12px 8px;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }

        .donations-table tbody tr:hover {
            background: #f8fafc;
        }

        .table-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-article {
            background: #dbeafe;
            color: #1e40af;
        }

        .badge-equipement {
            background: #f3e8ff;
            color: #7c3aed;
        }

        .table-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-brouillon { background: #f3f4f6; color: #6b7280; }
        .status-bp_cree { background: #dbeafe; color: #1e40af; }
        .status-chez_magasinier { background: #fef3c7; color: #d97706; }
        .status-chez_transitaire { background: #fed7aa; color: #ea580c; }
        .status-chez_receptionniste { background: #fecaca; color: #dc2626; }
        .status-bs_cree { background: #d1fae5; color: #059669; }
        .status-chez_rve { background: #e0e7ff; color: #4338ca; }
        .status-recap_cree { background: #f3e8ff; color: #7c3aed; }
        .status-envoye_comptabilite { background: #dcfce7; color: #16a34a; }
        .status-archive { background: #f1f5f9; color: #475569; }

        .table-step {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            font-size: 11px;
            font-weight: bold;
            color: white;
            background: #6b7280;
        }

        .step-current {
            background: #3b82f6;
            animation: pulse 2s infinite;
        }

        .step-completed {
            background: #10b981;
        }

        .step-delayed {
            background: #ef4444;
        }

        .actions-column {
            width: 120px;
            text-align: center;
        }

        .table-actions {
            display: flex;
            gap: 4px;
            justify-content: center;
        }

        .btn-table {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 2px;
        }

        .btn-table:hover {
            transform: translateY(-1px);
            opacity: 0.9;
        }

        .btn-view { background: #6b7280; color: white; }
        .btn-edit { background: #f59e0b; color: white; }
        .btn-validate { background: #10b981; color: white; }
        .btn-reject { background: #ef4444; color: white; }

        .table-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-top: 1px solid #e5e7eb;
            background: #f8fafc;
        }

        .pagination-controls {
            display: flex;
            gap: 5px;
            align-items: center;
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #f3f4f6;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-btn.active {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .designation-cell {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .designation-cell:hover {
            white-space: normal;
            overflow: visible;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .table-wrapper {
                font-size: 12px;
            }

            .donations-table th,
            .donations-table td {
                padding: 8px 4px;
            }

            .table-header {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .table-footer {
                flex-direction: column;
                gap: 10px;
                align-items: stretch;
            }

            .filters-row {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1>🎁 Gestion des Dons - IPT</h1>
                <div class="header-actions">
                    <button id="btnNouveauDon" class="btn btn-primary">
                        ➕ Nouveau Don
                    </button>
                    <button id="btnExport" class="btn btn-info">
                        📊 Exporter
                    </button>
                    <button id="btnStatistiques" class="btn btn-secondary">
                        📈 Statistiques
                    </button>
                </div>
            </div>
        </header>

        <!-- Onglets de vue -->
        <div class="view-tabs">
            <button class="tab-btn active" onclick="switchView('table')">📊 Vue Tableau</button>
            <button class="tab-btn" onclick="switchView('cards')">🗃️ Vue Cartes</button>
        </div>

        <!-- Filtres avancés -->
        <div class="filters-section">
            <div class="filters-header">
                <h3>🔍 Filtres et Recherche</h3>
                <button id="btnToggleFilters" class="btn btn-secondary btn-sm">Afficher/Masquer</button>
            </div>
            <div class="filters-content" id="filtersContent">
                <div class="filters-row">
                    <div class="filter-group">
                        <label>Recherche globale</label>
                        <input type="text" id="searchGlobal" placeholder="Rechercher dans tous les champs...">
                    </div>
                    <div class="filter-group">
                        <label>Statut</label>
                        <select id="filterStatut">
                            <option value="">Tous les statuts</option>
                            <option value="brouillon">Brouillon</option>
                            <option value="bp_cree">BP Créé</option>
                            <option value="chez_magasinier">Chez Magasinier</option>
                            <option value="chez_transitaire">Chez Transitaire</option>
                            <option value="chez_receptionniste">Chez Réceptionniste</option>
                            <option value="bs_cree">BS Créé</option>
                            <option value="chez_rve">Chez RVE</option>
                            <option value="recap_cree">Récap Créé</option>
                            <option value="envoye_comptabilite">Envoyé Comptabilité</option>
                            <option value="archive">Archivé</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Type</label>
                        <select id="filterType">
                            <option value="">Tous les types</option>
                            <option value="article">Articles</option>
                            <option value="equipement">Équipements</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>Étape</label>
                        <select id="filterEtape">
                            <option value="">Toutes les étapes</option>
                            <option value="1">Étape 1 - Création</option>
                            <option value="2">Étape 2 - BP</option>
                            <option value="3">Étape 3 - Magasinier</option>
                            <option value="4">Étape 4 - Transitaire</option>
                            <option value="5">Étape 5 - Réception</option>
                            <option value="6">Étape 6 - BS</option>
                            <option value="7">Étape 7 - RVE</option>
                            <option value="8">Étape 8 - Récap</option>
                            <option value="9">Étape 9 - Comptabilité</option>
                            <option value="10">Étape 10 - Archive</option>
                        </select>
                    </div>
                </div>
                <div class="filters-row">
                    <div class="filter-group">
                        <label>Date de création</label>
                        <input type="date" id="filterDateDebut" placeholder="Date début">
                    </div>
                    <div class="filter-group">
                        <label>à</label>
                        <input type="date" id="filterDateFin" placeholder="Date fin">
                    </div>
                    <div class="filter-group">
                        <label>Donateur</label>
                        <input type="text" id="filterDonateur" placeholder="Nom du donateur">
                    </div>
                    <div class="filter-group">
                        <label>Bénéficiaire</label>
                        <input type="text" id="filterBeneficiaire" placeholder="Service bénéficiaire">
                    </div>
                </div>
                <div class="filters-actions">
                    <button id="btnApplyFilters" class="btn btn-primary">🔍 Appliquer</button>
                    <button id="btnClearFilters" class="btn btn-secondary">🗑️ Effacer</button>
                    <button id="btnRefresh" class="btn btn-secondary">🔄 Actualiser</button>
                </div>
            </div>
        </div>

        <!-- Vue Tableau -->
        <div id="tableView" class="table-view">
            <div class="table-container">
                <div class="table-header">
                    <div class="table-info">
                        <span id="tableResultsCount">0 résultats</span>
                        <span id="tableResultsInfo"></span>
                    </div>
                    <div class="table-controls">
                        <select id="pageSize" class="page-size-select">
                            <option value="10">10 par page</option>
                            <option value="25" selected>25 par page</option>
                            <option value="50">50 par page</option>
                            <option value="100">100 par page</option>
                        </select>
                    </div>
                </div>

                <div class="table-wrapper">
                    <table id="donationsTable" class="donations-table">
                        <thead>
                            <tr>
                                <th data-column="numero_don" class="sortable">
                                    N° Don <span class="sort-indicator"></span>
                                </th>
                                <th data-column="type_don" class="sortable">
                                    Type <span class="sort-indicator"></span>
                                </th>
                                <th data-column="donneur_nom" class="sortable">
                                    Donateur <span class="sort-indicator"></span>
                                </th>
                                <th data-column="demandeur_service" class="sortable">
                                    Bénéficiaire <span class="sort-indicator"></span>
                                </th>
                                <th data-column="code_don" class="sortable">
                                    Code <span class="sort-indicator"></span>
                                </th>
                                <th data-column="designation" class="sortable">
                                    Désignation <span class="sort-indicator"></span>
                                </th>
                                <th data-column="statut" class="sortable">
                                    Statut <span class="sort-indicator"></span>
                                </th>
                                <th data-column="etape_actuelle" class="sortable">
                                    Étape <span class="sort-indicator"></span>
                                </th>
                                <th data-column="created_at" class="sortable">
                                    Date Création <span class="sort-indicator"></span>
                                </th>
                                <th class="actions-column">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="donationsTableBody">
                            <!-- Les données seront chargées ici -->
                        </tbody>
                    </table>
                </div>

                <div class="table-footer">
                    <div class="pagination-info">
                        <span id="paginationInfo">Affichage de 0 à 0 sur 0 résultats</span>
                    </div>
                    <div class="pagination-controls" id="paginationControls">
                        <!-- Les contrôles de pagination seront générés ici -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Vue Cartes (existante) -->
        <div id="cardsView" class="cards-view" style="display: none;">
            <!-- Statistiques (masquées par défaut) -->
            <div id="statistiquesSection" class="stats-section" style="display: none;">
                <h2>📊 Statistiques des Dons</h2>
                <div class="stats-grid" id="statsGrid">
                    <!-- Les statistiques seront chargées ici -->
                </div>
            </div>

            <!-- Liste des dons -->
            <div class="donations-container">
                <div id="donationsList">
                    <!-- Les dons seront chargés ici -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Nouveau Don -->
    <div id="modalNouveauDon" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>➕ Nouveau Don</h2>
                <span class="close" onclick="fermerModal('modalNouveauDon')">&times;</span>
            </div>
            
            <form id="formNouveauDon">
                <div class="form-group">
                    <label for="typeDon">Type de don *</label>
                    <select id="typeDon" required>
                        <option value="">Sélectionner le type</option>
                        <option value="article">Article</option>
                        <option value="equipement">Équipement</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="donneurNom">Nom du donneur *</label>
                    <input type="text" id="donneurNom" required>
                </div>
                
                <div class="form-group">
                    <label for="donneurContact">Contact du donneur</label>
                    <input type="text" id="donneurContact">
                </div>
                
                <div class="form-group">
                    <label for="donneurAdresse">Adresse du donneur</label>
                    <textarea id="donneurAdresse"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="demandeurService">Service demandeur *</label>
                    <input type="text" id="demandeurService" required>
                </div>
                
                <div class="form-group">
                    <label for="lieuAffectation">Lieu d'affectation *</label>
                    <input type="text" id="lieuAffectation" required>
                </div>
                
                <div class="form-group">
                    <label for="raisonDon">Raison du don (demande explicative) *</label>
                    <textarea id="raisonDon" required placeholder="Expliquez la raison et la justification de ce don..."></textarea>
                </div>
                
                <div class="form-group">
                    <label for="valeurEstimee">Valeur estimée (TND)</label>
                    <input type="number" id="valeurEstimee" step="0.01" min="0">
                </div>
                
                <!-- Section Articles/Équipements -->
                <div class="form-group">
                    <label>Articles/Équipements *</label>
                    <div class="items-container">
                        <div id="itemsList">
                            <!-- Les items seront ajoutés ici -->
                        </div>
                        <button type="button" id="btnAjouterItem" class="btn btn-secondary">
                            ➕ Ajouter un item
                        </button>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="button" onclick="fermerModal('modalNouveauDon')" class="btn btn-secondary">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-primary">
                        💾 Créer le don
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal Détails Don -->
    <div id="modalDetailsDon" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="detailsTitle">📋 Détails du Don</h2>
                <span class="close" onclick="fermerModal('modalDetailsDon')">&times;</span>
            </div>
            
            <div id="detailsContent">
                <!-- Le contenu sera chargé dynamiquement -->
            </div>
        </div>
    </div>

    <!-- Modal Actions Don -->
    <div id="modalActionsDon" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="actionsTitle">⚡ Actions sur le Don</h2>
                <span class="close" onclick="fermerModal('modalActionsDon')">&times;</span>
            </div>

            <div id="actionsContent">
                <form id="formActionDon">
                    <input type="hidden" id="actionDonationId">
                    <input type="hidden" id="actionType">

                    <div class="form-group">
                        <label for="actionCommentaire">Commentaire/Avis *</label>
                        <textarea id="actionCommentaire" required placeholder="Votre commentaire ou avis..."></textarea>
                    </div>

                    <div id="actionButtons" class="form-actions">
                        <!-- Les boutons seront générés dynamiquement -->
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Upload Documents -->
    <div id="modalUploadDoc" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📎 Ajouter un Document</h2>
                <span class="close" onclick="fermerModal('modalUploadDoc')">&times;</span>
            </div>

            <form id="formUploadDoc">
                <input type="hidden" id="uploadDonationId">

                <div class="form-group">
                    <label for="typeDocument">Type de document *</label>
                    <select id="typeDocument" required>
                        <option value="">Sélectionner le type</option>
                        <option value="demande_explicative">Demande explicative</option>
                        <option value="lettre_don">Lettre de don</option>
                        <option value="bon_prelevement">Bon de prélèvement</option>
                        <option value="facture">Facture</option>
                        <option value="bon_livraison">Bon de livraison</option>
                        <option value="bon_sortie">Bon de sortie</option>
                        <option value="autorisation_ms">Autorisation MS</option>
                        <option value="avis_technique">Avis technique</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="nomDocument">Nom du document *</label>
                    <input type="text" id="nomDocument" required>
                </div>

                <div class="form-group">
                    <label for="numeroDocument">Numéro du document</label>
                    <input type="text" id="numeroDocument">
                </div>

                <div class="form-group">
                    <label for="dateDocument">Date du document</label>
                    <input type="date" id="dateDocument">
                </div>

                <div class="form-group">
                    <label for="fichierDocument">Fichier *</label>
                    <input type="file" id="fichierDocument" required accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                    <small>Formats acceptés: PDF, Word, Images (max 10MB)</small>
                </div>

                <div class="form-actions">
                    <button type="button" onclick="fermerModal('modalUploadDoc')" class="btn btn-secondary">
                        Annuler
                    </button>
                    <button type="submit" class="btn btn-primary">
                        📎 Ajouter le document
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/donations-management.js"></script>
    <script src="assets/js/donations-table-manager.js"></script>

    <script>
        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🎁 Initialisation du module de gestion des dons...');

            try {
                // Vérifier l'authentification
                if (typeof SupabaseAuth !== 'undefined' && authSystem) {
                    const user = await authSystem.getCurrentUser();
                    if (!user) {
                        window.location.href = 'login.html';
                        return;
                    }
                    console.log('✅ Utilisateur authentifié:', user.email);
                }

                // Initialiser le module de gestion des dons
                if (typeof DonationsManager !== 'undefined') {
                    await DonationsManager.initialize();
                    console.log('✅ Module de gestion des dons initialisé');
                } else {
                    console.error('❌ Module DonationsManager non trouvé');
                }

                // Initialiser le gestionnaire de table
                if (typeof DonationsTableManager !== 'undefined') {
                    await donationsTableManager.initialize();
                    console.log('✅ Gestionnaire de table initialisé');
                } else {
                    console.error('❌ Gestionnaire de table non trouvé');
                }

            } catch (error) {
                console.error('❌ Erreur lors de l\'initialisation:', error);
                showNotification('Erreur lors de l\'initialisation du système', 'error');
            }
        });

        // Fonctions utilitaires globales
        function fermerModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        function ouvrirModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
        }

        function showNotification(message, type = 'info') {
            // Utiliser le système de notifications existant si disponible
            if (typeof showMessage === 'function') {
                showMessage(message, type);
            } else {
                // Fallback simple
                const notification = document.createElement('div');
                notification.className = `notification notification-${type}`;
                notification.textContent = message;
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 4px;
                    color: white;
                    z-index: 10000;
                    max-width: 300px;
                    background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6'};
                `;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.remove();
                }, 5000);
            }
        }

        // Fonction pour basculer entre les vues
        function switchView(viewType) {
            const tableView = document.getElementById('tableView');
            const cardsView = document.getElementById('cardsView');
            const tabBtns = document.querySelectorAll('.tab-btn');

            // Mettre à jour les onglets
            tabBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Basculer les vues
            if (viewType === 'table') {
                tableView.style.display = 'block';
                cardsView.style.display = 'none';

                // Charger la table si pas encore fait
                if (window.donationsTableManager && !window.donationsTableManager.data.length) {
                    donationsTableManager.loadData();
                }
            } else {
                tableView.style.display = 'none';
                cardsView.style.display = 'block';

                // Charger les cartes si pas encore fait
                if (typeof DonationsManager !== 'undefined' && DonationsManager.chargerDons) {
                    DonationsManager.chargerDons();
                }
            }
        }

        // Fonction pour basculer les filtres
        function toggleFilters() {
            if (window.donationsTableManager) {
                donationsTableManager.toggleFilters();
            }
        }

        // Fermer les modals en cliquant à l'extérieur
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
            }
        }
    </script>
</body>
</html>
