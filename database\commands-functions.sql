-- =====================================================
-- FONCTIONS POUR LA GESTION DES COMMANDES
-- Système complet de gestion des commandes IPT
-- =====================================================

-- =====================================================
-- FONCTIONS UTILITAIRES
-- =====================================================

-- Fonction pour obtenir l'email de l'utilisateur connecté
CREATE OR REPLACE FUNCTION get_user_email()
RETURNS TEXT AS $$
BEGIN
    RETURN COALESCE(
        current_setting('request.jwt.claims', true)::json->>'email',
        current_setting('app.current_user_email', true),
        '<EMAIL>'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fonction pour obtenir le rôle de l'utilisateur connecté
CREATE OR REPLACE FUNCTION get_user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN COALESCE(
        current_setting('request.jwt.claims', true)::json->'user_metadata'->>'role',
        current_setting('app.current_user_role', true),
        'user'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- GÉNÉRATION DE NUMÉROS DE COMMANDE
-- =====================================================

CREATE OR REPLACE FUNCTION generate_command_number()
RETURNS TEXT AS $$
DECLARE
    current_year TEXT;
    sequence_num INTEGER;
    command_number TEXT;
BEGIN
    current_year := EXTRACT(YEAR FROM CURRENT_DATE)::TEXT;
    
    -- Obtenir le prochain numéro de séquence pour l'année
    SELECT COALESCE(MAX(CAST(SUBSTRING(numero_commande FROM 'CMD-' || current_year || '-(.*)') AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM gestion_commandes
    WHERE numero_commande LIKE 'CMD-' || current_year || '-%';
    
    -- Formater le numéro avec padding
    command_number := 'CMD-' || current_year || '-' || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN command_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- GESTION DU WORKFLOW
-- =====================================================

-- Fonction pour initialiser le workflow d'une commande
CREATE OR REPLACE FUNCTION initialize_command_workflow(
    p_commande_id UUID,
    p_created_by TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    workflow_steps RECORD;
BEGIN
    -- Insérer toutes les étapes du workflow
    FOR workflow_steps IN
        SELECT numero, nom, description FROM (VALUES
            (1, 'Création', 'Création de la demande de commande'),
            (2, 'Validation Chef', 'Validation par le chef de service'),
            (3, 'Validation DG', 'Validation par le Directeur Général'),
            (4, 'Approbation MS', 'Approbation par le Ministère de la Santé'),
            (5, 'Passation Commande', 'Passation de la commande au fournisseur'),
            (6, 'Suivi Commande', 'Suivi de l''exécution de la commande'),
            (7, 'Expédition', 'Expédition par le fournisseur'),
            (8, 'Livraison', 'Livraison et réception des items'),
            (9, 'Contrôle Qualité', 'Contrôle qualité et conformité'),
            (10, 'Clôture', 'Clôture de la commande et archivage')
        ) AS steps(numero, nom, description)
    LOOP
        INSERT INTO gestion_commande_workflow (
            commande_id, numero_etape, nom_etape, description_etape,
            statut_etape, created_by
        ) VALUES (
            p_commande_id, workflow_steps.numero, workflow_steps.nom, workflow_steps.description,
            CASE WHEN workflow_steps.numero = 1 THEN 'termine' ELSE 'en_attente' END,
            p_created_by
        );
    END LOOP;
    
    -- Marquer l'étape 2 comme en cours
    UPDATE gestion_commande_workflow
    SET statut_etape = 'en_cours', date_debut = NOW()
    WHERE commande_id = p_commande_id AND numero_etape = 2;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTIONS DE VALIDATION
-- =====================================================

-- Fonction pour valider une commande par le chef de service
CREATE OR REPLACE FUNCTION validate_command_by_chef(
    p_commande_id UUID,
    p_user_email TEXT,
    p_commentaire TEXT DEFAULT NULL,
    p_approve BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
DECLARE
    command_record RECORD;
    new_status TEXT;
    new_etape INTEGER;
BEGIN
    -- Vérifier que la commande existe et est dans le bon statut
    SELECT * INTO command_record
    FROM gestion_commandes
    WHERE id = p_commande_id AND statut = 'soumise';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Commande non trouvée ou pas dans le statut approprié';
    END IF;
    
    -- Déterminer le nouveau statut
    IF p_approve THEN
        new_status := 'validee_chef';
        new_etape := 3;
    ELSE
        new_status := 'refuse';
        new_etape := 1;
    END IF;
    
    -- Mettre à jour la commande
    UPDATE gestion_commandes
    SET 
        statut = new_status,
        etape_actuelle = new_etape,
        validee_chef_par = p_user_email,
        validee_chef_date = NOW(),
        validee_chef_commentaire = p_commentaire,
        updated_by = p_user_email
    WHERE id = p_commande_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_commande_workflow
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        resultat = CASE WHEN p_approve THEN 'approuve' ELSE 'refuse' END,
        commentaire = p_commentaire,
        responsable_email = p_user_email
    WHERE commande_id = p_commande_id AND numero_etape = 2;
    
    -- Si approuvé, passer à l'étape suivante
    IF p_approve THEN
        UPDATE gestion_commande_workflow
        SET 
            statut_etape = 'en_cours',
            date_debut = NOW(),
            responsable_email = '<EMAIL>'
        WHERE commande_id = p_commande_id AND numero_etape = 3;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour valider une commande par le DG
CREATE OR REPLACE FUNCTION validate_command_by_dg(
    p_commande_id UUID,
    p_user_email TEXT,
    p_commentaire TEXT DEFAULT NULL,
    p_approve BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
DECLARE
    command_record RECORD;
    new_status TEXT;
    new_etape INTEGER;
BEGIN
    -- Vérifier que la commande existe et est dans le bon statut
    SELECT * INTO command_record
    FROM gestion_commandes
    WHERE id = p_commande_id AND statut = 'validee_chef';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Commande non trouvée ou pas dans le statut approprié';
    END IF;
    
    -- Déterminer le nouveau statut
    IF p_approve THEN
        new_status := 'validee_dg';
        new_etape := 4;
    ELSE
        new_status := 'refuse';
        new_etape := 1;
    END IF;
    
    -- Mettre à jour la commande
    UPDATE gestion_commandes
    SET 
        statut = new_status,
        etape_actuelle = new_etape,
        validee_dg_par = p_user_email,
        validee_dg_date = NOW(),
        validee_dg_commentaire = p_commentaire,
        updated_by = p_user_email
    WHERE id = p_commande_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_commande_workflow
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        resultat = CASE WHEN p_approve THEN 'approuve' ELSE 'refuse' END,
        commentaire = p_commentaire,
        responsable_email = p_user_email
    WHERE commande_id = p_commande_id AND numero_etape = 3;
    
    -- Si approuvé, passer à l'étape suivante
    IF p_approve THEN
        UPDATE gestion_commande_workflow
        SET 
            statut_etape = 'en_cours',
            date_debut = NOW(),
            responsable_email = '<EMAIL>'
        WHERE commande_id = p_commande_id AND numero_etape = 4;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour approuver une commande par le MS
CREATE OR REPLACE FUNCTION approve_command_by_ms(
    p_commande_id UUID,
    p_user_email TEXT,
    p_commentaire TEXT DEFAULT NULL,
    p_approve BOOLEAN DEFAULT TRUE
)
RETURNS BOOLEAN AS $$
DECLARE
    command_record RECORD;
    new_status TEXT;
    new_etape INTEGER;
BEGIN
    -- Vérifier que la commande existe et est dans le bon statut
    SELECT * INTO command_record
    FROM gestion_commandes
    WHERE id = p_commande_id AND statut = 'validee_dg';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Commande non trouvée ou pas dans le statut approprié';
    END IF;
    
    -- Déterminer le nouveau statut
    IF p_approve THEN
        new_status := 'approuvee_ms';
        new_etape := 5;
    ELSE
        new_status := 'refuse';
        new_etape := 1;
    END IF;
    
    -- Mettre à jour la commande
    UPDATE gestion_commandes
    SET 
        statut = new_status,
        etape_actuelle = new_etape,
        approuvee_ms_par = p_user_email,
        approuvee_ms_date = NOW(),
        approuvee_ms_commentaire = p_commentaire,
        updated_by = p_user_email
    WHERE id = p_commande_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_commande_workflow
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        resultat = CASE WHEN p_approve THEN 'approuve' ELSE 'refuse' END,
        commentaire = p_commentaire,
        responsable_email = p_user_email
    WHERE commande_id = p_commande_id AND numero_etape = 4;
    
    -- Si approuvé, passer à l'étape suivante
    IF p_approve THEN
        UPDATE gestion_commande_workflow
        SET 
            statut_etape = 'en_cours',
            date_debut = NOW()
        WHERE commande_id = p_commande_id AND numero_etape = 5;
        
        -- Mettre le statut en cours
        UPDATE gestion_commandes
        SET statut = 'en_cours'
        WHERE id = p_commande_id;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTIONS DE SUIVI
-- =====================================================

-- Fonction pour marquer une commande comme expédiée
CREATE OR REPLACE FUNCTION mark_command_shipped(
    p_commande_id UUID,
    p_user_email TEXT,
    p_date_expedition DATE DEFAULT CURRENT_DATE,
    p_observations TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Mettre à jour la commande
    UPDATE gestion_commandes
    SET 
        statut = 'expedie',
        etape_actuelle = 8,
        updated_by = p_user_email
    WHERE id = p_commande_id AND statut = 'en_cours';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Commande non trouvée ou pas dans le statut approprié';
    END IF;
    
    -- Mettre à jour le workflow
    UPDATE gestion_commande_workflow
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        resultat = 'approuve',
        commentaire = p_observations,
        responsable_email = p_user_email
    WHERE commande_id = p_commande_id AND numero_etape = 7;
    
    -- Passer à l'étape livraison
    UPDATE gestion_commande_workflow
    SET 
        statut_etape = 'en_cours',
        date_debut = NOW()
    WHERE commande_id = p_commande_id AND numero_etape = 8;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour marquer une commande comme livrée
CREATE OR REPLACE FUNCTION mark_command_delivered(
    p_commande_id UUID,
    p_user_email TEXT,
    p_date_livraison DATE DEFAULT CURRENT_DATE,
    p_responsable_reception TEXT DEFAULT NULL,
    p_observations TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Mettre à jour la commande
    UPDATE gestion_commandes
    SET 
        statut = 'livre_complet',
        etape_actuelle = 9,
        date_livraison_reelle = p_date_livraison,
        responsable_reception = COALESCE(p_responsable_reception, p_user_email),
        observations_reception = p_observations,
        updated_by = p_user_email
    WHERE id = p_commande_id AND statut = 'expedie';
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Commande non trouvée ou pas dans le statut approprié';
    END IF;
    
    -- Mettre à jour le workflow
    UPDATE gestion_commande_workflow
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        resultat = 'approuve',
        commentaire = p_observations,
        responsable_email = p_user_email
    WHERE commande_id = p_commande_id AND numero_etape = 8;
    
    -- Passer à l'étape contrôle qualité
    UPDATE gestion_commande_workflow
    SET 
        statut_etape = 'en_cours',
        date_debut = NOW()
    WHERE commande_id = p_commande_id AND numero_etape = 9;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTIONS DE STATISTIQUES
-- =====================================================

-- Fonction pour obtenir les statistiques des commandes
CREATE OR REPLACE FUNCTION get_commands_statistics(
    p_date_debut DATE DEFAULT NULL,
    p_date_fin DATE DEFAULT NULL,
    p_type_commande TEXT DEFAULT NULL
)
RETURNS TABLE(
    total_commandes BIGINT,
    commandes_en_cours BIGINT,
    commandes_livrees BIGINT,
    commandes_en_retard BIGINT,
    montant_total_ht DECIMAL,
    montant_total_ttc DECIMAL,
    delai_moyen_livraison DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_commandes,
        COUNT(*) FILTER (WHERE statut IN ('soumise', 'validee_chef', 'validee_dg', 'approuvee_ms', 'en_cours', 'expedie')) as commandes_en_cours,
        COUNT(*) FILTER (WHERE statut IN ('livre_complet', 'cloture')) as commandes_livrees,
        COUNT(*) FILTER (WHERE date_livraison_prevue < CURRENT_DATE AND statut NOT IN ('livre_complet', 'cloture', 'annule')) as commandes_en_retard,
        COALESCE(SUM(montant_ht), 0) as montant_total_ht,
        COALESCE(SUM(montant_ttc), 0) as montant_total_ttc,
        COALESCE(AVG(EXTRACT(DAYS FROM (date_livraison_reelle - date_commande))), 0) as delai_moyen_livraison
    FROM gestion_commandes
    WHERE deleted_at IS NULL
        AND (p_date_debut IS NULL OR date_commande >= p_date_debut)
        AND (p_date_fin IS NULL OR date_commande <= p_date_fin)
        AND (p_type_commande IS NULL OR type_commande = p_type_commande);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTIONS DE NOTIFICATION
-- =====================================================

-- Fonction pour créer une notification de commande
CREATE OR REPLACE FUNCTION create_command_notification(
    p_user_email TEXT,
    p_title TEXT,
    p_message TEXT,
    p_commande_id UUID,
    p_type TEXT DEFAULT 'info'
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    -- Insérer dans la table notifications si elle existe
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
        INSERT INTO notifications (
            user_email, title, message, type, 
            related_table, related_id, created_at
        ) VALUES (
            p_user_email, p_title, p_message, p_type,
            'gestion_commandes', p_commande_id, NOW()
        ) RETURNING id INTO notification_id;
    END IF;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- FONCTIONS DE VALIDATION
-- =====================================================

-- Fonction pour valider l'intégrité d'une commande
CREATE OR REPLACE FUNCTION validate_command_integrity(p_commande_id UUID)
RETURNS TABLE(
    is_valid BOOLEAN,
    errors TEXT[]
) AS $$
DECLARE
    command_record RECORD;
    error_list TEXT[] := '{}';
    items_count INTEGER;
    total_ht DECIMAL;
BEGIN
    -- Récupérer la commande
    SELECT * INTO command_record
    FROM gestion_commandes
    WHERE id = p_commande_id;
    
    IF NOT FOUND THEN
        error_list := array_append(error_list, 'Commande non trouvée');
        RETURN QUERY SELECT FALSE, error_list;
        RETURN;
    END IF;
    
    -- Vérifier qu'il y a des items
    SELECT COUNT(*) INTO items_count
    FROM gestion_commande_items
    WHERE commande_id = p_commande_id;
    
    IF items_count = 0 THEN
        error_list := array_append(error_list, 'Aucun item dans la commande');
    END IF;
    
    -- Vérifier la cohérence des montants
    SELECT SUM(prix_total_ht) INTO total_ht
    FROM gestion_commande_items
    WHERE commande_id = p_commande_id;
    
    IF ABS(COALESCE(total_ht, 0) - command_record.montant_ht) > 0.01 THEN
        error_list := array_append(error_list, 'Incohérence dans les montants');
    END IF;
    
    -- Vérifier les dates
    IF command_record.date_livraison_prevue IS NOT NULL AND command_record.date_livraison_prevue < command_record.date_commande THEN
        error_list := array_append(error_list, 'Date de livraison prévue antérieure à la date de commande');
    END IF;
    
    RETURN QUERY SELECT (array_length(error_list, 1) IS NULL), error_list;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- PERMISSIONS ET ACCÈS
-- =====================================================

-- Accorder les permissions d'exécution aux utilisateurs authentifiés
GRANT EXECUTE ON FUNCTION generate_command_number() TO authenticated;
GRANT EXECUTE ON FUNCTION initialize_command_workflow(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_command_by_chef(UUID, TEXT, TEXT, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_command_by_dg(UUID, TEXT, TEXT, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION approve_command_by_ms(UUID, TEXT, TEXT, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_command_shipped(UUID, TEXT, DATE, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_command_delivered(UUID, TEXT, DATE, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_commands_statistics(DATE, DATE, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_command_notification(TEXT, TEXT, TEXT, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION validate_command_integrity(UUID) TO authenticated;
