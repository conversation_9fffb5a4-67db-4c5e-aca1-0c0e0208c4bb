<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord Supabase - Gestion Interne</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #666;
            font-size: 16px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #6c757d;
            transition: transform 0.2s;
        }

        .metric-card:hover {
            transform: translateY(-2px);
        }

        .metric-card.connected {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }

        .metric-card.disconnected {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        }

        .metric-card.warning {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #666;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .status-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .status-panel h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-indicator.connected {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .status-indicator.disconnected {
            background: #dc3545;
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
        }

        .status-indicator.warning {
            background: #ffc107;
            box-shadow: 0 0 10px rgba(255, 193, 7, 0.5);
        }

        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success { color: #68d391; }
        .log-entry.error { color: #fc8181; }
        .log-entry.warning { color: #f6e05e; }
        .log-entry.info { color: #63b3ed; }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.secondary {
            background: linear-gradient(135deg, #6c757d, #495057);
        }

        .btn.danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .btn.success {
            background: linear-gradient(135deg, #28a745, #218838);
        }

        .sync-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .sync-progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .sync-progress-bar {
            background: linear-gradient(135deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .conflicts-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .conflict-item {
            background: white;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #ffc107;
        }

        .conflict-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .conflict-actions .btn {
            padding: 5px 15px;
            font-size: 12px;
        }

        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .table-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #dee2e6;
        }

        .table-card h4 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .table-stat {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }

        .table-stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #333;
        }

        .table-stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }

        @media (max-width: 768px) {
            .status-section {
                grid-template-columns: 1fr;
            }

            .controls {
                justify-content: center;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>📊 Tableau de Bord Supabase</h1>
            <p class="subtitle">Monitoring et gestion de la base de données en temps réel</p>
        </div>

        <!-- Métriques principales -->
        <div class="metrics-grid">
            <div class="metric-card" id="connectionCard">
                <div class="metric-value" id="connectionStatus">🔌</div>
                <div class="metric-label">Statut Connexion</div>
            </div>

            <div class="metric-card">
                <div class="metric-value" id="uptimeValue">--</div>
                <div class="metric-label">Temps de fonctionnement</div>
            </div>

            <div class="metric-card">
                <div class="metric-value" id="successRateValue">--%</div>
                <div class="metric-label">Taux de succès</div>
            </div>

            <div class="metric-card">
                <div class="metric-value" id="responseTimeValue">--ms</div>
                <div class="metric-label">Temps de réponse</div>
            </div>

            <div class="metric-card">
                <div class="metric-value" id="syncCountValue">--</div>
                <div class="metric-label">Éléments synchronisés</div>
            </div>

            <div class="metric-card" id="errorCard">
                <div class="metric-value" id="errorCountValue">--</div>
                <div class="metric-label">Erreurs</div>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="controls">
            <button class="btn" onclick="initializeSupabase()">🔄 Initialiser</button>
            <button class="btn" onclick="forceHealthCheck()">🔍 Vérifier Santé</button>
            <button class="btn" onclick="forceSync()">🔄 Synchroniser</button>
            <button class="btn secondary" onclick="resetMetrics()">📊 Reset Métriques</button>
            <button class="btn secondary" onclick="exportLogs()">📋 Exporter Logs</button>
            <button class="btn danger" onclick="clearLogs()">🗑️ Vider Logs</button>
        </div>

        <!-- Section de synchronisation -->
        <div class="sync-section">
            <h3>🔄 Synchronisation</h3>
            <div class="sync-progress">
                <div class="sync-progress-bar" id="syncProgressBar" style="width: 0%"></div>
            </div>
            <div id="syncStatus">En attente...</div>
            <div class="controls" style="margin-top: 15px;">
                <button class="btn success" onclick="startAutoSync()">▶️ Démarrer Auto-Sync</button>
                <button class="btn danger" onclick="stopAutoSync()">⏹️ Arrêter Auto-Sync</button>
            </div>
        </div>

        <!-- Section des conflits -->
        <div class="conflicts-section" id="conflictsSection" style="display: none;">
            <h3>⚠️ Conflits de Synchronisation</h3>
            <div id="conflictsList"></div>
        </div>

        <!-- Statut et logs -->
        <div class="status-section">
            <div class="status-panel">
                <h3>
                    <span class="status-indicator" id="realtimeIndicator"></span>
                    Temps Réel
                </h3>
                <div id="realtimeStatus">Initialisation...</div>
                <div class="log-container" id="realtimeLog"></div>
            </div>

            <div class="status-panel">
                <h3>
                    <span class="status-indicator" id="syncIndicator"></span>
                    Synchronisation
                </h3>
                <div id="syncStatusDetail">Initialisation...</div>
                <div class="log-container" id="syncLog"></div>
            </div>
        </div>

        <!-- Statistiques des tables -->
        <div class="tables-grid" id="tablesGrid">
            <!-- Les cartes de tables seront générées dynamiquement -->
        </div>

        <!-- Logs généraux -->
        <div class="status-panel">
            <h3>📋 Logs Système</h3>
            <div class="controls">
                <select id="logLevel">
                    <option value="all">Tous les logs</option>
                    <option value="error">Erreurs seulement</option>
                    <option value="warning">Avertissements</option>
                    <option value="info">Informations</option>
                    <option value="success">Succès</option>
                </select>
                <button class="btn secondary" onclick="filterLogs()">🔍 Filtrer</button>
            </div>
            <div class="log-container" id="systemLog"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auto-init.js"></script>
    <script src="assets/js/supabase-monitor.js"></script>
    <script src="assets/js/supabase-sync.js"></script>

    <script>
        // Variables globales du tableau de bord
        let autoInit = null;
        let monitor = null;
        let syncManager = null;
        let supabaseClient = null;
        let dashboardInterval = null;

        // Logs du système
        let systemLogs = [];
        let maxLogs = 1000;

        // Initialisation du tableau de bord
        document.addEventListener('DOMContentLoaded', async function() {
            log('🚀 Initialisation du tableau de bord Supabase...', 'info');

            try {
                // Démarrer le tableau de bord
                await initializeDashboard();

                // Démarrer les mises à jour périodiques
                startDashboardUpdates();

                log('✅ Tableau de bord initialisé avec succès', 'success');

            } catch (error) {
                log(`❌ Erreur initialisation tableau de bord: ${error.message}`, 'error');
            }
        });

        // Initialiser le tableau de bord
        async function initializeDashboard() {
            // Initialiser Supabase automatiquement
            if (typeof SupabaseAutoInit !== 'undefined' && SUPABASE_CONFIG) {
                autoInit = new SupabaseAutoInit(SUPABASE_CONFIG);

                const initResult = await autoInit.initialize();
                if (initResult.success) {
                    supabaseClient = autoInit.client;
                    log('✅ Auto-initialisation Supabase réussie', 'success');
                } else {
                    log(`⚠️ Auto-initialisation échouée: ${initResult.error}`, 'warning');
                }
            }

            // Initialiser le monitoring
            if (typeof SupabaseMonitor !== 'undefined' && supabaseClient) {
                monitor = new SupabaseMonitor(SUPABASE_CONFIG);

                // Configurer les callbacks
                monitor.on('onConnect', () => {
                    log('🔗 Connexion Supabase établie', 'success');
                    updateConnectionStatus('connected');
                });

                monitor.on('onDisconnect', () => {
                    log('🔌 Connexion Supabase perdue', 'error');
                    updateConnectionStatus('disconnected');
                });

                monitor.on('onError', (error) => {
                    log(`❌ Erreur Supabase: ${error.message || error}`, 'error');
                });

                monitor.on('onReconnect', () => {
                    log('🔄 Reconnexion Supabase réussie', 'success');
                });

                await monitor.startMonitoring(supabaseClient);
            }

            // Initialiser la synchronisation
            if (typeof SupabaseSync !== 'undefined' && supabaseClient) {
                syncManager = new SupabaseSync(SUPABASE_CONFIG, supabaseClient);

                // Configurer les callbacks de synchronisation
                syncManager.on('onSync', (results) => {
                    log(`🔄 Synchronisation terminée: ${JSON.stringify(results)}`, 'info');
                    updateSyncStatus(results);
                });

                syncManager.on('onConflict', (conflict) => {
                    log(`⚠️ Conflit détecté: ${conflict.table}/${conflict.id}`, 'warning');
                    showConflict(conflict);
                });

                syncManager.on('onRealtimeUpdate', (update) => {
                    log(`🔄 Mise à jour temps réel: ${update.table}/${update.eventType}`, 'info');
                });
            }

            // Générer les cartes de tables
            generateTableCards();
        }

        // Démarrer les mises à jour périodiques du tableau de bord
        function startDashboardUpdates() {
            dashboardInterval = setInterval(() => {
                updateMetrics();
                updateTableStats();
            }, 5000); // Mise à jour toutes les 5 secondes
        }

        // Arrêter les mises à jour du tableau de bord
        function stopDashboardUpdates() {
            if (dashboardInterval) {
                clearInterval(dashboardInterval);
                dashboardInterval = null;
            }
        }

        // Mettre à jour les métriques
        function updateMetrics() {
            if (monitor) {
                const metrics = monitor.getMetrics();

                // Temps de fonctionnement
                const uptimeSeconds = Math.floor(metrics.uptime / 1000);
                const uptimeFormatted = formatUptime(uptimeSeconds);
                document.getElementById('uptimeValue').textContent = uptimeFormatted;

                // Taux de succès
                const successRate = Math.round(metrics.successRate || 0);
                document.getElementById('successRateValue').textContent = successRate + '%';

                // Temps de réponse
                const responseTime = Math.round(metrics.averageResponseTime || 0);
                document.getElementById('responseTimeValue').textContent = responseTime + 'ms';

                // Nombre d'erreurs
                document.getElementById('errorCountValue').textContent = metrics.errorCount;

                // Mettre à jour la couleur de la carte d'erreurs
                const errorCard = document.getElementById('errorCard');
                if (metrics.errorCount > 5) {
                    errorCard.className = 'metric-card disconnected';
                } else if (metrics.errorCount > 0) {
                    errorCard.className = 'metric-card warning';
                } else {
                    errorCard.className = 'metric-card connected';
                }
            }

            if (syncManager) {
                const syncStats = syncManager.getStats();
                document.getElementById('syncCountValue').textContent = syncStats.totalSynced;
            }
        }

        // Mettre à jour le statut de connexion
        function updateConnectionStatus(status) {
            const connectionCard = document.getElementById('connectionCard');
            const connectionStatus = document.getElementById('connectionStatus');

            switch (status) {
                case 'connected':
                    connectionCard.className = 'metric-card connected';
                    connectionStatus.textContent = '🟢 Connecté';
                    break;
                case 'disconnected':
                    connectionCard.className = 'metric-card disconnected';
                    connectionStatus.textContent = '🔴 Déconnecté';
                    break;
                case 'warning':
                    connectionCard.className = 'metric-card warning';
                    connectionStatus.textContent = '🟡 Instable';
                    break;
                default:
                    connectionCard.className = 'metric-card';
                    connectionStatus.textContent = '🔌 Inconnu';
            }
        }

        // Mettre à jour le statut de synchronisation
        function updateSyncStatus(results) {
            const syncStatus = document.getElementById('syncStatus');
            const progressBar = document.getElementById('syncProgressBar');

            let totalItems = 0;
            let syncedItems = 0;

            Object.values(results).forEach(result => {
                if (result.synced !== undefined) {
                    syncedItems += result.synced;
                    totalItems += result.synced + (result.conflicts || 0);
                }
            });

            const progress = totalItems > 0 ? (syncedItems / totalItems) * 100 : 100;
            progressBar.style.width = progress + '%';

            syncStatus.textContent = `${syncedItems} éléments synchronisés sur ${totalItems}`;

            // Mettre à jour l'indicateur de synchronisation
            const syncIndicator = document.getElementById('syncIndicator');
            if (progress === 100) {
                syncIndicator.className = 'status-indicator connected';
            } else if (progress > 50) {
                syncIndicator.className = 'status-indicator warning';
            } else {
                syncIndicator.className = 'status-indicator disconnected';
            }
        }

        // Afficher un conflit
        function showConflict(conflict) {
            const conflictsSection = document.getElementById('conflictsSection');
            const conflictsList = document.getElementById('conflictsList');

            conflictsSection.style.display = 'block';

            const conflictDiv = document.createElement('div');
            conflictDiv.className = 'conflict-item';
            conflictDiv.innerHTML = `
                <h4>Conflit: ${conflict.table}/${conflict.id}</h4>
                <p><strong>Type:</strong> ${conflict.type}</p>
                <p><strong>Local:</strong> ${JSON.stringify(conflict.local).substring(0, 100)}...</p>
                <p><strong>Distant:</strong> ${JSON.stringify(conflict.remote).substring(0, 100)}...</p>
                <div class="conflict-actions">
                    <button class="btn success" onclick="resolveConflict('${conflict.id}', 'use_local')">
                        Utiliser Local
                    </button>
                    <button class="btn" onclick="resolveConflict('${conflict.id}', 'use_remote')">
                        Utiliser Distant
                    </button>
                    <button class="btn secondary" onclick="resolveConflict('${conflict.id}', 'merge')">
                        Fusionner
                    </button>
                </div>
            `;

            conflictsList.appendChild(conflictDiv);
        }

        // Résoudre un conflit
        async function resolveConflict(conflictId, resolution) {
            try {
                if (syncManager) {
                    await syncManager.resolveConflict(conflictId, resolution);
                    log(`✅ Conflit résolu: ${conflictId} (${resolution})`, 'success');

                    // Supprimer l'élément de conflit de l'interface
                    const conflictElement = event.target.closest('.conflict-item');
                    if (conflictElement) {
                        conflictElement.remove();
                    }

                    // Masquer la section si plus de conflits
                    const conflictsList = document.getElementById('conflictsList');
                    if (conflictsList.children.length === 0) {
                        document.getElementById('conflictsSection').style.display = 'none';
                    }
                }
            } catch (error) {
                log(`❌ Erreur résolution conflit: ${error.message}`, 'error');
            }
        }

        // Générer les cartes de tables
        function generateTableCards() {
            const tablesGrid = document.getElementById('tablesGrid');
            const tables = Object.keys(SUPABASE_CONFIG.tables);

            tables.forEach(table => {
                const tableCard = document.createElement('div');
                tableCard.className = 'table-card';
                tableCard.innerHTML = `
                    <h4>
                        ${table.charAt(0).toUpperCase() + table.slice(1)}
                        <span class="status-indicator" id="${table}Indicator"></span>
                    </h4>
                    <div class="table-stats">
                        <div class="table-stat">
                            <div class="table-stat-value" id="${table}LocalCount">--</div>
                            <div class="table-stat-label">Local</div>
                        </div>
                        <div class="table-stat">
                            <div class="table-stat-value" id="${table}RemoteCount">--</div>
                            <div class="table-stat-label">Distant</div>
                        </div>
                    </div>
                    <div class="controls">
                        <button class="btn" onclick="syncTable('${table}')">🔄 Sync</button>
                        <button class="btn secondary" onclick="viewTableData('${table}')">👁️ Voir</button>
                    </div>
                `;

                tablesGrid.appendChild(tableCard);
            });
        }

        // Mettre à jour les statistiques des tables
        async function updateTableStats() {
            if (!supabaseClient) return;

            const tables = Object.keys(SUPABASE_CONFIG.tables);

            for (const table of tables) {
                try {
                    // Compter les éléments locaux
                    const localData = JSON.parse(localStorage.getItem(`gestion_${table}`) || '[]');
                    document.getElementById(`${table}LocalCount`).textContent = localData.length;

                    // Compter les éléments distants
                    const { count, error } = await supabaseClient
                        .from(SUPABASE_CONFIG.tables[table])
                        .select('*', { count: 'exact', head: true });

                    if (!error) {
                        document.getElementById(`${table}RemoteCount`).textContent = count || 0;
                        document.getElementById(`${table}Indicator`).className = 'status-indicator connected';
                    } else {
                        document.getElementById(`${table}RemoteCount`).textContent = 'Err';
                        document.getElementById(`${table}Indicator`).className = 'status-indicator disconnected';
                    }

                } catch (error) {
                    console.warn(`Erreur stats table ${table}:`, error);
                    document.getElementById(`${table}Indicator`).className = 'status-indicator disconnected';
                }
            }
        }

        // Fonctions de contrôle
        async function initializeSupabase() {
            log('🔄 Réinitialisation de Supabase...', 'info');
            try {
                if (autoInit) {
                    const result = await autoInit.initialize();
                    if (result.success) {
                        log('✅ Réinitialisation réussie', 'success');
                    } else {
                        log(`❌ Réinitialisation échouée: ${result.error}`, 'error');
                    }
                }
            } catch (error) {
                log(`❌ Erreur réinitialisation: ${error.message}`, 'error');
            }
        }

        async function forceHealthCheck() {
            log('🔍 Vérification de santé forcée...', 'info');
            try {
                if (monitor) {
                    const report = await monitor.forceHealthCheck();
                    log(`✅ Vérification terminée: ${report.status}`, 'success');
                }
            } catch (error) {
                log(`❌ Erreur vérification: ${error.message}`, 'error');
            }
        }

        async function forceSync() {
            log('🔄 Synchronisation forcée...', 'info');
            try {
                if (syncManager) {
                    const result = await syncManager.forceSync();
                    log('✅ Synchronisation forcée terminée', 'success');
                }
            } catch (error) {
                log(`❌ Erreur synchronisation: ${error.message}`, 'error');
            }
        }

        async function startAutoSync() {
            log('▶️ Démarrage de la synchronisation automatique...', 'info');
            try {
                if (syncManager) {
                    await syncManager.startSync();
                    log('✅ Synchronisation automatique démarrée', 'success');
                }
            } catch (error) {
                log(`❌ Erreur démarrage sync: ${error.message}`, 'error');
            }
        }

        function stopAutoSync() {
            log('⏹️ Arrêt de la synchronisation automatique...', 'info');
            if (syncManager) {
                syncManager.stopSync();
                log('✅ Synchronisation automatique arrêtée', 'success');
            }
        }

        async function syncTable(table) {
            log(`🔄 Synchronisation de la table ${table}...`, 'info');
            try {
                if (syncManager) {
                    const result = await syncManager.syncTable(table);
                    log(`✅ Table ${table} synchronisée: ${result.synced} éléments`, 'success');
                }
            } catch (error) {
                log(`❌ Erreur sync table ${table}: ${error.message}`, 'error');
            }
        }

        function viewTableData(table) {
            log(`👁️ Affichage des données de la table ${table}`, 'info');
            // Ouvrir une nouvelle fenêtre ou modal avec les données de la table
            // Pour l'instant, on log les données
            const localData = JSON.parse(localStorage.getItem(`gestion_${table}`) || '[]');
            console.log(`Données locales ${table}:`, localData);
        }

        function resetMetrics() {
            log('📊 Réinitialisation des métriques...', 'info');
            if (monitor) {
                monitor.resetMetrics();
                log('✅ Métriques réinitialisées', 'success');
            }
        }

        function exportLogs() {
            log('📋 Export des logs...', 'info');
            const logsData = {
                timestamp: new Date().toISOString(),
                systemLogs: systemLogs,
                metrics: monitor ? monitor.getMetrics() : null,
                syncStats: syncManager ? syncManager.getStats() : null
            };

            const blob = new Blob([JSON.stringify(logsData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `supabase-logs-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            log('✅ Logs exportés', 'success');
        }

        function clearLogs() {
            systemLogs = [];
            document.getElementById('systemLog').innerHTML = '';
            document.getElementById('realtimeLog').innerHTML = '';
            document.getElementById('syncLog').innerHTML = '';
            log('🗑️ Logs vidés', 'info');
        }

        function filterLogs() {
            const level = document.getElementById('logLevel').value;
            const systemLog = document.getElementById('systemLog');

            systemLog.innerHTML = '';

            const filteredLogs = level === 'all' ? systemLogs : systemLogs.filter(log => log.level === level);

            filteredLogs.forEach(logEntry => {
                const logDiv = document.createElement('div');
                logDiv.className = `log-entry ${logEntry.level}`;
                logDiv.textContent = `[${logEntry.timestamp}] ${logEntry.message}`;
                systemLog.appendChild(logDiv);
            });

            systemLog.scrollTop = systemLog.scrollHeight;
        }

        // Fonction de logging
        function log(message, level = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                message,
                level,
                timestamp,
                fullTimestamp: new Date().toISOString()
            };

            systemLogs.unshift(logEntry);

            // Limiter le nombre de logs
            if (systemLogs.length > maxLogs) {
                systemLogs = systemLogs.slice(0, maxLogs);
            }

            // Afficher dans la console
            console.log(`[${level.toUpperCase()}] ${message}`);

            // Afficher dans l'interface si le niveau correspond au filtre
            const currentFilter = document.getElementById('logLevel').value;
            if (currentFilter === 'all' || currentFilter === level) {
                const systemLog = document.getElementById('systemLog');
                const logDiv = document.createElement('div');
                logDiv.className = `log-entry ${level}`;
                logDiv.textContent = `[${timestamp}] ${message}`;
                systemLog.insertBefore(logDiv, systemLog.firstChild);

                // Limiter l'affichage
                while (systemLog.children.length > 100) {
                    systemLog.removeChild(systemLog.lastChild);
                }
            }
        }

        // Formater le temps de fonctionnement
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        }

        // Nettoyage lors de la fermeture
        window.addEventListener('beforeunload', function() {
            stopDashboardUpdates();
            if (monitor) monitor.stopMonitoring();
            if (syncManager) syncManager.stopSync();
        });
    </script>
</body>
</html>