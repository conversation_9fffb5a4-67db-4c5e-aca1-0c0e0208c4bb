-- إعد<PERSON> قاعدة بيانات المصادقة Supabase
-- Supabase Authentication Database Setup

-- ===================================
-- 1. إنشاء جدول الملفات الشخصية للمستخدمين
-- ===================================

-- إنشاء جدول profiles لتخزين معلومات إضافية عن المستخدمين
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE,
    nom TEXT NOT NULL,
    prenom TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'chef_service', 'responsable_hygiene', 'admin')),
    service TEXT,
    avatar_url TEXT,
    phone TEXT,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- إضافة فهارس للأداء
CREATE INDEX IF NOT EXISTS profiles_username_idx ON public.profiles(username);
CREATE INDEX IF NOT EXISTS profiles_role_idx ON public.profiles(role);
CREATE INDEX IF NOT EXISTS profiles_service_idx ON public.profiles(service);
CREATE INDEX IF NOT EXISTS profiles_is_active_idx ON public.profiles(is_active);

-- ===================================
-- 2. إنشاء جدول جلسات المستخدمين
-- ===================================

-- جدول لتتبع جلسات المستخدمين النشطة
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_token TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    is_active BOOLEAN DEFAULT true,
    last_activity TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS user_sessions_user_id_idx ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS user_sessions_token_idx ON public.user_sessions(session_token);
CREATE INDEX IF NOT EXISTS user_sessions_active_idx ON public.user_sessions(is_active);
CREATE INDEX IF NOT EXISTS user_sessions_expires_idx ON public.user_sessions(expires_at);

-- ===================================
-- 3. إنشاء جدول سجل المصادقة
-- ===================================

-- جدول لتسجيل أحداث المصادقة
CREATE TABLE IF NOT EXISTS public.auth_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL CHECK (event_type IN ('login', 'logout', 'signup', 'password_reset', 'password_change', 'profile_update', 'failed_login')),
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT true,
    error_message TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS auth_logs_user_id_idx ON public.auth_logs(user_id);
CREATE INDEX IF NOT EXISTS auth_logs_event_type_idx ON public.auth_logs(event_type);
CREATE INDEX IF NOT EXISTS auth_logs_created_at_idx ON public.auth_logs(created_at);
CREATE INDEX IF NOT EXISTS auth_logs_success_idx ON public.auth_logs(success);

-- ===================================
-- 4. إنشاء جدول الأذونات والأدوار
-- ===================================

-- جدول الأذونات
CREATE TABLE IF NOT EXISTS public.permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    resource TEXT NOT NULL, -- مثل: messages, commands, pv
    action TEXT NOT NULL,   -- مثل: create, read, update, delete
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- جدول ربط الأدوار بالأذونات
CREATE TABLE IF NOT EXISTS public.role_permissions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    role TEXT NOT NULL,
    permission_id UUID REFERENCES public.permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(role, permission_id)
);

-- فهارس للأداء
CREATE INDEX IF NOT EXISTS role_permissions_role_idx ON public.role_permissions(role);
CREATE INDEX IF NOT EXISTS role_permissions_permission_idx ON public.role_permissions(permission_id);

-- ===================================
-- 5. إدراج الأذونات الأساسية
-- ===================================

-- أذونات الرسائل
INSERT INTO public.permissions (name, description, resource, action) VALUES
('messages.create', 'إنشاء رسائل جديدة', 'messages', 'create'),
('messages.read', 'قراءة الرسائل', 'messages', 'read'),
('messages.update', 'تعديل الرسائل', 'messages', 'update'),
('messages.delete', 'حذف الرسائل', 'messages', 'delete'),
('messages.read_all', 'قراءة جميع الرسائل', 'messages', 'read_all')
ON CONFLICT (name) DO NOTHING;

-- أذونات الطلبات
INSERT INTO public.permissions (name, description, resource, action) VALUES
('commands.create', 'إنشاء طلبات جديدة', 'commands', 'create'),
('commands.read', 'قراءة الطلبات', 'commands', 'read'),
('commands.update', 'تعديل الطلبات', 'commands', 'update'),
('commands.delete', 'حذف الطلبات', 'commands', 'delete'),
('commands.approve', 'الموافقة على الطلبات', 'commands', 'approve'),
('commands.read_all', 'قراءة جميع الطلبات', 'commands', 'read_all')
ON CONFLICT (name) DO NOTHING;

-- أذونات المحاضر
INSERT INTO public.permissions (name, description, resource, action) VALUES
('pv.create', 'إنشاء محاضر جديدة', 'pv', 'create'),
('pv.read', 'قراءة المحاضر', 'pv', 'read'),
('pv.update', 'تعديل المحاضر', 'pv', 'update'),
('pv.delete', 'حذف المحاضر', 'pv', 'delete'),
('pv.finalize', 'إنهاء المحاضر', 'pv', 'finalize'),
('pv.read_all', 'قراءة جميع المحاضر', 'pv', 'read_all')
ON CONFLICT (name) DO NOTHING;

-- أذونات إدارية
INSERT INTO public.permissions (name, description, resource, action) VALUES
('users.manage', 'إدارة المستخدمين', 'users', 'manage'),
('system.admin', 'إدارة النظام', 'system', 'admin'),
('reports.view', 'عرض التقارير', 'reports', 'view')
ON CONFLICT (name) DO NOTHING;

-- ===================================
-- 6. ربط الأدوار بالأذونات
-- ===================================

-- أذونات المستخدم العادي
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'user', id FROM public.permissions 
WHERE name IN ('messages.create', 'messages.read', 'messages.update', 'commands.create', 'commands.read', 'pv.read')
ON CONFLICT (role, permission_id) DO NOTHING;

-- أذونات رئيس القسم
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'chef_service', id FROM public.permissions 
WHERE name IN ('messages.create', 'messages.read', 'messages.update', 'messages.delete', 'commands.create', 'commands.read', 'commands.update', 'commands.approve', 'pv.create', 'pv.read', 'pv.update')
ON CONFLICT (role, permission_id) DO NOTHING;

-- أذونات مسؤول النظافة
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'responsable_hygiene', id FROM public.permissions 
WHERE name IN ('messages.create', 'messages.read', 'messages.update', 'commands.read', 'pv.create', 'pv.read', 'pv.update', 'pv.finalize')
ON CONFLICT (role, permission_id) DO NOTHING;

-- أذونات المدير
INSERT INTO public.role_permissions (role, permission_id)
SELECT 'admin', id FROM public.permissions
ON CONFLICT (role, permission_id) DO NOTHING;

-- ===================================
-- 7. إنشاء الدوال المساعدة
-- ===================================

-- دالة للحصول على ملف المستخدم الشخصي
CREATE OR REPLACE FUNCTION public.get_user_profile(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    email TEXT,
    username TEXT,
    nom TEXT,
    prenom TEXT,
    role TEXT,
    service TEXT,
    avatar_url TEXT,
    phone TEXT,
    is_active BOOLEAN,
    last_login TIMESTAMPTZ,
    created_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        u.email::TEXT,
        p.username,
        p.nom,
        p.prenom,
        p.role,
        p.service,
        p.avatar_url,
        p.phone,
        p.is_active,
        p.last_login,
        p.created_at
    FROM public.profiles p
    JOIN auth.users u ON u.id = p.id
    WHERE p.id = user_uuid;
END;
$$;

-- دالة للتحقق من الأذونات
CREATE OR REPLACE FUNCTION public.check_user_permission(user_uuid UUID, permission_name TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
    has_permission BOOLEAN := FALSE;
BEGIN
    -- الحصول على دور المستخدم
    SELECT role INTO user_role
    FROM public.profiles
    WHERE id = user_uuid;
    
    -- التحقق من وجود الإذن
    SELECT EXISTS(
        SELECT 1
        FROM public.role_permissions rp
        JOIN public.permissions p ON p.id = rp.permission_id
        WHERE rp.role = user_role AND p.name = permission_name
    ) INTO has_permission;
    
    RETURN has_permission;
END;
$$;

-- دالة لتسجيل أحداث المصادقة
CREATE OR REPLACE FUNCTION public.log_auth_event(
    user_uuid UUID,
    event_type TEXT,
    ip_address INET DEFAULT NULL,
    user_agent TEXT DEFAULT NULL,
    success BOOLEAN DEFAULT TRUE,
    error_message TEXT DEFAULT NULL,
    metadata JSONB DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO public.auth_logs (user_id, event_type, ip_address, user_agent, success, error_message, metadata)
    VALUES (user_uuid, event_type, ip_address, user_agent, success, error_message, metadata)
    RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$;

-- ===================================
-- 8. إنشاء المشغلات (Triggers)
-- ===================================

-- مشغل لإنشاء ملف شخصي عند إنشاء مستخدم جديد
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    INSERT INTO public.profiles (id, username, nom, prenom, role, service)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'nom', ''),
        COALESCE(NEW.raw_user_meta_data->>'prenom', ''),
        COALESCE(NEW.raw_user_meta_data->>'role', 'user'),
        COALESCE(NEW.raw_user_meta_data->>'service', '')
    );
    
    -- تسجيل حدث إنشاء الحساب
    PERFORM public.log_auth_event(NEW.id, 'signup', NULL, NULL, TRUE, NULL, NEW.raw_user_meta_data);
    
    RETURN NEW;
END;
$$;

-- ربط المشغل بجدول المستخدمين
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- مشغل لتحديث updated_at عند تعديل الملف الشخصي
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

DROP TRIGGER IF EXISTS on_profiles_updated ON public.profiles;
CREATE TRIGGER on_profiles_updated
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- ===================================
-- 9. إعداد سياسات الأمان (RLS)
-- ===================================

-- تفعيل RLS على الجداول
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.auth_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- سياسات جدول profiles
CREATE POLICY "Users can view their own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles" ON public.profiles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can update all profiles" ON public.profiles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات جدول user_sessions
CREATE POLICY "Users can view their own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all sessions" ON public.user_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات جدول auth_logs
CREATE POLICY "Users can view their own logs" ON public.auth_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all logs" ON public.auth_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- سياسات جداول الأذونات (قراءة فقط للجميع)
CREATE POLICY "Everyone can read permissions" ON public.permissions
    FOR SELECT USING (true);

CREATE POLICY "Everyone can read role permissions" ON public.role_permissions
    FOR SELECT USING (true);

-- ===================================
-- 10. إنشاء Views مفيدة
-- ===================================

-- عرض للمستخدمين مع أذوناتهم
CREATE OR REPLACE VIEW public.users_with_permissions AS
SELECT 
    p.id,
    p.username,
    p.nom,
    p.prenom,
    p.role,
    p.service,
    p.is_active,
    array_agg(perm.name) as permissions
FROM public.profiles p
LEFT JOIN public.role_permissions rp ON rp.role = p.role
LEFT JOIN public.permissions perm ON perm.id = rp.permission_id
GROUP BY p.id, p.username, p.nom, p.prenom, p.role, p.service, p.is_active;

-- عرض لإحصائيات المصادقة
CREATE OR REPLACE VIEW public.auth_statistics AS
SELECT 
    COUNT(*) as total_users,
    COUNT(*) FILTER (WHERE is_active = true) as active_users,
    COUNT(*) FILTER (WHERE role = 'admin') as admin_users,
    COUNT(*) FILTER (WHERE role = 'chef_service') as chef_service_users,
    COUNT(*) FILTER (WHERE role = 'responsable_hygiene') as responsable_hygiene_users,
    COUNT(*) FILTER (WHERE role = 'user') as regular_users,
    COUNT(*) FILTER (WHERE last_login > NOW() - INTERVAL '24 hours') as users_last_24h,
    COUNT(*) FILTER (WHERE created_at > NOW() - INTERVAL '7 days') as new_users_week
FROM public.profiles;

-- ===================================
-- 11. إنشاء فهارس إضافية للأداء
-- ===================================

-- فهارس مركبة للاستعلامات الشائعة
CREATE INDEX IF NOT EXISTS profiles_role_active_idx ON public.profiles(role, is_active);
CREATE INDEX IF NOT EXISTS auth_logs_user_event_idx ON public.auth_logs(user_id, event_type);
CREATE INDEX IF NOT EXISTS auth_logs_created_success_idx ON public.auth_logs(created_at, success);

-- ===================================
-- 12. تنظيف البيانات القديمة
-- ===================================

-- دالة لتنظيف الجلسات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION public.cleanup_expired_sessions()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.user_sessions 
    WHERE expires_at < NOW() OR (is_active = false AND created_at < NOW() - INTERVAL '7 days');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- دالة لتنظيف سجلات المصادقة القديمة (أكثر من 6 أشهر)
CREATE OR REPLACE FUNCTION public.cleanup_old_auth_logs()
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.auth_logs 
    WHERE created_at < NOW() - INTERVAL '6 months';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$;

-- ===================================
-- انتهاء إعداد قاعدة البيانات
-- ===================================

-- رسالة تأكيد
DO $$
BEGIN
    RAISE NOTICE 'تم إعداد قاعدة بيانات المصادقة Supabase بنجاح!';
    RAISE NOTICE 'الجداول المنشأة: profiles, user_sessions, auth_logs, permissions, role_permissions';
    RAISE NOTICE 'الدوال المنشأة: get_user_profile, check_user_permission, log_auth_event';
    RAISE NOTICE 'المشغلات المنشأة: handle_new_user, handle_updated_at';
    RAISE NOTICE 'سياسات الأمان (RLS) مفعلة على جميع الجداول';
END $$;
