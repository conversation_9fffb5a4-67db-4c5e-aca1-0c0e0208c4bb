<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test - Gestion Interne Entreprise</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #007bff;
        }
        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .status-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .accounts-section {
            background: #e9ecef;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
        }
        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .account-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .account-card:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .account-card.admin { border-left: 4px solid #dc3545; }
        .account-card.manager { border-left: 4px solid #28a745; }
        .account-card.finance { border-left: 4px solid #ffc107; }
        .account-card.user { border-left: 4px solid #007bff; }
        .account-card.depot { border-left: 4px solid #6f42c1; }
        .account-card.hygiene { border-left: 4px solid #fd7e14; }
        .account-card h4 {
            margin: 0 0 8px 0;
            color: #333;
        }
        .account-card p {
            margin: 4px 0;
            font-size: 14px;
            color: #666;
        }
        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
        .btn.success {
            background: #28a745;
        }
        .btn.success:hover {
            background: #1e7e34;
        }
        .features-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
        }
        .features-list ul {
            list-style: none;
            padding: 0;
        }
        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .features-list li:last-child {
            border-bottom: none;
        }
        .features-list li::before {
            content: "✅ ";
            margin-right: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            h1 {
                font-size: 2em;
            }
            .status-grid,
            .accounts-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test - Gestion Interne Entreprise</h1>
        
        <div class="status-grid">
            <div class="status-card success">
                <h3>✅ Application Prête</h3>
                <p>L'application est correctement configurée et prête à être utilisée.</p>
                <p><strong>Mode:</strong> <span id="connectionMode">Vérification...</span></p>
            </div>
            
            <div class="status-card" id="supabaseStatus">
                <h3>🔗 Statut Supabase</h3>
                <p id="supabaseText">Vérification de la connexion...</p>
            </div>
            
            <div class="status-card" id="librariesStatus">
                <h3>📚 Bibliothèques</h3>
                <p id="librariesText">Vérification des dépendances...</p>
            </div>
        </div>

        <div class="accounts-section">
            <h2>👥 Comptes de Test Disponibles</h2>
            <p>Cliquez sur un compte pour vous connecter automatiquement :</p>
            <p style="background: #e9ecef; padding: 10px; border-radius: 5px; margin: 10px 0;">
                <strong>📊 Total :</strong> Plus de 80 comptes utilisateurs disponibles<br>
                <strong>🏢 Répartition :</strong> 1 Admin, 2 Managers, 3 Finance, 17 Utilisateurs, 2 Hygiène, 55+ Dépôts
            </p>
            
            <div class="accounts-grid">
                <div class="account-card admin" onclick="loginAs('admin123', 'admin123*+')">
                    <h4>👑 Administrateur</h4>
                    <p><strong>Login:</strong> admin123</p>
                    <p><strong>Mot de passe:</strong> admin123*+</p>
                    <p><strong>Accès:</strong> Complet (toutes fonctionnalités)</p>
                </div>

                <div class="account-card manager" onclick="loginAs('rym', 'rym')">
                    <h4>👔 Manager - Rym</h4>
                    <p><strong>Login:</strong> rym</p>
                    <p><strong>Mot de passe:</strong> rym</p>
                    <p><strong>Accès:</strong> Commandes + Analyse</p>
                </div>

                <div class="account-card finance" onclick="loginAs('besma', 'besma')">
                    <h4>💰 Finance - Besma</h4>
                    <p><strong>Login:</strong> besma</p>
                    <p><strong>Mot de passe:</strong> besma</p>
                    <p><strong>Accès:</strong> Gestion commandes</p>
                </div>

                <div class="account-card user" onclick="loginAs('rsoltani', 'rsoltani')">
                    <h4>👤 Utilisateur - RSoltani</h4>
                    <p><strong>Login:</strong> rsoltani</p>
                    <p><strong>Mot de passe:</strong> rsoltani</p>
                    <p><strong>Accès:</strong> Messagerie + Convertisseur</p>
                </div>

                <div class="account-card depot" onclick="loginAs('10', '*********+')">
                    <h4>📦 Dépôt 10</h4>
                    <p><strong>Login:</strong> 10</p>
                    <p><strong>Mot de passe:</strong> *********+</p>
                    <p><strong>Accès:</strong> Gestion PV dépôts</p>
                </div>

                <div class="account-card hygiene" onclick="loginAs('yosra', 'yosra2025*+')">
                    <h4>🧼 Hygiène - Yosra</h4>
                    <p><strong>Login:</strong> yosra</p>
                    <p><strong>Mot de passe:</strong> yosra2025*+</p>
                    <p><strong>Accès:</strong> Consultation PV</p>
                </div>
            </div>
        </div>

        <div class="features-list">
            <h2>🚀 Fonctionnalités Testées</h2>
            <ul>
                <li>Système d'authentification avec 6 rôles différents (82 comptes)</li>
                <li>Messagerie interne avec partage de fichiers</li>
                <li>Gestion des commandes fournisseurs (workflow 3 étapes)</li>
                <li>Gestion des PV de dépôts avec 50 produits organisés par catégories</li>
                <li>Convertisseur de devises avec Dinar Tunisien (TND, EUR, USD, GBP, JPY)</li>
                <li>Analyse des messages avec recommandations</li>
                <li>Export PDF et Excel des données</li>
                <li>Interface responsive (mobile, tablette, desktop)</li>
                <li>Mode hors ligne avec localStorage</li>
                <li>Intégration Supabase avec fallback automatique</li>
                <li>Produits PV avec dates d'expiration et alertes visuelles</li>
                <li>Sélection par catégories dans les PV</li>
                <li>Taux de change en temps réel avec symboles de devises</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <button class="btn success" onclick="openApp()">
                🚀 Ouvrir l'Application Principale
            </button>
            <button class="btn" onclick="runTests()">
                🧪 Lancer les Tests Techniques
            </button>
        </div>

        <div id="testResults" style="margin-top: 30px;"></div>

        <div class="footer">
            <p><strong>Gestion Interne Entreprise v1.0.0</strong></p>
            <p>Application complète avec intégration Supabase</p>
            <p>Développée avec ❤️ en français</p>
        </div>
    </div>

    <script>
        // Vérifications au chargement
        document.addEventListener('DOMContentLoaded', function() {
            checkConnectionMode();
            checkSupabase();
            checkLibraries();
        });

        function checkConnectionMode() {
            const modeSpan = document.getElementById('connectionMode');
            if (typeof window.SUPABASE_CONFIG !== 'undefined') {
                modeSpan.textContent = window.SUPABASE_CONFIG.mode || 'Local';
            } else {
                modeSpan.textContent = 'Local (config non chargée)';
            }
        }

        function checkSupabase() {
            const statusCard = document.getElementById('supabaseStatus');
            const statusText = document.getElementById('supabaseText');
            
            if (typeof window.supabase !== 'undefined') {
                statusCard.className = 'status-card success';
                statusText.textContent = 'SDK Supabase chargé avec succès';
            } else {
                statusCard.className = 'status-card warning';
                statusText.textContent = 'SDK Supabase non disponible - Mode local activé';
            }
        }

        function checkLibraries() {
            const statusCard = document.getElementById('librariesStatus');
            const statusText = document.getElementById('librariesText');
            
            const libraries = [];
            if (typeof XLSX !== 'undefined') libraries.push('XLSX');
            if (typeof jsPDF !== 'undefined') libraries.push('jsPDF');
            
            if (libraries.length === 2) {
                statusCard.className = 'status-card success';
                statusText.textContent = `Toutes les bibliothèques chargées: ${libraries.join(', ')}`;
            } else {
                statusCard.className = 'status-card warning';
                statusText.textContent = `Bibliothèques manquantes. Chargées: ${libraries.join(', ') || 'Aucune'}`;
            }
        }

        function loginAs(username, password) {
            // Ouvrir l'application avec les identifiants pré-remplis
            const url = `index.html?user=${username}&pass=${password}`;
            window.open(url, '_blank');
        }

        function openApp() {
            window.open('index.html', '_blank');
        }

        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = `
                <div class="status-card">
                    <h3>🧪 Résultats des Tests</h3>
                    <div id="testDetails">Exécution des tests en cours...</div>
                </div>
            `;

            setTimeout(() => {
                const details = document.getElementById('testDetails');
                let results = '<ul>';
                
                // Test localStorage
                try {
                    localStorage.setItem('test', 'ok');
                    localStorage.removeItem('test');
                    results += '<li>✅ LocalStorage: Fonctionnel</li>';
                } catch (e) {
                    results += '<li>❌ LocalStorage: Erreur</li>';
                }
                
                // Test des bibliothèques
                results += `<li>${typeof XLSX !== 'undefined' ? '✅' : '❌'} XLSX.js: ${typeof XLSX !== 'undefined' ? 'Chargé' : 'Non disponible'}</li>`;
                results += `<li>${typeof jsPDF !== 'undefined' ? '✅' : '❌'} jsPDF: ${typeof jsPDF !== 'undefined' ? 'Chargé' : 'Non disponible'}</li>`;
                results += `<li>${typeof window.supabase !== 'undefined' ? '✅' : '⚠️'} Supabase: ${typeof window.supabase !== 'undefined' ? 'Chargé' : 'Mode local'}</li>`;
                
                // Test responsive
                const width = window.innerWidth;
                const device = width <= 480 ? 'Mobile' : width <= 768 ? 'Tablette' : 'Desktop';
                results += `<li>📱 Résolution: ${width}px (${device})</li>`;
                
                results += '</ul>';
                details.innerHTML = results;
            }, 1000);
        }
    </script>
</body>
</html>
