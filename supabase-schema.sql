-- Schema Supabase pour l'application de gestion interne IPT
-- Exécuter ce script dans l'éditeur SQL de Supabase

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS gestion_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    nom TEXT NOT NULL,
    prenom TEXT NOT NULL,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('admin', 'user', 'chef_service', 'responsable_hygiene')),
    service TEXT,
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des messages
CREATE TABLE IF NOT EXISTS gestion_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_username TEXT NOT NULL,
    sender_name TEXT NOT NULL,
    recipient TEXT NOT NULL,
    content TEXT NOT NULL,
    file_name TEXT,
    file_data TEXT, -- Base64 encoded file data
    file_type TEXT,
    is_read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des commandes fournisseurs
CREATE TABLE IF NOT EXISTS gestion_commands (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    numero_commande TEXT UNIQUE NOT NULL,
    fournisseur TEXT NOT NULL,
    date_commande DATE NOT NULL,
    statut TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut IN ('en_attente', 'soumise', 'validee', 'livree', 'annulee')),
    produits JSONB DEFAULT '[]'::jsonb, -- Array of products
    total_ht DECIMAL(10,2),
    total_ttc DECIMAL(10,2),
    observations TEXT,
    created_by TEXT NOT NULL,
    validated_by TEXT,
    validated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des procès-verbaux
CREATE TABLE IF NOT EXISTS gestion_pv (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    numero_pv TEXT UNIQUE NOT NULL,
    type_pv TEXT NOT NULL DEFAULT 'depot' CHECK (type_pv IN ('depot', 'controle_qualite', 'inventaire', 'reception')),
    depot TEXT NOT NULL,
    date_pv DATE NOT NULL,
    responsable TEXT NOT NULL,
    statut TEXT NOT NULL DEFAULT 'brouillon' CHECK (statut IN ('brouillon', 'en_cours', 'finalise', 'archive')),
    produits JSONB DEFAULT '[]'::jsonb, -- Array of products with quantities and status
    observations TEXT,
    created_by TEXT NOT NULL,
    validated_by TEXT,
    validated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des produits (pour référence)
CREATE TABLE IF NOT EXISTS gestion_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    nom TEXT NOT NULL,
    categorie TEXT NOT NULL,
    reference TEXT UNIQUE,
    fournisseur TEXT,
    prix_unitaire DECIMAL(10,2),
    unite TEXT DEFAULT 'unité',
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des sessions utilisateur (optionnel)
CREATE TABLE IF NOT EXISTS gestion_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES gestion_users(id) ON DELETE CASCADE,
    username TEXT NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour améliorer les performances
CREATE INDEX IF NOT EXISTS idx_gestion_users_username ON gestion_users(username);
CREATE INDEX IF NOT EXISTS idx_gestion_users_email ON gestion_users(email);
CREATE INDEX IF NOT EXISTS idx_gestion_users_role ON gestion_users(role);

CREATE INDEX IF NOT EXISTS idx_gestion_messages_sender ON gestion_messages(sender_username);
CREATE INDEX IF NOT EXISTS idx_gestion_messages_recipient ON gestion_messages(recipient);
CREATE INDEX IF NOT EXISTS idx_gestion_messages_created_at ON gestion_messages(created_at);

CREATE INDEX IF NOT EXISTS idx_gestion_commands_numero ON gestion_commands(numero_commande);
CREATE INDEX IF NOT EXISTS idx_gestion_commands_fournisseur ON gestion_commands(fournisseur);
CREATE INDEX IF NOT EXISTS idx_gestion_commands_statut ON gestion_commands(statut);
CREATE INDEX IF NOT EXISTS idx_gestion_commands_created_by ON gestion_commands(created_by);

CREATE INDEX IF NOT EXISTS idx_gestion_pv_numero ON gestion_pv(numero_pv);
CREATE INDEX IF NOT EXISTS idx_gestion_pv_type ON gestion_pv(type_pv);
CREATE INDEX IF NOT EXISTS idx_gestion_pv_depot ON gestion_pv(depot);
CREATE INDEX IF NOT EXISTS idx_gestion_pv_statut ON gestion_pv(statut);
CREATE INDEX IF NOT EXISTS idx_gestion_pv_created_by ON gestion_pv(created_by);

CREATE INDEX IF NOT EXISTS idx_gestion_products_nom ON gestion_products(nom);
CREATE INDEX IF NOT EXISTS idx_gestion_products_categorie ON gestion_products(categorie);
CREATE INDEX IF NOT EXISTS idx_gestion_products_reference ON gestion_products(reference);

CREATE INDEX IF NOT EXISTS idx_gestion_sessions_user_id ON gestion_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_gestion_sessions_token ON gestion_sessions(session_token);
CREATE INDEX IF NOT EXISTS idx_gestion_sessions_expires ON gestion_sessions(expires_at);

-- Fonction pour mettre à jour updated_at automatiquement
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour updated_at
DROP TRIGGER IF EXISTS update_gestion_users_updated_at ON gestion_users;
CREATE TRIGGER update_gestion_users_updated_at
    BEFORE UPDATE ON gestion_users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_gestion_messages_updated_at ON gestion_messages;
CREATE TRIGGER update_gestion_messages_updated_at
    BEFORE UPDATE ON gestion_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_gestion_commands_updated_at ON gestion_commands;
CREATE TRIGGER update_gestion_commands_updated_at
    BEFORE UPDATE ON gestion_commands
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_gestion_pv_updated_at ON gestion_pv;
CREATE TRIGGER update_gestion_pv_updated_at
    BEFORE UPDATE ON gestion_pv
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_gestion_products_updated_at ON gestion_products;
CREATE TRIGGER update_gestion_products_updated_at
    BEFORE UPDATE ON gestion_products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Vues utiles pour les rapports
CREATE OR REPLACE VIEW gestion_stats AS
SELECT 
    (SELECT COUNT(*) FROM gestion_users WHERE is_active = true) as total_users,
    (SELECT COUNT(*) FROM gestion_messages WHERE created_at >= CURRENT_DATE) as messages_today,
    (SELECT COUNT(*) FROM gestion_commands WHERE statut = 'en_attente') as commandes_en_attente,
    (SELECT COUNT(*) FROM gestion_pv WHERE statut = 'brouillon') as pv_brouillon,
    (SELECT COUNT(*) FROM gestion_products WHERE is_active = true) as total_products;

CREATE OR REPLACE VIEW gestion_activity AS
SELECT 
    'message' as type,
    sender_name as user_name,
    content as description,
    created_at
FROM gestion_messages
UNION ALL
SELECT 
    'commande' as type,
    created_by as user_name,
    'Commande ' || numero_commande || ' - ' || fournisseur as description,
    created_at
FROM gestion_commands
UNION ALL
SELECT 
    'pv' as type,
    created_by as user_name,
    'PV ' || numero_pv || ' - ' || depot as description,
    created_at
FROM gestion_pv
ORDER BY created_at DESC
LIMIT 50;

-- Politiques RLS (Row Level Security) - optionnelles
-- Décommenter si vous voulez activer la sécurité au niveau des lignes

-- ALTER TABLE gestion_users ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE gestion_messages ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE gestion_commands ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE gestion_pv ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE gestion_products ENABLE ROW LEVEL SECURITY;

-- Exemple de politique pour permettre la lecture à tous les utilisateurs authentifiés
-- CREATE POLICY "Lecture pour utilisateurs authentifiés" ON gestion_users
--     FOR SELECT USING (auth.role() = 'authenticated');

-- CREATE POLICY "Lecture messages pour utilisateurs authentifiés" ON gestion_messages
--     FOR SELECT USING (auth.role() = 'authenticated');

-- Commentaires sur les tables
COMMENT ON TABLE gestion_users IS 'Table des utilisateurs de l''application de gestion interne';
COMMENT ON TABLE gestion_messages IS 'Table des messages entre utilisateurs';
COMMENT ON TABLE gestion_commands IS 'Table des commandes fournisseurs';
COMMENT ON TABLE gestion_pv IS 'Table des procès-verbaux';
COMMENT ON TABLE gestion_products IS 'Table de référence des produits';
COMMENT ON TABLE gestion_sessions IS 'Table des sessions utilisateur actives';

COMMENT ON COLUMN gestion_commands.produits IS 'Array JSON des produits commandés avec quantités et prix';
COMMENT ON COLUMN gestion_pv.produits IS 'Array JSON des produits dans le PV avec quantités et statuts';
COMMENT ON COLUMN gestion_messages.file_data IS 'Données du fichier encodées en Base64';

-- Données de test (optionnelles)
-- Décommenter pour insérer des données de test

-- INSERT INTO gestion_users (username, email, nom, prenom, role, service) VALUES
-- ('admin', '<EMAIL>', 'Administrateur', 'Système', 'admin', 'Direction'),
-- ('moncef', '<EMAIL>', 'Moncef', 'Chef', 'chef_service', 'Service Principal'),
-- ('yosra', '<EMAIL>', 'Yosra', 'Responsable', 'responsable_hygiene', 'Hygiène'),
-- ('user1', '<EMAIL>', 'Utilisateur', 'Test', 'user', 'Laboratoire')
-- ON CONFLICT (username) DO NOTHING;

-- INSERT INTO gestion_products (nom, categorie, reference, fournisseur, prix_unitaire, unite) VALUES
-- ('Acide sulfurique 98%', 'Acides', 'AS-98-1L', 'Sigma-Aldrich', 45.50, 'litre'),
-- ('Hydroxyde de sodium', 'Bases', 'NaOH-500G', 'Merck', 25.30, 'gramme'),
-- ('Méthanol HPLC', 'Solvants', 'MeOH-1L', 'Fisher Scientific', 35.80, 'litre'),
-- ('Milieu LB Agar', 'Milieux', 'LB-500G', 'Difco', 55.20, 'gramme'),
-- ('Trypsine', 'Enzymes', 'TRP-100MG', 'Sigma', 125.00, 'milligramme')
-- ON CONFLICT (reference) DO NOTHING;
