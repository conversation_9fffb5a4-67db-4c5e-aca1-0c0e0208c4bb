-- =====================================================
-- SYSTÈME DE GESTION DES DONS - INSTITUT PASTEUR TUNIS
-- Schéma de base de données complet
-- =====================================================

-- Extension pour UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- TABLE PRINCIPALE DES DONS
-- =====================================================

CREATE TABLE gestion_donations_ipt (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Identification du don
    numero_don TEXT UNIQUE NOT NULL, -- Format: DON-YYYY-NNNN
    type_don TEXT NOT NULL CHECK (type_don IN ('article', 'equipement')),
    
    -- Informations du demandeur
    demandeur_nom TEXT NOT NULL,
    demandeur_prenom TEXT NOT NULL,
    demandeur_email TEXT NOT NULL,
    demandeur_service TEXT NOT NULL,
    demandeur_laboratoire TEXT,
    demandeur_telephone TEXT,
    
    -- Informations du don
    motif_don TEXT NOT NULL,
    lieu_affectation TEXT NOT NULL,
    description_don TEXT NOT NULL,
    specifications_techniques TEXT, -- Pour les équipements
    
    -- Informations du donateur/fournisseur
    donateur_nom TEXT NOT NULL,
    donateur_contact TEXT,
    donateur_adresse TEXT,
    
    -- Valeur et quantité
    valeur_estimee DECIMAL(12,2),
    devise TEXT DEFAULT 'TND',
    quantite INTEGER DEFAULT 1,
    unite TEXT DEFAULT 'unité',
    
    -- État et workflow
    statut TEXT NOT NULL DEFAULT 'brouillon' CHECK (statut IN (
        'brouillon', 'soumis', 'valide_appro', 'avis_technique', 
        'valide_dg', 'valide_sd_achats', 'soumis_ms', 'approuve_ms',
        'refuse_ms', 'en_livraison', 'receptionne', 'inventorie', 
        'affecte', 'archive', 'refuse', 'annule'
    )),
    etape_actuelle INTEGER DEFAULT 1,
    priorite TEXT DEFAULT 'normale' CHECK (priorite IN ('faible', 'normale', 'elevee', 'urgente')),
    
    -- Dates importantes
    date_demande DATE NOT NULL DEFAULT CURRENT_DATE,
    date_livraison_prevue DATE,
    date_livraison_reelle DATE,
    date_reception DATE,
    date_affectation DATE,
    
    -- Validation et approbation
    valide_appro_par TEXT,
    valide_appro_date TIMESTAMP WITH TIME ZONE,
    valide_appro_commentaires TEXT,
    
    avis_technique_par TEXT,
    avis_technique_date TIMESTAMP WITH TIME ZONE,
    avis_technique_commentaires TEXT,
    avis_technique_favorable BOOLEAN,
    
    valide_dg_par TEXT,
    valide_dg_date TIMESTAMP WITH TIME ZONE,
    valide_dg_commentaires TEXT,
    
    valide_sd_achats_par TEXT,
    valide_sd_achats_date TIMESTAMP WITH TIME ZONE,
    valide_sd_achats_commentaires TEXT,
    
    decision_ms TEXT, -- 'approuve', 'refuse', 'en_attente'
    decision_ms_date TIMESTAMP WITH TIME ZONE,
    decision_ms_commentaires TEXT,
    decision_ms_reference TEXT, -- Référence officielle MS
    
    -- Réception et inventaire
    receptionne_par TEXT,
    receptionne_date TIMESTAMP WITH TIME ZONE,
    numero_inventaire TEXT UNIQUE,
    rve_responsable TEXT,
    
    -- Affectation finale
    service_affecte TEXT,
    laboratoire_affecte TEXT,
    responsable_affectation TEXT,
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by TEXT,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Index et contraintes
    CONSTRAINT valid_dates CHECK (
        date_livraison_prevue IS NULL OR date_livraison_prevue >= date_demande
    ),
    CONSTRAINT valid_reception CHECK (
        date_reception IS NULL OR date_reception >= date_demande
    )
);

-- =====================================================
-- WORKFLOW ET ÉTAPES DE VALIDATION
-- =====================================================

CREATE TABLE gestion_donation_workflow (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    donation_id UUID NOT NULL REFERENCES gestion_donations_ipt(id) ON DELETE CASCADE,
    
    -- Étape du workflow
    numero_etape INTEGER NOT NULL,
    nom_etape TEXT NOT NULL,
    description_etape TEXT,
    
    -- Responsable de l'étape
    role_responsable TEXT NOT NULL,
    utilisateur_responsable TEXT,
    
    -- État de l'étape
    statut_etape TEXT NOT NULL DEFAULT 'en_attente' CHECK (statut_etape IN (
        'en_attente', 'en_cours', 'termine', 'refuse', 'annule'
    )),
    
    -- Validation
    date_debut TIMESTAMP WITH TIME ZONE,
    date_fin TIMESTAMP WITH TIME ZONE,
    duree_traitement INTERVAL,
    commentaires TEXT,
    pieces_jointes TEXT[], -- URLs des documents
    
    -- Métadonnées
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(donation_id, numero_etape)
);

-- =====================================================
-- DOCUMENTS ET PIÈCES JUSTIFICATIVES
-- =====================================================

CREATE TABLE gestion_donation_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    donation_id UUID NOT NULL REFERENCES gestion_donations_ipt(id) ON DELETE CASCADE,
    
    -- Type de document
    type_document TEXT NOT NULL CHECK (type_document IN (
        'lettre_don', 'facture', 'bon_livraison', 'bon_prelevement',
        'decision_ms', 'piece_signee', 'photo_reception', 'autre'
    )),
    
    -- Informations du fichier
    nom_fichier TEXT NOT NULL,
    nom_original TEXT NOT NULL,
    taille_fichier BIGINT,
    type_mime TEXT,
    url_stockage TEXT NOT NULL,
    
    -- Métadonnées
    description_document TEXT,
    etape_associee INTEGER,
    obligatoire BOOLEAN DEFAULT false,
    
    -- Signature numérique (pour certains documents)
    signe_numeriquement BOOLEAN DEFAULT false,
    signature_par TEXT,
    signature_date TIMESTAMP WITH TIME ZONE,
    
    -- Upload
    uploaded_by TEXT NOT NULL,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Validation du document
    valide BOOLEAN DEFAULT true,
    valide_par TEXT,
    valide_date TIMESTAMP WITH TIME ZONE,
    commentaires_validation TEXT
);

-- =====================================================
-- INVENTAIRE ET NUMÉROTATION
-- =====================================================

CREATE TABLE gestion_donation_inventaire (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    donation_id UUID NOT NULL REFERENCES gestion_donations_ipt(id) ON DELETE CASCADE,
    
    -- Numérotation
    numero_inventaire TEXT UNIQUE NOT NULL, -- Format: INV-YYYY-NNNN
    code_barres TEXT UNIQUE,
    
    -- Localisation
    service_actuel TEXT NOT NULL,
    laboratoire_actuel TEXT,
    emplacement_precis TEXT,
    
    -- État de l'équipement/article
    etat_physique TEXT DEFAULT 'excellent' CHECK (etat_physique IN (
        'excellent', 'bon', 'moyen', 'mauvais', 'hors_service'
    )),
    etat_fonctionnel TEXT DEFAULT 'fonctionnel' CHECK (etat_fonctionnel IN (
        'fonctionnel', 'en_panne', 'en_maintenance', 'non_teste'
    )),
    
    -- Responsabilité
    rve_responsable TEXT NOT NULL,
    responsable_service TEXT,
    
    -- Dates importantes
    date_inventaire DATE NOT NULL DEFAULT CURRENT_DATE,
    date_derniere_verification DATE,
    date_prochaine_verification DATE,
    
    -- Informations techniques (pour équipements)
    marque TEXT,
    modele TEXT,
    numero_serie TEXT,
    annee_fabrication INTEGER,
    specifications JSONB,
    
    -- Maintenance
    contrat_maintenance BOOLEAN DEFAULT false,
    fournisseur_maintenance TEXT,
    date_derniere_maintenance DATE,
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- MOUVEMENTS ET TRAÇABILITÉ
-- =====================================================

CREATE TABLE gestion_donation_mouvements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    donation_id UUID NOT NULL REFERENCES gestion_donations_ipt(id) ON DELETE CASCADE,
    inventaire_id UUID REFERENCES gestion_donation_inventaire(id),
    
    -- Type de mouvement
    type_mouvement TEXT NOT NULL CHECK (type_mouvement IN (
        'affectation_initiale', 'transfert_service', 'transfert_laboratoire',
        'maintenance', 'reparation', 'mise_au_rebut', 'pret_temporaire'
    )),
    
    -- Origine et destination
    service_origine TEXT,
    laboratoire_origine TEXT,
    service_destination TEXT,
    laboratoire_destination TEXT,
    
    -- Responsables
    demandeur TEXT NOT NULL,
    approbateur TEXT,
    executant TEXT,
    
    -- Détails du mouvement
    motif TEXT NOT NULL,
    date_mouvement DATE NOT NULL DEFAULT CURRENT_DATE,
    date_retour_prevue DATE, -- Pour les prêts
    date_retour_effective DATE,
    
    -- Documents associés
    bon_sortie_numero TEXT,
    bon_sortie_url TEXT,
    
    -- État
    statut_mouvement TEXT DEFAULT 'en_cours' CHECK (statut_mouvement IN (
        'demande', 'approuve', 'en_cours', 'termine', 'annule'
    )),
    
    -- Métadonnées
    created_by TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    commentaires TEXT
);

-- =====================================================
-- UTILISATEURS ET RÔLES IPT
-- =====================================================

CREATE TABLE gestion_users_ipt (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    
    -- Informations personnelles
    email TEXT UNIQUE NOT NULL,
    nom TEXT NOT NULL,
    prenom TEXT NOT NULL,
    telephone TEXT,
    
    -- Rôle et affectation
    role_ipt TEXT NOT NULL CHECK (role_ipt IN (
        'demandeur', 'responsable_approvisionnement', 'direction_technique',
        'directeur_general', 'sous_direction_achats', 'receptionniste',
        'rve', 'beneficiaire', 'admin_systeme'
    )),
    
    service TEXT NOT NULL,
    laboratoire TEXT,
    fonction TEXT,
    
    -- Permissions spéciales
    peut_valider_dons BOOLEAN DEFAULT false,
    peut_gerer_inventaire BOOLEAN DEFAULT false,
    peut_voir_tous_dons BOOLEAN DEFAULT false,
    peut_generer_rapports BOOLEAN DEFAULT false,
    
    -- État du compte
    is_active BOOLEAN DEFAULT true,
    date_creation DATE DEFAULT CURRENT_DATE,
    derniere_connexion TIMESTAMP WITH TIME ZONE,
    
    -- Notifications
    notifications_email BOOLEAN DEFAULT true,
    notifications_sms BOOLEAN DEFAULT false,
    
    -- Métadonnées
    created_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- HISTORIQUE DES ACTIONS
-- =====================================================

CREATE TABLE gestion_donation_historique (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    donation_id UUID REFERENCES gestion_donations_ipt(id) ON DELETE CASCADE,
    
    -- Action effectuée
    type_action TEXT NOT NULL CHECK (type_action IN (
        'creation', 'modification', 'validation', 'refus', 'soumission',
        'reception', 'affectation', 'mouvement', 'archivage'
    )),
    
    description_action TEXT NOT NULL,
    
    -- Utilisateur
    utilisateur_email TEXT NOT NULL,
    utilisateur_nom TEXT,
    utilisateur_role TEXT,
    
    -- Détails
    ancienne_valeur JSONB,
    nouvelle_valeur JSONB,
    commentaires TEXT,
    
    -- Métadonnées
    date_action TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    adresse_ip INET,
    user_agent TEXT
);

-- =====================================================
-- INDEX POUR OPTIMISATION
-- =====================================================

-- Index sur les colonnes fréquemment utilisées
CREATE INDEX idx_donations_statut ON gestion_donations_ipt(statut);
CREATE INDEX idx_donations_type ON gestion_donations_ipt(type_don);
CREATE INDEX idx_donations_demandeur ON gestion_donations_ipt(demandeur_email);
CREATE INDEX idx_donations_service ON gestion_donations_ipt(demandeur_service);
CREATE INDEX idx_donations_created_at ON gestion_donations_ipt(created_at);
CREATE INDEX idx_donations_numero ON gestion_donations_ipt(numero_don);

CREATE INDEX idx_workflow_donation ON gestion_donation_workflow(donation_id);
CREATE INDEX idx_workflow_etape ON gestion_donation_workflow(numero_etape);
CREATE INDEX idx_workflow_statut ON gestion_donation_workflow(statut_etape);

CREATE INDEX idx_documents_donation ON gestion_donation_documents(donation_id);
CREATE INDEX idx_documents_type ON gestion_donation_documents(type_document);

CREATE INDEX idx_inventaire_numero ON gestion_donation_inventaire(numero_inventaire);
CREATE INDEX idx_inventaire_service ON gestion_donation_inventaire(service_actuel);

CREATE INDEX idx_mouvements_donation ON gestion_donation_mouvements(donation_id);
CREATE INDEX idx_mouvements_type ON gestion_donation_mouvements(type_mouvement);
CREATE INDEX idx_mouvements_date ON gestion_donation_mouvements(date_mouvement);

CREATE INDEX idx_users_email ON gestion_users_ipt(email);
CREATE INDEX idx_users_role ON gestion_users_ipt(role_ipt);
CREATE INDEX idx_users_service ON gestion_users_ipt(service);

CREATE INDEX idx_historique_donation ON gestion_donation_historique(donation_id);
CREATE INDEX idx_historique_action ON gestion_donation_historique(type_action);
CREATE INDEX idx_historique_date ON gestion_donation_historique(date_action);

-- =====================================================
-- COMMENTAIRES SUR LES TABLES
-- =====================================================

COMMENT ON TABLE gestion_donations_ipt IS 'Table principale des dons - Institut Pasteur Tunis';
COMMENT ON TABLE gestion_donation_workflow IS 'Workflow et étapes de validation des dons';
COMMENT ON TABLE gestion_donation_documents IS 'Documents et pièces justificatives associés aux dons';
COMMENT ON TABLE gestion_donation_inventaire IS 'Inventaire et numérotation des dons reçus';
COMMENT ON TABLE gestion_donation_mouvements IS 'Traçabilité des mouvements et transferts';
COMMENT ON TABLE gestion_users_ipt IS 'Utilisateurs et rôles spécifiques IPT';
COMMENT ON TABLE gestion_donation_historique IS 'Historique complet des actions sur les dons';

-- =====================================================
-- TRIGGERS POUR MISE À JOUR AUTOMATIQUE
-- =====================================================

-- Trigger pour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Appliquer le trigger aux tables principales
CREATE TRIGGER update_donations_updated_at BEFORE UPDATE ON gestion_donations_ipt
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflow_updated_at BEFORE UPDATE ON gestion_donation_workflow
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventaire_updated_at BEFORE UPDATE ON gestion_donation_inventaire
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON gestion_users_ipt
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
