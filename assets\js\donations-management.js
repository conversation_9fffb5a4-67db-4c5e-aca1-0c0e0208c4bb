/**
 * Module de gestion des dons IPT
 * Intégré avec Supabase et le système d'authentification existant
 */

class DonationsManager {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.donations = [];
        this.currentDonation = null;
        this.itemCounter = 0;
    }

    /**
     * Initialiser le gestionnaire de dons
     */
    async initialize() {
        console.log('🎁 Initialisation du gestionnaire de dons...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                throw new Error('Client Supabase non disponible');
            }

            // Récupérer l'utilisateur actuel
            if (window.authSystem) {
                this.currentUser = await window.authSystem.getCurrentUser();
            }

            if (!this.currentUser) {
                throw new Error('Utilisateur non authentifié');
            }

            console.log('✅ Utilisateur:', this.currentUser.email);

            // Configurer les événements
            this.setupEventListeners();

            // Charger les données initiales
            await this.loadDonations();

            console.log('✅ Gestionnaire de dons initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation gestionnaire de dons:', error);
            throw error;
        }
    }

    /**
     * Configurer les écouteurs d'événements
     */
    setupEventListeners() {
        // Bouton nouveau don
        document.getElementById('btnNouveauDon')?.addEventListener('click', () => {
            this.ouvrirModalNouveauDon();
        });

        // Bouton statistiques
        document.getElementById('btnStatistiques')?.addEventListener('click', () => {
            this.toggleStatistiques();
        });

        // Bouton actualiser
        document.getElementById('btnRefresh')?.addEventListener('click', () => {
            this.loadDonations();
        });

        // Filtres
        document.getElementById('filterStatut')?.addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('filterType')?.addEventListener('change', () => {
            this.applyFilters();
        });

        document.getElementById('searchDon')?.addEventListener('input', () => {
            this.applyFilters();
        });

        // Formulaire nouveau don
        document.getElementById('formNouveauDon')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.creerNouveauDon();
        });

        // Bouton ajouter item
        document.getElementById('btnAjouterItem')?.addEventListener('click', () => {
            this.ajouterItem();
        });

        // Type de don change
        document.getElementById('typeDon')?.addEventListener('change', (e) => {
            this.onTypeDonChange(e.target.value);
        });

        // Formulaire actions
        document.getElementById('formActionDon')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.executerAction();
        });

        // Formulaire upload document
        document.getElementById('formUploadDoc')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.uploadDocument();
        });
    }

    /**
     * Charger la liste des dons
     */
    async loadDonations() {
        try {
            console.log('📥 Chargement des dons...');

            const { data, error } = await this.supabase
                .from('donations_complete')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) {
                throw error;
            }

            this.donations = data || [];
            console.log(`✅ ${this.donations.length} dons chargés`);

            this.renderDonations();
            await this.loadStatistics();

        } catch (error) {
            console.error('❌ Erreur chargement dons:', error);
            showNotification('Erreur lors du chargement des dons', 'error');
        }
    }

    /**
     * Afficher la liste des dons
     */
    renderDonations() {
        const container = document.getElementById('donationsList');
        if (!container) return;

        if (this.donations.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <h3>Aucun don trouvé</h3>
                    <p>Commencez par créer votre premier don.</p>
                    <button onclick="donationsManager.ouvrirModalNouveauDon()" class="btn btn-primary">
                        ➕ Créer un don
                    </button>
                </div>
            `;
            return;
        }

        container.innerHTML = this.donations.map(don => this.renderDonationCard(don)).join('');
    }

    /**
     * Générer le HTML d'une carte de don
     */
    renderDonationCard(don) {
        const statusClass = `status-${don.statut}`;
        const typeClass = `type-${don.type_don}`;
        
        return `
            <div class="donation-card" data-id="${don.id}">
                <div class="donation-header">
                    <div>
                        <h3>${don.numero_don}</h3>
                        <div class="donation-meta">
                            <span class="donation-type ${typeClass}">${don.type_don}</span>
                            <span class="donation-status ${statusClass}">${don.statut_libelle || don.statut}</span>
                        </div>
                    </div>
                    <div class="donation-value">
                        ${don.valeur_estimee ? `${don.valeur_estimee} ${don.devise}` : 'N/A'}
                    </div>
                </div>
                
                <div class="donation-info">
                    <div class="info-row">
                        <strong>Donneur:</strong> ${don.donneur_nom}
                    </div>
                    <div class="info-row">
                        <strong>Demandeur:</strong> ${don.demandeur_nom} (${don.demandeur_service})
                    </div>
                    <div class="info-row">
                        <strong>Date:</strong> ${this.formatDate(don.date_demande)}
                    </div>
                    <div class="info-row">
                        <strong>Items:</strong> ${don.nombre_items} | 
                        <strong>Documents:</strong> ${don.nombre_documents}
                    </div>
                    ${don.items_list ? `<div class="info-row"><strong>Contenu:</strong> ${don.items_list}</div>` : ''}
                </div>
                
                <div class="donation-actions">
                    <button onclick="donationsManager.voirDetails('${don.id}')" class="btn-action btn-primary">
                        👁️ Détails
                    </button>
                    ${this.renderActionButtons(don)}
                </div>
            </div>
        `;
    }

    /**
     * Générer les boutons d'action selon le statut et les droits
     */
    renderActionButtons(don) {
        const userRole = this.currentUser?.user_metadata?.role || 'user';
        const isOwner = don.created_by === this.currentUser?.email || don.demandeur_email === this.currentUser?.email;
        let buttons = [];

        // Actions selon le statut
        switch (don.statut) {
            case 'brouillon':
                if (isOwner) {
                    buttons.push(`<button onclick="donationsManager.modifierDon('${don.id}')" class="btn-action btn-warning">✏️ Modifier</button>`);
                    buttons.push(`<button onclick="donationsManager.soumettreDG('${don.id}')" class="btn-action btn-success">📤 Soumettre au DG</button>`);
                    buttons.push(`<button onclick="donationsManager.supprimerDon('${don.id}')" class="btn-action btn-danger">🗑️ Supprimer</button>`);
                }
                break;

            case 'soumis_dg':
                if (userRole === 'dg' || userRole === 'admin') {
                    buttons.push(`<button onclick="donationsManager.validerDG('${don.id}')" class="btn-action btn-success">✅ Valider</button>`);
                    buttons.push(`<button onclick="donationsManager.refuserDG('${don.id}')" class="btn-action btn-danger">❌ Refuser</button>`);
                }
                break;

            case 'avis_dt':
                if (userRole === 'dt' || userRole === 'admin') {
                    buttons.push(`<button onclick="donationsManager.donnerAvisDT('${don.id}')" class="btn-action btn-primary">📋 Donner avis</button>`);
                }
                break;

            case 'soumis_ms':
                if (userRole === 'ms' || userRole === 'admin') {
                    buttons.push(`<button onclick="donationsManager.autoriserMS('${don.id}')" class="btn-action btn-success">✅ Autoriser</button>`);
                    buttons.push(`<button onclick="donationsManager.refuserMS('${don.id}')" class="btn-action btn-danger">❌ Refuser</button>`);
                }
                break;

            case 'approuve_ms':
                if (userRole === 'receptionniste' || userRole === 'admin') {
                    buttons.push(`<button onclick="donationsManager.marquerRecu('${don.id}')" class="btn-action btn-primary">📦 Marquer reçu</button>`);
                }
                break;

            case 'recu':
                if (don.type_don === 'equipement' && (userRole === 'rve' || userRole === 'admin')) {
                    buttons.push(`<button onclick="donationsManager.gererInventaire('${don.id}')" class="btn-action btn-warning">🏷️ Inventaire</button>`);
                }
                break;
        }

        // Actions communes
        buttons.push(`<button onclick="donationsManager.ajouterDocument('${don.id}')" class="btn-action btn-secondary">📎 Document</button>`);

        return buttons.join('');
    }

    /**
     * Ouvrir le modal de nouveau don
     */
    ouvrirModalNouveauDon() {
        // Réinitialiser le formulaire
        document.getElementById('formNouveauDon').reset();
        document.getElementById('itemsList').innerHTML = '';
        this.itemCounter = 0;
        
        // Ajouter un premier item
        this.ajouterItem();
        
        ouvrirModal('modalNouveauDon');
    }

    /**
     * Ajouter un item au formulaire
     */
    ajouterItem() {
        const container = document.getElementById('itemsList');
        const typeDon = document.getElementById('typeDon').value;
        this.itemCounter++;

        const itemHtml = `
            <div class="item-row" data-item="${this.itemCounter}">
                <input type="text" placeholder="Nom de l'item *" name="item_nom_${this.itemCounter}" required>
                <input type="number" placeholder="Quantité" name="item_quantite_${this.itemCounter}" value="1" min="1">
                <input type="text" placeholder="Unité" name="item_unite_${this.itemCounter}" value="pièce">
                <button type="button" onclick="this.parentElement.remove()" class="btn-action btn-danger">🗑️</button>
                
                ${typeDon === 'equipement' ? `
                    <input type="text" placeholder="Marque" name="item_marque_${this.itemCounter}">
                    <input type="text" placeholder="Modèle" name="item_modele_${this.itemCounter}">
                    <input type="text" placeholder="N° Série" name="item_serie_${this.itemCounter}">
                ` : ''}
                
                <textarea placeholder="Description" name="item_description_${this.itemCounter}"></textarea>
            </div>
        `;

        container.insertAdjacentHTML('beforeend', itemHtml);
    }

    /**
     * Gérer le changement de type de don
     */
    onTypeDonChange(type) {
        // Réinitialiser les items avec le nouveau type
        document.getElementById('itemsList').innerHTML = '';
        this.itemCounter = 0;
        this.ajouterItem();
    }

    /**
     * Créer un nouveau don
     */
    async creerNouveauDon() {
        try {
            const formData = new FormData(document.getElementById('formNouveauDon'));
            
            // Construire l'objet don
            const donData = {
                type_don: formData.get('typeDon'),
                donneur_nom: formData.get('donneurNom'),
                donneur_contact: formData.get('donneurContact'),
                donneur_adresse: formData.get('donneurAdresse'),
                demandeur_nom: this.currentUser.user_metadata?.nom || this.currentUser.email,
                demandeur_service: formData.get('demandeurService'),
                demandeur_email: this.currentUser.email,
                lieu_affectation: formData.get('lieuAffectation'),
                raison_don: formData.get('raisonDon'),
                valeur_estimee: formData.get('valeurEstimee') || null,
                created_by: this.currentUser.email
            };

            // Générer le numéro de don
            const { data: numeroData, error: numeroError } = await this.supabase
                .rpc('generate_donation_number');

            if (numeroError) throw numeroError;
            donData.numero_don = numeroData;

            // Créer le don
            const { data: donation, error: donError } = await this.supabase
                .from('gestion_donations')
                .insert([donData])
                .select()
                .single();

            if (donError) throw donError;

            // Ajouter les items
            const items = this.extractItemsFromForm(formData, donation.id);
            if (items.length > 0) {
                const { error: itemsError } = await this.supabase
                    .from('gestion_donation_items')
                    .insert(items);

                if (itemsError) throw itemsError;
            }

            // Enregistrer dans le workflow
            await this.supabase.rpc('log_donation_workflow', {
                p_donation_id: donation.id,
                p_etape: 'creation',
                p_statut_precedent: null,
                p_statut_nouveau: 'brouillon',
                p_acteur_nom: this.currentUser.email,
                p_acteur_role: 'demandeur',
                p_action: 'Création du don',
                p_commentaire: 'Don créé par le demandeur'
            });

            showNotification('Don créé avec succès!', 'success');
            fermerModal('modalNouveauDon');
            await this.loadDonations();

        } catch (error) {
            console.error('❌ Erreur création don:', error);
            showNotification('Erreur lors de la création du don', 'error');
        }
    }

    /**
     * Extraire les items du formulaire
     */
    extractItemsFromForm(formData, donationId) {
        const items = [];
        
        for (let i = 1; i <= this.itemCounter; i++) {
            const nom = formData.get(`item_nom_${i}`);
            if (nom) {
                const item = {
                    donation_id: donationId,
                    nom_item: nom,
                    description: formData.get(`item_description_${i}`) || null,
                    quantite: parseInt(formData.get(`item_quantite_${i}`)) || 1,
                    unite: formData.get(`item_unite_${i}`) || 'pièce',
                    marque: formData.get(`item_marque_${i}`) || null,
                    modele: formData.get(`item_modele_${i}`) || null,
                    numero_serie: formData.get(`item_serie_${i}`) || null
                };
                items.push(item);
            }
        }
        
        return items;
    }

    /**
     * Formater une date
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString('fr-FR');
    }

    /**
     * Formater une date avec heure
     */
    formatDateTime(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString('fr-FR');
    }

    /**
     * Voir les détails d'un don
     */
    async voirDetails(donationId) {
        try {
            // Charger les détails complets
            const { data: donation, error: donError } = await this.supabase
                .from('donations_complete')
                .select('*')
                .eq('id', donationId)
                .single();

            if (donError) throw donError;

            // Charger les items
            const { data: items, error: itemsError } = await this.supabase
                .from('gestion_donation_items')
                .select('*')
                .eq('donation_id', donationId);

            if (itemsError) throw itemsError;

            // Charger les documents
            const { data: documents, error: docsError } = await this.supabase
                .from('gestion_donation_documents')
                .select('*')
                .eq('donation_id', donationId);

            if (docsError) throw docsError;

            // Charger le workflow
            const { data: workflow, error: workflowError } = await this.supabase
                .from('gestion_donation_workflow')
                .select('*')
                .eq('donation_id', donationId)
                .order('created_at', { ascending: true });

            if (workflowError) throw workflowError;

            this.renderDetailsModal(donation, items, documents, workflow);

        } catch (error) {
            console.error('❌ Erreur chargement détails:', error);
            showNotification('Erreur lors du chargement des détails', 'error');
        }
    }

    /**
     * Afficher le modal des détails
     */
    renderDetailsModal(donation, items, documents, workflow) {
        document.getElementById('detailsTitle').textContent = `📋 ${donation.numero_don}`;

        const content = `
            <div class="details-section">
                <h3>Informations générales</h3>
                <div class="details-grid">
                    <div><strong>Type:</strong> ${donation.type_don}</div>
                    <div><strong>Statut:</strong> <span class="donation-status status-${donation.statut}">${donation.statut_libelle}</span></div>
                    <div><strong>Date demande:</strong> ${this.formatDate(donation.date_demande)}</div>
                    <div><strong>Valeur estimée:</strong> ${donation.valeur_estimee || 'N/A'} ${donation.devise}</div>
                </div>
            </div>

            <div class="details-section">
                <h3>Donneur</h3>
                <div class="details-grid">
                    <div><strong>Nom:</strong> ${donation.donneur_nom}</div>
                    <div><strong>Contact:</strong> ${donation.donneur_contact || 'N/A'}</div>
                    <div><strong>Adresse:</strong> ${donation.donneur_adresse || 'N/A'}</div>
                </div>
            </div>

            <div class="details-section">
                <h3>Demandeur</h3>
                <div class="details-grid">
                    <div><strong>Nom:</strong> ${donation.demandeur_nom}</div>
                    <div><strong>Service:</strong> ${donation.demandeur_service}</div>
                    <div><strong>Email:</strong> ${donation.demandeur_email}</div>
                    <div><strong>Lieu affectation:</strong> ${donation.lieu_affectation}</div>
                </div>
                <div class="raison-don">
                    <strong>Raison du don:</strong>
                    <p>${donation.raison_don}</p>
                </div>
            </div>

            <div class="details-section">
                <h3>Articles/Équipements (${items.length})</h3>
                <div class="items-list">
                    ${items.map(item => `
                        <div class="item-detail">
                            <div class="item-header">
                                <strong>${item.nom_item}</strong>
                                <span class="item-quantity">${item.quantite} ${item.unite}</span>
                            </div>
                            ${item.description ? `<div class="item-description">${item.description}</div>` : ''}
                            ${item.marque || item.modele || item.numero_serie ? `
                                <div class="item-specs">
                                    ${item.marque ? `Marque: ${item.marque} ` : ''}
                                    ${item.modele ? `Modèle: ${item.modele} ` : ''}
                                    ${item.numero_serie ? `N°Série: ${item.numero_serie}` : ''}
                                </div>
                            ` : ''}
                            ${item.numero_inventaire ? `
                                <div class="item-inventory">
                                    <strong>N° Inventaire:</strong> ${item.numero_inventaire}
                                    ${item.service_affecte ? `| Service: ${item.service_affecte}` : ''}
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="details-section">
                <h3>Documents (${documents.length})</h3>
                <div class="documents-list">
                    ${documents.map(doc => `
                        <div class="document-item">
                            <div class="doc-info">
                                <strong>${doc.nom_document}</strong>
                                <span class="doc-type">${doc.type_document}</span>
                            </div>
                            <div class="doc-meta">
                                ${doc.numero_document ? `N°: ${doc.numero_document} | ` : ''}
                                ${doc.date_document ? `Date: ${this.formatDate(doc.date_document)} | ` : ''}
                                Ajouté par: ${doc.uploaded_by}
                            </div>
                            ${doc.file_path ? `
                                <button onclick="donationsManager.downloadDocument('${doc.id}')" class="btn-action btn-secondary">
                                    📥 Télécharger
                                </button>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>

            <div class="details-section">
                <h3>Historique du workflow</h3>
                <div class="workflow-timeline">
                    ${workflow.map(step => `
                        <div class="timeline-item">
                            <div class="timeline-date">${this.formatDateTime(step.created_at)}</div>
                            <div class="timeline-content">
                                <div class="timeline-action">${step.action}</div>
                                <div class="timeline-actor">${step.acteur_nom} (${step.acteur_role})</div>
                                ${step.commentaire ? `<div class="timeline-comment">${step.commentaire}</div>` : ''}
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>

            ${donation.observations ? `
                <div class="details-section">
                    <h3>Observations</h3>
                    <p>${donation.observations}</p>
                </div>
            ` : ''}
        `;

        document.getElementById('detailsContent').innerHTML = content;
        ouvrirModal('modalDetailsDon');
    }

    /**
     * Soumettre un don au DG
     */
    async soumettreDG(donationId) {
        if (!confirm('Êtes-vous sûr de vouloir soumettre ce don au Directeur Général ?')) {
            return;
        }

        try {
            const { data, error } = await this.supabase
                .rpc('submit_donation_to_dg', {
                    p_donation_id: donationId,
                    p_user_email: this.currentUser.email
                });

            if (error) throw error;

            showNotification('Don soumis au DG avec succès!', 'success');
            await this.loadDonations();

        } catch (error) {
            console.error('❌ Erreur soumission DG:', error);
            showNotification(error.message || 'Erreur lors de la soumission', 'error');
        }
    }

    /**
     * Valider un don par le DG
     */
    async validerDG(donationId) {
        this.ouvrirModalAction(donationId, 'validation_dg', 'Validation par le DG');
    }

    /**
     * Refuser un don par le DG
     */
    async refuserDG(donationId) {
        this.ouvrirModalAction(donationId, 'refus_dg', 'Refus par le DG');
    }

    /**
     * Donner un avis technique DT
     */
    async donnerAvisDT(donationId) {
        this.ouvrirModalAction(donationId, 'avis_dt', 'Avis technique DT');
    }

    /**
     * Autoriser par le MS
     */
    async autoriserMS(donationId) {
        this.ouvrirModalAction(donationId, 'autorisation_ms', 'Autorisation MS');
    }

    /**
     * Refuser par le MS
     */
    async refuserMS(donationId) {
        this.ouvrirModalAction(donationId, 'refus_ms', 'Refus MS');
    }

    /**
     * Marquer comme reçu
     */
    async marquerRecu(donationId) {
        this.ouvrirModalAction(donationId, 'reception', 'Marquer comme reçu');
    }

    /**
     * Ouvrir le modal d'action
     */
    ouvrirModalAction(donationId, actionType, title) {
        document.getElementById('actionsTitle').textContent = `⚡ ${title}`;
        document.getElementById('actionDonationId').value = donationId;
        document.getElementById('actionType').value = actionType;
        document.getElementById('actionCommentaire').value = '';

        // Générer les boutons selon l'action
        const buttonsContainer = document.getElementById('actionButtons');
        let buttons = '';

        switch (actionType) {
            case 'validation_dg':
            case 'avis_dt':
            case 'autorisation_ms':
                buttons = `
                    <button type="button" onclick="fermerModal('modalActionsDon')" class="btn btn-secondary">Annuler</button>
                    <button type="button" onclick="donationsManager.executerAction(true)" class="btn btn-success">✅ Approuver</button>
                    <button type="button" onclick="donationsManager.executerAction(false)" class="btn btn-danger">❌ Refuser</button>
                `;
                break;
            case 'reception':
                buttons = `
                    <button type="button" onclick="fermerModal('modalActionsDon')" class="btn btn-secondary">Annuler</button>
                    <button type="button" onclick="donationsManager.executerAction(true)" class="btn btn-primary">📦 Marquer reçu</button>
                `;
                break;
            default:
                buttons = `
                    <button type="button" onclick="fermerModal('modalActionsDon')" class="btn btn-secondary">Annuler</button>
                    <button type="button" onclick="donationsManager.executerAction(true)" class="btn btn-primary">✅ Confirmer</button>
                `;
        }

        buttonsContainer.innerHTML = buttons;
        ouvrirModal('modalActionsDon');
    }

    /**
     * Exécuter une action sur un don
     */
    async executerAction(approve = true) {
        try {
            const donationId = document.getElementById('actionDonationId').value;
            const actionType = document.getElementById('actionType').value;
            const commentaire = document.getElementById('actionCommentaire').value;

            if (!commentaire.trim()) {
                showNotification('Le commentaire est requis', 'error');
                return;
            }

            let result;

            switch (actionType) {
                case 'validation_dg':
                    result = await this.supabase.rpc('validate_donation_by_dg', {
                        p_donation_id: donationId,
                        p_user_email: this.currentUser.email,
                        p_avis: commentaire,
                        p_approve: approve
                    });
                    break;

                case 'avis_dt':
                    result = await this.supabase.rpc('provide_technical_advice', {
                        p_donation_id: donationId,
                        p_user_email: this.currentUser.email,
                        p_avis: commentaire,
                        p_approve: approve
                    });
                    break;

                case 'autorisation_ms':
                    result = await this.supabase.rpc('authorize_donation_by_ms', {
                        p_donation_id: donationId,
                        p_user_email: this.currentUser.email,
                        p_decision: commentaire,
                        p_approve: approve
                    });
                    break;

                case 'reception':
                    result = await this.supabase.rpc('mark_donation_received', {
                        p_donation_id: donationId,
                        p_user_email: this.currentUser.email,
                        p_observations: commentaire
                    });
                    break;
            }

            if (result?.error) throw result.error;

            const actionText = approve ? 'approuvée' : 'refusée';
            showNotification(`Action ${actionText} avec succès!`, 'success');
            fermerModal('modalActionsDon');
            await this.loadDonations();

        } catch (error) {
            console.error('❌ Erreur exécution action:', error);
            showNotification(error.message || 'Erreur lors de l\'exécution de l\'action', 'error');
        }
    }

    /**
     * Ajouter un document
     */
    ajouterDocument(donationId) {
        document.getElementById('uploadDonationId').value = donationId;
        document.getElementById('formUploadDoc').reset();
        ouvrirModal('modalUploadDoc');
    }

    /**
     * Upload d'un document
     */
    async uploadDocument() {
        try {
            const donationId = document.getElementById('uploadDonationId').value;
            const typeDocument = document.getElementById('typeDocument').value;
            const nomDocument = document.getElementById('nomDocument').value;
            const numeroDocument = document.getElementById('numeroDocument').value;
            const dateDocument = document.getElementById('dateDocument').value;
            const fichier = document.getElementById('fichierDocument').files[0];

            if (!fichier) {
                showNotification('Veuillez sélectionner un fichier', 'error');
                return;
            }

            // Vérifier la taille (max 10MB)
            if (fichier.size > 10 * 1024 * 1024) {
                showNotification('Le fichier est trop volumineux (max 10MB)', 'error');
                return;
            }

            // Upload vers Supabase Storage
            const fileName = `${donationId}/${Date.now()}_${fichier.name}`;
            const { data: uploadData, error: uploadError } = await this.supabase.storage
                .from('documents')
                .upload(fileName, fichier);

            if (uploadError) throw uploadError;

            // Enregistrer les métadonnées du document
            const { error: docError } = await this.supabase
                .from('gestion_donation_documents')
                .insert([{
                    donation_id: donationId,
                    type_document: typeDocument,
                    nom_document: nomDocument,
                    numero_document: numeroDocument || null,
                    date_document: dateDocument || null,
                    file_path: uploadData.path,
                    file_name: fichier.name,
                    file_size: fichier.size,
                    file_type: fichier.type,
                    uploaded_by: this.currentUser.email
                }]);

            if (docError) throw docError;

            showNotification('Document ajouté avec succès!', 'success');
            fermerModal('modalUploadDoc');
            await this.loadDonations();

        } catch (error) {
            console.error('❌ Erreur upload document:', error);
            showNotification('Erreur lors de l\'ajout du document', 'error');
        }
    }

    /**
     * Télécharger un document
     */
    async downloadDocument(documentId) {
        try {
            // Récupérer les infos du document
            const { data: doc, error: docError } = await this.supabase
                .from('gestion_donation_documents')
                .select('*')
                .eq('id', documentId)
                .single();

            if (docError) throw docError;

            // Générer l'URL de téléchargement
            const { data: urlData, error: urlError } = await this.supabase.storage
                .from('documents')
                .createSignedUrl(doc.file_path, 3600); // 1 heure

            if (urlError) throw urlError;

            // Ouvrir le lien de téléchargement
            window.open(urlData.signedUrl, '_blank');

        } catch (error) {
            console.error('❌ Erreur téléchargement document:', error);
            showNotification('Erreur lors du téléchargement', 'error');
        }
    }

    /**
     * Appliquer les filtres
     */
    applyFilters() {
        const statutFilter = document.getElementById('filterStatut').value;
        const typeFilter = document.getElementById('filterType').value;
        const searchTerm = document.getElementById('searchDon').value.toLowerCase();

        let filteredDonations = this.donations;

        if (statutFilter) {
            filteredDonations = filteredDonations.filter(don => don.statut === statutFilter);
        }

        if (typeFilter) {
            filteredDonations = filteredDonations.filter(don => don.type_don === typeFilter);
        }

        if (searchTerm) {
            filteredDonations = filteredDonations.filter(don =>
                don.numero_don.toLowerCase().includes(searchTerm) ||
                don.donneur_nom.toLowerCase().includes(searchTerm) ||
                don.demandeur_nom.toLowerCase().includes(searchTerm) ||
                don.demandeur_service.toLowerCase().includes(searchTerm)
            );
        }

        // Temporairement remplacer les donations pour l'affichage
        const originalDonations = this.donations;
        this.donations = filteredDonations;
        this.renderDonations();
        this.donations = originalDonations;
    }

    /**
     * Charger et afficher les statistiques
     */
    async loadStatistics() {
        try {
            const { data: stats, error } = await this.supabase
                .from('donations_stats')
                .select('*')
                .single();

            if (error) throw error;

            this.renderStatistics(stats);

        } catch (error) {
            console.error('❌ Erreur chargement statistiques:', error);
        }
    }

    /**
     * Afficher les statistiques
     */
    renderStatistics(stats) {
        const container = document.getElementById('statsGrid');
        if (!container) return;

        container.innerHTML = `
            <div class="stat-card">
                <div class="stat-number">${stats.total_dons || 0}</div>
                <div class="stat-label">Total Dons</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.total_articles || 0}</div>
                <div class="stat-label">Articles</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.total_equipements || 0}</div>
                <div class="stat-label">Équipements</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.en_attente || 0}</div>
                <div class="stat-label">En Attente</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.approuves || 0}</div>
                <div class="stat-label">Approuvés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.affectes || 0}</div>
                <div class="stat-label">Affectés</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.ce_mois || 0}</div>
                <div class="stat-label">Ce Mois</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${(stats.valeur_totale || 0).toLocaleString()} TND</div>
                <div class="stat-label">Valeur Totale</div>
            </div>
        `;
    }

    /**
     * Basculer l'affichage des statistiques
     */
    toggleStatistiques() {
        const section = document.getElementById('statistiquesSection');
        if (section.style.display === 'none') {
            section.style.display = 'block';
            this.loadStatistics();
        } else {
            section.style.display = 'none';
        }
    }

    /**
     * Supprimer un don (brouillon seulement)
     */
    async supprimerDon(donationId) {
        if (!confirm('Êtes-vous sûr de vouloir supprimer ce don ? Cette action est irréversible.')) {
            return;
        }

        try {
            const { error } = await this.supabase
                .from('gestion_donations')
                .delete()
                .eq('id', donationId);

            if (error) throw error;

            showNotification('Don supprimé avec succès!', 'success');
            await this.loadDonations();

        } catch (error) {
            console.error('❌ Erreur suppression don:', error);
            showNotification('Erreur lors de la suppression', 'error');
        }
    }
}

// Instance globale
window.donationsManager = new DonationsManager();
window.DonationsManager = DonationsManager;
