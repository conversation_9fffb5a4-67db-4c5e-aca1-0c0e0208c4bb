-- =====================================================
-- POLITIQUES RLS POUR LA GESTION DES COMMANDES
-- Row Level Security pour sécuriser l'accès aux données
-- =====================================================

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_COMMANDES
-- =====================================================

-- Suppression des politiques existantes si elles existent
DROP POLICY IF EXISTS "commands_select_policy" ON gestion_commandes;
DROP POLICY IF EXISTS "commands_insert_policy" ON gestion_commandes;
DROP POLICY IF EXISTS "commands_update_policy" ON gestion_commandes;
DROP POLICY IF EXISTS "commands_delete_policy" ON gestion_commandes;

-- Lecture : Utilisateurs peuvent voir leurs propres commandes + managers selon leur rôle
CREATE POLICY "commands_select_policy" ON gestion_commandes
    FOR SELECT USING (
        -- L'utilisateur peut voir ses propres commandes
        demandeur_email = get_user_email()
        OR
        -- Les managers et admins peuvent voir toutes les commandes
        get_user_role() IN ('admin', 'chef_service', 'dg', 'ms', 'acheteur', 'magasinier')
        OR
        -- Les utilisateurs impliqués dans le workflow peuvent voir la commande
        created_by = get_user_email()
        OR validee_chef_par = get_user_email()
        OR validee_dg_par = get_user_email()
        OR approuvee_ms_par = get_user_email()
        OR responsable_reception = get_user_email()
    );

-- Insertion : Tous les utilisateurs authentifiés peuvent créer des commandes
CREATE POLICY "commands_insert_policy" ON gestion_commandes
    FOR INSERT WITH CHECK (
        -- L'utilisateur doit être authentifié
        get_user_email() IS NOT NULL
        AND
        -- Le demandeur doit être l'utilisateur connecté
        demandeur_email = get_user_email()
        AND
        -- Le créateur doit être l'utilisateur connecté
        created_by = get_user_email()
    );

-- Mise à jour : Créateur + managers selon le statut et le rôle
CREATE POLICY "commands_update_policy" ON gestion_commandes
    FOR UPDATE USING (
        -- Le créateur peut modifier tant que le statut est brouillon
        (created_by = get_user_email() AND statut = 'brouillon')
        OR
        -- Chef de service peut valider les commandes soumises
        (get_user_role() IN ('admin', 'chef_service') AND statut = 'soumise')
        OR
        -- DG peut valider les commandes validées par le chef
        (get_user_role() IN ('admin', 'dg') AND statut = 'validee_chef')
        OR
        -- MS peut approuver les commandes validées par le DG
        (get_user_role() IN ('admin', 'ms') AND statut = 'validee_dg')
        OR
        -- Acheteur peut gérer les commandes approuvées
        (get_user_role() IN ('admin', 'acheteur') AND statut IN ('approuvee_ms', 'en_cours'))
        OR
        -- Magasinier peut gérer la réception
        (get_user_role() IN ('admin', 'magasinier') AND statut IN ('expedie', 'livre_partiel'))
        OR
        -- Responsable de réception peut marquer comme reçu
        (responsable_reception = get_user_email() AND statut = 'expedie')
    );

-- Suppression : Seuls les créateurs (statut brouillon) et admins
CREATE POLICY "commands_delete_policy" ON gestion_commandes
    FOR DELETE USING (
        (created_by = get_user_email() AND statut = 'brouillon')
        OR get_user_role() = 'admin'
    );

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_COMMANDE_ITEMS
-- =====================================================

-- Suppression des politiques existantes
DROP POLICY IF EXISTS "command_items_select_policy" ON gestion_commande_items;
DROP POLICY IF EXISTS "command_items_insert_policy" ON gestion_commande_items;
DROP POLICY IF EXISTS "command_items_update_policy" ON gestion_commande_items;
DROP POLICY IF EXISTS "command_items_delete_policy" ON gestion_commande_items;

-- Lecture : Basée sur l'accès à la commande parent
CREATE POLICY "command_items_select_policy" ON gestion_commande_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM gestion_commandes c 
            WHERE c.id = commande_id 
            AND (
                c.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'dg', 'ms', 'acheteur', 'magasinier')
                OR c.created_by = get_user_email()
                OR c.responsable_reception = get_user_email()
            )
        )
    );

-- Insertion : Si l'utilisateur peut modifier la commande parent
CREATE POLICY "command_items_insert_policy" ON gestion_commande_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM gestion_commandes c 
            WHERE c.id = commande_id 
            AND (
                (c.created_by = get_user_email() AND c.statut = 'brouillon')
                OR get_user_role() = 'admin'
            )
        )
    );

-- Mise à jour : Si l'utilisateur peut modifier la commande parent
CREATE POLICY "command_items_update_policy" ON gestion_commande_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM gestion_commandes c 
            WHERE c.id = commande_id 
            AND (
                (c.created_by = get_user_email() AND c.statut = 'brouillon')
                OR (get_user_role() IN ('admin', 'magasinier') AND c.statut IN ('expedie', 'livre_partiel'))
                OR get_user_role() = 'admin'
            )
        )
    );

-- Suppression : Si l'utilisateur peut modifier la commande parent
CREATE POLICY "command_items_delete_policy" ON gestion_commande_items
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM gestion_commandes c 
            WHERE c.id = commande_id 
            AND (
                (c.created_by = get_user_email() AND c.statut = 'brouillon')
                OR get_user_role() = 'admin'
            )
        )
    );

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_COMMANDE_DOCUMENTS
-- =====================================================

-- Suppression des politiques existantes
DROP POLICY IF EXISTS "command_documents_select_policy" ON gestion_commande_documents;
DROP POLICY IF EXISTS "command_documents_insert_policy" ON gestion_commande_documents;
DROP POLICY IF EXISTS "command_documents_update_policy" ON gestion_commande_documents;
DROP POLICY IF EXISTS "command_documents_delete_policy" ON gestion_commande_documents;

-- Lecture : Basée sur l'accès à la commande parent
CREATE POLICY "command_documents_select_policy" ON gestion_commande_documents
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM gestion_commandes c 
            WHERE c.id = commande_id 
            AND (
                c.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'dg', 'ms', 'acheteur', 'magasinier')
                OR c.created_by = get_user_email()
                OR c.responsable_reception = get_user_email()
            )
        )
    );

-- Insertion : Utilisateurs autorisés à modifier la commande
CREATE POLICY "command_documents_insert_policy" ON gestion_commande_documents
    FOR INSERT WITH CHECK (
        uploaded_by = get_user_email()
        AND EXISTS (
            SELECT 1 FROM gestion_commandes c 
            WHERE c.id = commande_id 
            AND (
                c.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'dg', 'ms', 'acheteur', 'magasinier')
                OR c.created_by = get_user_email()
            )
        )
    );

-- Mise à jour : Seul l'uploader ou admin
CREATE POLICY "command_documents_update_policy" ON gestion_commande_documents
    FOR UPDATE USING (
        uploaded_by = get_user_email()
        OR get_user_role() = 'admin'
    );

-- Suppression : Seul l'uploader ou admin
CREATE POLICY "command_documents_delete_policy" ON gestion_commande_documents
    FOR DELETE USING (
        uploaded_by = get_user_email()
        OR get_user_role() = 'admin'
    );

-- =====================================================
-- POLITIQUES POUR LA TABLE GESTION_COMMANDE_WORKFLOW
-- =====================================================

-- Suppression des politiques existantes
DROP POLICY IF EXISTS "command_workflow_select_policy" ON gestion_commande_workflow;
DROP POLICY IF EXISTS "command_workflow_insert_policy" ON gestion_commande_workflow;
DROP POLICY IF EXISTS "command_workflow_update_policy" ON gestion_commande_workflow;
DROP POLICY IF EXISTS "command_workflow_delete_policy" ON gestion_commande_workflow;

-- Lecture : Basée sur l'accès à la commande parent
CREATE POLICY "command_workflow_select_policy" ON gestion_commande_workflow
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM gestion_commandes c 
            WHERE c.id = commande_id 
            AND (
                c.demandeur_email = get_user_email()
                OR get_user_role() IN ('admin', 'chef_service', 'dg', 'ms', 'acheteur', 'magasinier')
                OR c.created_by = get_user_email()
                OR c.responsable_reception = get_user_email()
            )
        )
        OR responsable_email = get_user_email()
    );

-- Insertion : Système et admins seulement
CREATE POLICY "command_workflow_insert_policy" ON gestion_commande_workflow
    FOR INSERT WITH CHECK (
        get_user_role() = 'admin'
        OR created_by = 'system'
    );

-- Mise à jour : Responsable de l'étape ou admin
CREATE POLICY "command_workflow_update_policy" ON gestion_commande_workflow
    FOR UPDATE USING (
        responsable_email = get_user_email()
        OR get_user_role() = 'admin'
        OR (
            -- Permettre la mise à jour par les rôles appropriés selon l'étape
            (numero_etape = 2 AND get_user_role() IN ('admin', 'chef_service'))
            OR (numero_etape = 3 AND get_user_role() IN ('admin', 'dg'))
            OR (numero_etape = 4 AND get_user_role() IN ('admin', 'ms'))
            OR (numero_etape >= 5 AND get_user_role() IN ('admin', 'acheteur', 'magasinier'))
        )
    );

-- Suppression : Admins seulement
CREATE POLICY "command_workflow_delete_policy" ON gestion_commande_workflow
    FOR DELETE USING (
        get_user_role() = 'admin'
    );

-- =====================================================
-- ACTIVATION DES POLITIQUES RLS
-- =====================================================

-- S'assurer que RLS est activé sur toutes les tables
ALTER TABLE gestion_commandes ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_commande_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_commande_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE gestion_commande_workflow ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PERMISSIONS SUR LES TABLES
-- =====================================================

-- Accorder les permissions de base aux utilisateurs authentifiés
GRANT SELECT, INSERT, UPDATE, DELETE ON gestion_commandes TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON gestion_commande_items TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON gestion_commande_documents TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON gestion_commande_workflow TO authenticated;

-- Accorder les permissions sur les séquences si nécessaire
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =====================================================
-- VALIDATION DES POLITIQUES
-- =====================================================

-- Vérifier que toutes les politiques ont été créées
DO $$
DECLARE
    policy_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE schemaname = 'public'
    AND tablename IN ('gestion_commandes', 'gestion_commande_items', 'gestion_commande_documents', 'gestion_commande_workflow');
    
    IF policy_count >= 16 THEN
        RAISE NOTICE '✅ Toutes les politiques RLS ont été créées avec succès (% politiques)', policy_count;
    ELSE
        RAISE WARNING '⚠️ Seulement % politiques créées, vérifiez la configuration', policy_count;
    END IF;
END $$;
