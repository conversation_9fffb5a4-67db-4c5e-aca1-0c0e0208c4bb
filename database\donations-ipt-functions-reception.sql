-- =====================================================
-- FONCTIONS DE RÉCEPTION ET INVENTAIRE - SYSTÈME DONS IPT
-- =====================================================

-- =====================================================
-- GESTION MINISTÈRE DE LA SANTÉ
-- =====================================================

-- Soumission au Ministère de la Santé
CREATE OR REPLACE FUNCTION submit_to_ministry(
    p_donation_id UUID,
    p_submitter_email TEXT,
    p_commentaires TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_statut TEXT;
BEGIN
    -- Vérifier le statut
    SELECT statut INTO current_statut 
    FROM gestion_donations_ipt 
    WHERE id = p_donation_id;
    
    IF current_statut != 'valide_sd_achats' THEN
        RAISE EXCEPTION 'Don non validé par SD Achats';
    END IF;
    
    -- Mettre à jour le statut
    UPDATE gestion_donations_ipt 
    SET 
        statut = 'soumis_ms',
        etape_actuelle = etape_actuelle + 1,
        updated_by = p_submitter_email
    WHERE id = p_donation_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_donation_workflow 
    SET 
        statut_etape = 'en_cours',
        date_debut = NOW(),
        commentaires = p_commentaires
    WHERE donation_id = p_donation_id AND nom_etape = 'Soumission MS';
    
    -- Historique
    INSERT INTO gestion_donation_historique (
        donation_id, type_action, description_action, 
        utilisateur_email, commentaires, date_action
    ) VALUES (
        p_donation_id, 'soumission', 'Soumission au Ministère de la Santé',
        p_submitter_email, p_commentaires, NOW()
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Enregistrement de la décision du Ministère
CREATE OR REPLACE FUNCTION record_ministry_decision(
    p_donation_id UUID,
    p_decision TEXT, -- 'approuve', 'refuse'
    p_reference_officielle TEXT,
    p_commentaires TEXT DEFAULT NULL,
    p_recorded_by TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Vérifier que le don est soumis au MS
    IF NOT EXISTS (
        SELECT 1 FROM gestion_donations_ipt 
        WHERE id = p_donation_id AND statut = 'soumis_ms'
    ) THEN
        RAISE EXCEPTION 'Don non soumis au Ministère de la Santé';
    END IF;
    
    -- Mettre à jour le don
    UPDATE gestion_donations_ipt 
    SET 
        statut = CASE WHEN p_decision = 'approuve' THEN 'approuve_ms' ELSE 'refuse_ms' END,
        etape_actuelle = CASE WHEN p_decision = 'approuve' THEN etape_actuelle + 1 ELSE etape_actuelle END,
        decision_ms = p_decision,
        decision_ms_date = NOW(),
        decision_ms_commentaires = p_commentaires,
        decision_ms_reference = p_reference_officielle,
        updated_by = p_recorded_by
    WHERE id = p_donation_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_donation_workflow 
    SET 
        statut_etape = CASE WHEN p_decision = 'approuve' THEN 'termine' ELSE 'refuse' END,
        date_fin = NOW(),
        duree_traitement = NOW() - date_debut,
        commentaires = p_commentaires || ' - Ref: ' || p_reference_officielle
    WHERE donation_id = p_donation_id AND nom_etape = 'Décision MS';
    
    IF p_decision = 'approuve' THEN
        -- Activer l'étape de réception
        UPDATE gestion_donation_workflow 
        SET statut_etape = 'en_attente', date_debut = NOW()
        WHERE donation_id = p_donation_id AND nom_etape = 'Réception';
    END IF;
    
    -- Historique
    INSERT INTO gestion_donation_historique (
        donation_id, type_action, description_action, 
        utilisateur_email, commentaires, date_action
    ) VALUES (
        p_donation_id, 
        CASE WHEN p_decision = 'approuve' THEN 'validation' ELSE 'refus' END,
        'Décision du Ministère de la Santé: ' || p_decision,
        p_recorded_by, p_commentaires || ' - Ref: ' || p_reference_officielle, NOW()
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- RÉCEPTION DES DONS
-- =====================================================

-- Réception physique du don
CREATE OR REPLACE FUNCTION receive_donation(
    p_donation_id UUID,
    p_receptionist_email TEXT,
    p_date_reception DATE DEFAULT CURRENT_DATE,
    p_commentaires TEXT DEFAULT NULL,
    p_signature_data TEXT DEFAULT NULL -- Données de signature numérique
)
RETURNS BOOLEAN AS $$
DECLARE
    current_statut TEXT;
BEGIN
    -- Vérifier le statut
    SELECT statut INTO current_statut 
    FROM gestion_donations_ipt 
    WHERE id = p_donation_id;
    
    IF current_statut != 'approuve_ms' THEN
        RAISE EXCEPTION 'Don non approuvé par le Ministère de la Santé';
    END IF;
    
    -- Mettre à jour le don
    UPDATE gestion_donations_ipt 
    SET 
        statut = 'receptionne',
        etape_actuelle = etape_actuelle + 1,
        date_reception = p_date_reception,
        receptionne_par = p_receptionist_email,
        receptionne_date = NOW(),
        updated_by = p_receptionist_email
    WHERE id = p_donation_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_donation_workflow 
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        duree_traitement = NOW() - date_debut,
        commentaires = p_commentaires
    WHERE donation_id = p_donation_id AND nom_etape = 'Réception';
    
    -- Activer l'étape d'inventaire
    UPDATE gestion_donation_workflow 
    SET statut_etape = 'en_cours', date_debut = NOW()
    WHERE donation_id = p_donation_id AND nom_etape = 'Inventaire';
    
    -- Enregistrer la signature si fournie
    IF p_signature_data IS NOT NULL THEN
        INSERT INTO gestion_donation_documents (
            donation_id, type_document, nom_fichier, nom_original,
            url_stockage, description_document, signe_numeriquement,
            signature_par, signature_date, uploaded_by, uploaded_at
        ) VALUES (
            p_donation_id, 'bon_livraison', 'BL_signe_' || p_donation_id || '.json',
            'Bon de livraison signé', p_signature_data, 'Signature numérique du réceptionniste',
            true, p_receptionist_email, NOW(), p_receptionist_email, NOW()
        );
    END IF;
    
    -- Historique
    INSERT INTO gestion_donation_historique (
        donation_id, type_action, description_action, 
        utilisateur_email, commentaires, date_action
    ) VALUES (
        p_donation_id, 'reception', 'Réception physique du don',
        p_receptionist_email, p_commentaires, NOW()
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- INVENTAIRE ET NUMÉROTATION
-- =====================================================

-- Attribution du numéro d'inventaire
CREATE OR REPLACE FUNCTION assign_inventory_number(
    p_donation_id UUID,
    p_rve_email TEXT,
    p_service_affecte TEXT,
    p_laboratoire_affecte TEXT DEFAULT NULL,
    p_emplacement_precis TEXT DEFAULT NULL,
    p_specifications JSONB DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
    new_inventory_number TEXT;
    current_statut TEXT;
    don_info RECORD;
BEGIN
    -- Vérifier le statut
    SELECT statut, type_don, description_don INTO current_statut, don_info
    FROM gestion_donations_ipt 
    WHERE id = p_donation_id;
    
    IF current_statut != 'receptionne' THEN
        RAISE EXCEPTION 'Don non réceptionné';
    END IF;
    
    -- Générer le numéro d'inventaire
    new_inventory_number := generate_inventory_number();
    
    -- Créer l'entrée d'inventaire
    INSERT INTO gestion_donation_inventaire (
        donation_id, numero_inventaire, service_actuel, laboratoire_actuel,
        emplacement_precis, rve_responsable, date_inventaire,
        specifications, created_by
    ) VALUES (
        p_donation_id, new_inventory_number, p_service_affecte, p_laboratoire_affecte,
        p_emplacement_precis, p_rve_email, CURRENT_DATE,
        p_specifications, p_rve_email
    );
    
    -- Mettre à jour le don
    UPDATE gestion_donations_ipt 
    SET 
        statut = 'inventorie',
        etape_actuelle = etape_actuelle + 1,
        numero_inventaire = new_inventory_number,
        rve_responsable = p_rve_email,
        service_affecte = p_service_affecte,
        laboratoire_affecte = p_laboratoire_affecte,
        updated_by = p_rve_email
    WHERE id = p_donation_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_donation_workflow 
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        duree_traitement = NOW() - date_debut,
        commentaires = 'Numéro d''inventaire attribué: ' || new_inventory_number
    WHERE donation_id = p_donation_id AND nom_etape = 'Inventaire';
    
    -- Activer l'étape d'affectation
    UPDATE gestion_donation_workflow 
    SET statut_etape = 'en_cours', date_debut = NOW()
    WHERE donation_id = p_donation_id AND nom_etape = 'Affectation';
    
    -- Historique
    INSERT INTO gestion_donation_historique (
        donation_id, type_action, description_action, 
        utilisateur_email, commentaires, date_action
    ) VALUES (
        p_donation_id, 'affectation', 'Attribution du numéro d''inventaire',
        p_rve_email, 'Numéro: ' || new_inventory_number || ' - Service: ' || p_service_affecte, NOW()
    );
    
    RETURN new_inventory_number;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- AFFECTATION FINALE
-- =====================================================

-- Affectation finale du don
CREATE OR REPLACE FUNCTION finalize_donation_assignment(
    p_donation_id UUID,
    p_rve_email TEXT,
    p_responsable_affectation TEXT,
    p_commentaires TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_statut TEXT;
BEGIN
    -- Vérifier le statut
    SELECT statut INTO current_statut 
    FROM gestion_donations_ipt 
    WHERE id = p_donation_id;
    
    IF current_statut != 'inventorie' THEN
        RAISE EXCEPTION 'Don non inventorié';
    END IF;
    
    -- Mettre à jour le don
    UPDATE gestion_donations_ipt 
    SET 
        statut = 'affecte',
        etape_actuelle = etape_actuelle + 1,
        date_affectation = CURRENT_DATE,
        responsable_affectation = p_responsable_affectation,
        updated_by = p_rve_email
    WHERE id = p_donation_id;
    
    -- Mettre à jour le workflow
    UPDATE gestion_donation_workflow 
    SET 
        statut_etape = 'termine',
        date_fin = NOW(),
        duree_traitement = NOW() - date_debut,
        commentaires = p_commentaires
    WHERE donation_id = p_donation_id AND nom_etape = 'Affectation';
    
    -- Activer l'étape d'archivage
    UPDATE gestion_donation_workflow 
    SET statut_etape = 'en_attente', date_debut = NOW()
    WHERE donation_id = p_donation_id AND nom_etape = 'Archivage';
    
    -- Créer le mouvement initial d'affectation
    INSERT INTO gestion_donation_mouvements (
        donation_id, type_mouvement, service_destination, laboratoire_destination,
        demandeur, approbateur, executant, motif, date_mouvement, 
        statut_mouvement, created_by
    ) VALUES (
        p_donation_id, 'affectation_initiale', 
        (SELECT service_affecte FROM gestion_donations_ipt WHERE id = p_donation_id),
        (SELECT laboratoire_affecte FROM gestion_donations_ipt WHERE id = p_donation_id),
        p_rve_email, p_rve_email, p_responsable_affectation,
        'Affectation initiale du don', CURRENT_DATE, 'termine', p_rve_email
    );
    
    -- Historique
    INSERT INTO gestion_donation_historique (
        donation_id, type_action, description_action, 
        utilisateur_email, commentaires, date_action
    ) VALUES (
        p_donation_id, 'affectation', 'Affectation finale du don',
        p_rve_email, p_commentaires, NOW()
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
