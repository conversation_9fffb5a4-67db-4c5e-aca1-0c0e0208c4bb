/**
 * Widget de tableau de bord des dons avec temps réel
 * Système complet de gestion des dons IPT
 */

class DonationsDashboardWidget {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.isVisible = false;
        this.refreshInterval = null;
        this.realtimeSubscription = null;
        this.data = {
            totalDons: 0,
            donsEnCours: 0,
            donsEnRetard: 0,
            donsCompletes: 0,
            valeurTotale: 0,
            valeurEnCours: 0,
            userActions: [],
            workflowSteps: []
        };
    }

    async initialize() {
        console.log('🎁 Initialisation du widget de gestion des dons...');
        
        try {
            // Récupérer l'instance Supabase
            this.supabase = window.supabaseClient || window.supabase;
            if (!this.supabase) {
                console.warn('⚠️ Client Supabase non disponible pour le widget dons');
                return;
            }

            // Récupérer l'utilisateur actuel
            const { data: { user } } = await this.supabase.auth.getUser();
            this.currentUser = user;

            if (!this.currentUser) {
                console.warn('⚠️ Utilisateur non authentifié pour le widget dons');
                return;
            }

            // Charger les données initiales
            await this.loadData();

            // Configurer les mises à jour en temps réel
            this.setupRealtimeSubscription();

            // Configurer le rafraîchissement automatique (toutes les 30 secondes)
            this.refreshInterval = setInterval(() => {
                this.loadData();
            }, 30000);

            console.log('✅ Widget de gestion des dons initialisé');

        } catch (error) {
            console.error('❌ Erreur initialisation widget dons:', error);
        }
    }

    async loadData() {
        try {
            // Charger les statistiques globales
            await this.loadStatistics();
            
            // Charger les actions utilisateur
            await this.loadUserActions();
            
            // Charger l'état du workflow
            await this.loadWorkflowStatus();
            
            // Mettre à jour l'affichage
            this.updateDisplay();

        } catch (error) {
            console.error('❌ Erreur chargement données widget dons:', error);
        }
    }

    async loadStatistics() {
        try {
            // Statistiques globales depuis la vue donations_workflow_complete
            const { data: stats, error } = await this.supabase
                .from('donations_workflow_complete')
                .select('*');

            if (error && error.code !== 'PGRST116') { // Ignore "no rows" error
                throw error;
            }

            if (stats && stats.length > 0) {
                // Calculer les statistiques
                this.data.totalDons = stats.length;
                this.data.donsEnCours = stats.filter(d => 
                    !['archive', 'refuse', 'annule'].includes(d.statut_donation)
                ).length;
                this.data.donsCompletes = stats.filter(d => 
                    d.statut_donation === 'archive'
                ).length;
                this.data.donsEnRetard = stats.filter(d => 
                    d.en_retard === true
                ).length;

                // Calculer les valeurs (si disponibles)
                this.data.valeurTotale = stats.reduce((sum, don) => {
                    return sum + (parseFloat(don.valeur_estimee) || 0);
                }, 0);
                
                this.data.valeurEnCours = stats.filter(d => 
                    !['archive', 'refuse', 'annule'].includes(d.statut_donation)
                ).reduce((sum, don) => {
                    return sum + (parseFloat(don.valeur_estimee) || 0);
                }, 0);
            }

            // Fallback vers la table principale si la vue n'existe pas
            if (!stats || stats.length === 0) {
                await this.loadStatisticsFallback();
            }

        } catch (error) {
            console.error('❌ Erreur chargement statistiques dons:', error);
            await this.loadStatisticsFallback();
        }
    }

    async loadStatisticsFallback() {
        try {
            // Utiliser la table principale gestion_donations
            const { data: dons, error } = await this.supabase
                .from('gestion_donations')
                .select('*')
                .is('deleted_at', null);

            if (error) throw error;

            if (dons) {
                this.data.totalDons = dons.length;
                this.data.donsEnCours = dons.filter(d => 
                    !['archive', 'refuse', 'annule'].includes(d.statut)
                ).length;
                this.data.donsCompletes = dons.filter(d => 
                    d.statut === 'archive'
                ).length;
                
                // Calculer les retards (dons créés il y a plus de 30 jours sans être archivés)
                const now = new Date();
                this.data.donsEnRetard = dons.filter(d => {
                    if (['archive', 'refuse', 'annule'].includes(d.statut)) return false;
                    const created = new Date(d.created_at);
                    const daysDiff = (now - created) / (1000 * 60 * 60 * 24);
                    return daysDiff > 30;
                }).length;

                // Calculer les valeurs
                this.data.valeurTotale = dons.reduce((sum, don) => {
                    return sum + (parseFloat(don.valeur_estimee) || 0);
                }, 0);
                
                this.data.valeurEnCours = dons.filter(d => 
                    !['archive', 'refuse', 'annule'].includes(d.statut)
                ).reduce((sum, don) => {
                    return sum + (parseFloat(don.valeur_estimee) || 0);
                }, 0);
            }

        } catch (error) {
            console.error('❌ Erreur fallback statistiques dons:', error);
            // Valeurs par défaut
            this.data = {
                totalDons: 0,
                donsEnCours: 0,
                donsEnRetard: 0,
                donsCompletes: 0,
                valeurTotale: 0,
                valeurEnCours: 0,
                userActions: [],
                workflowSteps: []
            };
        }
    }

    async loadUserActions() {
        try {
            const userRole = this.currentUser?.user_metadata?.role || 'user';
            const userEmail = this.currentUser?.email;

            // Requête pour les dons nécessitant une action de l'utilisateur
            let query = this.supabase
                .from('gestion_donations')
                .select('*')
                .is('deleted_at', null);

            // Filtrer selon le rôle de l'utilisateur
            switch (userRole) {
                case 'chef_service':
                    query = query.eq('statut', 'soumis_chef');
                    break;
                case 'dg':
                    query = query.eq('statut', 'valide_chef');
                    break;
                case 'ms':
                    query = query.eq('statut', 'valide_dg');
                    break;
                case 'magasinier':
                    query = query.in('statut', ['bp_cree', 'chez_magasinier']);
                    break;
                case 'transitaire':
                    query = query.eq('statut', 'chez_transitaire');
                    break;
                case 'receptionniste':
                    query = query.eq('statut', 'chez_receptionniste');
                    break;
                case 'rve':
                    query = query.in('statut', ['bs_cree', 'chez_rve']);
                    break;
                default:
                    // Pour les utilisateurs normaux, montrer leurs propres dons
                    query = query.eq('demandeur_email', userEmail);
            }

            const { data: actions, error } = await query.limit(5);

            if (error) throw error;

            this.data.userActions = (actions || []).map(don => ({
                id: don.id,
                title: `${don.numero_don || 'DON-' + don.id.substring(0, 8)} - ${don.type_don}`,
                description: this.getActionDescription(don, userRole),
                urgent: this.isActionUrgent(don),
                don: don
            }));

        } catch (error) {
            console.error('❌ Erreur chargement actions utilisateur dons:', error);
            this.data.userActions = [];
        }
    }

    getActionDescription(don, userRole) {
        const roleActions = {
            'chef_service': 'Validation requise',
            'dg': 'Validation DG requise',
            'ms': 'Approbation MS requise',
            'magasinier': 'Traitement magasin requis',
            'transitaire': 'Validation transitaire requise',
            'receptionniste': 'Réception à traiter',
            'rve': 'Traitement RVE requis'
        };

        return roleActions[userRole] || `Statut: ${don.statut}`;
    }

    isActionUrgent(don) {
        // Considérer comme urgent si créé il y a plus de 7 jours
        const created = new Date(don.created_at);
        const now = new Date();
        const daysDiff = (now - created) / (1000 * 60 * 60 * 24);
        
        return daysDiff > 7 && !['archive', 'refuse', 'annule'].includes(don.statut);
    }

    async loadWorkflowStatus() {
        try {
            // Charger l'état global du workflow (étapes en cours)
            const { data: etapes, error } = await this.supabase
                .from('gestion_donation_etapes')
                .select('numero_etape, statut_etape')
                .in('statut_etape', ['en_cours', 'termine'])
                .order('numero_etape');

            if (error) throw error;

            // Créer un résumé des étapes 1-10
            this.data.workflowSteps = [];
            for (let i = 1; i <= 10; i++) {
                const etapesNum = etapes?.filter(e => e.numero_etape === i) || [];
                const completed = etapesNum.filter(e => e.statut_etape === 'termine').length;
                const current = etapesNum.filter(e => e.statut_etape === 'en_cours').length;
                
                let status = 'pending';
                if (completed > 0 && current === 0) status = 'completed';
                else if (current > 0) status = 'current';
                
                this.data.workflowSteps.push({
                    number: i,
                    status: status,
                    count: etapesNum.length
                });
            }

        } catch (error) {
            console.error('❌ Erreur chargement workflow dons:', error);
            this.data.workflowSteps = [];
        }
    }

    setupRealtimeSubscription() {
        try {
            // S'abonner aux changements en temps réel
            this.realtimeSubscription = this.supabase
                .channel('donations_widget')
                .on('postgres_changes', 
                    { event: '*', schema: 'public', table: 'gestion_donations' },
                    (payload) => {
                        console.log('🔄 Mise à jour temps réel dons détectée:', payload);
                        // Recharger les données après un court délai
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .on('postgres_changes',
                    { event: '*', schema: 'public', table: 'gestion_donation_etapes' },
                    (payload) => {
                        console.log('🔄 Mise à jour workflow dons détectée:', payload);
                        setTimeout(() => this.loadData(), 1000);
                    }
                )
                .subscribe();

            console.log('✅ Abonnement temps réel configuré pour le widget dons');

        } catch (error) {
            console.error('❌ Erreur configuration temps réel dons:', error);
        }
    }

    updateDisplay() {
        // Mettre à jour les statistiques
        this.updateElement('widgetTotalDons', this.data.totalDons);
        this.updateElement('widgetDonsEnCours', this.data.donsEnCours);
        this.updateElement('widgetDonsEnRetard', this.data.donsEnRetard);
        this.updateElement('widgetDonsCompletes', this.data.donsCompletes);

        // Mettre à jour les valeurs
        this.updateElement('widgetValeurTotale', this.formatAmount(this.data.valeurTotale));
        this.updateElement('widgetValeurEnCours', this.formatAmount(this.data.valeurEnCours));

        // Mettre à jour les actions utilisateur
        this.updateUserActions();

        // Mettre à jour le workflow
        this.updateWorkflowSteps();

        // Mettre à jour le badge de notification
        this.updateNotificationBadge();
    }

    updateElement(id, value) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    }

    formatAmount(amount) {
        if (amount === 0) return '0 TND';
        if (amount >= 1000000) {
            return (amount / 1000000).toFixed(1) + 'M TND';
        } else if (amount >= 1000) {
            return (amount / 1000).toFixed(1) + 'K TND';
        } else {
            return amount.toFixed(0) + ' TND';
        }
    }

    updateUserActions() {
        const container = document.getElementById('donsActionsList');
        if (!container) return;
        
        if (this.data.userActions.length === 0) {
            container.innerHTML = '<div class="no-actions">Aucune action requise pour le moment</div>';
            return;
        }

        container.innerHTML = this.data.userActions.map(action => `
            <div class="action-item ${action.urgent ? 'urgent' : ''}" onclick="donationsWidget.openDonationDetails('${action.id}')">
                <div class="action-title">${action.title}</div>
                <div class="action-description">${action.description}</div>
            </div>
        `).join('');
    }

    updateWorkflowSteps() {
        const container = document.getElementById('donsWorkflowStepsMini');
        if (!container) return;
        
        container.innerHTML = this.data.workflowSteps.slice(0, 5).map(step => `
            <div class="workflow-step-mini ${step.status}" title="Étape ${step.number}">
                ${step.number}
            </div>
        `).join('');
    }

    updateNotificationBadge() {
        const badge = document.getElementById('donationsNotificationBadge');
        if (!badge) return;
        
        const urgentActions = this.data.userActions.filter(action => action.urgent).length;
        const totalActions = this.data.userActions.length;
        const totalUrgent = urgentActions + this.data.donsEnRetard;

        if (totalUrgent > 0) {
            badge.textContent = totalUrgent;
            badge.style.display = 'flex';
            badge.style.background = '#ef4444'; // Rouge pour urgent
        } else if (totalActions > 0) {
            badge.textContent = totalActions;
            badge.style.display = 'flex';
            badge.style.background = '#f59e0b'; // Orange pour actions normales
        } else {
            badge.style.display = 'none';
        }
    }

    show() {
        const widget = document.getElementById('donationsDashboardWidget');
        if (widget) {
            widget.style.display = 'block';
            widget.classList.add('show');
            this.isVisible = true;
            
            // Charger les données si pas encore fait
            if (!this.supabase) {
                this.initialize();
            } else {
                this.loadData();
            }
        }
    }

    hide() {
        const widget = document.getElementById('donationsDashboardWidget');
        if (widget) {
            widget.style.display = 'none';
            widget.classList.remove('show');
            this.isVisible = false;
        }
    }

    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    openDonationDetails(donationId) {
        // Ouvrir la page de gestion des dons avec le don sélectionné
        window.open(`donations-management.html?id=${donationId}`, '_blank');
    }

    destroy() {
        // Nettoyer les ressources
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        if (this.realtimeSubscription) {
            this.supabase.removeChannel(this.realtimeSubscription);
        }
    }
}

// Instance globale du widget
window.donationsWidget = new DonationsDashboardWidget();

// Fonctions globales pour l'interface
function toggleDonationsWidget() {
    donationsWidget.toggle();
}

function refreshDonationsDashboard() {
    donationsWidget.loadData();
}

function openDonationsManagement() {
    window.open('donations-management.html', '_blank');
}

function openDonationsWorkflowDashboard() {
    window.open('donations-workflow-dashboard.html', '_blank');
}

function openDonationsReports() {
    window.open('donations-reports.html', '_blank');
}

// Fonction pour basculer la gestion des dons
function toggleGestionDons() {
    const zone = document.getElementById('gestionDonsZone');
    
    if (zone && zone.style.display !== 'none') {
        // Si la zone complète est visible, la masquer et afficher le widget
        zone.style.display = 'none';
        donationsWidget.show();
    } else {
        // Sinon, juste basculer le widget
        donationsWidget.toggle();
    }
}
