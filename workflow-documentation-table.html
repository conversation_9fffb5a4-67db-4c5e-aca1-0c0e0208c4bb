<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentation Workflow - Gestion des Dons IPT</title>

    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/supabase-style.css">

    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Configuration -->
    <script src="assets/config/supabase-config.js"></script>
    <script src="assets/js/supabase-auth.js"></script>

    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            color: #2d3748;
            font-size: 28px;
            font-weight: 700;
        }

        .header p {
            margin: 10px 0 0 0;
            color: #718096;
            font-size: 16px;
        }

        .admin-controls {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
            display: none;
        }

        .admin-controls.show {
            display: block;
        }

        .admin-header {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .admin-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .workflow-table-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .table-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }

        .table-header h2 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
        }

        .workflow-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .workflow-table th {
            background: #f7fafc;
            color: #2d3748;
            font-weight: 600;
            padding: 15px 12px;
            text-align: left;
            border-bottom: 2px solid #e2e8f0;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .workflow-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #e2e8f0;
            vertical-align: top;
            line-height: 1.5;
        }

        .workflow-table tr:hover {
            background: #f7fafc;
        }

        .step-number {
            background: #4299e1;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin: 0 auto;
        }

        .step-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .step-description {
            color: #718096;
            font-size: 13px;
        }

        .role-badge {
            background: #48bb78;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 5px;
        }

        .role-badge.demandeur { background: #9f7aea; }
        .role-badge.beneficiaire { background: #ed8936; }
        .role-badge.magasinier { background: #38b2ac; }
        .role-badge.transitaire { background: #4299e1; }
        .role-badge.receptionniste { background: #48bb78; }
        .role-badge.rve { background: #e53e3e; }

        .document-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .document-list li {
            background: #f7fafc;
            margin: 3px 0;
            padding: 6px 10px;
            border-radius: 6px;
            border-left: 3px solid #4299e1;
            font-size: 12px;
        }

        .document-list li.input { border-left-color: #48bb78; }
        .document-list li.output { border-left-color: #e53e3e; }
        .document-list li.generated { border-left-color: #9f7aea; }

        .action-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .action-list li {
            margin: 5px 0;
            padding: 5px 0;
            border-bottom: 1px dotted #e2e8f0;
            font-size: 13px;
        }

        .action-list li:last-child {
            border-bottom: none;
        }

        .action-list li::before {
            content: "▶";
            color: #4299e1;
            margin-right: 8px;
            font-size: 10px;
        }

        .next-role {
            background: #ebf8ff;
            color: #2a4365;
            padding: 8px 12px;
            border-radius: 8px;
            font-weight: 500;
            text-align: center;
            border: 1px solid #90cdf4;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
        }

        .btn-secondary {
            background: #edf2f7;
            color: #4a5568;
            border: 1px solid #e2e8f0;
        }

        .legend {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .legend h3 {
            margin: 0 0 15px 0;
            color: #2d3748;
            font-size: 18px;
            font-weight: 600;
        }

        .legend-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .legend-color.input { background: #48bb78; }
        .legend-color.output { background: #e53e3e; }
        .legend-color.generated { background: #9f7aea; }

        .stats-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .stat-card {
            text-align: center;
            padding: 15px;
            background: #f7fafc;
            border-radius: 8px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #718096;
            font-size: 12px;
            font-weight: 500;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #f0fff4;
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .alert-error {
            background: #fed7d7;
            color: #742a2a;
            border: 1px solid #feb2b2;
        }

        .alert-info {
            background: #ebf8ff;
            color: #2a4365;
            border: 1px solid #90cdf4;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 12px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px 30px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s;
        }

        .modal-close:hover {
            background: rgba(255,255,255,0.2);
        }

        .modal-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input,
        .form-textarea,
        .form-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s;
            box-sizing: border-box;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .modal-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .workflow-table {
                font-size: 12px;
            }

            .workflow-table th,
            .workflow-table td {
                padding: 10px 8px;
            }

            .admin-buttons {
                flex-direction: column;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Print styles */
        @media print {
            body {
                background: white;
            }

            .admin-controls,
            .btn {
                display: none !important;
            }

            .workflow-table-container {
                box-shadow: none;
                border: 1px solid #e2e8f0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📋 Documentation Workflow - Gestion des Dons IPT</h1>
            <p>Processus complet en 10 étapes - Institut Pasteur de Tunis</p>
        </div>

        <!-- Admin Controls -->
        <div class="admin-controls" id="adminControls">
            <div class="admin-header">
                🔧 Contrôles Administrateur - Accès Restreint
            </div>
            <div class="admin-buttons">
                <button class="btn btn-primary" onclick="addNewStep()">
                    ➕ Ajouter Étape
                </button>
                <button class="btn btn-success" onclick="validateAllSteps()">
                    ✅ Valider Toutes les Étapes
                </button>
                <button class="btn btn-warning" onclick="editWorkflow()">
                    ✏️ Modifier Workflow
                </button>
                <button class="btn btn-danger" onclick="resetWorkflow()">
                    🔄 Réinitialiser
                </button>
                <button class="btn btn-secondary" onclick="exportWorkflow()">
                    📤 Exporter Documentation
                </button>
            </div>
        </div>

        <!-- Alerts Container -->
        <div id="alertContainer"></div>

        <!-- Statistics Section -->
        <div class="stats-section">
            <h3>📊 Statistiques du Workflow</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">10</div>
                    <div class="stat-label">Étapes Totales</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">6</div>
                    <div class="stat-label">Rôles Impliqués</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">15</div>
                    <div class="stat-label">Documents Types</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">10-15</div>
                    <div class="stat-label">Jours Moyens</div>
                </div>
            </div>
        </div>

        <!-- Legend -->
        <div class="legend">
            <h3>🏷️ Légende des Documents</h3>
            <div class="legend-grid">
                <div class="legend-item">
                    <div class="legend-color input"></div>
                    <span>Documents d'entrée (reçus)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color output"></div>
                    <span>Documents de sortie (transmis)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color generated"></div>
                    <span>Documents générés automatiquement</span>
                </div>
            </div>
        </div>

        <!-- Workflow Table -->
        <div class="workflow-table-container">
            <div class="table-header">
                <h2>🔄 Workflow Complet - Gestion des Dons IPT</h2>
            </div>

            <div style="overflow-x: auto;">
                <table class="workflow-table" id="workflowTable">
                    <thead>
                        <tr>
                            <th style="width: 80px;">Étape</th>
                            <th style="width: 200px;">Nom / Description</th>
                            <th style="width: 150px;">Responsable</th>
                            <th style="width: 200px;">Documents d'Entrée</th>
                            <th style="width: 250px;">Actions Réalisées</th>
                            <th style="width: 200px;">Documents de Sortie</th>
                            <th style="width: 150px;">Responsable Suivant</th>
                        </tr>
                    </thead>
                    <tbody id="workflowTableBody">
                        <!-- Les données seront générées dynamiquement -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="legend">
            <h3>📝 Informations Complémentaires</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div>
                    <h4>🎯 Objectifs du Workflow</h4>
                    <ul>
                        <li>Traçabilité complète des dons</li>
                        <li>Validation hiérarchique</li>
                        <li>Conformité réglementaire</li>
                        <li>Optimisation des délais</li>
                    </ul>
                </div>
                <div>
                    <h4>📋 Abréviations Utilisées</h4>
                    <ul>
                        <li><strong>BP</strong> : Bon de Prélèvement</li>
                        <li><strong>BL</strong> : Bon de Livraison</li>
                        <li><strong>BS</strong> : Bon de Sortie</li>
                        <li><strong>RVE</strong> : Responsable Volet Équipement</li>
                    </ul>
                </div>
                <div>
                    <h4>⏱️ Délais Standards</h4>
                    <ul>
                        <li>Validation : 1-2 jours</li>
                        <li>Vérification : 1-2 jours</li>
                        <li>Transport : 1-3 jours</li>
                        <li>Installation : 2-5 jours</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour ajouter/modifier étape -->
    <div id="stepModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modalTitle">Ajouter une Étape</h3>
                <button class="modal-close" onclick="closeStepModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="stepForm">
                    <div class="form-group">
                        <label class="form-label" for="stepNumber">Numéro d'Étape</label>
                        <input type="number" id="stepNumber" class="form-input" min="1" max="20" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="stepName">Nom de l'Étape</label>
                        <input type="text" id="stepName" class="form-input" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="stepDescription">Description</label>
                        <textarea id="stepDescription" class="form-textarea" required></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="responsibleRole">Rôle Responsable</label>
                        <select id="responsibleRole" class="form-select" required>
                            <option value="">Sélectionner un rôle</option>
                            <option value="demandeur">Demandeur</option>
                            <option value="beneficiaire">Bénéficiaire</option>
                            <option value="magasinier">Magasinier</option>
                            <option value="transitaire">Transitaire</option>
                            <option value="receptionniste">Réceptionniste</option>
                            <option value="rve">RVE</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="inputDocuments">Documents d'Entrée (un par ligne)</label>
                        <textarea id="inputDocuments" class="form-textarea" placeholder="Facture&#10;BL&#10;Lettre du don"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="actions">Actions Réalisées (une par ligne)</label>
                        <textarea id="actions" class="form-textarea" placeholder="Vérification des documents&#10;Validation de la demande&#10;Génération du BP" required></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="outputDocuments">Documents de Sortie (un par ligne)</label>
                        <textarea id="outputDocuments" class="form-textarea" placeholder="BP du bénéficiaire&#10;Accusé de réception"></textarea>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="nextRole">Responsable Suivant</label>
                        <select id="nextRole" class="form-select">
                            <option value="">Sélectionner le responsable suivant</option>
                            <option value="demandeur">Demandeur</option>
                            <option value="beneficiaire">Bénéficiaire</option>
                            <option value="magasinier">Magasinier</option>
                            <option value="transitaire">Transitaire</option>
                            <option value="receptionniste">Réceptionniste</option>
                            <option value="rve">RVE</option>
                            <option value="archive">Archivage</option>
                        </select>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeStepModal()">
                            Annuler
                        </button>
                        <button type="submit" class="btn btn-primary">
                            Sauvegarder
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/workflow-documentation.js"></script>

    <script>
        // Initialisation de la page
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('📋 Initialisation de la documentation workflow...');

            try {
                // Initialiser Supabase
                if (typeof initializeSupabase === 'function') {
                    const { client, available } = await initializeSupabase();

                    if (available) {
                        console.log('✅ Supabase connecté pour la documentation');

                        // Vérifier les permissions admin
                        await checkAdminPermissions();

                        // Initialiser le gestionnaire de workflow
                        if (typeof WorkflowDocumentation !== 'undefined') {
                            await workflowDocumentation.initialize();
                            console.log('✅ Documentation workflow initialisée');
                        }
                    } else {
                        console.warn('⚠️ Supabase non disponible - mode lecture seule');
                        loadStaticWorkflow();
                    }
                } else {
                    console.error('❌ Fonction initializeSupabase non trouvée');
                    loadStaticWorkflow();
                }

            } catch (error) {
                console.error('❌ Erreur initialisation documentation:', error);
                loadStaticWorkflow();
            }
        });

        // Vérification des permissions administrateur
        async function checkAdminPermissions() {
            try {
                const { data: { user } } = await supabase.auth.getUser();

                if (user) {
                    const authorizedUsers = [
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>',
                        '<EMAIL>'
                    ];

                    const userRole = user.user_metadata?.role_ipt;
                    const isAuthorized = authorizedUsers.includes(user.email) || userRole === 'admin_systeme';

                    if (isAuthorized) {
                        document.getElementById('adminControls').classList.add('show');
                        showAlert('Accès administrateur activé', 'success');
                    }
                }
            } catch (error) {
                console.error('Erreur vérification permissions:', error);
            }
        }

        // Chargement du workflow statique
        function loadStaticWorkflow() {
            if (typeof WorkflowDocumentation !== 'undefined') {
                workflowDocumentation.loadStaticData();
            }
        }

        // Fonctions globales pour l'interface
        function showAlert(message, type = 'info') {
            const container = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;

            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            alert.innerHTML = `<span>${icon}</span><div>${message}</div>`;

            container.appendChild(alert);

            // Supprimer l'alerte après 5 secondes
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }

        function addNewStep() {
            if (window.workflowDocumentation) {
                workflowDocumentation.openStepModal();
            }
        }

        function validateAllSteps() {
            if (window.workflowDocumentation) {
                workflowDocumentation.validateAllSteps();
            }
        }

        function editWorkflow() {
            if (window.workflowDocumentation) {
                workflowDocumentation.enableEditMode();
            }
        }

        function resetWorkflow() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser le workflow ? Cette action est irréversible.')) {
                if (window.workflowDocumentation) {
                    workflowDocumentation.resetWorkflow();
                }
            }
        }

        function exportWorkflow() {
            if (window.workflowDocumentation) {
                workflowDocumentation.exportDocumentation();
            }
        }

        function closeStepModal() {
            document.getElementById('stepModal').style.display = 'none';
        }

        // Gestion du formulaire d'étape
        document.getElementById('stepForm').addEventListener('submit', function(e) {
            e.preventDefault();
            if (window.workflowDocumentation) {
                workflowDocumentation.saveStep();
            }
        });
    </script>
</body>
</html>